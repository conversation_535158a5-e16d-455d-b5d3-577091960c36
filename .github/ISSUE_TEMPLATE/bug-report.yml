name: "\U0001F41B Bug Report"
description: Report a bug in LangChain.js. To report a security issue, please instead use the security option below. For questions, please use the GitHub Discussions.
labels: ["02 Bug Report"]
body:
  - type: markdown
    attributes:
      value: >
        Thank you for taking the time to file a bug report. 
        
        Use this to report bugs in LangChain.js. 
        
        If you're not certain that your issue is due to a bug in LangChain, please use [GitHub Discussions](https://github.com/langchain-ai/langchainjs/discussions)
        to ask for help with your issue.
        
        Relevant links to check before filing a bug report to see if your issue has already been reported, fixed or
        if there's another way to solve your problem:
        
        [LangChain.js documentation with the integrated search](https://js.langchain.com/docs/introduction),
        [API Reference](https://api.js.langchain.com/),
        [GitHub search](https://github.com/langchain-ai/langchainjs),
        [LangChain.js Github Discussions](https://github.com/langchain-ai/langchainjs/discussions),
        [LangChain.js Github Issues](https://github.com/langchain-ai/langchainjs/issues?q=is%3Aissue),
        [LangChain.js ChatBot](https://chatjs.langchain.com/)
  - type: checkboxes
    id: checks
    attributes:
      label: Checked other resources
      description: Please confirm and check all the following options.
      options:
        - label: I added a very descriptive title to this issue.
          required: true
        - label: I searched the LangChain.js documentation with the integrated search.
          required: true
        - label: I used the GitHub search to find a similar question and didn't find it.
          required: true
        - label: I am sure that this is a bug in LangChain.js rather than my code.
          required: true
        - label: The bug is not resolved by updating to the latest stable version of LangChain (or the specific integration package).
          required: true
  - type: textarea
    id: reproduction
    validations:
      required: true
    attributes:
      label: Example Code
      description: |
        Please add a self-contained, [minimal, reproducible, example](https://stackoverflow.com/help/minimal-reproducible-example) with your use case.
        
        If a maintainer can copy it, run it, and see it right away, there's a much higher chance that you'll be able to get help.
        
        **Important!** 
        
        * Use code tags (e.g., ```typescript ... ```) to correctly [format your code](https://help.github.com/en/github/writing-on-github/creating-and-highlighting-code-blocks#syntax-highlighting).
        * INCLUDE the language label (e.g. `typescript`) after the first three backticks to enable syntax highlighting. (e.g., ```typescript rather than ```).
        * Reduce your code to the minimum required to reproduce the issue if possible. This makes it much easier for others to help you.
        * Avoid screenshots when possible, as they are hard to read and (more importantly) don't allow others to copy-and-paste your code.

      placeholder: |
        The following code: 
        
        ```typescript
        import { RunnableLambda } from "@langchain/core/runnables"

        const badCode = (inputs: Record<string, any>) => {
          throw new Error('For demo purposes');
        }

        const chain = new RunnableLambda({ func: badCode });
        await chain.invoke({ input: 'Hello!' });
        ```
  - type: textarea
    id: error
    validations:
      required: false
    attributes:
      label: Error Message and Stack Trace (if applicable)
      description: |
        If you are reporting an error, please include the full error message and stack trace.
      placeholder: |
        Exception + full stack trace
  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        What is the problem, question, or error?

        Write a short description telling what you are doing, what you expect to happen, and what is currently happening.
      placeholder: |
        * I'm trying to use the `langchain` library to do X.
        * I expect to see Y.
        * Instead, it does Z.
    validations:
      required: true
  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: |
        Please share your system info with us. 
        
        "yarn info langchain" 
        platform (windows / linux / mac)
        Node version
        yarn version
      placeholder: |
        "yarn info langchain"
        platform
        Node version
        yarn version
        
        These will only surface LangChain.js packages, don't forget to include any other relevant
        packages you're using (if you're not sure what's relevant, you can paste the entire output of `yarn info`).
    validations:
      required: true
