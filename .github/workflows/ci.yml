# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: CI

on:
  pull_request:
    paths_ignore:
      - 'docs/**'
      - 'dependency_range_tests/**'
      - 'environment_tests/**'
      - '.github/**'
      - '!.github/workflows/ci.yml'
  workflow_dispatch:  # Allows triggering the workflow manually in GitHub UI


# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time, so it's better to cancel
# pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: Check linting
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Check linting
        run: yarn run lint
