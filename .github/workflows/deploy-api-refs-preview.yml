name: Deploy API Refs Preview

on:
  workflow_dispatch:  # Allows triggering the workflow manually in GitHub UI
  # Avoid deploying on PR due to rate limits
  # pull_request:

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time, so it's better to cancel
# pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: exports-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy-api-refs-preview:
    name: Deploy Preview API Refs to Vercel
    runs-on: ubuntu-latest
    environment: Staging
    env:
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_API_DOCS_PROJECT_ID }}
      VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
      PR_URL: ${{ github.event.pull_request.html_url }}
      GITHUB_TOKEN: ${{ secrets.PR_COMMENT_GITHUB_TOKEN }}
    steps:
      - uses: actions/checkout@v2
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Build All Projects
        run: yarn turbo:command build --filter=!examples --filter=!api_refs --filter=!core_docs --filter=!create-langchain-integration
      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }} > deployment-url-api-refs.txt
      - name: Post Preview URL Comment
        run: gh pr comment $PR_URL -F deployment-url-api-refs.txt