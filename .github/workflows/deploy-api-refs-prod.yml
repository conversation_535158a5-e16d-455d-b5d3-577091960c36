name: Deploy API Refs Prod

on:
  workflow_dispatch:  # Allows triggering the workflow manually in GitHub UI
  push:
    branches: ["main", "v0.2"]

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time, so it's better to cancel
# pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: exports-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy-api-refs-prod:
    name: Deploy Prod API Refs to Vercel
    runs-on: ubuntu-latest
    environment: Production
    env:
      VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
      VERCEL_PROJECT_ID: ${{ secrets.VERCEL_API_DOCS_PROJECT_ID }}
    steps:
      - uses: actions/checkout@v2
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Build All Projects
        run: yarn turbo:command build --filter=!examples --filter=!api_refs --filter=!core_docs --filter=!create-langchain-integration
      - name: Build Project Artifacts
        run: |
          if [ ${{ github.ref }} = 'refs/heads/main' ]; then
            vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
          else
            vercel build --token=${{ secrets.VERCEL_TOKEN }}
          fi
      - name: Deploy to Vercel
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
        run: |
          if [ ${{ github.ref }} = 'refs/heads/main' ]; then
            vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
          elif [ ${{ github.ref }} = 'refs/heads/v0.2' ]; then
            .github/scripts/deployDomainVercel.sh v02
          else
            echo "Error: Deployment is only allowed for 'main' or 'v0.2' branches."
            exit 1
          fi