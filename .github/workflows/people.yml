name: <PERSON><PERSON><PERSON><PERSON> People

on:
  schedule:
    - cron: "0 2 1 * *"
  push:
    branches: [jacob/people]
  workflow_dispatch:

jobs:
  langchain-people:
    if: github.repository_owner == 'langchain-ai'
    runs-on: ubuntu-latest
    permissions: write-all
    steps:
      - name: Dump GitHub context
        env:
          GITHUB_CONTEXT: ${{ toJson(github) }}
        run: echo "$GITHUB_CONTEXT"
      - uses: actions/checkout@v4
      # Ref: https://github.com/actions/runner/issues/2033
      - name: Fix git safe.directory in container
        run: mkdir -p /home/<USER>/work/_temp/_github_home && printf "[safe]\n\tdirectory = /github/workspace" > /home/<USER>/work/_temp/_github_home/.gitconfig
      # Allow debugging with tmate
      - uses: ./.github/actions/people
        with:
          token: ${{ secrets.LANGCHAIN_PEOPLE_GITHUB_TOKEN }}
