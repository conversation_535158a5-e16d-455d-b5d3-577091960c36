# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Platform Compatibility

on:
  pull_request:
    # Only run this workflow if the following directories have changed.
    paths:
      - 'langchain/**'
      - 'langchain-core/**'
      - 'libs/**'
  workflow_dispatch:  # Allows triggering the workflow manually in GitHub UI


# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time, so it's better to cancel
# pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  platform-compatibility:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: cd ./langchain-core && yarn workspaces focus
      - name: Build @langchain/scripts
        run: cd ./libs/langchain-scripts && yarn build:internal
      - name: Build `@langchain/core`
        run: cd ./langchain-core && yarn build:internal