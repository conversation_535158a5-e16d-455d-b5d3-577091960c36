name: Standard Tests (Integration)

on:
  workflow_dispatch:
  schedule:
    - cron: '0 13 * * *'

jobs:
  standard-tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        package: [anthropic, cohere, google-genai, groq, mistralai, aws, google-vertexai-web]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Run integration tests for ${{ matrix.package }}
        run: yarn turbo test:integration --filter=@langchain/${{ matrix.package }}
        env:
          ANTHROPIC_API_KEY: ${{ matrix.package == 'anthropic' && secrets.ANTHROPIC_API_KEY || '' }}
          COHERE_API_KEY: ${{ matrix.package == 'cohere' && secrets.COHERE_API_KEY || '' }}
          GOOGLE_API_KEY: ${{ matrix.package == 'google-genai' && secrets.GOOGLE_API_KEY || '' }}
          GROQ_API_KEY: ${{ matrix.package == 'groq' && secrets.GROQ_API_KEY || '' }}
          MISTRAL_API_KEY: ${{ matrix.package == 'mistralai' && secrets.MISTRAL_API_KEY || '' }}
          BEDROCK_AWS_ACCESS_KEY_ID: ${{ matrix.package == 'aws' && secrets.BEDROCK_AWS_ACCESS_KEY_ID || '' }}
          BEDROCK_AWS_SECRET_ACCESS_KEY: ${{ matrix.package == 'aws' && secrets.BEDROCK_AWS_SECRET_ACCESS_KEY || '' }}
          BEDROCK_AWS_REGION: ${{ matrix.package == 'aws' && 'us-east-1' || '' }}
          GOOGLE_VERTEX_AI_WEB_CREDENTIALS: ${{ matrix.package == 'google-vertexai-web' && secrets.GOOGLE_VERTEX_AI_WEB_CREDENTIALS || '' }}

  # The `@langchain/openai` package contains standard tests for ChatOpenAI and AzureChatOpenAI
  # We want to run these separately, so we need to pass the exact path for each test, which means
  # we need separate jobs for each test.
  standard-tests-openai:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Build `@langchain/openai`
        run: yarn build --filter=@langchain/openai
      - name: Run integration tests for ChatOpenAI
        run: yarn workspace @langchain/openai test:single src/tests/chat_models_structured_output.int.test.ts src/tests/chat_models_responses.int.test.ts src/tests/chat_models-extended.int.test.ts src/tests/chat_models-vision.int.test.ts src/tests/chat_models.int.test.ts src/tests/chat_models.standard.int.test.ts src/tests/chat_models_responses.standard.int.test.ts
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

  standard-tests-azure-openai:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Build `@langchain/openai`
        run: yarn build --filter=@langchain/openai
      - name: Run integration tests for `@langchain/openai` AzureChatOpenAI
        run: yarn workspace @langchain/openai test:single src/tests/azure/chat_models.standard.int.test.ts src/tests/azure/chat_models.int.test.ts
        env:
          AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
          AZURE_OPENAI_API_DEPLOYMENT_NAME: "chat"
          AZURE_OPENAI_API_VERSION: ${{ secrets.AZURE_OPENAI_API_VERSION }}
          AZURE_OPENAI_BASE_PATH: ${{ secrets.AZURE_OPENAI_BASE_PATH }}

  standard-tests-bedrock:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Build `@langchain/community`
        run: yarn build --filter=@langchain/community
      - name: Run standard tests (integration) for `@langchain/community` BedrockChat
        run: yarn workspace @langchain/community test:single src/chat_models/tests/chatbedrock.standard.int.test.ts
        env:
          BEDROCK_AWS_REGION: "us-east-1"
          BEDROCK_AWS_SECRET_ACCESS_KEY: ${{ secrets.BEDROCK_AWS_SECRET_ACCESS_KEY }}
          BEDROCK_AWS_ACCESS_KEY_ID: ${{ secrets.BEDROCK_AWS_ACCESS_KEY_ID }}

  standard-tests-fireworks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 18.x
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable --mode=skip-build
      - name: Build `@langchain/community`
        run: yarn build --filter=@langchain/community
      - name: Run standard tests (integration) for `@langchain/community` ChatFireworks
        run: yarn workspace @langchain/community test:single src/chat_models/tests/chatfireworks.standard.int.test.ts
        env:
          FIREWORKS_API_KEY: ${{ secrets.FIREWORKS_API_KEY }}
