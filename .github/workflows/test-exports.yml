name: Environment tests

on:
  pull_request:
    # Only run workflow if files in these directories are changed
    paths:
      - 'langchain/**'
      - 'langchain-core/**'
      - 'libs/langchain-anthropic/**'
      - 'libs/langchain-community/**'
      - 'libs/langchain-openai/**'
  workflow_dispatch: # Allows triggering the workflow manually in GitHub UI

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time, so it's better to cancel
# pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: exports-${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PUPPETEER_SKIP_DOWNLOAD: "true"
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
  NODE_VERSION: "18.x"

# Run a separate job for each check in the docker-compose file,
# so that they run in parallel instead of overwhelming the default 2 CPU runner.
jobs:
  exports-esbuild:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable

      - name: Build dependencies
        run: yarn build --filter=langchain --filter=@langchain/anthropic --filter=@langchain/community --filter=@langchain/openai
        shell: bash

      - name: Test esbuild exports
        run: docker compose -f environment_tests/docker-compose.yml run test-exports-esbuild

  exports-esm:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable

      - name: Build dependencies
        run: yarn build --filter=langchain --filter=@langchain/anthropic --filter=@langchain/community --filter=@langchain/openai
        shell: bash

      - name: Test esm exports
        run: docker compose -f environment_tests/docker-compose.yml run test-exports-esm

  exports-cjs:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable

      - name: Build dependencies
        run: yarn build --filter=langchain --filter=@langchain/anthropic --filter=@langchain/community --filter=@langchain/openai
        shell: bash

      - name: Test cjs exports
        run: docker compose -f environment_tests/docker-compose.yml run test-exports-cjs

  exports-cf:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable

      - name: Build dependencies
        run: yarn build --filter=langchain --filter=@langchain/anthropic --filter=@langchain/community --filter=@langchain/openai
        shell: bash

      - name: Test cf exports
        run: docker compose -f environment_tests/docker-compose.yml run test-exports-cf

  exports-vercel:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable
    
      - name: Build dependencies
        run: yarn build --filter=langchain --filter=@langchain/anthropic --filter=@langchain/community --filter=@langchain/openai
        shell: bash

      - name: Test vercel exports
        run: docker compose -f environment_tests/docker-compose.yml run test-exports-vercel

  exports-vite:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable
      
      - name: Build dependencies
        run: yarn build --filter=langchain --filter=@langchain/anthropic --filter=@langchain/community --filter=@langchain/openai
        shell: bash

      - name: Test vite exports
        run: docker compose -f environment_tests/docker-compose.yml run test-exports-vite

  exports-tsc:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ env.NODE_VERSION }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable

      - name: Build dependencies
        run: yarn build --filter=langchain --filter=@langchain/anthropic --filter=@langchain/community --filter=@langchain/openai
        shell: bash

      - name: Test compiling exports with tsc
        run: docker compose -f environment_tests/docker-compose.yml run test-exports-tsc

  # exports-bun:
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v4
  #     - name: Use Node.js ${{ env.NODE_VERSION }}
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: ${{ env.NODE_VERSION }}
  #         cache: "yarn"
  #     - name: Install dependencies
  #       run: yarn install --immutable
  #     - name: Build
  #       run: yarn workspace langchain build
  #       shell: bash

  #     - name: Test bun exports
  #       run: docker compose -f environment_tests/docker-compose.yml run test-exports-bun
