# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Unit Tests (LangChain Integrations)

on:
  push:
    branches: ["main"]
  pull_request:
    # Only run when LangChain Core, or integration package code changes.
    paths:
      - "langchain-core/**"
      - "libs/**/**"
  workflow_dispatch: # Allows triggering the workflow manually in GitHub UI

# If another push to the same PR or branch happens while this workflow is still running,
# cancel the earlier run in favor of the next run.
#
# There's no point in testing an outdated version of the code. GitHub only allows
# a limited number of job runners to be active at the same time, so it's better to cancel
# pointless jobs early so that more useful jobs can run sooner.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  get-changed-files:
    runs-on: ubuntu-latest
    outputs:
      changed_files: ${{ steps.get_changes.outputs.changed_files }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 2
      - name: Get changes
        id: get_changes
        run: |
          echo "changed_files<<EOF" >> $GITHUB_OUTPUT
          git diff --name-only -r HEAD^1 HEAD | while read line; do printf "%s\n" "$line"; done >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

  prepare-matrix:
    needs: get-changed-files
    runs-on: ubuntu-latest
    env:
      PACKAGES: "anthropic,azure-openai,cloudflare,cohere,core,community,exa,google-common,google-gauth,google-genai,google-vertexai,google-vertexai-web,google-webauth,groq,mistralai,mongo,nomic,openai,pinecone,qdrant,redis,textsplitters,weaviate,yandex,baidu-qianfan,google-cloud-sql-pg"
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      matrix_length: ${{ steps.set-matrix.outputs.matrix_length }}
    steps:
      - id: set-matrix
        run: |
          changed_files="${{ needs.get-changed-files.outputs.changed_files }}"
          echo "Changed files: $changed_files"
          # Convert PACKAGES environment variable into an array
          IFS=',' read -r -a packages <<< "$PACKAGES"
          echo "Packages: ${packages[*]}"
          matrix="{\"include\": ["
          first_entry=true
          for package in "${packages[@]}"; do
            echo "Checking package: $package"
            if echo "$changed_files" | grep -q "$package"; then
              echo "Package $package found in changed files."
              if [ "$first_entry" = true ]; then
              matrix="$matrix{\"os\": \"ubuntu-latest\", \"node-version\": \"18\", \"package\": \"$package\"},{\"os\": \"ubuntu-latest\", \"node-version\": \"20\", \"package\": \"$package\"}"
              first_entry=false
            else
              matrix="$matrix, {\"os\": \"ubuntu-latest\", \"node-version\": \"18\", \"package\": \"$package\"},{\"os\": \"ubuntu-latest\", \"node-version\": \"20\", \"package\": \"$package\"}"
            fi
            fi
          done
          matrix="$matrix]}"
          matrix_length=$(echo $matrix | jq '.include | length')
          echo "Matrix: $matrix"
          echo "Matrix length: $matrix_length"
          echo "matrix_length=$matrix_length" >> $GITHUB_OUTPUT
          echo "matrix=$matrix" >> $GITHUB_OUTPUT

  unit-tests:
    name: Unit Tests
    needs: prepare-matrix
    # Only run this job if there are packages to test
    if: needs.prepare-matrix.outputs.matrix_length > 0
    runs-on: ubuntu-latest
    strategy:
      matrix: ${{ fromJson(needs.prepare-matrix.outputs.matrix) }}
    env:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: "yarn"
      - name: Install dependencies
        run: yarn install --immutable
        # Running `test:unit:ci` from root will build first, however debugging
        # in CI is easier if we separate the build step from the test step.
      - name: Build '@langchain/${{ matrix.package }}' package
        run: yarn build --filter=@langchain/${{ matrix.package }}
      - name: Test '@langchain/${{ matrix.package }}' package
        run: yarn test:unit:ci --filter=@langchain/${{ matrix.package }}
