version: "3"
services:
  # <PERSON><PERSON><PERSON>n
  langchain-latest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../langchain:/langchain
      - ./scripts:/scripts
    command: bash /scripts/langchain/test-with-latest-deps.sh
  langchain-lowest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../langchain:/langchain
      - ./scripts:/scripts
    command: bash /scripts/langchain/test-with-lowest-deps.sh

  # Community
  community-latest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/community/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-community:/libs/langchain-community
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/community/test-with-latest-deps.sh
  community-lowest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/community/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-community:/libs/langchain-community
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/community/test-with-lowest-deps.sh
  community-npm-install:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/community/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-community:/libs/langchain-community
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/community/npm-install.sh

  # OpenAI
  openai-latest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/openai/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-openai:/libs/langchain-openai
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/openai/test-with-latest-deps.sh
  openai-lowest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/openai/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-openai:/libs/langchain-openai
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/openai/test-with-lowest-deps.sh

  # Anthropic
  anthropic-latest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/anthropic/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-anthropic:/libs/langchain-anthropic
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/anthropic/test-with-latest-deps.sh
  anthropic-lowest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/anthropic/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-anthropic:/libs/langchain-anthropic
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/anthropic/test-with-lowest-deps.sh

  # Google VertexAI
  google-vertexai-latest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/google-vertexai/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-google-vertexai:/libs/langchain-google-vertexai
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/google-vertexai/test-with-latest-deps.sh
  google-vertexai-lowest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/google-vertexai/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-google-vertexai:/libs/langchain-google-vertexai
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/google-vertexai/test-with-lowest-deps.sh

  # Cohere
  cohere-latest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/cohere/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-cohere:/libs/langchain-cohere
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/cohere/test-with-latest-deps.sh
  cohere-lowest-deps:
    image: node:20
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
      COHERE_API_KEY: ${COHERE_API_KEY}
    working_dir: /app
    volumes:
      - ../turbo.json:/turbo.json
      - ./scripts/with_standard_tests/cohere/node/package.json:/package.json
      - ../libs/langchain-standard-tests:/libs/langchain-standard-tests
      - ../libs/langchain-cohere:/libs/langchain-cohere
      - ./scripts:/scripts
    command: bash /scripts/with_standard_tests/cohere/test-with-lowest-deps.sh
