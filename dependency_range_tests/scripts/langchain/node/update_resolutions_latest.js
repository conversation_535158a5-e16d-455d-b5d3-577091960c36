const fs = require("fs");

const communityPackageJsonPath = "package.json";
const currentPackageJson = JSON.parse(fs.readFileSync(communityPackageJsonPath));

if (currentPackageJson.devDependencies["@langchain/core"]) {
  delete currentPackageJson.devDependencies["@langchain/core"];
  currentPackageJson.peerDependencies["@langchain/core"] = "latest";
}

// Stupid hack
currentPackageJson.resolutions = {
  ...currentPackageJson.resolutions,
  "jackspeak": "2.1.1"
};

fs.writeFileSync(communityPackageJsonPath, JSON.stringify(currentPackageJson, null, 2));
