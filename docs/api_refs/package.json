{"name": "api_refs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "typedoc:build": "npx typedoc --options typedoc.json", "build:scripts": "node ./scripts/create-entrypoints.js && yarn typedoc:build && node ./scripts/update-typedoc-css.js", "build": "yarn clean && yarn build:scripts && next build", "start": "yarn build && next start -p 3001", "lint": "next lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx}\"", "clean": "rm -rf .next .turbo public/ && mkdir public"}, "dependencies": {"next": "14.2.28", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.2.28", "glob": "^10.3.10", "postcss": "^8", "prettier": "^2.8.3", "tailwindcss": "^3.3.0", "ts-morph": "^23.0.0", "typedoc": "^0.26.1", "typedoc-plugin-expand-object-like-types": "^0.1.2", "typescript": "~5.1.6"}}