{"buildCommand": "yarn build", "trailingSlash": false, "redirects": [{"source": "/:path*/langchain_load:rest", "destination": "/:path*/langchain.load:rest"}, {"source": "/:path*/langchain_agents:rest", "destination": "/:path*/langchain.agents:rest"}, {"source": "/:path*/langchain_tools:rest", "destination": "/:path*/langchain.tools:rest"}, {"source": "/:path*/langchain_chains:rest", "destination": "/:path*/langchain.chains:rest"}, {"source": "/:path*/langchain_chat_models:rest", "destination": "/:path*/langchain.chat_models:rest"}, {"source": "/:path*/langchain_embeddings:rest", "destination": "/:path*/langchain.embeddings:rest"}, {"source": "/:path*/langchain_vectorstores:rest", "destination": "/:path*/langchain.vectorstores:rest"}, {"source": "/:path*/langchain_text_splitter:rest", "destination": "/:path*/langchain.text_splitter:rest"}, {"source": "/:path*/langchain_memory:rest", "destination": "/:path*/langchain.memory:rest"}, {"source": "/:path*/langchain_document:rest", "destination": "/:path*/langchain.document:rest"}, {"source": "/:path*/langchain_document_loaders:rest", "destination": "/:path*/langchain.document_loaders:rest"}, {"source": "/:path*/langchain_document_transformers:rest", "destination": "/:path*/langchain.document_transformers:rest"}, {"source": "/:path*/langchain_sql_db:rest", "destination": "/:path*/langchain.sql_db:rest"}, {"source": "/:path*/langchain_callbacks:rest", "destination": "/:path*/langchain.callbacks:rest"}, {"source": "/:path*/langchain_output_parsers:rest", "destination": "/:path*/langchain.output_parsers:rest"}, {"source": "/:path*/langchain_retrievers:rest", "destination": "/:path*/langchain.retrievers:rest"}, {"source": "/:path*/langchain_cache:rest", "destination": "/:path*/langchain.cache:rest"}, {"source": "/:path*/langchain_stores:rest", "destination": "/:path*/langchain.stores:rest"}, {"source": "/:path*/langchain_storage:rest", "destination": "/:path*/langchain.storage:rest"}, {"source": "/:path*/langchain_hub:rest", "destination": "/:path*/langchain.hub:rest"}, {"source": "/:path*/langchain_util:rest", "destination": "/:path*/langchain.util:rest"}, {"source": "/:path*/langchain_experimental:rest", "destination": "/:path*/langchain.experimental:rest"}, {"source": "/:path*/langchain_evaluation:rest", "destination": "/:path*/langchain.evaluation:rest"}, {"source": "/:path*/langchain_smith:rest", "destination": "/:path*/langchain.smith:rest"}, {"source": "/:path*/langchain_runnables:rest", "destination": "/:path*/langchain.runnables:rest"}, {"source": "/:path*/langchain_indexes:rest", "destination": "/:path*/langchain.indexes:rest"}, {"source": "/:path*/langchain_schema:rest", "destination": "/:path*/langchain.schema:rest"}, {"source": "/:path*/langchain_core_:rest", "destination": "/:path*/_langchain_core.:rest"}, {"source": "/:path*/langchain_core.:rest", "destination": "/:path*/_langchain_core.:rest"}, {"source": "/:path*/langchain_anthropic_experimental(_|\\.):rest", "destination": "/:path*/_langchain_anthropic.experimental.:rest"}, {"source": "/:path*/langchain_anthropic.ChatAnthropic.:rest", "destination": "/:path*/_langchain_anthropic.index.ChatAnthropic.:rest"}, {"source": "/:path*/langchain_anthropic.ChatAnthropicMessages.:rest", "destination": "/:path*/_langchain_anthropic.index.ChatAnthropicMessages.:rest"}, {"source": "/:path*/langchain_anthropic.AnthropicInput.:rest", "destination": "/:path*/_langchain_anthropic.index.AnthropicInput.:rest"}, {"source": "/:path*/langchain_anthropic.ChatAnthropicCallOptions.:rest", "destination": "/:path*/_langchain_anthropic.index.ChatAnthropicCallOptions.:rest"}, {"source": "/:path*/langchain_aws(_|\\.):rest", "destination": "/:path*/_langchain_aws.:rest"}, {"source": "/:path*/langchain_azure_cosmosdb(_|\\.):rest", "destination": "/:path*/_langchain_azure_cosmosdb.:rest"}, {"source": "/:path*/langchain_azure_dynamic_sessions(_|\\.):rest", "destination": "/:path*/_langchain_azure_dynamic_sessions.:rest"}, {"source": "/:path*/langchain_baidu_qianfan(_|\\.):rest", "destination": "/:path*/_langchain_baidu_qianfan.:rest"}, {"source": "/:path*/langchain_cloudflare_langgraph_checkpointers(_|\\.):rest", "destination": "/:path*/_langchain_cloudflare.langgraph_checkpointers.:rest"}, {"source": "/:path*/langchain_cloudflare.ChatCloudflareWorkersAI:rest", "destination": "/:path*/_langchain_cloudflare.index.ChatCloudflareWorkersAI:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareD1MessageHistory:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareD1MessageHistory:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareKVCache:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareKVCache:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareVectorizeStore:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareVectorizeStore:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareWorkersAI:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareWorkersAI:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareWorkersAIEmbeddings:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareWorkersAIEmbeddings:rest"}, {"source": "/:path*/langchain_cloudflare.ChatCloudflareWorkersAICallOptions:rest", "destination": "/:path*/_langchain_cloudflare.index.ChatCloudflareWorkersAICallOptions:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareWorkersAIEmbeddingsParams:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareWorkersAIEmbeddingsParams:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareWorkersAIInput:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareWorkersAIInput:rest"}, {"source": "/:path*/langchain_cloudflare.VectorizeLibArgs:rest", "destination": "/:path*/_langchain_cloudflare.index.VectorizeLibArgs:rest"}, {"source": "/:path*/langchain_cloudflare.CloudflareD1MessageHistoryInput:rest", "destination": "/:path*/_langchain_cloudflare.index.CloudflareD1MessageHistoryInput:rest"}, {"source": "/:path*/langchain_cloudflare.VectorizeDeleteParams:rest", "destination": "/:path*/_langchain_cloudflare.index.VectorizeDeleteParams:rest"}, {"source": "/:path*/langchain_cohere(_|\\.):rest", "destination": "/:path*/_langchain_cohere.:rest"}, {"source": "/:path*/langchain_community_:rest", "destination": "/:path*/_langchain_community.:rest"}, {"source": "/:path*/langchain_exa(_|\\.):rest", "destination": "/:path*/_langchain_exa.:rest"}, {"source": "/:path*/langchain_google_common_types(_|\\.):rest", "destination": "/:path*/_langchain_google_common.types.:rest"}, {"source": "/:path*/langchain_google_common_utils(_|\\.):rest", "destination": "/:path*/_langchain_google_common.utils.:rest"}, {"source": "/:path*/langchain_google_common.AbstractGoogleLLMConnection.:rest", "destination": "/:path*/_langchain_google_common.index.AbstractGoogleLLMConnection.:rest"}, {"source": "/:path*/langchain_google_common.ApiKeyGoogleAuth.:rest", "destination": "/:path*/_langchain_google_common.index.ApiKeyGoogleAuth.:rest"}, {"source": "/:path*/langchain_google_common.BaseGoogleEmbeddings.:rest", "destination": "/:path*/_langchain_google_common.index.BaseGoogleEmbeddings.:rest"}, {"source": "/:path*/langchain_google_common.ChatGoogleBase.:rest", "destination": "/:path*/_langchain_google_common.index.ChatGoogleBase.:rest"}, {"source": "/:path*/langchain_google_common.ComplexJsonStream.:rest", "destination": "/:path*/_langchain_google_common.index.ComplexJsonStream.:rest"}, {"source": "/:path*/langchain_google_common.GoogleAIConnection.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleAIConnection.:rest"}, {"source": "/:path*/langchain_google_common.GoogleAbstractedFetchClient.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleAbstractedFetchClient.:rest"}, {"source": "/:path*/langchain_google_common.GoogleBaseLLM.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleBaseLLM.:rest"}, {"source": "/:path*/langchain_google_common.GoogleConnection.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleConnection.:rest"}, {"source": "/:path*/langchain_google_common.GoogleHostConnection.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleHostConnection.:rest"}, {"source": "/:path*/langchain_google_common.JsonStream.:rest", "destination": "/:path*/_langchain_google_common.index.JsonStream.:rest"}, {"source": "/:path*/langchain_google_common.ReadableJsonStream.:rest", "destination": "/:path*/_langchain_google_common.index.ReadableJsonStream.:rest"}, {"source": "/:path*/langchain_google_common.BaseGoogleEmbeddingsOptions.:rest", "destination": "/:path*/_langchain_google_common.index.BaseGoogleEmbeddingsOptions.:rest"}, {"source": "/:path*/langchain_google_common.BaseGoogleEmbeddingsParams.:rest", "destination": "/:path*/_langchain_google_common.index.BaseGoogleEmbeddingsParams.:rest"}, {"source": "/:path*/langchain_google_common.ChatGoogleBaseInput.:rest", "destination": "/:path*/_langchain_google_common.index.ChatGoogleBaseInput.:rest"}, {"source": "/:path*/langchain_google_common.GoogleAbstractedClient.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleAbstractedClient.:rest"}, {"source": "/:path*/langchain_google_common.GoogleEmbeddingsInstance.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleEmbeddingsInstance.:rest"}, {"source": "/:path*/langchain_google_common.GoogleEmbeddingsResponse.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleEmbeddingsResponse.:rest"}, {"source": "/:path*/langchain_google_common.GoogleAbstractedClientOps.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleAbstractedClientOps.:rest"}, {"source": "/:path*/langchain_google_common.GoogleAbstractedClientOpsMethod.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleAbstractedClientOpsMethod.:rest"}, {"source": "/:path*/langchain_google_common.GoogleAbstractedClientOpsResponseType.:rest", "destination": "/:path*/_langchain_google_common.index.GoogleAbstractedClientOpsResponseType.:rest"}, {"source": "/:path*/langchain_google_common.aiPlatformScope.:rest", "destination": "/:path*/_langchain_google_common.index.aiPlatformScope.:rest"}, {"source": "/:path*/langchain_google_common.complexValue.:rest", "destination": "/:path*/_langchain_google_common.index.complexValue.:rest"}, {"source": "/:path*/langchain_google_common.convertToGeminiTools.:rest", "destination": "/:path*/_langchain_google_common.index.convertToGeminiTools.:rest"}, {"source": "/:path*/langchain_google_common.copyAIModelParams.:rest", "destination": "/:path*/_langchain_google_common.index.copyAIModelParams.:rest"}, {"source": "/:path*/langchain_google_common.copyAIModelParamsInto.:rest", "destination": "/:path*/_langchain_google_common.index.copyAIModelParamsInto.:rest"}, {"source": "/:path*/langchain_google_common.copyAndValidateModelParamsInto.:rest", "destination": "/:path*/_langchain_google_common.index.copyAndValidateModelParamsInto.:rest"}, {"source": "/:path*/langchain_google_common.ensureAuthOptionScopes.:rest", "destination": "/:path*/_langchain_google_common.index.ensureAuthOptionScopes.:rest"}, {"source": "/:path*/langchain_google_common.jsonSchemaToGeminiParameters.:rest", "destination": "/:path*/_langchain_google_common.index.jsonSchemaToGeminiParameters.:rest"}, {"source": "/:path*/langchain_google_common.modelToFamily.:rest", "destination": "/:path*/_langchain_google_common.index.modelToFamily.:rest"}, {"source": "/:path*/langchain_google_common.removeAdditionalProperties.:rest", "destination": "/:path*/_langchain_google_common.index.removeAdditionalProperties.:rest"}, {"source": "/:path*/langchain_google_common.simpleValue.:rest", "destination": "/:path*/_langchain_google_common.index.simpleValue.:rest"}, {"source": "/:path*/langchain_google_common.validateModelParams.:rest", "destination": "/:path*/_langchain_google_common.index.validateModelParams.:rest"}, {"source": "/:path*/langchain_google_common.zodToGeminiParameters.:rest", "destination": "/:path*/_langchain_google_common.index.zodToGeminiParameters.:rest"}, {"source": "/:path*/langchain_google_genai(_|\\.):rest", "destination": "/:path*/_langchain_google_genai.:rest"}, {"source": "/:path*/langchain_google_vertexai_types(_|\\.):rest", "destination": "/:path*/_langchain_google_vertexai.types.:rest"}, {"source": "/:path*/langchain_google_vertexai_utils(_|\\.):rest", "destination": "/:path*/_langchain_google_vertexai.utils.:rest"}, {"source": "/:path*/langchain_google_vertexai.ChatVertexAI.:rest", "destination": "/:path*/_langchain_google_vertexai.index.ChatVertexAI.:rest"}, {"source": "/:path*/langchain_google_vertexai.VertexAI.:rest", "destination": "/:path*/_langchain_google_vertexai.index.VertexAI.:rest"}, {"source": "/:path*/langchain_google_vertexai.VertexAIEmbeddings.:rest", "destination": "/:path*/_langchain_google_vertexai.index.VertexAIEmbeddings.:rest"}, {"source": "/:path*/langchain_google_vertexai.ChatVertexAIInput.:rest", "destination": "/:path*/_langchain_google_vertexai.index.ChatVertexAIInput.:rest"}, {"source": "/:path*/langchain_google_vertexai.GoogleVertexAIEmbeddingsInput.:rest", "destination": "/:path*/_langchain_google_vertexai.index.GoogleVertexAIEmbeddingsInput.:rest"}, {"source": "/:path*/langchain_google_vertexai.VertexAIInput.:rest", "destination": "/:path*/_langchain_google_vertexai.index.VertexAIInput.:rest"}, {"source": "/:path*/langchain_google_vertexai_web_types(_|\\.):rest", "destination": "/:path*/_langchain_google_vertexai_web.types.:rest"}, {"source": "/:path*/langchain_google_vertexai_web_utils(_|\\.):rest", "destination": "/:path*/_langchain_google_vertexai_web.utils.:rest"}, {"source": "/:path*/langchain_google_vertexai_web.ChatVertexAI.:rest", "destination": "/:path*/_langchain_google_vertexai_web.index.ChatVertexAI.:rest"}, {"source": "/:path*/langchain_google_vertexai_web.VertexAI.:rest", "destination": "/:path*/_langchain_google_vertexai_web.index.VertexAI.:rest"}, {"source": "/:path*/langchain_google_vertexai_web.VertexAIEmbeddings.:rest", "destination": "/:path*/_langchain_google_vertexai_web.index.VertexAIEmbeddings.:rest"}, {"source": "/:path*/langchain_google_vertexai_web.ChatVertexAIInput.:rest", "destination": "/:path*/_langchain_google_vertexai_web.index.ChatVertexAIInput.:rest"}, {"source": "/:path*/langchain_google_vertexai_web.GoogleVertexAIEmbeddingsInput.:rest", "destination": "/:path*/_langchain_google_vertexai_web.index.GoogleVertexAIEmbeddingsInput.:rest"}, {"source": "/:path*/langchain_google_vertexai_web.VertexAIInput.:rest", "destination": "/:path*/_langchain_google_vertexai_web.index.VertexAIInput.:rest"}, {"source": "/:path*/langchain_groq(_|\\.):rest", "destination": "/:path*/_langchain_groq.:rest"}, {"source": "/:path*/langchain_mistralai(_|\\.):rest", "destination": "/:path*/_langchain_mistralai.:rest"}, {"source": "/:path*/langchain_mixedbread_ai(_|\\.):rest", "destination": "/:path*/_langchain_mixedbread_ai.:rest"}, {"source": "/:path*/langchain_mongodb(_|\\.):rest", "destination": "/:path*/_langchain_mongodb.:rest"}, {"source": "/:path*/langchain_nomic(_|\\.):rest", "destination": "/:path*/_langchain_nomic.:rest"}, {"source": "/:path*/langchain_ollama(_|\\.):rest", "destination": "/:path*/_langchain_ollama.:rest"}, {"source": "/:path*/langchain_openai(_|\\.):rest", "destination": "/:path*/_langchain_openai.:rest"}, {"source": "/:path*/langchain_pinecone(_|\\.):rest", "destination": "/:path*/_langchain_pinecone.:rest"}, {"source": "/:path*/langchain_qdrant(_|\\.):rest", "destination": "/:path*/_langchain_qdrant.:rest"}, {"source": "/:path*/langchain_redis(_|\\.):rest", "destination": "/:path*/_langchain_redis.:rest"}, {"source": "/:path*/langchain_textsplitters(_|\\.):rest", "destination": "/:path*/_langchain_textsplitters.:rest"}, {"source": "/:path*/langchain_weaviate(_|\\.):rest", "destination": "/:path*/_langchain_weaviate.:rest"}, {"source": "/:path*/langchain_yandex(_|\\.):rest", "destination": "/:path*/_langchain_yandex.:rest"}]}