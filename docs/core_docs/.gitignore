# Dependencies
/node_modules

# Production
/build

# Generated files
.docusaurus
.cache-loader
docs/api
src/supabase.d.ts

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
/scripts/tmp

# ESLint
.eslintcache

.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

/.quarto/
# AUTO_GENERATED_DOCS
docs/tutorials/summarization.md
docs/tutorials/summarization.mdx
docs/tutorials/sql_qa.md
docs/tutorials/sql_qa.mdx
docs/tutorials/retrievers.md
docs/tutorials/retrievers.mdx
docs/tutorials/rag.md
docs/tutorials/rag.mdx
docs/tutorials/qa_chat_history.md
docs/tutorials/qa_chat_history.mdx
docs/tutorials/llm_chain.md
docs/tutorials/llm_chain.mdx
docs/tutorials/graph.md
docs/tutorials/graph.mdx
docs/tutorials/extraction.md
docs/tutorials/extraction.mdx
docs/tutorials/classification.md
docs/tutorials/classification.mdx
docs/tutorials/chatbot.md
docs/tutorials/chatbot.mdx
docs/how_to/trim_messages.md
docs/how_to/trim_messages.mdx
docs/how_to/tools_prompting.md
docs/how_to/tools_prompting.mdx
docs/how_to/tools_few_shot.md
docs/how_to/tools_few_shot.mdx
docs/how_to/tools_error.md
docs/how_to/tools_error.mdx
docs/how_to/tools_builtin.md
docs/how_to/tools_builtin.mdx
docs/how_to/tool_streaming.md
docs/how_to/tool_streaming.mdx
docs/how_to/tool_stream_events.md
docs/how_to/tool_stream_events.mdx
docs/how_to/tool_runtime.md
docs/how_to/tool_runtime.mdx
docs/how_to/tool_results_pass_to_model.md
docs/how_to/tool_results_pass_to_model.mdx
docs/how_to/tool_configure.md
docs/how_to/tool_configure.mdx
docs/how_to/tool_choice.md
docs/how_to/tool_choice.mdx
docs/how_to/tool_calls_multimodal.md
docs/how_to/tool_calls_multimodal.mdx
docs/how_to/tool_calling_parallel.md
docs/how_to/tool_calling_parallel.mdx
docs/how_to/tool_calling.md
docs/how_to/tool_calling.mdx
docs/how_to/tool_artifacts.md
docs/how_to/tool_artifacts.mdx
docs/how_to/structured_output.md
docs/how_to/structured_output.mdx
docs/how_to/streaming.md
docs/how_to/streaming.mdx
docs/how_to/split_by_token.md
docs/how_to/split_by_token.mdx
docs/how_to/sequence.md
docs/how_to/sequence.mdx
docs/how_to/self_query.md
docs/how_to/self_query.mdx
docs/how_to/recursive_text_splitter.md
docs/how_to/recursive_text_splitter.mdx
docs/how_to/query_no_queries.md
docs/how_to/query_no_queries.mdx
docs/how_to/query_multiple_retrievers.md
docs/how_to/query_multiple_retrievers.mdx
docs/how_to/query_multiple_queries.md
docs/how_to/query_multiple_queries.mdx
docs/how_to/query_high_cardinality.md
docs/how_to/query_high_cardinality.mdx
docs/how_to/query_few_shot.md
docs/how_to/query_few_shot.mdx
docs/how_to/query_constructing_filters.md
docs/how_to/query_constructing_filters.mdx
docs/how_to/qa_streaming.md
docs/how_to/qa_streaming.mdx
docs/how_to/qa_sources.md
docs/how_to/qa_sources.mdx
docs/how_to/qa_per_user.md
docs/how_to/qa_per_user.mdx
docs/how_to/qa_citations.md
docs/how_to/qa_citations.mdx
docs/how_to/qa_chat_history_how_to.md
docs/how_to/qa_chat_history_how_to.mdx
docs/how_to/prompts_composition.md
docs/how_to/prompts_composition.mdx
docs/how_to/passthrough.md
docs/how_to/passthrough.mdx
docs/how_to/output_parser_xml.md
docs/how_to/output_parser_xml.mdx
docs/how_to/output_parser_structured.md
docs/how_to/output_parser_structured.mdx
docs/how_to/output_parser_json.md
docs/how_to/output_parser_json.mdx
docs/how_to/output_parser_fixing.md
docs/how_to/output_parser_fixing.mdx
docs/how_to/multiple_queries.md
docs/how_to/multiple_queries.mdx
docs/how_to/multimodal_prompts.md
docs/how_to/multimodal_prompts.mdx
docs/how_to/multimodal_inputs.md
docs/how_to/multimodal_inputs.mdx
docs/how_to/migrate_agent.md
docs/how_to/migrate_agent.mdx
docs/how_to/message_history.md
docs/how_to/message_history.mdx
docs/how_to/merge_message_runs.md
docs/how_to/merge_message_runs.mdx
docs/how_to/logprobs.md
docs/how_to/logprobs.mdx
docs/how_to/lcel_cheatsheet.md
docs/how_to/lcel_cheatsheet.mdx
docs/how_to/graph_semantic.md
docs/how_to/graph_semantic.mdx
docs/how_to/graph_prompting.md
docs/how_to/graph_prompting.mdx
docs/how_to/graph_mapping.md
docs/how_to/graph_mapping.mdx
docs/how_to/graph_constructing.md
docs/how_to/graph_constructing.mdx
docs/how_to/functions.md
docs/how_to/functions.mdx
docs/how_to/filter_messages.md
docs/how_to/filter_messages.mdx
docs/how_to/few_shot_examples_chat.md
docs/how_to/few_shot_examples_chat.mdx
docs/how_to/few_shot_examples.md
docs/how_to/few_shot_examples.mdx
docs/how_to/extraction_parse.md
docs/how_to/extraction_parse.mdx
docs/how_to/extraction_long_text.md
docs/how_to/extraction_long_text.mdx
docs/how_to/extraction_examples.md
docs/how_to/extraction_examples.mdx
docs/how_to/example_selectors_langsmith.md
docs/how_to/example_selectors_langsmith.mdx
docs/how_to/example_selectors.md
docs/how_to/example_selectors.mdx
docs/how_to/document_loader_markdown.md
docs/how_to/document_loader_markdown.mdx
docs/how_to/document_loader_html.md
docs/how_to/document_loader_html.mdx
docs/how_to/custom_tools.md
docs/how_to/custom_tools.mdx
docs/how_to/custom_llm.md
docs/how_to/custom_llm.mdx
docs/how_to/custom_chat.md
docs/how_to/custom_chat.mdx
docs/how_to/custom_callbacks.md
docs/how_to/custom_callbacks.mdx
docs/how_to/convert_runnable_to_tool.md
docs/how_to/convert_runnable_to_tool.mdx
docs/how_to/code_splitter.md
docs/how_to/code_splitter.mdx
docs/how_to/chatbots_tools.md
docs/how_to/chatbots_tools.mdx
docs/how_to/chatbots_retrieval.md
docs/how_to/chatbots_retrieval.mdx
docs/how_to/chatbots_memory.md
docs/how_to/chatbots_memory.mdx
docs/how_to/chat_streaming.md
docs/how_to/chat_streaming.mdx
docs/how_to/character_text_splitter.md
docs/how_to/character_text_splitter.mdx
docs/how_to/cancel_execution.md
docs/how_to/cancel_execution.mdx
docs/how_to/callbacks_serverless.md
docs/how_to/callbacks_serverless.mdx
docs/how_to/callbacks_runtime.md
docs/how_to/callbacks_runtime.mdx
docs/how_to/callbacks_custom_events.md
docs/how_to/callbacks_custom_events.mdx
docs/how_to/callbacks_constructor.md
docs/how_to/callbacks_constructor.mdx
docs/how_to/callbacks_attach.md
docs/how_to/callbacks_attach.mdx
docs/how_to/binding.md
docs/how_to/binding.mdx
docs/how_to/assign.md
docs/how_to/assign.mdx
docs/how_to/agent_executor.md
docs/how_to/agent_executor.mdx
docs/concepts/t.md
docs/concepts/t.mdx
docs/versions/migrating_memory/conversation_summary_memory.md
docs/versions/migrating_memory/conversation_summary_memory.mdx
docs/versions/migrating_memory/conversation_buffer_window_memory.md
docs/versions/migrating_memory/conversation_buffer_window_memory.mdx
docs/versions/migrating_memory/chat_history.md
docs/versions/migrating_memory/chat_history.mdx
docs/troubleshooting/errors/INVALID_TOOL_RESULTS.md
docs/troubleshooting/errors/INVALID_TOOL_RESULTS.mdx
docs/integrations/vectorstores/weaviate.md
docs/integrations/vectorstores/weaviate.mdx
docs/integrations/vectorstores/upstash.md
docs/integrations/vectorstores/upstash.mdx
docs/integrations/vectorstores/supabase.md
docs/integrations/vectorstores/supabase.mdx
docs/integrations/vectorstores/redis.md
docs/integrations/vectorstores/redis.mdx
docs/integrations/vectorstores/qdrant.md
docs/integrations/vectorstores/qdrant.mdx
docs/integrations/vectorstores/pinecone.md
docs/integrations/vectorstores/pinecone.mdx
docs/integrations/vectorstores/pgvector.md
docs/integrations/vectorstores/pgvector.mdx
docs/integrations/vectorstores/mongodb_atlas.md
docs/integrations/vectorstores/mongodb_atlas.mdx
docs/integrations/vectorstores/memory.md
docs/integrations/vectorstores/memory.mdx
docs/integrations/vectorstores/mariadb.md
docs/integrations/vectorstores/mariadb.mdx
docs/integrations/vectorstores/hnswlib.md
docs/integrations/vectorstores/hnswlib.mdx
docs/integrations/vectorstores/google_cloudsql_pg.md
docs/integrations/vectorstores/google_cloudsql_pg.mdx
docs/integrations/vectorstores/faiss.md
docs/integrations/vectorstores/faiss.mdx
docs/integrations/vectorstores/elasticsearch.md
docs/integrations/vectorstores/elasticsearch.mdx
docs/integrations/vectorstores/chroma.md
docs/integrations/vectorstores/chroma.mdx
docs/integrations/vectorstores/azion-edgesql.md
docs/integrations/vectorstores/azion-edgesql.mdx
docs/integrations/toolkits/vectorstore.md
docs/integrations/toolkits/vectorstore.mdx
docs/integrations/toolkits/sql.md
docs/integrations/toolkits/sql.mdx
docs/integrations/toolkits/openapi.md
docs/integrations/toolkits/openapi.mdx
docs/integrations/toolkits/ibm.md
docs/integrations/toolkits/ibm.mdx
docs/integrations/tools/tavily_search_community.md
docs/integrations/tools/tavily_search_community.mdx
docs/integrations/tools/tavily_search.md
docs/integrations/tools/tavily_search.mdx
docs/integrations/tools/tavily_extract.md
docs/integrations/tools/tavily_extract.mdx
docs/integrations/tools/serpapi.md
docs/integrations/tools/serpapi.mdx
docs/integrations/tools/google_scholar.md
docs/integrations/tools/google_scholar.mdx
docs/integrations/tools/exa_search.md
docs/integrations/tools/exa_search.mdx
docs/integrations/tools/duckduckgo_search.md
docs/integrations/tools/duckduckgo_search.mdx
docs/integrations/stores/in_memory.md
docs/integrations/stores/in_memory.mdx
docs/integrations/stores/file_system.md
docs/integrations/stores/file_system.mdx
docs/integrations/text_embedding/togetherai.md
docs/integrations/text_embedding/togetherai.mdx
docs/integrations/text_embedding/pinecone.md
docs/integrations/text_embedding/pinecone.mdx
docs/integrations/text_embedding/openai.md
docs/integrations/text_embedding/openai.mdx
docs/integrations/text_embedding/ollama.md
docs/integrations/text_embedding/ollama.mdx
docs/integrations/text_embedding/mistralai.md
docs/integrations/text_embedding/mistralai.mdx
docs/integrations/text_embedding/ibm.md
docs/integrations/text_embedding/ibm.mdx
docs/integrations/text_embedding/google_vertex_ai.md
docs/integrations/text_embedding/google_vertex_ai.mdx
docs/integrations/text_embedding/google_generativeai.md
docs/integrations/text_embedding/google_generativeai.mdx
docs/integrations/text_embedding/fireworks.md
docs/integrations/text_embedding/fireworks.mdx
docs/integrations/text_embedding/cohere.md
docs/integrations/text_embedding/cohere.mdx
docs/integrations/text_embedding/cloudflare_ai.md
docs/integrations/text_embedding/cloudflare_ai.mdx
docs/integrations/text_embedding/bytedance_doubao.md
docs/integrations/text_embedding/bytedance_doubao.mdx
docs/integrations/text_embedding/bedrock.md
docs/integrations/text_embedding/bedrock.mdx
docs/integrations/text_embedding/azure_openai.md
docs/integrations/text_embedding/azure_openai.mdx
docs/integrations/retrievers/tavily.md
docs/integrations/retrievers/tavily.mdx
docs/integrations/retrievers/kendra-retriever.md
docs/integrations/retrievers/kendra-retriever.mdx
docs/integrations/retrievers/exa.md
docs/integrations/retrievers/exa.mdx
docs/integrations/retrievers/bm25.md
docs/integrations/retrievers/bm25.mdx
docs/integrations/retrievers/bedrock-knowledge-bases.md
docs/integrations/retrievers/bedrock-knowledge-bases.mdx
docs/integrations/retrievers/azion-edgesql.md
docs/integrations/retrievers/azion-edgesql.mdx
docs/integrations/llms/together.md
docs/integrations/llms/together.mdx
docs/integrations/llms/openai.md
docs/integrations/llms/openai.mdx
docs/integrations/llms/ollama.md
docs/integrations/llms/ollama.mdx
docs/integrations/llms/mistral.md
docs/integrations/llms/mistral.mdx
docs/integrations/llms/ibm.md
docs/integrations/llms/ibm.mdx
docs/integrations/llms/google_vertex_ai.md
docs/integrations/llms/google_vertex_ai.mdx
docs/integrations/llms/fireworks.md
docs/integrations/llms/fireworks.mdx
docs/integrations/llms/cohere.md
docs/integrations/llms/cohere.mdx
docs/integrations/llms/cloudflare_workersai.md
docs/integrations/llms/cloudflare_workersai.mdx
docs/integrations/llms/bedrock.md
docs/integrations/llms/bedrock.mdx
docs/integrations/llms/azure.md
docs/integrations/llms/azure.mdx
docs/integrations/llms/arcjet.md
docs/integrations/llms/arcjet.mdx
docs/integrations/document_compressors/ibm.md
docs/integrations/document_compressors/ibm.mdx
docs/integrations/chat/xai.md
docs/integrations/chat/xai.mdx
docs/integrations/chat/togetherai.md
docs/integrations/chat/togetherai.mdx
docs/integrations/chat/perplexity.md
docs/integrations/chat/perplexity.mdx
docs/integrations/chat/openai.md
docs/integrations/chat/openai.mdx
docs/integrations/chat/ollama.md
docs/integrations/chat/ollama.mdx
docs/integrations/chat/novita.md
docs/integrations/chat/novita.mdx
docs/integrations/chat/mistral.md
docs/integrations/chat/mistral.mdx
docs/integrations/chat/ibm.md
docs/integrations/chat/ibm.mdx
docs/integrations/chat/groq.md
docs/integrations/chat/groq.mdx
docs/integrations/chat/google_vertex_ai.md
docs/integrations/chat/google_vertex_ai.mdx
docs/integrations/chat/google_generativeai.md
docs/integrations/chat/google_generativeai.mdx
docs/integrations/chat/fireworks.md
docs/integrations/chat/fireworks.mdx
docs/integrations/chat/deepseek.md
docs/integrations/chat/deepseek.mdx
docs/integrations/chat/cohere.md
docs/integrations/chat/cohere.mdx
docs/integrations/chat/cloudflare_workersai.md
docs/integrations/chat/cloudflare_workersai.mdx
docs/integrations/chat/cerebras.md
docs/integrations/chat/cerebras.mdx
docs/integrations/chat/bedrock_converse.md
docs/integrations/chat/bedrock_converse.mdx
docs/integrations/chat/bedrock.md
docs/integrations/chat/bedrock.mdx
docs/integrations/chat/azure.md
docs/integrations/chat/azure.mdx
docs/integrations/chat/arcjet.md
docs/integrations/chat/arcjet.mdx
docs/integrations/chat/anthropic.md
docs/integrations/chat/anthropic.mdx
docs/integrations/retrievers/self_query/weaviate.md
docs/integrations/retrievers/self_query/weaviate.mdx
docs/integrations/retrievers/self_query/vectara.md
docs/integrations/retrievers/self_query/vectara.mdx
docs/integrations/retrievers/self_query/supabase.md
docs/integrations/retrievers/self_query/supabase.mdx
docs/integrations/retrievers/self_query/qdrant.md
docs/integrations/retrievers/self_query/qdrant.mdx
docs/integrations/retrievers/self_query/pinecone.md
docs/integrations/retrievers/self_query/pinecone.mdx
docs/integrations/retrievers/self_query/memory.md
docs/integrations/retrievers/self_query/memory.mdx
docs/integrations/retrievers/self_query/hnswlib.md
docs/integrations/retrievers/self_query/hnswlib.mdx
docs/integrations/retrievers/self_query/chroma.md
docs/integrations/retrievers/self_query/chroma.mdx
docs/integrations/document_loaders/web_loaders/web_puppeteer.md
docs/integrations/document_loaders/web_loaders/web_puppeteer.mdx
docs/integrations/document_loaders/web_loaders/web_cheerio.md
docs/integrations/document_loaders/web_loaders/web_cheerio.mdx
docs/integrations/document_loaders/web_loaders/recursive_url_loader.md
docs/integrations/document_loaders/web_loaders/recursive_url_loader.mdx
docs/integrations/document_loaders/web_loaders/pdf.md
docs/integrations/document_loaders/web_loaders/pdf.mdx
docs/integrations/document_loaders/web_loaders/langsmith.md
docs/integrations/document_loaders/web_loaders/langsmith.mdx
docs/integrations/document_loaders/web_loaders/firecrawl.md
docs/integrations/document_loaders/web_loaders/firecrawl.mdx
docs/integrations/document_loaders/file_loaders/unstructured.md
docs/integrations/document_loaders/file_loaders/unstructured.mdx
docs/integrations/document_loaders/file_loaders/text.md
docs/integrations/document_loaders/file_loaders/text.mdx
docs/integrations/document_loaders/file_loaders/pdf.md
docs/integrations/document_loaders/file_loaders/pdf.mdx
docs/integrations/document_loaders/file_loaders/directory.md
docs/integrations/document_loaders/file_loaders/directory.mdx
docs/integrations/document_loaders/file_loaders/csv.md
docs/integrations/document_loaders/file_loaders/csv.mdx