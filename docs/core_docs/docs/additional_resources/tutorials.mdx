# External guides

Below are links to external tutorials and courses on LangChain.js. For other written guides on common use cases for LangChain.js, check out the [tutorials](/docs/tutorials/) and [how to](/docs/how_to/) sections.

---

## Deeplearning.ai

We've partnered with [Deeplearning.ai](https://deeplearning.ai) and [<PERSON>](https://en.wikipedia.org/wiki/<PERSON>_<PERSON>)
on a LangChain.js short course.

It covers LCEL and other building blocks you can combine to build more complex chains, as well as fundamentals around
loading data for retrieval augmented generation (RAG). Try it for free below:

- [Build LLM Apps with LangChain.js](https://www.deeplearning.ai/short-courses/build-llm-apps-with-langchain-js)

## Scrimba interactive guides

[Scrimba](https://scrimba.com) is a code-learning platform that allows you to interactively edit and run
code while watching a video walkthrough.

We've partnered with Scrimba on course materials (called "scrims") that teach the fundamentals of building with LangChain.js -
check them out below, and check back for more as they become available!

### Learn Lang<PERSON>hain.js

- [Learn <PERSON>hain.js on Scrimba](https://scrimba.com/learn/langchain)

An full end-to-end course that walks through how to build a chatbot that can answer questions about a provided document. A great
introduction to LangChain and a great first project for learning how to use LangChain Expression Language primitives to perform retrieval!

### LangChain Expression Language (LCEL)

- [The basics (PromptTemplate + LLM)](https://v2.scrimba.com/s05iemh)
- [Adding an output parser](https://scrimba.com/scrim/co6ae44248eacc1abd87ae3dc)
- [Attaching function calls to a model](https://scrimba.com/scrim/cof5449f5bc972f8c90be6a82)
- [Composing multiple chains](https://scrimba.com/scrim/co14344c29595bfb29c41f12a)
- [Retrieval chains](https://scrimba.com/scrim/co0e040d09941b4000244db46)
- [Conversational retrieval chains ("Chat with Docs")](https://scrimba.com/scrim/co3ed4a9eb4c6c6d0361a507c)

### Deeper dives

- [Setting up a new `PromptTemplate`](https://scrimba.com/scrim/cbGwRwuV)
- [Setting up `ChatOpenAI` parameters](https://scrimba.com/scrim/cEgbBBUw)
- [Attaching stop sequences](https://scrimba.com/scrim/co9704e389428fe2193eb955c)

## Neo4j GraphAcademy

[Neo4j](https://neo4j.com) has put together a hands-on, practical course that shows how to build a movie-recommending chatbot in Next.js.
It covers retrieval-augmented generation (RAG), tracking history, and more. Check it out below:

- [Build a Neo4j-backed Chatbot with TypeScript](https://graphacademy.neo4j.com/courses/llm-chatbot-typescript/?ref=langchainjs)

## LangChain.js x AI SDK

How to use LangChain.js with AI SDK and React Server Components.

- [Streaming agentic data to the client](https://github.com/langchain-ai/langchain-nextjs-template/blob/main/app/ai_sdk/agent/README.md)
- [Streaming tool responses to the client](https://github.com/langchain-ai/langchain-nextjs-template/blob/main/app/ai_sdk/tools/README.md)

---
