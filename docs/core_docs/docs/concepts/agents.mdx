# Agents

By themselves, language models can't take actions - they just output text. Agents are systems that take a high-level task and use an LLM as a reasoning engine to decide what actions to take and execute those actions.

[LangGraph](/docs/concepts/architecture#langgraph) is an extension of <PERSON><PERSON><PERSON><PERSON> specifically aimed at creating highly controllable and customizable agents. We recommend that you use LangGraph for building agents.

Please see the following resources for more information:

- LangGraph docs on [common agent architectures](https://langchain-ai.github.io/langgraphjs/concepts/agentic_concepts/)
- [Pre-built agents in LangGraph](https://langchain-ai.github.io/langgraphjs/reference/functions/langgraph_prebuilt.createReactAgent.html)

## Legacy agent concept: AgentExecutor

LangChain previously introduced the `AgentExecutor` as a runtime for agents.
While it served as an excellent starting point, its limitations became apparent when dealing with more sophisticated and customized agents.
As a result, we're gradually phasing out `AgentExecutor` in favor of more flexible solutions in LangGraph.

### Transitioning from AgentExecutor to langgraph

If you're currently using `AgentExecutor`, don't worry! We've prepared resources to help you:

1. For those who still need to use `AgentExecutor`, we offer a comprehensive guide on [how to use AgentExecutor](/docs/how_to/agent_executor).

2. However, we strongly recommend transitioning to LangGraph for improved flexibility and control. To facilitate this transition, we've created a detailed [migration guide](/docs/how_to/migrate_agent) to help you move from `AgentExecutor` to LangGraph seamlessly.
