import ThemedImage from "@theme/ThemedImage";
import useBaseUrl from "@docusaurus/useBaseUrl";

# Architecture

LangChain is a framework that consists of a number of packages.

<ThemedImage
  alt="Diagram outlining the hierarchical organization of the LangChain framework, displaying the interconnected parts across multiple layers."
  sources={{
    light: useBaseUrl("/svg/langchain_stack_062024.svg"),
    dark: useBaseUrl("/svg/langchain_stack_062024_dark.svg"),
  }}
  title="LangChain Framework Overview"
  style={{ width: "100%" }}
/>

## @langchain/core

This package contains base abstractions for different components and ways to compose them together.
The interfaces for core components like chat models, vector stores, tools and more are defined here.
No third-party integrations are defined here.
The dependencies are very lightweight.

## langchain

The main `langchain` package contains chains and retrieval strategies that make up an application's cognitive architecture.
These are NOT third-party integrations.
All chains, agents, and retrieval strategies here are NOT specific to any one integration, but rather generic across all integrations.

## Integration packages

Popular integrations have their own packages (e.g. `@langchain/openai`, `@langchain/anthropic`, etc) so that they can be properly versioned and appropriately lightweight.

For more information see:

- A list [integrations packages](/docs/integrations/platforms/)
- The [API Reference](https://api.js.langchain.com/) where you can find detailed information about each of the integration package.

## @langchain/community

This package contains third-party integrations that are maintained by the LangChain community.
Key integration packages are separated out (see above).
This contains integrations for various components (chat models, vector stores, tools, etc).
All dependencies in this package are optional to keep the package as lightweight as possible.

## @langchain/langgraph

`@langchain/langgraph` is an orchestration framework aimed at building robust and stateful multi-actor applications with LLMs by modeling steps as edges and nodes in a graph.

LangGraph exposes high level interfaces for creating common types of agents, as well as a low-level API for composing custom flows.

:::info [Further reading]

- See our LangGraph overview [here](https://langchain-ai.github.io/langgraphjs/concepts/high_level/#core-principles).
- See our LangGraph Academy Course [here](https://academy.langchain.com/courses/intro-to-langgraph).

:::

## LangSmith

A developer platform that lets you debug, test, evaluate, and monitor LLM applications.

For more information, see the [LangSmith documentation](https://docs.smith.langchain.com)
