# Chat models

## Overview

Large Language Models (LLMs) are advanced machine learning models that excel in a wide range of language-related tasks such as text generation, translation, summarization, question answering, and more, without needing task-specific tuning for every scenario.

Modern LLMs are typically accessed through a chat model interface that takes a list of [messages](/docs/concepts/messages) as input and returns a [message](/docs/concepts/messages) as output.

The newest generation of chat models offer additional capabilities:

- [Tool calling](/docs/concepts/tool_calling): Many popular chat models offer a native [tool calling](/docs/concepts/tool_calling) API. This API allows developers to build rich applications that enable AI to interact with external services, APIs, and databases. Tool calling can also be used to extract structured information from unstructured data and perform various other tasks.
- [Structured output](/docs/concepts/structured_outputs): A technique to make a chat model respond in a structured format, such as JSON that matches a given schema.
- [Multimodality](/docs/concepts/multimodality): The ability to work with data other than text; for example, images, audio, and video.

## Features

LangChain provides a consistent interface for working with chat models from different providers while offering additional features for monitoring, debugging, and optimizing the performance of applications that use LLMs.

- Integrations with many chat model providers (e.g., Anthropic, OpenAI, Ollama, Microsoft Azure, Google Vertex, Amazon Bedrock, Hugging Face, Cohere, Groq). Please see [chat model integrations](/docs/integrations/chat/) for an up-to-date list of supported models.
- Use either LangChain's [messages](/docs/concepts/messages) format or OpenAI format.
- Standard [tool calling API](/docs/concepts/tool_calling): standard interface for binding tools to models, accessing tool call requests made by models, and sending tool results back to the model.
- Standard API for [structuring outputs](/docs/concepts/structured_outputs) via the `withStructuredOutput` method.
- Integration with [LangSmith](https://docs.smith.langchain.com) for monitoring and debugging production-grade applications based on LLMs.
- Additional features like standardized [token usage](/docs/concepts/messages#token_usage), [rate limiting](#rate-limiting), [caching](#cache) and more.

## Integrations

LangChain has many chat model integrations that allow you to use a wide variety of models from different providers.

These integrations are one of two types:

1. **Official models**: These are models that are officially supported by LangChain and/or model provider. You can find these models in the `@langchain/<provider>` packages.
2. **Community models**: There are models that are mostly contributed and supported by the community. You can find these models in the `@langchain/community` package.

LangChain chat models are named with a convention that prefixes "Chat" to their class names (e.g., `ChatOllama`, `ChatAnthropic`, `ChatOpenAI`, etc.).

Please review the [chat model integrations](/docs/integrations/chat/) for a list of supported models.

:::note
Models that do **not** include the prefix "Chat" in their name or include "LLM" as a suffix in their name typically refer to older models that do not follow the chat model interface and instead use an interface that takes a string as input and returns a string as output.
:::

## Interface

LangChain chat models implement the [BaseChatModel](https://api.js.langchain.com/classes/_langchain_core.language_models_chat_models.BaseChatModel.html) interface. Because BaseChatModel also implements the [Runnable Interface](/docs/concepts/runnables), chat models support a [standard streaming interface](/docs/concepts/streaming), optimized [batching](/docs/concepts/runnables#batch), and more. Please see the [Runnable Interface](/docs/concepts/runnables) for more details.

Many of the key methods of chat models operate on [messages](/docs/concepts/messages) as input and return messages as output.

Chat models offer a standard set of parameters that can be used to configure the model. These parameters are typically used to control the behavior of the model, such as the temperature of the output, the maximum number of tokens in the response, and the maximum time to wait for a response. Please see the [standard parameters](#standard-parameters) section for more details.

:::note
In documentation, we will often use the terms "LLM" and "Chat Model" interchangeably. This is because most modern LLMs are exposed to users via a chat model interface.

However, LangChain also has implementations of older LLMs that do not follow the chat model interface and instead use an interface that takes a string as input and returns a string as output. These models are typically named without the "Chat" prefix (e.g., `Ollama`, `Anthropic`, `OpenAI`, etc.).
These models implement the [BaseLLM](https://api.js.langchain.com/classes/_langchain_core.language_models_llms.BaseLLM.html) interface and may be named with the "LLM" suffix (e.g., `OpenAILLM`, etc.). Generally, users should not use these models.
:::

### Key methods

The key methods of a chat model are:

1. **invoke**: The primary method for interacting with a chat model. It takes a list of [messages](/docs/concepts/messages) as input and returns a list of messages as output.
2. **stream**: A method that allows you to stream the output of a chat model as it is generated.
3. **batch**: A method that allows you to batch multiple requests to a chat model together for more efficient processing.
4. **bindTools**: A method that allows you to bind a tool to a chat model for use in the model's execution context.
5. **withStructuredOutput**: A wrapper around the `invoke` method for models that natively support [structured output](/docs/concepts/structured_outputs).

Other important methods can be found in the [BaseChatModel API Reference](https://api.js.langchain.com/classes/_langchain_core.language_models_chat_models.BaseChatModel.html).

### Inputs and outputs

Modern LLMs are typically accessed through a chat model interface that takes [messages](/docs/concepts/messages) as input and returns [messages](/docs/concepts/messages) as output. Messages are typically associated with a role (e.g., "system", "human", "assistant") and one or more content blocks that contain text or potentially multimodal data (e.g., images, audio, video).

LangChain supports two message formats to interact with chat models:

1. **LangChain Message Format**: LangChain's own message format, which is used by default and is used internally by LangChain.
2. **OpenAI's Message Format**: OpenAI's message format.

### Standard parameters

Many chat models have standardized parameters that can be used to configure the model:

| Parameter     | Description                                                                                                                                                                         |
| ------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `model`       | The name or identifier of the specific AI model you want to use (e.g., `"gpt-3.5-turbo"` or `"gpt-4"`).                                                                             |
| `temperature` | Controls the randomness of the model's output. A higher value (e.g., 1.0) makes responses more creative, while a lower value (e.g., 0.1) makes them more deterministic and focused. |
| `timeout`     | The maximum time (in seconds) to wait for a response from the model before canceling the request. Ensures the request doesn’t hang indefinitely.                                    |
| `maxTokens`   | Limits the total number of tokens (words and punctuation) in the response. This controls how long the output can be.                                                                |
| `stop`        | Specifies stop sequences that indicate when the model should stop generating tokens. For example, you might use specific strings to signal the end of a response.                   |
| `maxRetries`  | The maximum number of attempts the system will make to resend a request if it fails due to issues like network timeouts or rate limits.                                             |
| `apiKey`      | The API key required for authenticating with the model provider. This is usually issued when you sign up for access to the model.                                                   |
| `baseUrl`     | The URL of the API endpoint where requests are sent. This is typically provided by the model's provider and is necessary for directing your requests.                               |

Some important things to note:

- Standard parameters only apply to model providers that expose parameters with the intended functionality. For example, some providers do not expose a configuration for maximum output tokens, so max_tokens can't be supported on these.
- Standard params are currently only enforced on integrations that have their own integration packages (e.g. `@langchain/openai`, `@langchain/anthropic`, etc.), they're not enforced on models in `@langchain/community`.

ChatModels also accept other parameters that are specific to that integration. To find all the parameters supported by a ChatModel head to the [API reference](https://api.js.langchain.com/) for that model.

## Tool calling

Chat models can call [tools](/docs/concepts/tools) to perform tasks such as fetching data from a database, making API requests, or running custom code. Please
see the [tool calling](/docs/concepts/tool_calling) guide for more information.

## Structured outputs

Chat models can be requested to respond in a particular format (e.g., JSON or matching a particular schema). This feature is extremely
useful for information extraction tasks. Please read more about
the technique in the [structured outputs](/docs/concepts/structured_outputs) guide.

## Multimodality

Large Language Models (LLMs) are not limited to processing text. They can also be used to process other types of data, such as images, audio, and video. This is known as [multimodality](/docs/concepts/multimodality).

Currently, only some LLMs support multimodal inputs, and almost none support multimodal outputs. Please consult the specific model documentation for details.

## Context window

A chat model's context window refers to the maximum size of the input sequence the model can process at one time. While the context windows of modern LLMs are quite large, they still present a limitation that developers must keep in mind when working with chat models.

If the input exceeds the context window, the model may not be able to process the entire input and could raise an error. In conversational applications, this is especially important because the context window determines how much information the model can "remember" throughout a conversation. Developers often need to manage the input within the context window to maintain a coherent dialogue without exceeding the limit. For more details on handling memory in conversations, refer to the [memory](https://langchain-ai.github.io/langgraphjs/concepts/memory/).

The size of the input is measured in [tokens](/docs/concepts/tokens) which are the unit of processing that the model uses.

## Advanced topics

### Caching

Chat model APIs can be slow, so a natural question is whether to cache the results of previous conversations. Theoretically, caching can help improve performance by reducing the number of requests made to the model provider. In practice, caching chat model responses is a complex problem and should be approached with caution.

The reason is that getting a cache hit is unlikely after the first or second interaction in a conversation if relying on caching the **exact** inputs into the model. For example, how likely do you think that multiple conversations start with the exact same message? What about the exact same three messages?

An alternative approach is to use semantic caching, where you cache responses based on the meaning of the input rather than the exact input itself. This can be effective in some situations, but not in others.

A semantic cache introduces a dependency on another model on the critical path of your application (e.g., the semantic cache may rely on an [embedding model](/docs/concepts/embedding_models) to convert text to a vector representation), and it's not guaranteed to capture the meaning of the input accurately.

However, there might be situations where caching chat model responses is beneficial. For example, if you have a chat model that is used to answer frequently asked questions, caching responses can help reduce the load on the model provider and improve response times.

Please see the [how to cache chat model responses](/docs/how_to/#chat-model-caching) guide for more details.

## Related resources

- How-to guides on using chat models: [how-to guides](/docs/how_to/#chat-models).
- List of supported chat models: [chat model integrations](/docs/integrations/chat/).

### Conceptual guides

- [Messages](/docs/concepts/messages)
- [Tool calling](/docs/concepts/tool_calling)
- [Multimodality](/docs/concepts/multimodality)
- [Structured outputs](/docs/concepts/structured_outputs)
- [Tokens](/docs/concepts/tokens)
