# Key-value stores

## Overview

LangChain provides a key-value store interface for storing and retrieving data.

LangChain includes a [`BaseStore`](https://api.js.langchain.com/classes/_langchain_core.stores.BaseStore.html) interface,
which allows for storage of arbitrary data. However, LangChain components that require KV-storage accept a
more specific `BaseStore<string, Uint8Array>` instance that stores binary data (referred to as a `ByteStore`), and internally take care of
encoding and decoding data for their specific needs.

This means that as a user, you only need to think about one type of store rather than different ones for different types of data.

## Usage

The key-value store interface in LangChain is used primarily for:

1. Caching [embeddings](/docs/concepts/embedding_models) via [CachedBackedEmbeddings](https://api.js.langchain.com/classes/langchain.embeddings_cache_backed.CacheBackedEmbeddings.html) to avoid recomputing embeddings for repeated queries or when re-indexing content.

2. As a simple [Document](https://api.js.langchain.com/classes/_langchain_core.documents.Document.html) persistence layer in some retrievers.

Please see these how-to guides for more information:

- [How to cache embeddings guide](/docs/how_to/caching_embeddings/).
- [How to retriever using multiple vectors per document](/docs/how_to/custom_retriever/).

## Interface

All [`BaseStore`](https://api.js.langchain.com/classes/_langchain_core.stores.BaseStore.html)s support the following interface. Note that the interface allows for modifying **multiple** key-value pairs at once:

- `mget(keys: string[]): Promise<(Uint8Array | undefined)[]>`: get the contents of multiple keys, returning `undefined` if the key does not exist
- `mset(keyValuePairs: [string, Uint8Array][]): Promise<void>`: set the contents of multiple keys
- `mdelete(keys: string[]): Promise<void>`: delete multiple keys
- `yieldKeys(prefix?: string): AsyncIterator<string>`: yield all keys in the store, optionally filtering by a prefix

## Integrations

Please reference the [stores integration page](/docs/integrations/stores/) for a list of available key-value store integrations.
