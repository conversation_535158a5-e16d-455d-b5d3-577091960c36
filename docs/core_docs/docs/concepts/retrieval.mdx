# Retrieval

:::info[Prerequisites]

- [Retrievers](/docs/concepts/retrievers/)
- [Vector stores](/docs/concepts/vectorstores/)
- [Embeddings](/docs/concepts/embedding_models/)
- [Text splitters](/docs/concepts/text_splitters/)

:::

:::danger[Security]

Some of the concepts reviewed here utilize models to generate queries (e.g., for SQL or graph databases).
There are inherent risks in doing this.
Make sure that your database connection permissions are scoped as narrowly as possible for your application's needs.
This will mitigate, though not eliminate, the risks of building a model-driven system capable of querying databases.
For more on general security best practices, see our [security guide](/docs/security/).

:::

## Overview

Retrieval systems are fundamental to many AI applications, efficiently identifying relevant information from large datasets.
These systems accommodate various data formats:

- Unstructured text (e.g., documents) is often stored in vector stores or lexical search indexes.
- Structured data is typically housed in relational or graph databases with defined schemas.

Despite this diversity in data formats, modern AI applications increasingly aim to make all types of data accessible through natural language interfaces.
Models play a crucial role in this process by translating natural language queries into formats compatible with the underlying search index or database.
This translation enables more intuitive and flexible interactions with complex data structures.

## Key concepts

![Retrieval](/img/retrieval_concept.png)

(1) **Query analysis**: A process where models transform or construct search queries to optimize retrieval.

(2) **Information retrieval**: Search queries are used to fetch information from various retrieval systems.

## Query analysis

While users typically prefer to interact with retrieval systems using natural language, retrieval systems can specific query syntax or benefit from particular keywords.
Query analysis serves as a bridge between raw user input and optimized search queries. Some common applications of query analysis include:

1. **Query Re-writing**: Queries can be re-written or expanded to improve semantic or lexical searches.
2. **Query Construction**: Search indexes may require structured queries (e.g., SQL for databases).

Query analysis employs models to transform or construct optimized search queries from raw user input.

### Query re-writing

Retrieval systems should ideally handle a wide spectrum of user inputs, from simple and poorly worded queries to complex, multi-faceted questions.
To achieve this versatility, a popular approach is to use models to transform raw user queries into more effective search queries.
This transformation can range from simple keyword extraction to sophisticated query expansion and reformulation.
Here are some key benefits of using models for query analysis in unstructured data retrieval:

1. **Query Clarification**: Models can rephrase ambiguous or poorly worded queries for clarity.
2. **Semantic Understanding**: They can capture the intent behind a query, going beyond literal keyword matching.
3. **Query Expansion**: Models can generate related terms or concepts to broaden the search scope.
4. **Complex Query Handling**: They can break down multi-part questions into simpler sub-queries.

Various techniques have been developed to leverage models for query re-writing, including:

| Name                                                                                                      | When to use                                                                     | Description                                                                                                                                                                                                                                                                            |
| --------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Decomposition](https://github.com/langchain-ai/rag-from-scratch/blob/main/rag_from_scratch_5_to_9.ipynb) | When a question can be broken down into smaller subproblems.                    | Decompose a question into a set of subproblems / questions, which can either be solved sequentially (use the answer from first + retrieval to answer the second) or in parallel (consolidate each answer into final answer).                                                           |
| [Step-back](https://github.com/langchain-ai/rag-from-scratch/blob/main/rag_from_scratch_5_to_9.ipynb)     | When a higher-level conceptual understanding is required.                       | First prompt the LLM to ask a generic step-back question about higher-level concepts or principles, and retrieve relevant facts about them. Use this grounding to help answer the user question. [Paper](https://arxiv.org/pdf/2310.06117).                                            |
| [HyDE](https://github.com/langchain-ai/rag-from-scratch/blob/main/rag_from_scratch_5_to_9.ipynb)          | If you have challenges retrieving relevant documents using the raw user inputs. | Use an LLM to convert questions into hypothetical documents that answer the question. Use the embedded hypothetical documents to retrieve real documents with the premise that doc-doc similarity search can produce more relevant matches. [Paper](https://arxiv.org/abs/2212.10496). |

As an example, query decomposition can simply be accomplished using prompting and a structured output that enforces a list of sub-questions.
These can then be run sequentially or in parallel on a downstream retrieval system.

```typescript
import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import { SystemMessage, HumanMessage } from "@langchain/core/messages";

// Define a zod object for the structured output
const Questions = z.object({
  questions: z
    .array(z.string())
    .describe("A list of sub-questions related to the input query."),
});

// Create an instance of the model and enforce the output structure
const model = new ChatOpenAI({ modelName: "gpt-4", temperature: 0 });
const structuredModel = model.withStructuredOutput(Questions);

// Define the system prompt
const system = `You are a helpful assistant that generates multiple sub-questions related to an input question.
The goal is to break down the input into a set of sub-problems / sub-questions that can be answers in isolation.`;

// Pass the question to the model
const question =
  "What are the main components of an LLM-powered autonomous agent system?";
const questions = await structuredModel.invoke([
  new SystemMessage(system),
  new HumanMessage(question),
]);
```

:::tip

See our RAG from Scratch videos for a few different specific approaches:

- [Multi-query](https://youtu.be/JChPi0CRnDY?feature=shared)
- [Decomposition](https://youtu.be/h0OPWlEOank?feature=shared)
- [Step-back](https://youtu.be/xn1jEjRyJ2U?feature=shared)
- [HyDE](https://youtu.be/SaDzIVkYqyY?feature=shared)

:::

### Query construction

Query analysis also can focus on translating natural language queries into specialized query languages or filters.
This translation is crucial for effectively interacting with various types of databases that house structured or semi-structured data.

1. **Structured Data examples**: For relational and graph databases, Domain-Specific Languages (DSLs) are used to query data.

   - **Text-to-SQL**: [Converts natural language to SQL](https://paperswithcode.com/task/text-to-sql) for relational databases.
   - **Text-to-Cypher**: [Converts natural language to Cypher](https://neo4j.com/labs/neodash/2.4/user-guide/extensions/natural-language-queries/) for graph databases.

2. **Semi-structured Data examples**: For vectorstores, queries can combine semantic search with metadata filtering.
   - **Natural Language to Metadata Filters**: Converts user queries into [appropriate metadata filters](https://docs.pinecone.io/guides/data/filter-with-metadata).

These approaches leverage models to bridge the gap between user intent and the specific query requirements of different data storage systems. Here are some popular techniques:

| Name                                     | When to Use                                                                                                                          | Description                                                                                                                                                                                                                                          |
| ---------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Self Query](/docs/how_to/self_query/)   | If users are asking questions that are better answered by fetching documents based on metadata rather than similarity with the text. | This uses an LLM to transform user input into two things: (1) a string to look up semantically, (2) a metadata filter to go along with it. This is useful because oftentimes questions are about the METADATA of documents (not the content itself). |
| [Text to SQL](/docs/tutorials/sql_qa/)   | If users are asking questions that require information housed in a relational database, accessible via SQL.                          | This uses an LLM to transform user input into a SQL query.                                                                                                                                                                                           |
| [Text-to-Cypher](/docs/tutorials/graph/) | If users are asking questions that require information housed in a graph database, accessible via Cypher.                            | This uses an LLM to transform user input into a Cypher query.                                                                                                                                                                                        |

As an example, here is how to use the `SelfQueryRetriever` to convert natural language queries into metadata filters.

```typescript
import { SelfQueryRetriever } from "langchain/retrievers/self_query";
import { AttributeInfo } from "langchain/chains/query_constructor";
import { ChatOpenAI } from "@langchain/openai";

const attributeInfo: AttributeInfo[] = schemaForMetadata;
const documentContents = "Brief summary of a movie";
const llm = new ChatOpenAI({ temperature: 0 });
const retriever = SelfQueryRetriever.fromLLM({
  llm,
  vectorStore,
  documentContents,
  attributeInfo,
});
```

:::info[Further reading]

- See our tutorials on [text-to-SQL](/docs/tutorials/sql_qa/), [text-to-Cypher](/docs/tutorials/graph/), and [query analysis for metadata filters](/docs/tutorials/rag#query-analysis).
- See our [blog post overview](https://blog.langchain.dev/query-construction/).
- See our RAG from Scratch video on [query construction](https://youtu.be/kl6NwWYxvbM?feature=shared).

:::

## Information retrieval

### Common retrieval systems

#### Lexical search indexes

Many search engines are based upon matching words in a query to the words in each document.
This approach is called lexical retrieval, using search [algorithms that are typically based upon word frequencies](https://cameronrwolfe.substack.com/p/the-basics-of-ai-powered-vector-search?utm_source=profile&utm_medium=reader2).
The intution is simple: a word appears frequently both in the user’s query and a particular document, then this document might be a good match.

The particular data structure used to implement this is often an [_inverted index_](https://www.geeksforgeeks.org/inverted-index/).
This types of index contains a list of words and a mapping of each word to a list of locations at which it occurs in various documents.
Using this data structure, it is possible to efficiently match the words in search queries to the documents in which they appear.
[BM25](https://en.wikipedia.org/wiki/Okapi_BM25#:~:text=BM25%20is%20a%20bag%2Dof,slightly%20different%20components%20and%20parameters.) and [TF-IDF](https://en.wikipedia.org/wiki/Tf%E2%80%93idf) are [two popular lexical search algorithms](https://cameronrwolfe.substack.com/p/the-basics-of-ai-powered-vector-search?utm_source=profile&utm_medium=reader2).

:::info[Further reading]

- See the [BM25](/docs/integrations/retrievers/bm25/) retriever integration.

:::

#### Vector indexes

Vector indexes are an alternative way to index and store unstructured data.
See our conceptual guide on [vectorstores](/docs/concepts/vectorstores/) for a detailed overview.  
In short, rather than using word frequencies, vectorstores use an [embedding model](/docs/concepts/embedding_models/) to compress documents into high-dimensional vector representation.
This allows for efficient similarity search over embedding vectors using simple mathematical operations like cosine similarity.

:::info[Further reading]

- See our [how-to guide](/docs/how_to/vectorstore_retriever/) for more details on working with vectorstores.
- See our [list of vectorstore integrations](/docs/integrations/vectorstores/).
- See Cameron Wolfe's [blog post](https://cameronrwolfe.substack.com/p/the-basics-of-ai-powered-vector-search?utm_source=profile&utm_medium=reader2) on the basics of vector search.

:::

#### Relational databases

Relational databases are a fundamental type of structured data storage used in many applications.
They organize data into tables with predefined schemas, where each table represents an entity or relationship.
Data is stored in rows (records) and columns (attributes), allowing for efficient querying and manipulation through SQL (Structured Query Language).
Relational databases excel at maintaining data integrity, supporting complex queries, and handling relationships between different data entities.

:::info[Further reading]

- See our [tutorial](/docs/tutorials/sql_qa/) for working with SQL databases.

:::

#### Graph databases

Graph databases are a specialized type of database designed to store and manage highly interconnected data.
Unlike traditional relational databases, graph databases use a flexible structure consisting of nodes (entities), edges (relationships), and properties.
This structure allows for efficient representation and querying of complex, interconnected data.
Graph databases store data in a graph structure, with nodes, edges, and properties.
They are particularly useful for storing and querying complex relationships between data points, such as social networks, supply-chain management, fraud detection, and recommendation services

:::info[Further reading]

- See our [tutorial](/docs/tutorials/graph/) for working with graph databases.
- See Neo4j's [starter kit for LangChain](https://neo4j.com/developer-blog/langchain-neo4j-starter-kit/).

:::

### Retriever

LangChain provides a unified interface for interacting with various retrieval systems through the [retriever](/docs/concepts/retrievers/) concept. The interface is straightforward:

1. Input: A query (string)
2. Output: A list of documents (standardized LangChain [Document](https://api.js.langchain.com/classes/_langchain_core.documents.Document.html) objects)

You can create a retriever using any of the retrieval systems mentioned earlier. The query analysis techniques we discussed are particularly useful here, as they enable natural language interfaces for databases that typically require structured query languages.
For example, you can build a retriever for a SQL database using text-to-SQL conversion. This allows a natural language query (string) to be transformed into a SQL query behind the scenes.
Regardless of the underlying retrieval system, all retrievers in LangChain share a common interface. You can use them with the simple `invoke` method:

```typescript
const docs = await retriever.invoke(query);
```

:::info[Further reading]

- See our [conceptual guide on retrievers](/docs/concepts/retrievers/).
- See our [how-to guide](/docs/how_to/#retrievers) on working with retrievers.

:::
