{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatPromptValue {\n", "  lc_serializable: true,\n", "  lc_kwargs: {\n", "    messages: [\n", "      SystemMessage {\n", "        \"content\": \"You are a helpful assistant\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {}\n", "      },\n", "      HumanMessage {\n", "        \"content\": \"hi!\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {}\n", "      }\n", "    ]\n", "  },\n", "  lc_namespace: [ 'langchain_core', 'prompt_values' ],\n", "  messages: [\n", "    SystemMessage {\n", "      \"content\": \"You are a helpful assistant\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    HumanMessage {\n", "      \"content\": \"hi!\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    }\n", "  ]\n", "}\n"]}], "source": ["import {\n", "  ChatPromptTemplate,\n", "  MessagesPlaceholder,\n", "} from \"@langchain/core/prompts\";\n", "import { HumanMessage } from \"@langchain/core/messages\";\n", "\n", "const promptTemplate = ChatPromptTemplate.fromMessages([\n", "  [\"system\", \"You are a helpful assistant\"],\n", "  new MessagesPlaceholder(\"msgs\"),\n", "]);\n", "\n", "await promptTemplate.invoke({ msgs: [new HumanMessage(\"hi!\")] });"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}