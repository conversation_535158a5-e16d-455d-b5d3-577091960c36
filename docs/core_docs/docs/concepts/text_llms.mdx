# String-in, string-out llms

:::tip
You are probably looking for the [Chat Model Concept Guide](/docs/concepts/chat_models) page for more information.
:::

Lang<PERSON><PERSON><PERSON> has implementations for older language models that take a string as input and return a string as output. These models are typically named without the "Chat" prefix (e.g., `Ollama`, `Anthropic`, `OpenAI`, etc.), and may include the "LLM" suffix (e.g., `OpenAILLM`, etc.). These models implement the [`BaseLLM`](https://api.js.langchain.com/classes/_langchain_core.language_models_llms.BaseLLM.html) interface.

Users should be using almost exclusively the newer [Chat Models](/docs/concepts/chat_models) as most
model providers have adopted a chat like interface for interacting with language models.
