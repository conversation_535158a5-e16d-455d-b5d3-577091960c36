# Tracing

<span data-heading-keywords="trace,tracing"></span>

A trace is essentially a series of steps that your application takes to go from input to output.
Traces contain individual steps called `runs`. These can be individual calls from a model, retriever,
tool, or sub-chains.
Tracing gives you observability inside your chains and agents, and is vital in diagnosing issues.

For a deeper dive, check out [this <PERSON><PERSON><PERSON> conceptual guide](https://docs.smith.langchain.com/concepts/tracing).
