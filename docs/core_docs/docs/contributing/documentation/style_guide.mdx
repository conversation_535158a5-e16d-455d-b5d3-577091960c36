---
sidebar_label: "Style guide"
---

# <PERSON><PERSON><PERSON>n Documentation Style Guide

## Introduction

As <PERSON><PERSON><PERSON><PERSON> continues to grow, the surface area of documentation required to cover it continues to grow too.
This page provides guidelines for anyone writing documentation for <PERSON><PERSON><PERSON><PERSON>, as well as some of our philosophies around
organization and structure.

## Philosophy

LangChain's documentation aspires to follow the [Diataxis framework](https://diataxis.fr).
Under this framework, all documentation falls under one of four categories:

- **Tutorials**: Lessons that take the reader by the hand through a series of conceptual steps to complete a project.
  - An example of this is our [LCEL streaming guide](/docs/how_to/streaming).
  - Our guides on [custom components](/docs/how_to/custom_chat) is another one.
- **How-to guides**: Guides that take the reader through the steps required to solve a real-world problem.
  - The clearest examples of this are our [Use case](/docs/how_to/#use-cases) pages.
- **Reference**: Technical descriptions of the machinery and how to operate it.
  - Our [Runnable](/docs/how_to/#langchain-expression-language-lcel) pages is an example of this.
  - The [API reference pages](https://api.js.langchain.com/) are another.
- **Explanation**: Explanations that clarify and illuminate a particular topic.

Each category serves a distinct purpose and requires a specific approach to writing and structuring the content.

## Taxonomy

Keeping the above in mind, we have sorted LangChain's docs into categories. It is helpful to think in these terms
when contributing new documentation:

### Getting started

The [getting started section](/docs/introduction) includes a high-level introduction to LangChain, a quickstart that
tours LangChain's various features, and logistical instructions around installation and project setup.

It contains elements of **How-to guides** and **Explanations**.

### Use cases

[Use cases](/docs/how_to/#use-cases) are guides that are meant to show how to use LangChain to accomplish a specific task (RAG, information extraction, etc.).
The quickstarts should be good entrypoints for first-time LangChain developers who prefer to learn by getting something practical prototyped,
then taking the pieces apart retrospectively. These should mirror what LangChain is good at.

The quickstart pages here should fit the **How-to guide** category, with the other pages intended to be **Explanations** of more
in-depth concepts and strategies that accompany the main happy paths.

:::note
The below sections are listed roughly in order of increasing level of abstraction.
:::

### Expression Language

[LangChain Expression Language (LCEL)](/docs/how_to/#langchain-expression-language-lcel) is the fundamental way that most LangChain components fit together, and this section is designed to teach
developers how to use it to build with LangChain's primitives effectively.

This section should contains **Tutorials** that teach how to stream and use LCEL primitives for more abstract tasks, **Explanations** of specific behaviors,
and some **References** for how to use different methods in the Runnable interface.

### Components

The [how to section](/docs/how_to) covers concepts one level of abstraction higher than LCEL.
Abstract base classes like `BaseChatModel` and `BaseRetriever` should be covered here, as well as core implementations of these base classes,
such as `ChatPromptTemplate` and `RecursiveCharacterTextSplitter`. Customization guides belong here too.

This section should contain mostly conceptual **Tutorials**, **References**, and **Explanations** of the components they cover.

:::note
As a general rule of thumb, everything covered in the `Expression Language` and `Components` sections (with the exception of the `Composition` section of components) should
cover only components that exist in `@langchain/core`.
:::

### Integrations

The [integrations](/docs/integrations/platforms/) are specific implementations of components. These often involve third-party APIs and services.
If this is the case, as a general rule, these are maintained by the third-party partner.

This section should contain mostly **Explanations** and **References**, though the actual content here is more flexible than other sections and more at the
discretion of the third-party provider.

:::note
Concepts covered in `Integrations` should generally exist in `@langchain/community` or specific partner packages.
:::

### Tutorials and Ecosystem

The [Tutorials](/docs/tutorials) and [Ecosystem](https://docs.smith.langchain.com) sections should contain guides that address higher-level problems than the sections above.
This includes, but is not limited to, considerations around productionization and development workflows.

These should contain mostly **How-to guides**, **Explanations**, and **Tutorials**.

### API references

LangChain's API references. Should act as **References** (as the name implies) with some **Explanation**-focused content as well.

## Sample developer journey

We have set up our docs to assist a new developer to LangChain. Let's walk through the intended path:

- The developer lands on https://js.langchain.com, and reads through the introduction and the diagram.
- If they are just curious, they may be drawn to the [Quickstart](/docs/tutorials/llm_chain) to get a high-level tour of what LangChain contains.
- If they have a specific task in mind that they want to accomplish, they will be drawn to the Use-Case section. The use-case should provide a good, concrete hook that shows the value LangChain can provide them and be a good entrypoint to the framework.
- They can then move to learn more about the fundamentals of LangChain through the Expression Language sections.
- Next, they can learn about LangChain's various components and integrations.
- Finally, they can get additional knowledge through the Guides.

This is only an ideal of course - sections will inevitably reference lower or higher-level concepts that are documented in other sections.

## Guidelines

Here are some other guidelines you should think about when writing and organizing documentation.

### Linking to other sections

Because sections of the docs do not exist in a vacuum, it is important to link to other sections as often as possible
to allow a developer to learn more about an unfamiliar topic inline.

This includes linking to the API references as well as conceptual sections!

### Conciseness

In general, take a less-is-more approach. If a section with a good explanation of a concept already exists, you should link to it rather than
re-explain it, unless the concept you are documenting presents some new wrinkle.

Be concise, including in code samples.

### General style

- Use active voice and present tense whenever possible.
- Use examples and code snippets to illustrate concepts and usage.
- Use appropriate header levels (`#`, `##`, `###`, etc.) to organize the content hierarchically.
- Use bullet points and numbered lists to break down information into easily digestible chunks.
- Use tables (especially for **Reference** sections) and diagrams often to present information visually.
- Include the table of contents for longer documentation pages to help readers navigate the content, but hide it for shorter pages.
