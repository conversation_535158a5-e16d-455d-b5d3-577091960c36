{"cells": [{"cell_type": "markdown", "id": "f4c03f40-1328-412d-8a48-1db0cd481b77", "metadata": {}, "source": ["# How to use legacy LangChain Agents (AgentExecutor)\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Tools](/docs/concepts/tools)\n", "\n", ":::\n", "\n", "By themselves, language models can't take actions - they just output text.\n", "Agents are systems that use an LLM as a reasoning engine to determine which actions to take and what the inputs to those actions should be.\n", "The results of those actions can then be fed back into the agent and it determine whether more actions are needed, or whether it is okay to finish.\n", "\n", "In this tutorial we will build an agent that can interact with multiple different tools: one being a local database, the other being a search engine. You will be able to ask this agent questions, watch it call tools, and have conversations with it.\n", "\n", ":::{.callout-important}\n", "This section will cover building with LangChain Agents. LangChain Agents are fine for getting started, but past a certain point you will likely want flexibility and control that they do not offer. For working with more advanced agents, we'd recommend checking out [LangGraph](https://langchain-ai.github.io/langgraphjs).\n", ":::\n", "\n", "## Concepts\n", "\n", "Concepts we will cover are:\n", "- Using [language models](/docs/concepts/chat_models), in particular their tool calling ability\n", "- Creating a [Retriever](/docs/concepts/retrievers) to expose specific information to our agent\n", "- Using a Search [Tool](/docs/concepts/tools) to look up things online\n", "- [`Chat History`](/docs/concepts/chat_history), which allows a chatbot to \"remember\" past interactions and take them into account when responding to followup questions. \n", "- Debugging and tracing your application using [LangSmith](/docs/concepts/#langsmith)\n", "\n", "## Setup\n", "\n", "### Jupyter Notebook\n", "\n", "This guide (and most of the other guides in the documentation) uses [Jupy<PERSON> notebooks](https://jupyter.org/) and assumes the reader is as well. Jupyter notebooks are perfect for learning how to work with LLM systems because oftentimes things can go wrong (unexpected output, API down, etc) and going through guides in an interactive environment is a great way to better understand them.\n", "\n", "This and other tutorials are perhaps most conveniently run in a Ju<PERSON><PERSON> notebook. See [here](https://jupyter.org/install) for instructions on how to install.\n", "\n", "### Installation\n", "\n", "To install <PERSON><PERSON><PERSON><PERSON> (and `cheerio` for the web loader) run:\n", "\n", "```{=mdx}\n", "import Npm2Yarn from '@theme/Npm2Yarn';\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core cheerio\n", "</Npm2Yarn>\n", "```\n", "\n", "For more details, see our [Installation guide](/docs/how_to/installation/).\n", "\n", "### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls.\n", "As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent.\n", "The best way to do this is with [Lang<PERSON><PERSON>](https://smith.langchain.com).\n", "\n", "After you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "```shell\n", "export LANGSMITH_TRACING=\"true\"\n", "export LANGSMITH_API_KEY=\"...\"\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# export LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```\n"]}, {"cell_type": "markdown", "id": "c335d1bf", "metadata": {}, "source": ["## Define tools\n", "\n", "We first need to create the tools we want to use. We will use two tools: [<PERSON><PERSON>](/docs/integrations/tools/tavily_search) (to search online) and then a retriever over a local index we will create\n", "\n", "### [<PERSON><PERSON>](/docs/integrations/tools/tavily_search)\n", "\n", "We have a built-in tool in LangChain to easily use Tavily search engine as tool.\n", "Note that this requires an API key - they have a free tier, but if you don't have one or don't want to create one, you can always ignore this step.\n", "\n", "Once you create your API key, you will need to export that as:\n", "\n", "```bash\n", "export TAVILY_API_KEY=\"...\"\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "9cc86c0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m`[{\"title\":\"Weather in San Francisco\",\"url\":\"https://www.weatherapi.com/\",\"content\":\"{'location': {'n`\u001b[39m... 1358 more characters"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import \"cheerio\"; // This is required in notebooks to use the `CheerioWebBaseLoader`\n", "import { TavilySearchResults } from \"@langchain/community/tools/tavily_search\"\n", "\n", "const search = new TavilySearchResults({\n", "  maxResults: 2\n", "});\n", "\n", "await search.invoke(\"what is the weather in SF\")"]}, {"cell_type": "markdown", "id": "e8097977", "metadata": {}, "source": ["### Retriever\n", "\n", "We will also create a retriever over some data of our own. For a deeper explanation of each step here, see [this tutorial](/docs/tutorials/rag)."]}, {"cell_type": "code", "execution_count": 2, "id": "9c9ce713", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document {\n", "  pageContent: \u001b[32m'description=\"A sample dataset in LangSmith.\")client.create_examples(    inputs=[        {\"postfix\": '\u001b[39m... 891 more characters,\n", "  metadata: {\n", "    source: \u001b[32m\"https://docs.smith.langchain.com/overview\"\u001b[39m,\n", "    loc: { lines: { from: \u001b[33m4\u001b[39m, to: \u001b[33m4\u001b[39m } }\n", "  },\n", "  id: \u001b[90mundefined\u001b[39m\n", "}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "import { RecursiveCharacterTextSplitter } from \"@langchain/textsplitters\";\n", "\n", "const loader = new CheerioWebBaseLoader(\"https://docs.smith.langchain.com/overview\");\n", "const docs = await loader.load();\n", "const splitter = new RecursiveCharacterTextSplitter(\n", "  {\n", "    chunkSize: 1000,\n", "    chunkOverlap: 200\n", "  }\n", ");\n", "const documents = await splitter.splitDocuments(docs);\n", "const vectorStore = await MemoryVectorStore.fromDocuments(documents, new OpenAIEmbeddings());\n", "const retriever = vectorStore.asRetriever();\n", "\n", "(await retriever.invoke(\"how to upload a dataset\"))[0];"]}, {"cell_type": "markdown", "id": "04aeca39", "metadata": {}, "source": ["Now that we have populated our index that we will do doing retrieval over, we can easily turn it into a tool (the format needed for an agent to properly use it)"]}, {"cell_type": "code", "execution_count": 7, "id": "7280b031", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { tool } from \"@langchain/core/tools\";\n", "\n", "const retrieverTool = tool(async ({ input }, config) => {\n", "  const docs = await retriever.invoke(input, config);\n", "  return docs.map((doc) => doc.pageContent).join(\"\\n\\n\");\n", "}, {\n", "  name: \"langsmith_search\",\n", "  description:\n", "    \"Search for information about <PERSON><PERSON><PERSON>. For any questions about <PERSON><PERSON><PERSON>, you must use this tool!\",\n", "  schema: z.object({\n", "    input: z.string()\n", "  }),\n", "});"]}, {"cell_type": "markdown", "id": "c3b47c1d", "metadata": {}, "source": ["### Tools\n", "\n", "Now that we have created both, we can create a list of tools that we will use downstream."]}, {"cell_type": "code", "execution_count": 8, "id": "b8e8e710", "metadata": {}, "outputs": [], "source": ["const tools = [search, retrieverTool];"]}, {"cell_type": "markdown", "id": "e00068b0", "metadata": {}, "source": ["## Using Language Models\n", "\n", "Next, let's learn how to use a language model by to call tools. LangChain supports many different language models that you can use interchangably - select the one you want to use below!\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs openaiParams={`{ model: \"gpt-4\" }`} />\n", "```"]}, {"cell_type": "markdown", "id": "642ed8bf", "metadata": {}, "source": ["You can call the language model by passing in a list of messages. By default, the response is a `content` string."]}, {"cell_type": "code", "execution_count": 9, "id": "def033a4", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const model = new ChatOpenAI({ model: \"gpt-4o-mini\", temperature: 0 })"]}, {"cell_type": "code", "execution_count": 10, "id": "c96c960b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"Hello! How can I assist you today?\"\u001b[39m"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["const response = await model.invoke([{\n", "  role: \"user\",\n", "  content: \"hi!\"\n", "}]);\n", "\n", "response.content;"]}, {"cell_type": "markdown", "id": "47bf8210", "metadata": {}, "source": ["We can now see what it is like to enable this model to do tool calling. In order to enable that we use `.bind` to give the language model knowledge of these tools"]}, {"cell_type": "code", "execution_count": 11, "id": "ba692a74", "metadata": {}, "outputs": [], "source": ["const modelWithTools = model.bindTools(tools);"]}, {"cell_type": "markdown", "id": "fd920b69", "metadata": {}, "source": ["We can now call the model. Let's first call it with a normal message, and see how it responds. We can look at both the `content` field as well as the `tool_calls` field."]}, {"cell_type": "code", "execution_count": 12, "id": "b6a7e925", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Content: Hello! How can I assist you today?\n", "Tool calls: \n"]}], "source": ["const responseWithTools = await modelWithTools.invoke([{\n", "  role: \"user\",\n", "  content: \"Hi!\"\n", "}])\n", "\n", "console.log(`Content: ${responseWithTools.content}`)\n", "console.log(`Tool calls: ${responseWithTools.tool_calls}`)"]}, {"cell_type": "markdown", "id": "e8c81e76", "metadata": {}, "source": ["Now, let's try calling it with some input that would expect a tool to be called."]}, {"cell_type": "code", "execution_count": 13, "id": "688b465d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Content: \n", "Tool calls: [\n", "  {\n", "    \"name\": \"tavily_search_results_json\",\n", "    \"args\": {\n", "      \"input\": \"current weather in San Francisco\"\n", "    },\n", "    \"type\": \"tool_call\",\n", "    \"id\": \"call_gtJ5rrjXswO8EIvePrxyGQbR\"\n", "  }\n", "]\n"]}], "source": ["const responseWithToolCalls = await modelWithTools.invoke([{\n", "  role: \"user\",\n", "  content: \"What's the weather in SF?\"\n", "}])\n", "\n", "console.log(`Content: ${responseWithToolCalls.content}`)\n", "console.log(`Tool calls: ${JSON.stringify(responseWithToolCalls.tool_calls, null, 2)}`)"]}, {"cell_type": "markdown", "id": "83c4bcd3", "metadata": {}, "source": ["We can see that there's now no content, but there is a tool call! It wants us to call the Tavily Search tool.\n", "\n", "This isn't calling that tool yet - it's just telling us to. In order to actually calll it, we'll want to create our agent."]}, {"cell_type": "markdown", "id": "40ccec80", "metadata": {}, "source": ["## Create the agent\n", "\n", "Now that we have defined the tools and the LLM, we can create the agent. We will be using a tool calling agent - for more information on this type of agent, as well as other options, see [this guide](/docs/concepts/agents/).\n", "\n", "We can first choose the prompt we want to use to guide the agent:"]}, {"cell_type": "code", "execution_count": 14, "id": "af83d3e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessagePromptTemplate {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      prompt: PromptTemplate {\n", "        lc_serializable: true,\n", "        lc_kwargs: {\n", "          inputVariables: [],\n", "          templateFormat: \"f-string\",\n", "          template: \"You are a helpful assistant\"\n", "        },\n", "        lc_runnable: true,\n", "        name: undefined,\n", "        lc_namespace: [ \"langchain_core\", \"prompts\", \"prompt\" ],\n", "        inputVariables: [],\n", "        outputParser: undefined,\n", "        partialVariables: undefined,\n", "        templateFormat: \"f-string\",\n", "        template: \"You are a helpful assistant\",\n", "        validateTemplate: true,\n", "        additionalContentFields: undefined\n", "      }\n", "    },\n", "    lc_runnable: true,\n", "    name: undefined,\n", "    lc_namespace: [ \"langchain_core\", \"prompts\", \"chat\" ],\n", "    inputVariables: [],\n", "    additionalOptions: {},\n", "    prompt: PromptTemplate {\n", "      lc_serializable: true,\n", "      lc_kwargs: {\n", "        inputVariables: [],\n", "        templateFormat: \"f-string\",\n", "        template: \"You are a helpful assistant\"\n", "      },\n", "      lc_runnable: true,\n", "      name: undefined,\n", "      lc_namespace: [ \"langchain_core\", \"prompts\", \"prompt\" ],\n", "      inputVariables: [],\n", "      outputParser: undefined,\n", "      partialVariables: undefined,\n", "      templateFormat: \"f-string\",\n", "      template: \"You are a helpful assistant\",\n", "      validateTemplate: true,\n", "      additionalContentFields: undefined\n", "    },\n", "    messageClass: undefined,\n", "    chatMessageClass: undefined\n", "  },\n", "  MessagesPlaceholder {\n", "    lc_serializable: true,\n", "    lc_kwargs: { variableName: \"chat_history\", optional: true },\n", "    lc_runnable: true,\n", "    name: undefined,\n", "    lc_namespace: [ \"langchain_core\", \"prompts\", \"chat\" ],\n", "    variableName: \"chat_history\",\n", "    optional: true\n", "  },\n", "  HumanMessagePromptTemplate {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      prompt: PromptTemplate {\n", "        lc_serializable: true,\n", "        lc_kwargs: {\n", "          inputVariables: [<PERSON>rra<PERSON>],\n", "          templateFormat: \"f-string\",\n", "          template: \"{input}\"\n", "        },\n", "        lc_runnable: true,\n", "        name: undefined,\n", "        lc_namespace: [ \"langchain_core\", \"prompts\", \"prompt\" ],\n", "        inputVariables: [ \"input\" ],\n", "        outputParser: undefined,\n", "        partialVariables: undefined,\n", "        templateFormat: \"f-string\",\n", "        template: \"{input}\",\n", "        validateTemplate: true,\n", "        additionalContentFields: undefined\n", "      }\n", "    },\n", "    lc_runnable: true,\n", "    name: undefined,\n", "    lc_namespace: [ \"langchain_core\", \"prompts\", \"chat\" ],\n", "    inputVariables: [ \"input\" ],\n", "    additionalOptions: {},\n", "    prompt: PromptTemplate {\n", "      lc_serializable: true,\n", "      lc_kwargs: {\n", "        inputVariables: [ \"input\" ],\n", "        templateFormat: \"f-string\",\n", "        template: \"{input}\"\n", "      },\n", "      lc_runnable: true,\n", "      name: undefined,\n", "      lc_namespace: [ \"langchain_core\", \"prompts\", \"prompt\" ],\n", "      inputVariables: [ \"input\" ],\n", "      outputParser: undefined,\n", "      partialVariables: undefined,\n", "      templateFormat: \"f-string\",\n", "      template: \"{input}\",\n", "      validateTemplate: true,\n", "      additionalContentFields: undefined\n", "    },\n", "    messageClass: undefined,\n", "    chatMessageClass: undefined\n", "  },\n", "  MessagesPlaceholder {\n", "    lc_serializable: true,\n", "    lc_kwargs: { variableName: \"agent_scratchpad\", optional: true },\n", "    lc_runnable: true,\n", "    name: undefined,\n", "    lc_namespace: [ \"langchain_core\", \"prompts\", \"chat\" ],\n", "    variableName: \"agent_scratchpad\",\n", "    optional: true\n", "  }\n", "]\n"]}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const prompt = ChatPromptTemplate.fromMessages([\n", "  [\"system\", \"You are a helpful assistant\"],\n", "  [\"placeholder\", \"{chat_history}\"],\n", "  [\"human\", \"{input}\"],\n", "  [\"placeholder\", \"{agent_scratchpad}\"],\n", "]);\n", "\n", "console.log(prompt.promptMessages);"]}, {"cell_type": "markdown", "id": "f8014c9d", "metadata": {}, "source": ["Now, we can initalize the agent with the LLM, the prompt, and the tools. The agent is responsible for taking in input and deciding what actions to take. Crucially, the Agent does not execute those actions - that is done by the AgentExecutor (next step). For more information about how to think about these components, see our [conceptual guide](/docs/concepts/agents).\n", "\n", "Note that we are passing in the `model`, not `modelWithTools`. That is because `createToolCallingAgent` will call `.bind` for us under the hood."]}, {"cell_type": "code", "execution_count": 15, "id": "89cf72b4-6046-4b47-8f27-5522d8cb8036", "metadata": {}, "outputs": [], "source": ["import { createToolCallingAgent } from \"langchain/agents\";\n", "\n", "const agent = await createToolCallingAgent({ llm: model, tools, prompt })"]}, {"cell_type": "markdown", "id": "1a58c9f8", "metadata": {}, "source": ["Finally, we combine the agent (the brains) with the tools inside the AgentExecutor (which will repeatedly call the agent and execute tools)."]}, {"cell_type": "code", "execution_count": 16, "id": "ce33904a", "metadata": {}, "outputs": [], "source": ["import { AgentExecutor } from \"langchain/agents\";\n", "\n", "const agentExecutor = new AgentExecutor({\n", "  agent,\n", "  tools\n", "})"]}, {"cell_type": "markdown", "id": "e4df0e06", "metadata": {}, "source": ["## Run the agent\n", "\n", "We can now run the agent on a few queries! Note that for now, these are all **stateless** queries (it won't remember previous interactions).\n", "\n", "First up, let's how it responds when there's no need to call a tool:"]}, {"cell_type": "code", "execution_count": 17, "id": "114ba50d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{ input: \u001b[32m\"hi!\"\u001b[39m, output: \u001b[32m\"Hello! How can I assist you today?\"\u001b[39m }"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["await agentExecutor.invoke({ input: \"hi!\" })"]}, {"cell_type": "markdown", "id": "71493a42", "metadata": {}, "source": ["In order to see exactly what is happening under the hood (and to make sure it's not calling a tool) we can take a look at the [Lang<PERSON>mith trace](https://smith.langchain.com/public/b8051e80-14fd-4931-be0f-6416280bc500/r)\n", "\n", "Let's now try it out on an example where it should be invoking the retriever"]}, {"cell_type": "code", "execution_count": 18, "id": "3fa4780a", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{\n", "  input: \u001b[32m\"how can langsmith help with testing?\"\u001b[39m,\n", "  output: \u001b[32m\"Lang<PERSON><PERSON> can assist with testing in several ways, particularly for applications built using large l\"\u001b[39m... 1474 more characters\n", "}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["await agentExecutor.invoke({ input: \"how can langsmith help with testing?\" })"]}, {"cell_type": "markdown", "id": "f2d94242", "metadata": {}, "source": ["Let's take a look at the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/35bd4f0f-aa2f-4ac2-b9a9-89ce0ca306ca/r) to make sure it's actually calling that.\n", "\n", "Now let's try one where it needs to call the search tool:"]}, {"cell_type": "code", "execution_count": 19, "id": "77c2f769", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  input: \u001b[32m\"whats the weather in sf?\"\u001b[39m,\n", "  output: \u001b[32m\"The current weather in San Francisco is as follows:\\n\"\u001b[39m +\n", "    \u001b[32m\"\\n\"\u001b[39m +\n", "    \u001b[32m\"- **Temperature**: 15.6°C (60.1°F)\\n\"\u001b[39m +\n", "    \u001b[32m\"- **<PERSON><PERSON><PERSON>\"\u001b[39m... 303 more characters\n", "}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["await agentExecutor.invoke({ input: \"whats the weather in sf?\" })"]}, {"cell_type": "markdown", "id": "c174f838", "metadata": {}, "source": ["We can check out the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/dfde6f46-0e7b-4dfe-813c-87d7bfb2ade5/r) to make sure it's calling the search tool effectively."]}, {"cell_type": "markdown", "id": "022cbc8a", "metadata": {}, "source": ["## Adding in memory\n", "\n", "As mentioned earlier, this agent is stateless. This means it does not remember previous interactions. To give it memory we need to pass in previous `chat_history`.\n", "\n", "**Note**: The input variable needs to be called `chat_history` because of the prompt we are using. If we use a different prompt, we could change the variable name."]}, {"cell_type": "code", "execution_count": 20, "id": "c4073e35", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  input: \u001b[32m\"hi! my name is bob\"\u001b[39m,\n", "  chat_history: [],\n", "  output: \u001b[32m\"Hello <PERSON>! How can I assist you today?\"\u001b[39m\n", "}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["// Here we pass in an empty list of messages for chat_history because it is the first message in the chat\n", "await agentExecutor.invoke({ input: \"hi! my name is bob\", chat_history: [] })"]}, {"cell_type": "code", "execution_count": 21, "id": "550e0c6e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  chat_history: [\n", "    { role: \u001b[32m\"user\"\u001b[39m, content: \u001b[32m\"hi! my name is bob\"\u001b[39m },\n", "    {\n", "      role: \u001b[32m\"assistant\"\u001b[39m,\n", "      content: \u001b[32m\"Hello <PERSON>! How can I assist you today?\"\u001b[39m\n", "    }\n", "  ],\n", "  input: \u001b[32m\"what's my name?\"\u001b[39m,\n", "  output: \u001b[32m\"Your name is <PERSON>. How can I help you today, <PERSON>?\"\u001b[39m\n", "}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["await agentExecutor.invoke(\n", "  {\n", "    chat_history: [\n", "      { role: \"user\", content: \"hi! my name is bob\" },\n", "      { role: \"assistant\", content: \"Hello <PERSON>! How can I assist you today?\" },\n", "    ],\n", "    input: \"what's my name?\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "07b3bcf2", "metadata": {}, "source": ["If we want to keep track of these messages automatically, we can wrap this in a RunnableWithMessageHistory.\n", "\n", "Because we have multiple inputs, we need to specify two things:\n", "\n", "- `inputMessagesKey`: The input key to use to add to the conversation history.\n", "- `historyMessagesKey`: The key to add the loaded messages into.\n", "\n", "For more information on how to use this, see [this guide](/docs/how_to/message_history). "]}, {"cell_type": "code", "execution_count": 22, "id": "8edd96e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  input: \u001b[32m\"hi! I'm bob\"\u001b[39m,\n", "  chat_history: [\n", "    HumanMessage {\n", "      \"content\": \"hi! I'm bob\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"content\": \"Hello <PERSON>! How can I assist you today?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {},\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    }\n", "  ],\n", "  output: \u001b[32m\"Hello <PERSON>! How can I assist you today?\"\u001b[39m\n", "}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import { ChatMessageHistory } from \"@langchain/community/stores/message/in_memory\";\n", "import { BaseChatMessageHistory } from \"@langchain/core/chat_history\";\n", "import { RunnableWithMessageHistory } from \"@langchain/core/runnables\";\n", "\n", "const store = {};\n", "\n", "function getMessageHistory(sessionId: string): BaseChatMessageHistory {\n", "  if (!(sessionId in store)) {\n", "    store[sessionId] = new ChatMessageHistory();\n", "  }\n", "  return store[sessionId];\n", "}\n", "\n", "const agentWithChatHistory = new RunnableWithMessageHistory({\n", "  runnable: <PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "  getMessageHistory,\n", "  inputMessagesKey: \"input\",\n", "  historyMessagesKey: \"chat_history\",\n", "})\n", "\n", "await agentWithChatHistory.invoke(\n", "  { input: \"hi! I'm bob\" },\n", "  { configurable: { sessionId: \"<foo>\" }},\n", ")"]}, {"cell_type": "code", "execution_count": 23, "id": "ae627966", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  input: \u001b[32m\"what's my name?\"\u001b[39m,\n", "  chat_history: [\n", "    HumanMessage {\n", "      \"content\": \"hi! I'm bob\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"content\": \"Hello <PERSON>! How can I assist you today?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {},\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    },\n", "    HumanMessage {\n", "      \"content\": \"what's my name?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"content\": \"Your name is <PERSON>! How can I help you today, <PERSON>?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {},\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    }\n", "  ],\n", "  output: \u001b[32m\"Your name is <PERSON>! How can I help you today, <PERSON>?\"\u001b[39m\n", "}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["await agentWithChatHistory.invoke(\n", "  { input: \"what's my name?\" },\n", "  { configurable: { sessionId: \"<foo>\" }},\n", ")"]}, {"cell_type": "markdown", "id": "6de2798e", "metadata": {}, "source": ["Example Lang<PERSON>mith trace: https://smith.langchain.com/public/98c8d162-60ae-4493-aa9f-992d87bd0429/r"]}, {"cell_type": "markdown", "id": "c029798f", "metadata": {}, "source": ["## Next steps\n", "\n", "That's a wrap! In this quick start we covered how to create a simple agent. Agents are a complex topic, and there's lot to learn! \n", "\n", ":::{.callout-important}\n", "This section covered building with LangChain Agents. LangChain Agents are fine for getting started, but past a certain point you will likely want flexibility and control that they do not offer. For working with more advanced agents, we'd recommend checking out [LangGraph](https://langchain-ai.github.io/langgraphjs).\n", "\n", "You can also see [this guide to help migrate to LangGraph](/docs/how_to/migrate_agent).\n", ":::"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}