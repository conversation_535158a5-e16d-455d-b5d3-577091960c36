{"cells": [{"cell_type": "raw", "id": "fe63ffaf", "metadata": {}, "source": ["---\n", "keywords: [RunnableBinding, LCEL]\n", "---"]}, {"cell_type": "markdown", "id": "711752cb-4f15-42a3-9838-a0c67f397771", "metadata": {}, "source": ["# How to attach runtime configuration to a Runnable\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)\n", "- [Chaining runnables](/docs/how_to/sequence/)\n", "- [Tool calling](/docs/how_to/tool_calling/)\n", "\n", ":::\n", "\n", "Sometimes we want to invoke a [`Runnable`](https://api.js.langchain.com/classes/langchain_core.runnables.Runnable.html) with predefined configuration that doesn't need to be assigned by the caller. We can use the [`Runnable.withConfig()`](https://api.js.langchain.com/classes/langchain_core.runnables.Runnable.html#withConfig) method to set these arguments ahead of time.\n", "\n", "## Binding stop sequences\n", "\n", "Suppose we have a simple prompt + model chain:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/openai @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "f3fdf86d-155f-4587-b7cd-52d363970c1d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EQUATION: x^3 + 7 = 12\n", "\n", "SOLUTION: \n", "Subtract 7 from both sides:\n", "x^3 = 5\n", "\n", "Take the cube root of both sides:\n", "x = ∛5\n"]}], "source": ["import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "    [\n", "        [\n", "            \"system\",\n", "            \"Write out the following equation using algebraic symbols then solve it. Use the format\\n\\nEQUATION:...\\nSOLUTION:...\\n\\n\",\n", "        ],\n", "        [\"human\", \"{equation_statement}\"],\n", "    ]\n", ")\n", "\n", "const model = new ChatOpenAI({ temperature: 0 });\n", "\n", "const runnable = prompt.pipe(model).pipe(new StringOutputParser());\n", "\n", "const res = await runnable.invoke({\n", "  equation_statement: \"x raised to the third plus seven equals 12\"\n", "});\n", "\n", "console.log(res);"]}, {"cell_type": "markdown", "id": "929c9aba-a4a0-462c-adac-2cfc2156e117", "metadata": {}, "source": ["In certain prompting techniques it can be useful to set one or more `stop` words that halt generation when they are emitted by the model. When using the model directly we set `stop` words via the extra `options` argument passed to `invoke`. For example, what if we wanted to modify the example to work as an equation formatter utility? We could instruct the generation process to stop at the word `SOLUTION`, and the resulting output would be only the formatted equation.\n", "\n", "In this case we are using our model as part of a [RunnableSequence](https://api.js.langchain.com/classes/langchain_core.runnables.RunnableSequence.html). Rather than relying on the preceeding step to output config that contains stop words, we can simply bind the necessary config using `withConfig` while creating the `RunnableSequence`:"]}, {"cell_type": "code", "execution_count": 2, "id": "32e0484a-78c5-4570-a00b-20d597245a96", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EQUATION: x^3 + 7 = 12\n", "\n", "\n"]}], "source": ["// stop generating after the equation is written\n", "const equationFormatter = prompt\n", "  .pipe(model.withConfig({ stop: [\"SOLUTION\"] }))\n", "  .pipe(new StringOutputParser());\n", "\n", "// generate only the equation, without needing to set the stop word\n", "const formattedEquation = await equationFormatter.invoke({\n", "  equation_statement: \"x raised to the third plus seven equals 12\"\n", "});\n", "\n", "console.log(formattedEquation);"]}, {"cell_type": "markdown", "id": "f07d7528-9269-4d6f-b12e-3669592a9e03", "metadata": {}, "source": ["This makes the resulting `Runnable` pipeline easier to consume. The caller doesn't need to know the exact prompting format used, nor do they need to specify the stop word upon invocation. They can simply run their query and get back the result they expect.\n", "\n", "## Attaching OpenAI tools\n", "\n", "Another common use-case is tool calling. While you should generally use the [`.bindTools()`](/docs/how_to/tool_calling/) method for tool-calling models, you can also bind provider-specific args directly if you want lower level control:"]}, {"cell_type": "code", "execution_count": 3, "id": "2cdeeb4c-0c1f-43da-bd58-4f591d9e0671", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-BXjkosti03tvSmaxAuYtpRvbEkhRx\",\n", "  \"content\": \"\",\n", "  \"additional_kwargs\": {\n", "    \"tool_calls\": [\n", "      {\n", "        \"id\": \"call_a15PVBt9g3eCHULn7DBRbL9a\",\n", "        \"type\": \"function\",\n", "        \"function\": \"[Object]\"\n", "      },\n", "      {\n", "        \"id\": \"call_bQrHLyJ6fAaNkEPNBtgYfIFb\",\n", "        \"type\": \"function\",\n", "        \"function\": \"[Object]\"\n", "      },\n", "      {\n", "        \"id\": \"call_FxRswXKWou53G0LNQQCvPod4\",\n", "        \"type\": \"function\",\n", "        \"function\": \"[Object]\"\n", "      }\n", "    ]\n", "  },\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"promptTokens\": 82,\n", "      \"completionTokens\": 71,\n", "      \"totalTokens\": 153\n", "    },\n", "    \"finish_reason\": \"tool_calls\",\n", "    \"model_name\": \"gpt-4o-2024-08-06\",\n", "    \"usage\": {\n", "      \"prompt_tokens\": 82,\n", "      \"completion_tokens\": 71,\n", "      \"total_tokens\": 153,\n", "      \"prompt_tokens_details\": {\n", "        \"cached_tokens\": 0,\n", "        \"audio_tokens\": 0\n", "      },\n", "      \"completion_tokens_details\": {\n", "        \"reasoning_tokens\": 0,\n", "        \"audio_tokens\": 0,\n", "        \"accepted_prediction_tokens\": 0,\n", "        \"rejected_prediction_tokens\": 0\n", "      }\n", "    },\n", "    \"system_fingerprint\": \"fp_90122d973c\"\n", "  },\n", "  \"tool_calls\": [\n", "    {\n", "      \"name\": \"get_current_weather\",\n", "      \"args\": {\n", "        \"location\": \"San Francisco, CA\"\n", "      },\n", "      \"type\": \"tool_call\",\n", "      \"id\": \"call_a15PVBt9g3eCHULn7DBRbL9a\"\n", "    },\n", "    {\n", "      \"name\": \"get_current_weather\",\n", "      \"args\": {\n", "        \"location\": \"New York, NY\"\n", "      },\n", "      \"type\": \"tool_call\",\n", "      \"id\": \"call_bQrHLyJ6fAaNkEPNBtgYfIFb\"\n", "    },\n", "    {\n", "      \"name\": \"get_current_weather\",\n", "      \"args\": {\n", "        \"location\": \"Los Angeles, CA\"\n", "      },\n", "      \"type\": \"tool_call\",\n", "      \"id\": \"call_FxRswXKWou53G0LNQQCvPod4\"\n", "    }\n", "  ],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"output_tokens\": 71,\n", "    \"input_tokens\": 82,\n", "    \"total_tokens\": 153,\n", "    \"input_token_details\": {\n", "      \"audio\": 0,\n", "      \"cache_read\": 0\n", "    },\n", "    \"output_token_details\": {\n", "      \"audio\": 0,\n", "      \"reasoning\": 0\n", "    }\n", "  }\n", "}\n"]}], "source": ["const tools = [\n", "  {\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "      \"name\": \"get_current_weather\",\n", "      \"description\": \"Get the current weather in a given location\",\n", "      \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "          \"location\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The city and state, e.g. San Francisco, CA\",\n", "          },\n", "          \"unit\": {\"type\": \"string\", \"enum\": [\"celsius\", \"fahrenheit\"]},\n", "        },\n", "        \"required\": [\"location\"],\n", "      },\n", "    },\n", "  }\n", "];\n", "\n", "const modelWithTools = new ChatOpenAI({ model: \"gpt-4o\" }).withConfig({ tools });\n", "\n", "await modelWithTools.invoke(\"What's the weather in SF, NYC and LA?\")"]}, {"cell_type": "markdown", "id": "095001f7", "metadata": {}, "source": ["## Next steps\n", "\n", "You now know how to bind runtime arguments to a Run<PERSON>ble.\n", "\n", "Next, you might be interested in our how-to guides on [passing data through a chain](/docs/how_to/passthrough/)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}