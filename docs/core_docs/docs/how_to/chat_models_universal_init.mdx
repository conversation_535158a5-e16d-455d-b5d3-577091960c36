# How to init any model in one line

import CodeBlock from "@theme/CodeBlock";

Many LLM applications let end users specify what model provider and model they want the application to be powered by.
This requires writing some logic to initialize different ChatModels based on some user configuration.
The `initChatModel()` helper method makes it easy to initialize a number of different model integrations without having to worry about import paths and class names.
Keep in mind this feature is only for chat models.

:::info Prerequisites

This guide assumes familiarity with the following concepts:

- [Chat models](/docs/concepts/chat_models)

- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)

- [Tool calling](/docs/concepts/tools)

:::

:::caution Compatibility
**This feature is only intended to be used in Node environments. Use in non Node environments or with bundlers is not guaranteed to work and not officially supported.**

`initChatModel` requires `langchain>=0.2.11`. See [this guide](/docs/how_to/installation/#installing-integration-packages) for some considerations to take when upgrading.

See the [initChatModel()](https://api.js.langchain.com/functions/langchain.chat_models_universal.initChatModel.html) API reference for a full list of supported integrations.

Make sure you have the integration packages installed for any model providers you want to support. E.g. you should have `@langchain/openai` installed to init an OpenAI model.
:::

## Basic usage

import BasicExample from "@examples/models/chat/configurable/basic.ts";

<CodeBlock language="typescript">{BasicExample}</CodeBlock>

## Inferring model provider

For common and distinct model names `initChatModel()` will attempt to infer the model provider.
See the [API reference](https://api.js.langchain.com/functions/langchain.chat_models_universal.initChatModel.html) for a full list of inference behavior.
E.g. any model that starts with `gpt-3...` or `gpt-4...` will be inferred as using model provider `openai`.

import InferringProviderExample from "@examples/models/chat/configurable/inferring_model_provider.ts";

<CodeBlock language="typescript">{InferringProviderExample}</CodeBlock>

## Creating a configurable model

You can also create a runtime-configurable model by specifying `configurableFields`.
If you don't specify a `model` value, then "model" and "modelProvider" be configurable by default.

import ConfigurableModelExample from "@examples/models/chat/configurable/configurable_model.ts";

<CodeBlock language="typescript">{ConfigurableModelExample}</CodeBlock>

### Configurable model with default values

We can create a configurable model with default model values, specify which parameters are configurable, and add prefixes to configurable params:

import ConfigurableModelWithDefaultsExample from "@examples/models/chat/configurable/configurable_model_with_defaults.ts";

<CodeBlock language="typescript">
  {ConfigurableModelWithDefaultsExample}
</CodeBlock>

### Using a configurable model declaratively

We can call declarative operations like `bindTools`, `withStructuredOutput`, `withConfig`, etc. on a configurable model and chain a configurable model in the same way that we would a regularly instantiated chat model object.

import ConfigurableModelDeclarativelyExample from "@examples/models/chat/configurable/configurable_model_declaratively.ts";

<CodeBlock language="typescript">
  {ConfigurableModelDeclarativelyExample}
</CodeBlock>
