{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_position: 1\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to add memory to chatbots\n", "\n", "A key feature of chatbots is their ability to use content of previous conversation turns as context. This state management can take several forms, including:\n", "\n", "- Simply stuffing previous messages into a chat model prompt.\n", "- The above, but trimming old messages to reduce the amount of distracting information the model has to deal with.\n", "- More complex modifications like synthesizing summaries for long running conversations.\n", "\n", "We'll go into more detail on a few techniques below!\n", "\n", ":::note\n", "\n", "This how-to guide previously built a chatbot using [RunnableWithMessageHistory](https://v03.api.js.langchain.com/classes/_langchain_core.runnables.RunnableWithMessageHistory.html). You can access this version of the tutorial in the [v0.2 docs](https://js.langchain.com/v0.2/docs/how_to/chatbots_memory/).\n", "\n", "The LangGraph implementation offers a number of advantages over `RunnableWithMessageHistory`, including the ability to persist arbitrary components of an application's state (instead of only messages).\n", "\n", ":::\n", "\n", "## Setup\n", "\n", "You'll need to install a few packages, select your chat model, and set its enviroment variable.\n", "\n", "```{=mdx}\n", "import Npm2Yarn from \"@theme/Npm2Yarn\"\n", "\n", "<Npm2Yarn>\n", "  @langchain/core @langchain/langgraph\n", "</Npm2Yarn>\n", "```\n", "\n", "Let's set up a chat model that we'll use for the below examples.\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs />\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Message passing\n", "\n", "The simplest form of memory is simply passing chat history messages into a chain. Here's an example:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({ model: \"gpt-4o\" })"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-ABSxUXVIBitFRBh9MpasB5jeEHfCA\",\n", "  \"content\": \"I said \\\"J'adore la programmation,\\\" which means \\\"I love programming\\\" in French.\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 18,\n", "      \"promptTokens\": 58,\n", "      \"totalTokens\": 76\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"system_fingerprint\": \"fp_e375328146\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 58,\n", "    \"output_tokens\": 18,\n", "    \"total_tokens\": 76\n", "  }\n", "}\n"]}], "source": ["import { HumanMessage, AIMessage } from \"@langchain/core/messages\";\n", "import {\n", "  ChatPromptTemplate,\n", "  MessagesPlaceholder,\n", "} from \"@langchain/core/prompts\";\n", "\n", "const prompt = ChatPromptTemplate.fromMessages([\n", "  [\n", "    \"system\",\n", "    \"You are a helpful assistant. Answer all questions to the best of your ability.\",\n", "  ],\n", "  new MessagesPlaceholder(\"messages\"),\n", "]);\n", "\n", "const chain = prompt.pipe(llm);\n", "\n", "await chain.invoke({\n", "  messages: [\n", "    new HumanMessage(\n", "      \"Translate this sentence from English to French: I love programming.\"\n", "    ),\n", "    new AIMessage(\"J'adore la programmation.\"),\n", "    new HumanMessage(\"What did you just say?\"),\n", "  ],\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that by passing the previous conversation into a chain, it can use it as context to answer questions. This is the basic concept underpinning chatbot memory - the rest of the guide will demonstrate convenient techniques for passing or reformatting messages."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automatic history management\n", "\n", "The previous examples pass messages to the chain (and model) explicitly. This is a completely acceptable approach, but it does require external management of new messages. LangChain also provides a way to build applications that have memory using LangGraph's persistence. You can enable persistence in LangGraph applications by providing a `checkpointer` when compiling the graph."]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["import { START, END, MessagesAnnotation, StateGraph, MemorySaver } from \"@langchain/langgraph\";\n", "\n", "\n", "// Define the function that calls the model\n", "const callModel = async (state: typeof MessagesAnnotation.State) => {\n", "  const systemPrompt = \n", "    \"You are a helpful assistant. \" +\n", "    \"Answer all questions to the best of your ability.\";\n", "  const messages = [{ role: \"system\", content: systemPrompt }, ...state.messages];\n", "  const response = await llm.invoke(messages);\n", "  return { messages: response };\n", "};\n", "\n", "const workflow = new StateGraph(MessagesAnnotation)\n", "// Define the node and edge\n", "  .addNode(\"model\", callModel)\n", "  .addEdge(START, \"model\")\n", "  .addEdge(\"model\", END);\n", "\n", "// Add simple in-memory checkpointer\n", "// highlight-start\n", "const memory = new MemorySaver();\n", "const app = workflow.compile({ checkpointer: memory });\n", "// highlight-end"]}, {"cell_type": "markdown", "metadata": {}, "source": [" We'll pass the latest input to the conversation here and let the LangGraph keep track of the conversation history using the checkpointer:"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  messages: [\n", "    HumanMessage {\n", "      \"id\": \"227b82a9-4084-46a5-ac79-ab9a3faa140e\",\n", "      \"content\": \"Translate to French: I love programming.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"chatcmpl-ABSxVrvztgnasTeMSFbpZQmyYqjJZ\",\n", "      \"content\": \"J'adore la programmation.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {\n", "        \"tokenUsage\": {\n", "          \"completionTokens\": 5,\n", "          \"promptTokens\": 35,\n", "          \"totalTokens\": 40\n", "        },\n", "        \"finish_reason\": \"stop\",\n", "        \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "      },\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": [],\n", "      \"usage_metadata\": {\n", "        \"input_tokens\": 35,\n", "        \"output_tokens\": 5,\n", "        \"total_tokens\": 40\n", "      }\n", "    }\n", "  ]\n", "}\n"]}], "source": ["await app.invoke(\n", "  {\n", "    messages: [\n", "      {\n", "        role: \"user\",\n", "        content: \"Translate to French: I love programming.\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    configurable: { thread_id: \"1\" }\n", "  }\n", ");"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  messages: [\n", "    HumanMessage {\n", "      \"id\": \"1a0560a4-9dcb-47a1-b441-80717e229706\",\n", "      \"content\": \"Translate to French: I love programming.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"chatcmpl-ABSxVrvztgnasTeMSFbpZQmyYqjJZ\",\n", "      \"content\": \"J'adore la programmation.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {\n", "        \"tokenUsage\": {\n", "          \"completionTokens\": 5,\n", "          \"promptTokens\": 35,\n", "          \"totalTokens\": 40\n", "        },\n", "        \"finish_reason\": \"stop\",\n", "        \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "      },\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    },\n", "    HumanMessage {\n", "      \"id\": \"4f233a7d-4b08-4f53-bb60-cf0141a59721\",\n", "      \"content\": \"What did I just ask you?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"chatcmpl-ABSxVs5QnlPfbihTOmJrCVg1Dh7Ol\",\n", "      \"content\": \"You asked me to translate \\\"I love programming\\\" into French.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {\n", "        \"tokenUsage\": {\n", "          \"completionTokens\": 13,\n", "          \"promptTokens\": 55,\n", "          \"totalTokens\": 68\n", "        },\n", "        \"finish_reason\": \"stop\",\n", "        \"system_fingerprint\": \"fp_9f2bfdaa89\"\n", "      },\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": [],\n", "      \"usage_metadata\": {\n", "        \"input_tokens\": 55,\n", "        \"output_tokens\": 13,\n", "        \"total_tokens\": 68\n", "      }\n", "    }\n", "  ]\n", "}\n"]}], "source": ["await app.invoke(\n", "  {\n", "    messages: [\n", "      {\n", "        role: \"user\",\n", "        content: \"What did I just ask you?\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    configurable: { thread_id: \"1\" }\n", "  }\n", ");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Modifying chat history\n", "\n", "Modifying stored chat messages can help your chatbot handle a variety of situations. Here are some examples:\n", "\n", "### Trimming messages\n", "\n", "LLMs and chat models have limited context windows, and even if you're not directly hitting limits, you may want to limit the amount of distraction the model has to deal with. One solution is trim the history messages before passing them to the model. Let's use an example history with the `app` we declared above:"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  messages: [\n", "    HumanMessage {\n", "      \"id\": \"63057c3d-f980-4640-97d6-497a9f83ddee\",\n", "      \"content\": \"Hey there! I'm <PERSON><PERSON><PERSON>.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"c9f0c20a-8f55-4909-b281-88f2a45c4f05\",\n", "      \"content\": \"Hello!\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {},\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    },\n", "    HumanMessage {\n", "      \"id\": \"fd7fb3a0-7bc7-4e84-99a9-731b30637b55\",\n", "      \"content\": \"How are you today?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"09b0debb-1d4a-4856-8821-b037f5d96ecf\",\n", "      \"content\": \"Fine thanks!\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {},\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    },\n", "    HumanMessage {\n", "      \"id\": \"edc13b69-25a0-40ac-81b3-175e65dc1a9a\",\n", "      \"content\": \"What's my name?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"chatcmpl-ABSxWKCTdRuh2ZifXsvFHSo5z5I0J\",\n", "      \"content\": \"Your name is <PERSON><PERSON><PERSON>! How can I assist you today, <PERSON><PERSON><PERSON>?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {\n", "        \"tokenUsage\": {\n", "          \"completionTokens\": 14,\n", "          \"promptTokens\": 63,\n", "          \"totalTokens\": 77\n", "        },\n", "        \"finish_reason\": \"stop\",\n", "        \"system_fingerprint\": \"fp_a5d11b2ef2\"\n", "      },\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": [],\n", "      \"usage_metadata\": {\n", "        \"input_tokens\": 63,\n", "        \"output_tokens\": 14,\n", "        \"total_tokens\": 77\n", "      }\n", "    }\n", "  ]\n", "}\n"]}], "source": ["const demoEphemeralChatHistory = [\n", "  { role: \"user\", content: \"Hey there! I'm <PERSON><PERSON><PERSON>.\" },\n", "  { role: \"assistant\", content: \"Hello!\" },\n", "  { role: \"user\", content: \"How are you today?\" },\n", "  { role: \"assistant\", content: \"Fine thanks!\" },\n", "];\n", "\n", "await app.invoke(\n", "  {\n", "    messages: [\n", "      ...demoEphemeralChatHistory,\n", "      { role: \"user\", content: \"What's my name?\" }\n", "    ]\n", "  },\n", "  {\n", "    configurable: { thread_id: \"2\" }\n", "  }\n", ");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see the app remembers the preloaded name.\n", "\n", "But let's say we have a very small context window, and we want to trim the number of messages passed to the model to only the 2 most recent ones. We can use the built in [trimMessages](/docs/how_to/trim_messages/) util to trim messages based on their token count before they reach our prompt. In this case we'll count each message as 1 \"token\" and keep only the last two messages:"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["import { START, END, MessagesAnnotation, StateGraph, MemorySaver } from \"@langchain/langgraph\";\n", "import { trimMessages } from \"@langchain/core/messages\";\n", "\n", "// Define trimmer\n", "// highlight-start\n", "// count each message as 1 \"token\" (tokenCounter: (msgs) => msgs.length) and keep only the last two messages\n", "const trimmer = trimMessages({ strategy: \"last\", maxTokens: 2, tokenCounter: (msgs) => msgs.length });\n", "// highlight-end\n", "\n", "// Define the function that calls the model\n", "const callModel2 = async (state: typeof MessagesAnnotation.State) => {\n", "  // highlight-start\n", "  const trimmedMessages = await trimmer.invoke(state.messages);\n", "  const systemPrompt = \n", "    \"You are a helpful assistant. \" +\n", "    \"Answer all questions to the best of your ability.\";\n", "  const messages = [{ role: \"system\", content: systemPrompt }, ...trimmedMessages];\n", "  // highlight-end\n", "  const response = await llm.invoke(messages);\n", "  return { messages: response };\n", "};\n", "\n", "const workflow2 = new StateGraph(MessagesAnnotation)\n", "  // Define the node and edge\n", "  .addNode(\"model\", callModel2)\n", "  .addEdge(START, \"model\")\n", "  .addEdge(\"model\", END);\n", "\n", "// Add simple in-memory checkpointer\n", "const app2 = workflow2.compile({ checkpointer: new MemorySaver() });"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's call this new app and check the response"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  messages: [\n", "    HumanMessage {\n", "      \"id\": \"0d9330a0-d9d1-4aaf-8171-ca1ac6344f7c\",\n", "      \"content\": \"What is my name?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"3a24e88b-7525-4797-9fcd-d751a378d22c\",\n", "      \"content\": \"Fine thanks!\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {},\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    },\n", "    HumanMessage {\n", "      \"id\": \"276039c8-eba8-4c68-b015-81ec7704140d\",\n", "      \"content\": \"How are you today?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"2ad4f461-20e1-4982-ba3b-235cb6b02abd\",\n", "      \"content\": \"Hello!\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {},\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": []\n", "    },\n", "    HumanMessage {\n", "      \"id\": \"52213cae-953a-463d-a4a0-a7368c9ee4db\",\n", "      \"content\": \"Hey there! I'm <PERSON><PERSON><PERSON>.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"chatcmpl-ABSxWe9BRDl1pmzkNIDawWwU3hvKm\",\n", "      \"content\": \"I'm sorry, but I don't have access to personal information about you unless you've shared it with me during our conversation. How can I assist you today?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {\n", "        \"tokenUsage\": {\n", "          \"completionTokens\": 30,\n", "          \"promptTokens\": 39,\n", "          \"totalTokens\": 69\n", "        },\n", "        \"finish_reason\": \"stop\",\n", "        \"system_fingerprint\": \"fp_3537616b13\"\n", "      },\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": [],\n", "      \"usage_metadata\": {\n", "        \"input_tokens\": 39,\n", "        \"output_tokens\": 30,\n", "        \"total_tokens\": 69\n", "      }\n", "    }\n", "  ]\n", "}\n"]}], "source": ["await app2.invoke(\n", "  {\n", "    messages: [\n", "      ...demoEphemeralChatHistory,\n", "      { role: \"user\", content: \"What is my name?\" }\n", "    ]\n", "  },\n", "  {\n", "    configurable: { thread_id: \"3\" }\n", "  }\n", ");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that `trimMessages` was called and only the two most recent messages will be passed to the model. In this case, this means that the model forgot the name we gave it.\n", "\n", "Check out our [how to guide on trimming messages](/docs/how_to/trim_messages/) for more."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Summary memory\n", "\n", "We can use this same pattern in other ways too. For example, we could use an additional LLM call to generate a summary of the conversation before calling our app. Let's recreate our chat history:"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["const demoEphemeralChatHistory2 = [\n", "  { role: \"user\", content: \"Hey there! I'm <PERSON><PERSON><PERSON>.\" },\n", "  { role: \"assistant\", content: \"Hello!\" },\n", "  { role: \"user\", content: \"How are you today?\" },\n", "  { role: \"assistant\", content: \"Fine thanks!\" },\n", "];"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now, let's update the model-calling function to distill previous interactions into a summary:"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["import { START, END, MessagesAnnotation, StateGraph, MemorySaver } from \"@langchain/langgraph\";\n", "import { RemoveMessage } from \"@langchain/core/messages\";\n", "\n", "\n", "// Define the function that calls the model\n", "const callModel3 = async (state: typeof MessagesAnnotation.State) => {\n", "  const systemPrompt = \n", "    \"You are a helpful assistant. \" +\n", "    \"Answer all questions to the best of your ability. \" +\n", "    \"The provided chat history includes a summary of the earlier conversation.\";\n", "  const systemMessage = { role: \"system\", content: systemPrompt };\n", "  const messageHistory = state.messages.slice(0, -1); // exclude the most recent user input\n", "  \n", "  // Summarize the messages if the chat history reaches a certain size\n", "  if (messageHistory.length >= 4) {\n", "    const lastHumanMessage = state.messages[state.messages.length - 1];\n", "    // Invoke the model to generate conversation summary\n", "    const summaryPrompt = \n", "      \"Distill the above chat messages into a single summary message. \" +\n", "      \"Include as many specific details as you can.\";\n", "    const summaryMessage = await llm.invoke([\n", "      ...messageHistory,\n", "      { role: \"user\", content: summaryPrompt }\n", "    ]);\n", "\n", "    // Delete messages that we no longer want to show up\n", "    const deleteMessages = state.messages.map(m => new RemoveMessage({ id: m.id }));\n", "    // Re-add user message\n", "    const humanMessage = { role: \"user\", content: lastHumanMessage.content };\n", "    // Call the model with summary & response\n", "    const response = await llm.invoke([systemMessage, summaryMessage, humanMessage]);\n", "    return { messages: [summaryMessage, humanMessage, response, ...deleteMessages] };\n", "  } else {\n", "    const response = await llm.invoke([systemMessage, ...state.messages]);\n", "    return { messages: response };\n", "  }\n", "};\n", "\n", "const workflow3 = new StateGraph(MessagesAnnotation)\n", "  // Define the node and edge\n", "  .addNode(\"model\", callModel3)\n", "  .addEdge(START, \"model\")\n", "  .addEdge(\"model\", END);\n", "\n", "// Add simple in-memory checkpointer\n", "const app3 = workflow3.compile({ checkpointer: new MemorySaver() });"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see if it remembers the name we gave it:"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  messages: [\n", "    AIMessage {\n", "      \"id\": \"chatcmpl-ABSxXjFDj6WRo7VLSneBtlAxUumPE\",\n", "      \"content\": \"<PERSON><PERSON><PERSON> greeted the assistant and asked how it was doing, to which the assistant responded that it was fine.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {\n", "        \"tokenUsage\": {\n", "          \"completionTokens\": 22,\n", "          \"promptTokens\": 60,\n", "          \"totalTokens\": 82\n", "        },\n", "        \"finish_reason\": \"stop\",\n", "        \"system_fingerprint\": \"fp_e375328146\"\n", "      },\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": [],\n", "      \"usage_metadata\": {\n", "        \"input_tokens\": 60,\n", "        \"output_tokens\": 22,\n", "        \"total_tokens\": 82\n", "      }\n", "    },\n", "    HumanMessage {\n", "      \"id\": \"8b1309b7-c09e-47fb-9ab3-34047f6973e3\",\n", "      \"content\": \"What did I say my name was?\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    AIMessage {\n", "      \"id\": \"chatcmpl-ABSxYAQKiBsQ6oVypO4CLFDsi1HRH\",\n", "      \"content\": \"You mentioned that your name is <PERSON><PERSON><PERSON>.\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {\n", "        \"tokenUsage\": {\n", "          \"completionTokens\": 8,\n", "          \"promptTokens\": 73,\n", "          \"totalTokens\": 81\n", "        },\n", "        \"finish_reason\": \"stop\",\n", "        \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "      },\n", "      \"tool_calls\": [],\n", "      \"invalid_tool_calls\": [],\n", "      \"usage_metadata\": {\n", "        \"input_tokens\": 73,\n", "        \"output_tokens\": 8,\n", "        \"total_tokens\": 81\n", "      }\n", "    }\n", "  ]\n", "}\n"]}], "source": ["await app3.invoke(\n", "  {\n", "    messages: [\n", "      ...demoEphemeralChatHistory2,\n", "      { role: \"user\", content: \"What did I say my name was?\" }\n", "    ]\n", "  },\n", "  {\n", "    configurable: { thread_id: \"4\" }\n", "  }\n", ");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that invoking the app again will keep accumulating the history until it reaches the specified number of messages (four in our case). At that point we will generate another summary generated from the initial summary plus new messages and so on."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}