{"cells": [{"cell_type": "raw", "id": "04171ad7", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "keywords: [custom tool, custom tools]\n", "---"]}, {"cell_type": "markdown", "id": "5436020b", "metadata": {}, "source": ["# How to create Tools\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [LangChain tools](/docs/concepts/tools)\n", "- [Agents](/docs/concepts/agents)\n", "\n", ":::\n", "\n", "When constructing your own agent, you will need to provide it with a list of Tools that it can use. While LangChain includes some prebuilt tools, it can often be more useful to use tools that use custom logic. This guide will walk you through some ways you can create custom tools.\n", "\n", "The biggest difference here is that the first function requires an object with multiple input fields, while the second one only accepts an object with a single field. Some older agents only work with functions that require single inputs, so it's important to understand the distinction.\n", "\n", "<PERSON><PERSON><PERSON><PERSON> has a handful of ways to construct tools for different applications. Below I'll show the two most common ways to create tools, and where you might use each."]}, {"cell_type": "markdown", "id": "82bb159d", "metadata": {}, "source": ["## Tool schema\n", "\n", "```{=mdx}\n", ":::caution Compatibility\n", "Only available in `@langchain/core` version 0.2.19 and above.\n", ":::\n", "```\n", "\n", "The simplest way to create a tool is through the [`StructuredToolParams`](https://api.js.langchain.com/interfaces/_langchain_core.tools.StructuredToolParams.html) schema. Every chat model which supports tool calling in LangChain accepts binding tools to the model through this schema. This schema has only three fields\n", "\n", "- `name` - The name of the tool.\n", "- `schema` - The schema of the tool, defined with a Zod object.\n", "- `description` (optional) - A description of the tool.\n", "\n", "This schema does not include a function to pair with the tool, and for this reason it should only be used in situations where the generated output does not need to be passed as the input argument to a function."]}, {"cell_type": "code", "execution_count": null, "id": "4d129789", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { StructuredToolParams } from \"@langchain/core/tools\";\n", "\n", "const simpleToolSchema: StructuredToolParams = {\n", "  name: \"get_current_weather\",\n", "  description: \"Get the current weather for a location\",\n", "  schema: z.object({\n", "    city: z.string().describe(\"The city to get the weather for\"),\n", "    state: z.string().optional().describe(\"The state to get the weather for\"),\n", "  })\n", "}"]}, {"cell_type": "markdown", "id": "f6ec6ee8", "metadata": {}, "source": ["## `tool` function\n", "\n", "```{=mdx}\n", ":::caution Compatibility\n", "Only available in `@langchain/core` version 0.2.7 and above.\n", ":::\n", "```\n", "\n", "The [`tool`](https://api.js.langchain.com/classes/langchain_core.tools.Tool.html) wrapper function is a convenience method for turning a JavaScript function into a tool. It requires the function itself along with some additional arguments that define your tool. You should use this over `StructuredToolParams` tools when the resulting tool call executes a function. The most important are:\n", "\n", "- The tool's `name`, which the LLM will use as context as well as to reference the tool\n", "- An optional, but recommended `description`, which the LLM will use as context to know when to use the tool\n", "- A `schema`, which defines the shape of the tool's input\n", "\n", "The `tool` function will return an instance of the [`StructuredTool`](https://api.js.langchain.com/classes/langchain_core.tools.StructuredTool.html) class, so it is compatible with all the existing tool calling infrastructure in the LangChain library."]}, {"cell_type": "code", "execution_count": 1, "id": "ecc1ce9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"The sum of 1 and 2 is 3\"\u001b[39m"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import { z } from \"zod\";\n", "import { tool } from \"@langchain/core/tools\";\n", "\n", "const adderSchema = z.object({\n", "  a: z.number(),\n", "  b: z.number(),\n", "});\n", "const adderTool = tool(async (input): Promise<string> => {\n", "  const sum = input.a + input.b;\n", "  return `The sum of ${input.a} and ${input.b} is ${sum}`;\n", "}, {\n", "  name: \"adder\",\n", "  description: \"Adds two numbers together\",\n", "  schema: adderSchema,\n", "});\n", "\n", "await adderTool.invoke({ a: 1, b: 2 });"]}, {"cell_type": "markdown", "id": "213ee344", "metadata": {}, "source": ["## `DynamicStructuredTool`\n", "\n", "You can also use the [`DynamicStructuredTool`](https://api.js.langchain.com/classes/langchain_core.tools.DynamicStructuredTool.html) class to declare tools. Here's an example - note that tools must always return strings!"]}, {"cell_type": "code", "execution_count": 2, "id": "833dda4a", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"72\"\u001b[39m"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import { DynamicStructuredTool } from \"@langchain/core/tools\";\n", "import { z } from \"zod\";\n", "\n", "const multiplyTool = new DynamicStructuredTool({\n", "  name: \"multiply\",\n", "  description: \"multiply two numbers together\",\n", "  schema: z.object({\n", "    a: z.number().describe(\"the first number to multiply\"),\n", "    b: z.number().describe(\"the second number to multiply\"),\n", "  }),\n", "  func: async ({ a, b }: { a: number; b: number; }) => {\n", "    return (a * b).toString();\n", "  },\n", "});\n", "\n", "await multiplyTool.invoke({ a: 8, b: 9, });"]}, {"cell_type": "markdown", "id": "c7326b23", "metadata": {}, "source": ["## `DynamicTool`\n", "\n", "For older agents that require tools which accept only a single input, you can pass the relevant parameters to the [`DynamicTool`](https://api.js.langchain.com/classes/langchain_core.tools.DynamicTool.html) class. This is useful when working with older agents that only support tools that accept a single input. In this case, no schema is required:"]}, {"cell_type": "code", "execution_count": 3, "id": "b0ce7de8", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"<PERSON><PERSON><PERSON><PERSON>\"\u001b[39m"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import { DynamicTool } from \"@langchain/core/tools\";\n", "\n", "const searchTool = new DynamicTool({\n", "  name: \"search\",\n", "  description: \"look things up online\",\n", "  func: async (_input: string) => {\n", "    return \"<PERSON><PERSON><PERSON><PERSON>\";\n", "  },\n", "});\n", "\n", "await searchTool.invoke(\"foo\");"]}, {"cell_type": "markdown", "id": "d4093dea", "metadata": {}, "source": ["# Returning artifacts of Tool execution\n", "\n", "Sometimes there are artifacts of a tool's execution that we want to make accessible to downstream components in our chain or agent, but that we don't want to expose to the model itself. For example if a tool returns custom objects like Documents, we may want to pass some view or metadata about this output to the model without passing the raw output to the model. At the same time, we may want to be able to access this full output elsewhere, for example in downstream tools.\n", "\n", "The Tool and `ToolMessage` interfaces make it possible to distinguish between the parts of the tool output meant for the model (`ToolMessage.content`) and those parts which are meant for use outside the model (`ToolMessage.artifact`).\n", "\n", "```{=mdx}\n", ":::caution Compatibility\n", "This functionality was added in `@langchain/core>=0.2.16`. Please make sure your package is up to date.\n", ":::\n", "```\n", "\n", "If you want your tool to distinguish between message content and other artifacts, we need to do three things:\n", "\n", "- Set the `response_format` parameter to `\"content_and_artifact\"` when defining the tool.\n", "- Make sure that we return a tuple of `[content, artifact]`.\n", "- Call the tool with a a [`ToolCall`](https://api.js.langchain.com/types/langchain_core.messages_tool.ToolCall.html)    (like the ones generated by tool-calling models) rather than with the required schema directly.\n", "\n", "Here's an example of what this looks like. First, create a new tool:"]}, {"cell_type": "code", "execution_count": 4, "id": "ecf15c35", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { tool } from \"@langchain/core/tools\";\n", "\n", "const randomIntToolSchema = z.object({\n", "  min: z.number(),\n", "  max: z.number(),\n", "  size: z.number(),\n", "});\n", "\n", "const generateRandomInts = tool(async ({ min, max, size }) => {\n", "  const array: number[] = [];\n", "  for (let i = 0; i < size; i++) {\n", "    array.push(Math.floor(Math.random() * (max - min + 1)) + min);\n", "  }\n", "  return [\n", "    `Successfully generated array of ${size} random ints in [${min}, ${max}].`,\n", "    array,\n", "  ];\n", "}, {\n", "  name: \"generateRandomInts\",\n", "  description: \"Generate size random ints in the range [min, max].\",\n", "  schema: randomIntToolSchema,\n", "  responseFormat: \"content_and_artifact\",\n", "});"]}, {"cell_type": "markdown", "id": "5775e686", "metadata": {}, "source": ["If you invoke our tool directly with the tool arguments, you'll get back just the `content` part of the output:"]}, {"cell_type": "code", "execution_count": 5, "id": "ecbde6de", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"Successfully generated array of 10 random ints in [0, 9].\"\u001b[39m"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["await generateRandomInts.invoke({ min: 0, max: 9, size: 10 });"]}, {"cell_type": "markdown", "id": "6299ef60", "metadata": {}, "source": ["But if you invoke our tool with a `ToolCall`, you'll get back a ToolMessage that contains both the content and artifact generated by the `Tool`:"]}, {"cell_type": "code", "execution_count": 6, "id": "05209573", "metadata": {}, "outputs": [{"data": {"text/plain": ["ToolMessage {\n", "  lc_serializable: \u001b[33mtrue\u001b[39m,\n", "  lc_kwargs: {\n", "    content: \u001b[32m\"Successfully generated array of 10 random ints in [0, 9].\"\u001b[39m,\n", "    artifact: [\n", "      \u001b[33m7\u001b[39m, \u001b[33m7\u001b[39m, \u001b[33m1\u001b[39m, \u001b[33m4\u001b[39m, \u001b[33m8\u001b[39m,\n", "      \u001b[33m4\u001b[39m, \u001b[33m8\u001b[39m, \u001b[33m3\u001b[39m, \u001b[33m0\u001b[39m, \u001b[33m9\u001b[39m\n", "    ],\n", "    tool_call_id: \u001b[32m\"123\"\u001b[39m,\n", "    name: \u001b[32m\"generateRandomInts\"\u001b[39m,\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "  content: \u001b[32m\"Successfully generated array of 10 random ints in [0, 9].\"\u001b[39m,\n", "  name: \u001b[32m\"generateRandomInts\"\u001b[39m,\n", "  additional_kwargs: {},\n", "  response_metadata: {},\n", "  id: \u001b[90mundefined\u001b[39m,\n", "  tool_call_id: \u001b[32m\"123\"\u001b[39m,\n", "  artifact: [\n", "    \u001b[33m7\u001b[39m, \u001b[33m7\u001b[39m, \u001b[33m1\u001b[39m, \u001b[33m4\u001b[39m, \u001b[33m8\u001b[39m,\n", "    \u001b[33m4\u001b[39m, \u001b[33m8\u001b[39m, \u001b[33m3\u001b[39m, \u001b[33m0\u001b[39m, \u001b[33m9\u001b[39m\n", "  ]\n", "}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["await generateRandomInts.invoke({\n", "  name: \"generateRandomInts\",\n", "  args: { min: 0, max: 9, size: 10 },\n", "  id: \"123\", // required\n", "  type: \"tool_call\",\n", "});"]}, {"cell_type": "markdown", "id": "8eceaf09", "metadata": {}, "source": ["## Related\n", "\n", "You've now seen a few ways to create custom tools in LangChain.\n", "\n", "Next, you might be interested in learning [how to use a chat model to call tools](/docs/how_to/tool_calling/).\n", "\n", "You can also check out how to create your own [custom versions of other modules](/docs/how_to/#custom)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}, "vscode": {"interpreter": {"hash": "e90c8aa204a57276aa905271aff2d11799d0acb3547adabc5892e639a5e45e34"}}}, "nbformat": 4, "nbformat_minor": 5}