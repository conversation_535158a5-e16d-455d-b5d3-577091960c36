{"cells": [{"cell_type": "markdown", "id": "4f7e423b", "metadata": {}, "source": ["# How to select examples from a LangSmith dataset\n", "\n", "```{=mdx}\n", "\n", ":::tip Prerequisites\n", "\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Few-shot-prompting](/docs/concepts/few_shot_prompting)\n", "- [<PERSON><PERSON><PERSON>](/docs/concepts/#langsmith)\n", "\n", ":::\n", "\n", "\n", ":::note Compatibility\n", "\n", "- `langsmith` >= 0.1.43\n", "\n", ":::\n", "\n", "```\n", "\n", "LangSmith datasets have built-in support for similarity search, making them a great tool for building and querying few-shot examples.\n", "\n", "In this guide we'll see how to use an indexed LangSmith dataset as a few-shot example selector.\n", "\n", "## Setup\n", "\n", "Before getting started make sure you've [created a LangSmith account](https://smith.langchain.com/) and set your credentials:\n", "\n", "```typescript\n", "process.env.LANGSMITH_API_KEY=\"your-api-key\"\n", "process.env.LANGSMITH_TRACING=\"true\"\n", "```\n", "\n", "We'll need to install the `langsmith` SDK. In this example we'll also make use of `langchain` and `@langchain/anthropic`:\n", "\n", "```{=mdx}\n", "\n", "import Npm2Yarn from \"@theme/Npm2Yarn\"\n", "\n", "<Npm2Yarn>\n", "  langsmith langchain @langchain/anthropic @langchain/core zod zod-to-json-schema\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "fc716e12", "metadata": {}, "source": ["Now we'll clone a public dataset and turn on indexing for the dataset. We can also turn on indexing via the [LangSmith UI](https://docs.smith.langchain.com/how_to_guides/datasets/index_datasets_for_dynamic_few_shot_example_selection).\n", "\n", "We'll create a clone the [Multiverse math few shot example dataset](https://blog.langchain.dev/few-shot-prompting-to-improve-tool-calling-performance/).\n", "\n", "This enables searching over the dataset, and will make sure that anytime we update/add examples they are also indexed.\n", "\n", "The first step to creating a clone is to read the JSON file containing the examples and convert them to the format expected by Lang<PERSON>mith for creating examples:"]}, {"cell_type": "code", "execution_count": 1, "id": "2bcc86a0", "metadata": {}, "outputs": [], "source": ["import { Client as LangSmithClient } from 'langsmith';\n", "import { z } from 'zod';\n", "import { zodToJsonSchema } from 'zod-to-json-schema';\n", "import fs from \"fs/promises\";\n", "\n", "// Read the example dataset and convert to the format expected by the LangSmith API\n", "// for creating new examples\n", "const examplesJson = JSON.parse(\n", "  await fs.readFile(\"../../data/ls_few_shot_example_dataset.json\", \"utf-8\")\n", ");\n", "\n", "let inputs: Record<string, any>[] = [];\n", "let outputs: Record<string, any>[] = [];\n", "let metadata: Record<string, any>[] = [];\n", "\n", "examplesJson.forEach((ex) => {\n", "  inputs.push(ex.inputs);\n", "  outputs.push(ex.outputs);\n", "  metadata.push(ex.metadata);\n", "});\n", "\n", "// Define our input schema as this is required for indexing\n", "const inputsSchema = zodToJsonSchema(z.object({\n", "  input: z.string(),\n", "  system: z.boolean().optional(),\n", "}));\n", "\n", "const lsClient = new LangSmithClient();\n", "\n", "await lsClient.deleteDataset({ datasetName: \"multiverse-math-examples-for-few-shot-example\" })\n", "\n", "const dataset = await lsClient.createDataset(\"multiverse-math-examples-for-few-shot-example\", {\n", "  inputsSchema,\n", "});\n", "\n", "const createdExamples = await lsClient.createExamples({\n", "  inputs,\n", "  outputs,\n", "  metadata,\n", "  datasetId: dataset.id,\n", "})\n"]}, {"cell_type": "code", "execution_count": 2, "id": "01b5a8f3", "metadata": {}, "outputs": [], "source": ["await lsClient.indexDataset({ datasetId: dataset.id });"]}, {"cell_type": "markdown", "id": "5767d171", "metadata": {}, "source": ["Once the dataset is indexed, we can search for similar examples like so:"]}, {"cell_type": "code", "execution_count": 6, "id": "5013a56f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["const examples = await lsClient.similarExamples(\n", "  { input: \"whats the negation of the negation of the negation of 3\" },\n", "  dataset.id,\n", "  3,\n", ")\n", "console.log(examples.length)"]}, {"cell_type": "code", "execution_count": 7, "id": "a142db06", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["evaluate the negation of -100\n"]}], "source": ["console.log(examples[0].inputs.input)"]}, {"cell_type": "markdown", "id": "d2627125", "metadata": {}, "source": ["For this dataset the outputs are an entire chat history:"]}, {"cell_type": "code", "execution_count": 8, "id": "af5b9191", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    id: 'cbe7ed83-86e1-4e46-89de-6646f8b55cef',\n", "    type: 'system',\n", "    content: 'You are requested to solve math questions in an alternate mathematical universe. The operations have been altered to yield different results than expected. Do not guess the answer or rely on your  innate knowledge of math. Use the provided tools to answer the question. While associativity and commutativity apply, distributivity does not. Answer the question using the fewest possible tools. Only include the numeric response without any clarifications.',\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  {\n", "    id: '04946246-09a8-4465-be95-037efd7dae55',\n", "    type: 'human',\n", "    content: 'if one gazoink is 4 badoinks, each of which is 6 foos, each of wich is 3 bars - how many bars in 3 gazoinks?',\n", "    example: false,\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  {\n", "    id: 'run-d6f0954e-b21b-4ea8-ad98-0ee64cfc824e-0',\n", "    type: 'ai',\n", "    content: [ [Object] ],\n", "    example: false,\n", "    tool_calls: [ [Object] ],\n", "    usage_metadata: { input_tokens: 916, total_tokens: 984, output_tokens: 68 },\n", "    additional_kwargs: {},\n", "    response_metadata: {\n", "      id: 'msg_01MBWxgouUBzomwTvXhomGVq',\n", "      model: 'claude-3-sonnet-20240229',\n", "      usage: [Object],\n", "      stop_reason: 'tool_use',\n", "      stop_sequence: null\n", "    },\n", "    invalid_tool_calls: []\n", "  },\n", "  {\n", "    id: '3d4c72c4-f009-48ce-b739-1d3f28ee4803',\n", "    name: 'multiply',\n", "    type: 'tool',\n", "    content: '13.2',\n", "    tool_call_id: 'toolu_016RjRHSEyDZRqKhGrb8uvjJ',\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  {\n", "    id: 'run-26dd7e83-f5fb-4c70-8ba1-271300ffeb25-0',\n", "    type: 'ai',\n", "    content: [ [Object] ],\n", "    example: false,\n", "    tool_calls: [ [Object] ],\n", "    usage_metadata: { input_tokens: 999, total_tokens: 1070, output_tokens: 71 },\n", "    additional_kwargs: {},\n", "    response_metadata: {\n", "      id: 'msg_01VTFvtCxtR3rN58hCmjt2oH',\n", "      model: 'claude-3-sonnet-20240229',\n", "      usage: [Object],\n", "      stop_reason: 'tool_use',\n", "      stop_sequence: null\n", "    },\n", "    invalid_tool_calls: []\n", "  },\n", "  {\n", "    id: 'ca4e0317-7b3a-4638-933c-1efd98bc4fda',\n", "    name: 'multiply',\n", "    type: 'tool',\n", "    content: '87.12',\n", "    tool_call_id: 'toolu_01PqvszxiuXrVJ9bwgTWaH3q',\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  {\n", "    id: 'run-007794ac-3590-4b9e-b678-008f02e40042-0',\n", "    type: 'ai',\n", "    content: [ [Object] ],\n", "    example: false,\n", "    tool_calls: [ [Object] ],\n", "    usage_metadata: { input_tokens: 1084, total_tokens: 1155, output_tokens: 71 },\n", "    additional_kwargs: {},\n", "    response_metadata: {\n", "      id: 'msg_017BEkSqmTsmtJaTxAzfRMEh',\n", "      model: 'claude-3-sonnet-20240229',\n", "      usage: [Object],\n", "      stop_reason: 'tool_use',\n", "      stop_sequence: null\n", "    },\n", "    invalid_tool_calls: []\n", "  },\n", "  {\n", "    id: '7f58c121-6f21-4c7b-ba38-aa820e274ff8',\n", "    name: 'multiply',\n", "    type: 'tool',\n", "    content: '287.496',\n", "    tool_call_id: 'toolu_01LU3RqRUXZRLRoJ2AZNmPed',\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  {\n", "    id: 'run-51e35afb-7ec6-4738-93e2-92f80b5c9377-0',\n", "    type: 'ai',\n", "    content: '287.496',\n", "    example: false,\n", "    tool_calls: [],\n", "    usage_metadata: { input_tokens: 1169, total_tokens: 1176, output_tokens: 7 },\n", "    additional_kwargs: {},\n", "    response_metadata: {\n", "      id: 'msg_01Tx9kSNapSg8aUbWZXiS1NL',\n", "      model: 'claude-3-sonnet-20240229',\n", "      usage: [Object],\n", "      stop_reason: 'end_turn',\n", "      stop_sequence: null\n", "    },\n", "    invalid_tool_calls: []\n", "  }\n", "]\n"]}], "source": ["console.log(examples[1].outputs.output)"]}, {"cell_type": "markdown", "id": "e852c8ef", "metadata": {}, "source": ["The search returns the examples whose inputs are most similar to the query input. We can use this for few-shot prompting a model. The first step is to create a series of math tools we want to allow the model to call:"]}, {"cell_type": "code", "execution_count": 9, "id": "53e03aa1", "metadata": {}, "outputs": [], "source": ["import { tool } from '@langchain/core/tools';\n", "import { z } from 'zod';\n", "\n", "const add = tool((input) => {\n", "  return (input.a + input.b).toString();\n", "}, {\n", "  name: \"add\",\n", "  description: \"Add two numbers\",\n", "  schema: z.object({\n", "    a: z.number().describe(\"The first number to add\"),\n", "    b: z.number().describe(\"The second number to add\"),\n", "  }),\n", "});\n", "\n", "const cos = tool((input) => {\n", "  return Math.cos(input.angle).toString();\n", "}, {\n", "  name: \"cos\",\n", "  description: \"Calculate the cosine of an angle (in radians)\",\n", "  schema: z.object({\n", "    angle: z.number().describe(\"The angle in radians\"),\n", "  }),\n", "});\n", "\n", "const divide = tool((input) => {\n", "  return (input.a / input.b).toString();\n", "}, {\n", "  name: \"divide\",\n", "  description: \"Divide two numbers\",\n", "  schema: z.object({\n", "    a: z.number().describe(\"The dividend\"),\n", "    b: z.number().describe(\"The divisor\"),\n", "  }),\n", "});\n", "\n", "const log = tool((input) => {\n", "  return Math.log(input.value).toString();\n", "}, {\n", "  name: \"log\",\n", "  description: \"Calculate the natural logarithm of a number\",\n", "  schema: z.object({\n", "    value: z.number().describe(\"The number to calculate the logarithm of\"),\n", "  }),\n", "});\n", "\n", "const multiply = tool((input) => {\n", "  return (input.a * input.b).toString();\n", "}, {\n", "  name: \"multiply\",\n", "  description: \"Multiply two numbers\",\n", "  schema: z.object({\n", "    a: z.number().describe(\"The first number to multiply\"),\n", "    b: z.number().describe(\"The second number to multiply\"),\n", "  }),\n", "});\n", "\n", "const negate = tool((input) => {\n", "  return (-input.a).toString();\n", "}, {\n", "  name: \"negate\",\n", "  description: \"Negate a number\",\n", "  schema: z.object({\n", "    a: z.number().describe(\"The number to negate\"),\n", "  }),\n", "});\n", "\n", "const pi = tool(() => {\n", "  return Math.PI.toString();\n", "}, {\n", "  name: \"pi\",\n", "  description: \"Return the value of pi\",\n", "  schema: z.object({}),\n", "});\n", "\n", "const power = tool((input) => {\n", "  return Math.pow(input.base, input.exponent).toString();\n", "}, {\n", "  name: \"power\",\n", "  description: \"Raise a number to a power\",\n", "  schema: z.object({\n", "    base: z.number().describe(\"The base number\"),\n", "    exponent: z.number().describe(\"The exponent\"),\n", "  }),\n", "});\n", "\n", "const sin = tool((input) => {\n", "  return Math.sin(input.angle).toString();\n", "}, {\n", "  name: \"sin\",\n", "  description: \"Calculate the sine of an angle (in radians)\",\n", "  schema: z.object({\n", "    angle: z.number().describe(\"The angle in radians\"),\n", "  }),\n", "});\n", "\n", "const subtract = tool((input) => {\n", "  return (input.a - input.b).toString();\n", "}, {\n", "  name: \"subtract\",\n", "  description: \"Subtract two numbers\",\n", "  schema: z.object({\n", "    a: z.number().describe(\"The number to subtract from\"),\n", "    b: z.number().describe(\"The number to subtract\"),\n", "  }),\n", "});"]}, {"cell_type": "code", "execution_count": 17, "id": "12cba1e1", "metadata": {}, "outputs": [], "source": ["import { ChatOpenAI } from \"@langchain/openai\";\n", "import { HumanMessage, SystemMessage, BaseMessage, BaseMessageLike } from \"@langchain/core/messages\";\n", "import { RunnableLambda } from \"@langchain/core/runnables\";\n", "import { Client as LangSmithClient, Example } from \"langsmith\";\n", "import { coerceMessageLikeToMessage } from \"@langchain/core/messages\";\n", "\n", "const client = new LangSmithClient();\n", "\n", "async function similarExamples(input: Record<string, any>): Promise<Record<string, any>> {\n", "  const examples = await client.similarExamples(input, dataset.id, 5);\n", "  return { ...input, examples };\n", "}\n", "\n", "function constructPrompt(input: { examples: Example[], input: string }): BaseMessage[] {\n", "  const instructions = \"You are great at using mathematical tools.\";\n", "  let messages: BaseMessage[] = []\n", "  \n", "  for (const ex of input.examples) {\n", "    // Assuming ex.outputs.output is an array of message-like objects\n", "    messages = messages.concat(ex.outputs.output.flatMap((msg: BaseMessageLike) => coerceMessageLikeToMessage(msg)));\n", "  }\n", "  \n", "  const examples = messages.filter(msg => msg._getType() !== 'system');\n", "  examples.forEach((ex) => {\n", "    if (ex._getType() === 'human') {\n", "      ex.name = \"example_user\";\n", "    } else {\n", "      ex.name = \"example_assistant\";\n", "    }\n", "  });\n", "\n", "  return [new SystemMessage(instructions), ...examples, new HumanMessage(input.input)];\n", "}\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o\",\n", "  temperature: 0,\n", "});\n", "const tools = [add, cos, divide, log, multiply, negate, pi, power, sin, subtract];\n", "const llmWithTools = llm.bindTools(tools);\n", "\n", "const exampleSelector = new RunnableLambda(\n", "  { func: similarExamples }\n", ").withConfig({ runName: \"similarExamples\" });\n", "\n", "const chain = exampleSelector.pipe(\n", "  new RunnableLambda({\n", "    func: constructPrompt\n", "  }).withConfig({\n", "    runName: \"constructPrompt\"\n", "  })\n", ").pipe(llmWithTools);"]}, {"cell_type": "code", "execution_count": 16, "id": "c423b367", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'negate',\n", "    args: { a: 3 },\n", "    type: 'tool_call',\n", "    id: 'call_SX0dmb4AbFu39KkGQDqPXQwa'\n", "  }\n", "]\n"]}], "source": ["const aiMsg = await chain.invoke({ input: \"whats the negation of the negation of 3\", system: false })\n", "console.log(aiMsg.tool_calls)"]}, {"cell_type": "markdown", "id": "94489b4a", "metadata": {}, "source": ["Looking at the <PERSON><PERSON><PERSON> trace, we can see that relevant examples were pulled in in the `similarExamples` step and passed as messages to ChatOpenAI: https://smith.langchain.com/public/20e09618-0746-4973-9382-5b36c3f27083/r."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}