{"cells": [{"cell_type": "markdown", "id": "70403d4f-50c1-43f8-a7ea-a211167649a5", "metadata": {}, "source": ["# How to use reference examples\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following:\n", "\n", "- [Extraction](/docs/tutorials/extraction)\n", "\n", ":::\n", "\n", "The quality of extraction can often be improved by providing reference examples to the LLM.\n", "\n", ":::{.callout-tip}\n", "While this tutorial focuses how to use examples with a tool calling model, this technique is generally applicable, and will work\n", "also with JSON more or prompt based techniques.\n", ":::\n", "\n", "We'll use OpenAI's GPT-4 this time for their robust support for `ToolMessages`:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/openai @langchain/core zod uuid\n", "</Npm2Yarn>\n", "```\n", "\n", "Let's define a prompt:"]}, {"cell_type": "code", "execution_count": 1, "id": "89579144-bcb3-490a-8036-86a0a6bcd56b", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate, MessagesPlaceholder } from \"@langchain/core/prompts\";\n", "\n", "const SYSTEM_PROMPT_TEMPLATE = `You are an expert extraction algorithm.\n", "Only extract relevant information from the text.\n", "If you do not know the value of an attribute asked to extract, you may omit the attribute's value.`;\n", "\n", "// Define a custom prompt to provide instructions and any additional context.\n", "// 1) You can add examples into the prompt template to improve extraction quality\n", "// 2) Introduce additional parameters to take context into account (e.g., include metadata\n", "//    about the document from which the text was extracted.)\n", "const prompt = ChatPromptTemplate.fromMessages([\n", "  [\"system\", SYSTEM_PROMPT_TEMPLATE],\n", "  // ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓\n", "  new MessagesPlaceholder(\"examples\"),\n", "  // ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n", "  [\"human\", \"{text}\"]\n", "]);"]}, {"cell_type": "markdown", "id": "2484008c-ba1a-42a5-87a1-628a900de7fd", "metadata": {}, "source": ["Test out the template:"]}, {"cell_type": "code", "execution_count": 2, "id": "610c3025-ea63-4cd7-88bd-c8cbcb4d8a3f", "metadata": {}, "outputs": [{"data": {"text/plain": ["[\n", "  SystemMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: {\n", "      content: \u001b[32m\"You are an expert extraction algorithm.\\n\"\u001b[39m +\n", "        \u001b[32m\"Only extract relevant information from the text.\\n\"\u001b[39m +\n", "        \u001b[32m\"If you do n\"\u001b[39m... 87 more characters,\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"You are an expert extraction algorithm.\\n\"\u001b[39m +\n", "      \u001b[32m\"Only extract relevant information from the text.\\n\"\u001b[39m +\n", "      \u001b[32m\"If you do n\"\u001b[39m... 87 more characters,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {}\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: { content: \u001b[32m\"testing 1 2 3\"\u001b[39m, additional_kwargs: {} },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"testing 1 2 3\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {}\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: { content: \u001b[32m\"this is some text\"\u001b[39m, additional_kwargs: {} },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"this is some text\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {}\n", "  }\n", "]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import { HumanMessage } from \"@langchain/core/messages\";\n", "\n", "const promptValue = await prompt.invoke({\n", "  text: \"this is some text\",\n", "  examples: [new HumanMessage(\"testing 1 2 3\")],\n", "});\n", "\n", "promptValue.toChatMessages();"]}, {"cell_type": "markdown", "id": "368abd80-0cf0-41a7-8224-acf90dd6830d", "metadata": {}, "source": ["## Define the schema\n", "\n", "Let's re-use the people schema from the quickstart."]}, {"cell_type": "code", "execution_count": 3, "id": "d875a49a-d2cb-4b9e-b5bf-41073bc3905c", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "\n", "const personSchema = z.object({\n", "  name: z.optional(z.string()).describe(\"The name of the person\"),\n", "  hair_color: z.optional(z.string()).describe(\"The color of the person's hair, if known\"),\n", "  height_in_meters: z.optional(z.string()).describe(\"Height measured in meters\")\n", "}).describe(\"Information about a person.\");\n", "\n", "const peopleSchema = z.object({\n", "  people: z.array(personSchema)\n", "});"]}, {"cell_type": "markdown", "id": "96c42162-e4f6-4461-88fd-c76f5aab7e32", "metadata": {}, "source": ["## Define reference examples\n", "\n", "Examples can be defined as a list of input-output pairs. \n", "\n", "Each example contains an example `input` text and an example `output` showing what should be extracted from the text.\n", "\n", ":::{.callout-important}\n", "The below example is a bit more advanced - the format of the example needs to match the API used (e.g., tool calling or JSON mode etc.).\n", "\n", "Here, the formatted examples will match the format expected for the OpenAI tool calling API since that's what we're using.\n", ":::\n", "\n", "To provide reference examples to the model, we will mock out a fake chat history containing successful usages of the given tool.\n", "Because the model can choose to call multiple tools at once (or the same tool multiple times), the example's outputs are an array:"]}, {"cell_type": "code", "execution_count": 4, "id": "08356810-77ce-4e68-99d9-faa0326f2cee", "metadata": {}, "outputs": [], "source": ["import {\n", "  AIMessage,\n", "  type BaseMessage,\n", "  HumanMessage,\n", "  ToolMessage\n", "} from \"@langchain/core/messages\";\n", "import { v4 as uuid } from \"uuid\";\n", "\n", "type OpenAIToolCall = {\n", "  id: string,\n", "  type: \"function\",\n", "  function: {\n", "    name: string;\n", "    arguments: string;\n", "  }\n", "};\n", "\n", "type Example = {\n", "  input: string;\n", "  toolCallOutputs: Record<string, any>[];\n", "}\n", "\n", "/**\n", " * This function converts an example into a list of messages that can be fed into an LLM.\n", " *\n", " * This code serves as an adapter that transforms our example into a list of messages\n", " * that can be processed by a chat model.\n", " *\n", " * The list of messages for each example includes:\n", " *\n", " * 1) HumanMessage: This contains the content from which information should be extracted.\n", " * 2) AIMessage: This contains the information extracted by the model.\n", " * 3) ToolMessage: This provides confirmation to the model that the tool was requested correctly.\n", " *\n", " * The inclusion of ToolMessage is necessary because some chat models are highly optimized for agents,\n", " * making them less suitable for an extraction use case.\n", " */\n", "function toolExampleToMessages(example: Example): BaseMessage[] {\n", "  const openAIToolCalls: OpenAIToolCall[] = example.toolCallOutputs.map((output) => {\n", "    return {\n", "      id: uuid(),\n", "      type: \"function\",\n", "      function: {\n", "        // The name of the function right now corresponds\n", "        // to the passed name.\n", "        name: \"extract\",\n", "        arguments: JSON.stringify(output),\n", "      },\n", "    };\n", "  });\n", "  const messages: BaseMessage[] = [\n", "    new HumanMessage(example.input),\n", "    new AIMessage({\n", "      content: \"\",\n", "      additional_kwargs: { tool_calls: openAIToolCalls }\n", "    })\n", "  ];\n", "  const toolMessages = openAIToolCalls.map((toolCall, i) => {\n", "    // Return the mocked successful result for a given tool call.\n", "    return new ToolMessage({\n", "      content: \"You have correctly called this tool.\",\n", "      tool_call_id: toolCall.id\n", "    });\n", "  });\n", "  return messages.concat(toolMessages);\n", "}\n"]}, {"cell_type": "markdown", "id": "463aa282-51c4-42bf-9463-6ca3b2c08de6", "metadata": {}, "source": ["Next let's define our examples and then convert them into message format."]}, {"cell_type": "code", "execution_count": 5, "id": "7f59a745-5c81-4011-a4c5-a33ec1eca7ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[33m6\u001b[39m"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["const examples: Example[] = [\n", "  {\n", "    input: \"The ocean is vast and blue. It's more than 20,000 feet deep. There are many fish in it.\",\n", "    toolCallOutputs: [{}]\n", "  },\n", "  {\n", "    input: \"<PERSON> traveled far from France to Spain.\",\n", "    toolCallOutputs: [{\n", "      name: \"<PERSON>\",\n", "    }]\n", "  }\n", "];\n", "\n", "const exampleMessages = [];\n", "for (const example of examples) {\n", "  exampleMessages.push(...toolExampleToMessages(example));\n", "}"]}, {"cell_type": "markdown", "id": "6fdbda30-e7e3-46b5-a54a-1769c580af93", "metadata": {}, "source": ["Let's test out the prompt"]}, {"cell_type": "code", "execution_count": 6, "id": "e61fa3a5-3d15-46a2-a23b-788f9a3ede52", "metadata": {}, "outputs": [{"data": {"text/plain": ["[\n", "  SystemMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: {\n", "      content: \u001b[32m\"You are an expert extraction algorithm.\\n\"\u001b[39m +\n", "        \u001b[32m\"Only extract relevant information from the text.\\n\"\u001b[39m +\n", "        \u001b[32m\"If you do n\"\u001b[39m... 87 more characters,\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"You are an expert extraction algorithm.\\n\"\u001b[39m +\n", "      \u001b[32m\"Only extract relevant information from the text.\\n\"\u001b[39m +\n", "      \u001b[32m\"If you do n\"\u001b[39m... 87 more characters,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {}\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: {\n", "      content: \u001b[32m\"The ocean is vast and blue. It's more than 20,000 feet deep. There are many fish in it.\"\u001b[39m,\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"The ocean is vast and blue. It's more than 20,000 feet deep. There are many fish in it.\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {}\n", "  },\n", "  AIMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: { content: \u001b[32m\"\"\u001b[39m, additional_kwargs: { tool_calls: [ \u001b[36m[Object]\u001b[39m ] } },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {\n", "      tool_calls: [\n", "        {\n", "          id: \u001b[32m\"8fa4d00d-801f-470e-8737-51ee9dc82259\"\u001b[39m,\n", "          type: \u001b[32m\"function\"\u001b[39m,\n", "          function: \u001b[36m[Object]\u001b[39m\n", "        }\n", "      ]\n", "    }\n", "  },\n", "  ToolMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: {\n", "      content: \u001b[32m\"You have correctly called this tool.\"\u001b[39m,\n", "      tool_call_id: \u001b[32m\"8fa4d00d-801f-470e-8737-51ee9dc82259\"\u001b[39m,\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"You have correctly called this tool.\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {},\n", "    tool_call_id: \u001b[32m\"8fa4d00d-801f-470e-8737-51ee9dc82259\"\u001b[39m\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: {\n", "      content: \u001b[32m\"<PERSON> traveled far from France to Spain.\"\u001b[39m,\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"<PERSON> traveled far from France to Spain.\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {}\n", "  },\n", "  AIMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: { content: \u001b[32m\"\"\u001b[39m, additional_kwargs: { tool_calls: [ \u001b[36m[Object]\u001b[39m ] } },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {\n", "      tool_calls: [\n", "        {\n", "          id: \u001b[32m\"14ad6217-fcbd-47c7-9006-82f612e36c66\"\u001b[39m,\n", "          type: \u001b[32m\"function\"\u001b[39m,\n", "          function: \u001b[36m[Object]\u001b[39m\n", "        }\n", "      ]\n", "    }\n", "  },\n", "  ToolMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: {\n", "      content: \u001b[32m\"You have correctly called this tool.\"\u001b[39m,\n", "      tool_call_id: \u001b[32m\"14ad6217-fcbd-47c7-9006-82f612e36c66\"\u001b[39m,\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"You have correctly called this tool.\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {},\n", "    tool_call_id: \u001b[32m\"14ad6217-fcbd-47c7-9006-82f612e36c66\"\u001b[39m\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: { content: \u001b[32m\"this is some text\"\u001b[39m, additional_kwargs: {} },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: \u001b[32m\"this is some text\"\u001b[39m,\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {}\n", "  }\n", "]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["const promptValueWithExamples = await prompt.invoke({\n", "  text: \"this is some text\",\n", "  examples: exampleMessages\n", "});\n", "\n", "promptValueWithExamples.toChatMessages();"]}, {"cell_type": "markdown", "id": "47b0bbef-bc6b-4535-a8e2-5c84f09d5637", "metadata": {}, "source": ["## Create an extractor\n", "Here, we'll create an extractor using **gpt-4**."]}, {"cell_type": "code", "execution_count": 7, "id": "dbfea43d-769b-42e9-a76f-ce722f7d6f93", "metadata": {}, "outputs": [], "source": ["import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "// We will be using tool calling mode, which\n", "// requires a tool calling capable model.\n", "const llm = new ChatOpenAI({\n", "  // Consider benchmarking with the best model you can to get\n", "  // a sense of the best possible quality.\n", "  model: \"gpt-4-0125-preview\",\n", "  temperature: 0,\n", "});\n", "\n", "// For function/tool calling, we can also supply an name for the schema\n", "// to give the LLM additional context about what it's extracting.\n", "const extractionRunnable = prompt.pipe(llm.withStructuredOutput(peopleSchema, { name: \"people\" }));"]}, {"cell_type": "markdown", "id": "58a8139e-f201-4b8e-baf0-16a83e5fa987", "metadata": {}, "source": ["## Without examples 😿\n", "\n", "Notice that even though we're using `gpt-4`, it's unreliable with a **very simple** test case!\n", "\n", "We run it 5 times below to emphasize this:"]}, {"cell_type": "code", "execution_count": 8, "id": "8b1d6273-5ec5-4970-af8a-0da1f1efa293", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  people: [ { name: \"earth\", hair_color: \"grey\", height_in_meters: \"1\" } ]\n", "}\n", "{ people: [ { name: \"earth\", hair_color: \"moon\" } ] }\n", "{ people: [ { name: \"earth\", hair_color: \"moon\" } ] }\n", "{ people: [ { name: \"earth\", hair_color: \"1 moon\" } ] }\n", "{ people: [] }\n"]}], "source": ["const text = \"The solar system is large, but earth has only 1 moon.\";\n", "\n", "for (let i = 0; i < 5; i++) {\n", "  const result = await extractionRunnable.invoke({\n", "    text,\n", "    examples: []\n", "  });\n", "  console.log(result);\n", "}"]}, {"cell_type": "markdown", "id": "09840f17-ab26-4ea2-8a39-c747103804ec", "metadata": {}, "source": ["## With examples 😻\n", "\n", "Reference examples help fix the failure!"]}, {"cell_type": "code", "execution_count": 9, "id": "9bdfa49e-0005-4c06-9598-2adfd882b014", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ people: [] }\n", "{ people: [] }\n", "{ people: [] }\n", "{ people: [] }\n", "{ people: [] }\n"]}], "source": ["for (let i = 0; i < 5; i++) {\n", "  const result = await extractionRunnable.invoke({\n", "    text,\n", "    // Example messages from above\n", "    examples: exampleMessages\n", "  });\n", "  console.log(result);\n", "}"]}, {"cell_type": "code", "execution_count": 10, "id": "84413e17-608d-4f85-b70e-00b89b271927", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  people: [ { name: \u001b[32m\"Hair-ison\"\u001b[39m, hair_color: \u001b[32m\"black\"\u001b[39m, height_in_meters: \u001b[32m\"3\"\u001b[39m } ]\n", "}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["await extractionRunnable.invoke({\n", "  text: \"My name is <PERSON><PERSON><PERSON><PERSON>. My hair is black. I am 3 meters tall.\",\n", "  examples: exampleMessages,\n", "});"]}, {"cell_type": "markdown", "id": "3e624af2", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now learned how to improve extraction quality using few-shot examples.\n", "\n", "Next, check out some of the other guides in this section, such as [some tips on how to perform extraction on long text](/docs/how_to/extraction_long_text)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}