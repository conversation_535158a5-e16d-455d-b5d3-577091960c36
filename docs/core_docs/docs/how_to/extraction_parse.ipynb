{"cells": [{"cell_type": "markdown", "id": "ea37db49-d389-4291-be73-885d06c1fb7e", "metadata": {}, "source": ["# How to do extraction without using function calling\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following:\n", "\n", "- [Extraction](/docs/tutorials/extraction)\n", "\n", ":::\n", "\n", "LLMs that are able to follow prompt instructions well can be tasked with outputting information in a given format without using function calling.\n", "\n", "This approach relies on designing good prompts and then parsing the output of the LLMs to make them extract information well, though it lacks some of the guarantees provided by function calling or JSON mode.\n", "\n", "Here, we'll use <PERSON> which is great at following instructions! See [here for more about Anthropic models](/docs/integrations/chat/anthropic).\n", "\n", "First, we'll install the integration package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/anthropic @langchain/core zod zod-to-json-schema\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "d71b32de-a6b4-45ed-83a9-ba1925f9470c", "metadata": {}, "outputs": [], "source": ["import { ChatAnthropic } from \"@langchain/anthropic\";\n", "\n", "const model = new ChatAnthropic({\n", "  model: \"claude-3-sonnet-20240229\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "markdown", "id": "3e412374-3beb-4bbf-966b-400c1f66a258", "metadata": {}, "source": [":::{.callout-tip}\n", "All the same considerations for extraction quality apply for parsing approach.\n", "\n", "This tutorial is meant to be simple, but generally should really include reference examples to squeeze out performance!\n", ":::"]}, {"cell_type": "markdown", "id": "abc1a945-0f80-4953-add4-cd572b6f2a51", "metadata": {}, "source": ["## Using StructuredOutputParser\n", "\n", "The following example uses the built-in [`StructuredOutputParser`](/docs/how_to/output_parser_structured/) to parse the output of a chat model. We use the built-in prompt formatting instructions contained in the parser."]}, {"cell_type": "code", "execution_count": 2, "id": "497eb023-c043-443d-ac62-2d4ea85fe1b0", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { StructuredOutputParser } from \"langchain/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "let personSchema = z.object({\n", "  name: z.optional(z.string()).describe(\"The name of the person\"),\n", "  hair_color: z.optional(z.string()).describe(\"The color of the person's hair, if known\"),\n", "  height_in_meters: z.optional(z.string()).describe(\"Height measured in meters\")\n", "}).describe(\"Information about a person.\");\n", "\n", "const parser = StructuredOutputParser.fromZodSchema(personSchema);\n", "\n", "const prompt = ChatPromptTemplate.fromMessages([\n", "  [\"system\", \"Answer the user query. Wrap the output in `json` tags\\n{format_instructions}\"],\n", "  [\"human\", \"{query}\"],\n", "]);\n", "\n", "const partialedPrompt = await prompt.partial({\n", "  format_instructions: parser.getFormatInstructions(),\n", "});"]}, {"cell_type": "markdown", "id": "c31aa2c8-05a9-4a12-80c5-ea1250dea0ae", "metadata": {}, "source": ["Let's take a look at what information is sent to the model"]}, {"cell_type": "code", "execution_count": 3, "id": "20b99ffb-a114-49a9-a7be-154c525f8ada", "metadata": {}, "outputs": [], "source": ["const query = \"<PERSON> is 23 years old and she is 6 feet tall\";"]}, {"cell_type": "code", "execution_count": 4, "id": "4f3a66ce-de19-4571-9e54-67504ae3fba7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"Answer the user query. Wrap the output in `json` tags\\n\" +\n", "        \"You must format your output as a JSON value th\"... 1444 more characters,\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"Answer the user query. Wrap the output in `json` tags\\n\" +\n", "      \"You must format your output as a JSON value th\"... 1444 more characters,\n", "    name: undefined,\n", "    additional_kwargs: {}\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"<PERSON> is 23 years old and she is 6 feet tall\",\n", "      additional_kwargs: {}\n", "    },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"<PERSON> is 23 years old and she is 6 feet tall\",\n", "    name: undefined,\n", "    additional_kwargs: {}\n", "  }\n", "]\n"]}], "source": ["const promptValue = await partialedPrompt.invoke({ query });\n", "\n", "console.log(promptValue.toChatMessages());"]}, {"cell_type": "code", "execution_count": 5, "id": "3a46b5fd-9242-4b8c-a4e2-3f04fc19b3a4", "metadata": {}, "outputs": [{"data": {"text/plain": ["{ name: \u001b[32m\"<PERSON>\"\u001b[39m, hair_color: \u001b[32m\"\"\u001b[39m, height_in_meters: \u001b[32m\"1.83\"\u001b[39m }"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["const chain = partialedPrompt.pipe(model).pipe(parser);\n", "\n", "await chain.invoke({ query });"]}, {"cell_type": "markdown", "id": "815b3b87-3bc6-4b56-835e-c6b6703cef5d", "metadata": {}, "source": ["## Custom Parsing\n", "\n", "You can also create a custom prompt and parser with `<PERSON><PERSON><PERSON><PERSON>` and `LCEL`.\n", "\n", "You can use a raw function to parse the output from the model.\n", "\n", "In the below example, we'll pass the schema into the prompt as JSON schema. For convenience, we'll declare our schema with Zod, then use the [`zod-to-json-schema`](https://github.com/StefanTerdell/zod-to-json-schema) utility to convert it to JSON schema."]}, {"cell_type": "code", "execution_count": 6, "id": "b1f11912-c1bb-4a2a-a482-79bf3996961f", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { zodToJsonSchema } from \"zod-to-json-schema\";\n", "\n", "personSchema = z.object({\n", "  name: z.optional(z.string()).describe(\"The name of the person\"),\n", "  hair_color: z.optional(z.string()).describe(\"The color of the person's hair, if known\"),\n", "  height_in_meters: z.optional(z.string()).describe(\"Height measured in meters\")\n", "}).describe(\"Information about a person.\");\n", "\n", "const peopleSchema = z.object({\n", "  people: z.<PERSON>(personSchema),\n", "});\n", "\n", "const SYSTEM_PROMPT_TEMPLATE = [\n", "  \"Answer the user's query. You must return your answer as JSON that matches the given schema:\",\n", "  \"```json\\n{schema}\\n```.\",\n", "  \"Make sure to wrap the answer in ```json and ``` tags. Conform to the given schema exactly.\",\n", "].join(\"\\n\");\n", "\n", "const customParsingPrompt = ChatPromptTemplate.fromMessages([\n", "  [\"system\", SYSTEM_PROMPT_TEMPLATE],\n", "  [\"human\", \"{query}\"],\n", "]);\n", "\n", "const extractJsonFromOutput = (message) => {\n", "  const text = message.content;\n", "\n", "  // Define the regular expression pattern to match JSON blocks\n", "  const pattern = /```json\\s*((.|\\n)*?)\\s*```/gs;\n", "\n", "  // Find all non-overlapping matches of the pattern in the string\n", "  const matches = pattern.exec(text);\n", "\n", "  if (matches && matches[1]) {\n", "    try {\n", "      return JSON.parse(matches[1].trim());\n", "    } catch (error) {\n", "      throw new Error(`Failed to parse: ${matches[1]}`);\n", "    }\n", "  } else {\n", "    throw new Error(`No JSON found in: ${message}`);\n", "  }\n", "}"]}, {"cell_type": "code", "execution_count": 7, "id": "cda52ef5-a354-47a7-9c25-45153c2389e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"System: Answer the user's query. You must return your answer as JSON that matches the given schema:\\n\"\u001b[39m... 170 more characters"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["const customParsing<PERSON><PERSON><PERSON> = \"<PERSON> is 23 years old and she is 6 feet tall\";\n", "\n", "const customParsingPromptValue = await customParsingPrompt.invoke({\n", "  schema: zodToJsonSchema(peopleSchema),\n", "  customParsingQuery\n", "});\n", "\n", "customParsingPromptValue.toString();"]}, {"cell_type": "code", "execution_count": 8, "id": "993dc61a-229d-4795-a746-0d17df86b5c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["{ name: \u001b[32m\"<PERSON>\"\u001b[39m, age: \u001b[33m23\u001b[39m, height: { feet: \u001b[33m6\u001b[39m, inches: \u001b[33m0\u001b[39m } }"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["const customParsingChain = prompt.pipe(model).pipe(extractJsonFromOutput);\n", "\n", "await customParsingChain.invoke({\n", "  schema: zodToJsonSchema(peopleSchema),\n", "  customParsingQuery,\n", "});"]}, {"cell_type": "markdown", "id": "3df13ddc", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now learned how to perform extraction without using tool calling.\n", "\n", "Next, check out some of the other guides in this section, such as [some tips on how to improve extraction quality with examples](/docs/how_to/extraction_examples)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}