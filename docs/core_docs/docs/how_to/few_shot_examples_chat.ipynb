{"cells": [{"cell_type": "raw", "id": "beba2e0e", "metadata": {}, "source": ["---\n", "sidebar_position: 2\n", "---"]}, {"cell_type": "markdown", "id": "bb0735c0", "metadata": {}, "source": ["# How to use few shot examples in chat models\n", "\n", "This guide covers how to prompt a chat model with example inputs and outputs. Providing the model with a few such examples is called few-shotting, and is a simple yet powerful way to guide generation and in some cases drastically improve model performance.\n", "\n", "There does not appear to be solid consensus on how best to do few-shot prompting, and the optimal prompt compilation will likely vary by model. Because of this, we provide few-shot prompt templates like the [FewShotChatMessagePromptTemplate](https://api.js.langchain.com/classes/langchain_core.prompts.FewShotChatMessagePromptTemplate.html) as a flexible starting point, and you can modify or replace them as you see fit.\n", "\n", "The goal of few-shot prompt templates are to dynamically select examples based on an input, and then format the examples in a final prompt to provide for the model.\n", "\n", "**Note:** The following code examples are for chat models only, since `FewShotChatMessagePromptTemplates` are designed to output formatted [chat messages](/docs/concepts/messages) rather than pure strings. For similar few-shot prompt examples for pure string templates compatible with completion models (LLMs), see the [few-shot prompt templates](/docs/how_to/few_shot_examples/) guide.\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "- [Example selectors](/docs/concepts/example_selectors)\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Vectorstores](/docs/concepts/#vectorstores)\n", "\n", ":::"]}, {"cell_type": "markdown", "id": "d716f2de-cc29-4823-9360-a808c7bfdb86", "metadata": {"tags": []}, "source": ["## Fixed Examples\n", "\n", "The most basic (and common) few-shot prompting technique is to use fixed prompt examples. This way you can select a chain, evaluate it, and avoid worrying about additional moving parts in production.\n", "\n", "The basic components of the template are:\n", "- `examples`: An array of object examples to include in the final prompt.\n", "- `examplePrompt`: converts each example into 1 or more messages through its [`formatMessages`](https://api.js.langchain.com/classes/langchain_core.prompts.FewShotChatMessagePromptTemplate.html#formatMessages) method. A common example would be to convert each example into one human message and one AI message response, or a human message followed by a function call message.\n", "\n", "Below is a simple demonstration. First, define the examples you'd like to include:"]}, {"cell_type": "code", "execution_count": 4, "id": "0fc5a02a-6249-4e92-95c3-30fff9671e8b", "metadata": {"tags": []}, "outputs": [], "source": ["import {\n", "    ChatPromptTemplate,\n", "    FewShotChatMessagePromptTemplate,\n", "} from \"@langchain/core/prompts\"\n", "\n", "const examples = [\n", "    { input: \"2+2\", output: \"4\" },\n", "    { input: \"2+3\", output: \"5\" },\n", "]"]}, {"cell_type": "markdown", "id": "e8710ecc-2aa0-4172-a74c-250f6bc3d9e2", "metadata": {}, "source": ["Next, assemble them into the few-shot prompt template."]}, {"cell_type": "code", "execution_count": 7, "id": "65e72ad1-9060-47d0-91a1-bc130c8b98ac", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: { content: \"2+2\", additional_kwargs: {}, response_metadata: {} },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"2+2\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"4\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"4\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    tool_calls: [],\n", "    invalid_tool_calls: []\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: { content: \"2+3\", additional_kwargs: {}, response_metadata: {} },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"2+3\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"5\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"5\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    tool_calls: [],\n", "    invalid_tool_calls: []\n", "  }\n", "]\n"]}], "source": ["// This is a prompt template used to format each individual example.\n", "const examplePrompt = ChatPromptTemplate.fromMessages(\n", "    [\n", "        [\"human\", \"{input}\"],\n", "        [\"ai\", \"{output}\"],\n", "    ]\n", ")\n", "const fewShotPrompt = new FewShotChatMessagePromptTemplate({\n", "    examplePrompt,\n", "    examples,\n", "    inputVariables: [], // no input variables\n", "})\n", "\n", "const result = await fewShotPrompt.invoke({});\n", "console.log(result.toChatMessages())"]}, {"cell_type": "markdown", "id": "5490bd59-b28f-46a4-bbdf-0191802dd3c5", "metadata": {}, "source": ["Finally, we assemble the final prompt as shown below, passing `fewShotPrompt` directly into the `fromMessages` factory method, and use it with a model:"]}, {"cell_type": "code", "execution_count": 8, "id": "9f86d6d9-50de-41b6-b6c7-0f9980cc0187", "metadata": {"tags": []}, "outputs": [], "source": ["const finalPrompt = ChatPromptTemplate.fromMessages(\n", "    [\n", "        [\"system\", \"You are a wondrous wizard of math.\"],\n", "        fewShot<PERSON>rompt,\n", "        [\"human\", \"{input}\"],\n", "    ]\n", ")"]}, {"cell_type": "markdown", "id": "c74c6026", "metadata": {}, "source": ["```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs />\n", "```"]}, {"cell_type": "code", "execution_count": 9, "id": "97d443b1-6fae-4b36-bede-3ff7306288a3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage {\n", "  lc_serializable: \u001b[33mtrue\u001b[39m,\n", "  lc_kwargs: {\n", "    content: \u001b[32m\"A triangle does not have a square. The square of a number is the result of multiplying the number by\"\u001b[39m... 8 more characters,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    additional_kwargs: { function_call: \u001b[90mundefined\u001b[39m, tool_calls: \u001b[90mundefined\u001b[39m },\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "  content: \u001b[32m\"A triangle does not have a square. The square of a number is the result of multiplying the number by\"\u001b[39m... 8 more characters,\n", "  name: \u001b[90mundefined\u001b[39m,\n", "  additional_kwargs: { function_call: \u001b[90mundefined\u001b[39m, tool_calls: \u001b[90mundefined\u001b[39m },\n", "  response_metadata: {\n", "    tokenUsage: { completionTokens: \u001b[33m23\u001b[39m, promptTokens: \u001b[33m52\u001b[39m, totalTokens: \u001b[33m75\u001b[39m },\n", "    finish_reason: \u001b[32m\"stop\"\u001b[39m\n", "  },\n", "  tool_calls: [],\n", "  invalid_tool_calls: []\n", "}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["const chain = finalPrompt.pipe(model);\n", "\n", "await chain.invoke({ input: \"What's the square of a triangle?\" })"]}, {"cell_type": "markdown", "id": "70ab7114-f07f-46be-8874-3705a25aba5f", "metadata": {}, "source": ["## Dynamic few-shot prompting\n", "\n", "Sometimes you may want to select only a few examples from your overall set to show based on the input. For this, you can replace the `examples` passed into `FewShotChatMessagePromptTemplate` with an `exampleSelector`. The other components remain the same as above! Our dynamic few-shot prompt template would look like:\n", "\n", "- `exampleSelector`: responsible for selecting few-shot examples (and the order in which they are returned) for a given input. These implement the [BaseExampleSelector](https://api.js.langchain.com/classes/langchain_core.example_selectors.BaseExampleSelector.html) interface. A common example is the vectorstore-backed [SemanticSimilarityExampleSelector](https://api.js.langchain.com/classes/langchain_core.example_selectors.SemanticSimilarityExampleSelector.html)\n", "- `examplePrompt`: convert each example into 1 or more messages through its [`formatMessages`](https://api.js.langchain.com/classes/langchain_core.prompts.FewShotChatMessagePromptTemplate.html#formatMessages) method. A common example would be to convert each example into one human message and one AI message response, or a human message followed by a function call message.\n", "\n", "These once again can be composed with other messages and chat templates to assemble your final prompt.\n", "\n", "Let's walk through an example with the `SemanticSimilarityExampleSelector`. Since this implementation uses a vectorstore to select examples based on semantic similarity, we will want to first populate the store. Since the basic idea here is that we want to search for and return examples most similar to the text input, we embed the `values` of our prompt examples rather than considering the keys:"]}, {"cell_type": "code", "execution_count": 19, "id": "ad66f06a-66fd-4fcc-8166-5d0e3c801e57", "metadata": {"tags": []}, "outputs": [], "source": ["import { SemanticSimilarityExampleSelector } from \"@langchain/core/example_selectors\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "import { OpenAIEmbeddings } from '@langchain/openai';\n", "\n", "const examples = [\n", "  { input: '2+2', output: '4' },\n", "  { input: '2+3', output: '5' },\n", "  { input: '2+4', output: '6' },\n", "  { input: 'What did the cow say to the moon?', output: 'nothing at all' },\n", "  {\n", "    input: 'Write me a poem about the moon',\n", "    output: 'One for the moon, and one for me, who are we to talk about the moon?',\n", "  },\n", "];\n", "\n", "const toVectorize = examples.map((example) => `${example.input} ${example.output}`);\n", "const embeddings = new OpenAIEmbeddings();\n", "const vectorStore = await MemoryVectorStore.fromTexts(toVectorize, examples, embeddings);"]}, {"cell_type": "markdown", "id": "2f7e384a-2031-432b-951c-7ea8cf9262f1", "metadata": {}, "source": ["### Create the `exampleSelector`\n", "\n", "With a vectorstore created, we can create the `exampleSelector`. Here we will call it in isolation, and set `k` on it to only fetch the two example closest to the input."]}, {"cell_type": "code", "execution_count": 21, "id": "7790303a-f722-452e-8921-b14bdf20bdff", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["[\n", "  {\n", "    input: \u001b[32m\"What did the cow say to the moon?\"\u001b[39m,\n", "    output: \u001b[32m\"nothing at all\"\u001b[39m\n", "  },\n", "  { input: \u001b[32m\"2+4\"\u001b[39m, output: \u001b[32m\"6\"\u001b[39m }\n", "]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["const exampleSelector = new SemanticSimilarityExampleSelector(\n", "    {\n", "        vectorStore,\n", "        k: 2\n", "    }\n", ")\n", "\n", "// The prompt template will load examples by passing the input do the `select_examples` method\n", "await exampleSelector.selectExamples({ input: \"horse\"})"]}, {"cell_type": "markdown", "id": "cc77c40f-3f58-40a2-b757-a2a2ea43f24a", "metadata": {}, "source": ["### Create prompt template\n", "\n", "We now assemble the prompt template, using the `exampleSelector` created above."]}, {"cell_type": "code", "execution_count": 23, "id": "253c255e-41d7-45f6-9d88-c7a0ced4b1bd", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: { content: \"2+3\", additional_kwargs: {}, response_metadata: {} },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"2+3\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"5\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"5\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    tool_calls: [],\n", "    invalid_tool_calls: []\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: { content: \"2+2\", additional_kwargs: {}, response_metadata: {} },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"2+2\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"4\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "    content: \"4\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    tool_calls: [],\n", "    invalid_tool_calls: []\n", "  }\n", "]\n"]}], "source": ["import {\n", "    ChatPromptTemplate,\n", "    FewShotChatMessagePromptTemplate,\n", "} from \"@langchain/core/prompts\"\n", "\n", "// Define the few-shot prompt.\n", "const fewShotPrompt = new FewShotChatMessagePromptTemplate({\n", "    // The input variables select the values to pass to the example_selector\n", "    inputVariables: [\"input\"],\n", "    exampleSelector,\n", "    // Define how ech example will be formatted.\n", "    // In this case, each example will become 2 messages:\n", "    // 1 human, and 1 AI\n", "    examplePrompt: ChatPromptTemplate.fromMessages(\n", "        [[\"human\", \"{input}\"], [\"ai\", \"{output}\"]]\n", "    ),\n", "})\n", "\n", "const results = await fewShotPrompt.invoke({ input: \"What's 3+3?\" });\n", "console.log(results.toChatMessages())"]}, {"cell_type": "markdown", "id": "339cae7d-0eb0-44a6-852f-0267c5ff72b3", "metadata": {}, "source": ["And we can pass this few-shot chat message prompt template into another chat prompt template:"]}, {"cell_type": "code", "execution_count": 24, "id": "e731cb45-f0ea-422c-be37-42af2a6cb2c4", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatPromptValue {\n", "  lc_serializable: true,\n", "  lc_kwargs: {\n", "    messages: [\n", "      HumanMessage {\n", "        lc_serializable: true,\n", "        lc_kwargs: {\n", "          content: \"2+3\",\n", "          additional_kwargs: {},\n", "          response_metadata: {}\n", "        },\n", "        lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "        content: \"2+3\",\n", "        name: undefined,\n", "        additional_kwargs: {},\n", "        response_metadata: {}\n", "      },\n", "      AIMessage {\n", "        lc_serializable: true,\n", "        lc_kwargs: {\n", "          content: \"5\",\n", "          tool_calls: [],\n", "          invalid_tool_calls: [],\n", "          additional_kwargs: {},\n", "          response_metadata: {}\n", "        },\n", "        lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "        content: \"5\",\n", "        name: undefined,\n", "        additional_kwargs: {},\n", "        response_metadata: {},\n", "        tool_calls: [],\n", "        invalid_tool_calls: []\n", "      },\n", "      HumanMessage {\n", "        lc_serializable: true,\n", "        lc_kwargs: {\n", "          content: \"2+2\",\n", "          additional_kwargs: {},\n", "          response_metadata: {}\n", "        },\n", "        lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "        content: \"2+2\",\n", "        name: undefined,\n", "        additional_kwargs: {},\n", "        response_metadata: {}\n", "      },\n", "      AIMessage {\n", "        lc_serializable: true,\n", "        lc_kwargs: {\n", "          content: \"4\",\n", "          tool_calls: [],\n", "          invalid_tool_calls: [],\n", "          additional_kwargs: {},\n", "          response_metadata: {}\n", "        },\n", "        lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "        content: \"4\",\n", "        name: undefined,\n", "        additional_kwargs: {},\n", "        response_metadata: {},\n", "        tool_calls: [],\n", "        invalid_tool_calls: []\n", "      }\n", "    ]\n", "  },\n", "  lc_namespace: [ \"langchain_core\", \"prompt_values\" ],\n", "  messages: [\n", "    HumanMessage {\n", "      lc_serializable: true,\n", "      lc_kwargs: { content: \"2+3\", additional_kwargs: {}, response_metadata: {} },\n", "      lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "      content: \"2+3\",\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    AIMessage {\n", "      lc_serializable: true,\n", "      lc_kwargs: {\n", "        content: \"5\",\n", "        tool_calls: [],\n", "        invalid_tool_calls: [],\n", "        additional_kwargs: {},\n", "        response_metadata: {}\n", "      },\n", "      lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "      content: \"5\",\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      tool_calls: [],\n", "      invalid_tool_calls: []\n", "    },\n", "    HumanMessage {\n", "      lc_serializable: true,\n", "      lc_kwargs: { content: \"2+2\", additional_kwargs: {}, response_metadata: {} },\n", "      lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "      content: \"2+2\",\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    AIMessage {\n", "      lc_serializable: true,\n", "      lc_kwargs: {\n", "        content: \"4\",\n", "        tool_calls: [],\n", "        invalid_tool_calls: [],\n", "        additional_kwargs: {},\n", "        response_metadata: {}\n", "      },\n", "      lc_namespace: [ \"langchain_core\", \"messages\" ],\n", "      content: \"4\",\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      tool_calls: [],\n", "      invalid_tool_calls: []\n", "    }\n", "  ]\n", "}\n"]}], "source": ["const finalPrompt = ChatPromptTemplate.fromMessages(\n", "    [\n", "        [\"system\", \"You are a wondrous wizard of math.\"],\n", "        fewShot<PERSON>rompt,\n", "        [\"human\", \"{input}\"],\n", "    ]\n", ")\n", "\n", "const result = await fewShotPrompt.invoke({ input: \"What's 3+3?\" });\n", "console.log(result)"]}, {"cell_type": "markdown", "id": "2408ea69-1880-4ef5-a0fa-ffa8d2026aa9", "metadata": {}, "source": ["### Use with an chat model\n", "\n", "Finally, you can connect your model to the few-shot prompt."]}, {"cell_type": "markdown", "id": "ea48da1a", "metadata": {}, "source": ["```{=mdx}\n", "<ChatModelTabs\n", "  customVarName=\"model\"\n", "/>\n", "```"]}, {"cell_type": "code", "execution_count": 25, "id": "0568cbc6-5354-47f1-ab4d-dfcc616cf583", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage {\n", "  lc_serializable: \u001b[33mtrue\u001b[39m,\n", "  lc_kwargs: {\n", "    content: \u001b[32m\"6\"\u001b[39m,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    additional_kwargs: { function_call: \u001b[90mundefined\u001b[39m, tool_calls: \u001b[90mundefined\u001b[39m },\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "  content: \u001b[32m\"6\"\u001b[39m,\n", "  name: \u001b[90mundefined\u001b[39m,\n", "  additional_kwargs: { function_call: \u001b[90mundefined\u001b[39m, tool_calls: \u001b[90mundefined\u001b[39m },\n", "  response_metadata: {\n", "    tokenUsage: { completionTokens: \u001b[33m1\u001b[39m, promptTokens: \u001b[33m51\u001b[39m, totalTokens: \u001b[33m52\u001b[39m },\n", "    finish_reason: \u001b[32m\"stop\"\u001b[39m\n", "  },\n", "  tool_calls: [],\n", "  invalid_tool_calls: []\n", "}"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["const chain = finalPrompt.pipe(model);\n", "\n", "await chain.invoke({ input: \"What's 3+3?\" })"]}, {"cell_type": "markdown", "id": "c87fad3c", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now learned how to add few-shot examples to your chat prompts.\n", "\n", "Next, check out the other how-to guides on prompt templates in this section, the related how-to guide on [few shotting with text completion models](/docs/how_to/few_shot_examples), or the other [example selector how-to guides](/docs/how_to/example_selectors/)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}