# How to reindex data to keep your vectorstore in-sync with the underlying data source

:::info Prerequisites

This guide assumes familiarity with the following concepts:

- [Retrieval-augmented generation (RAG)](/docs/tutorials/rag/)
- [Vector stores](/docs/concepts/#vectorstores)

:::

Here, we will look at a basic indexing workflow using the LangChain indexing API.

The indexing API lets you load and keep in sync documents from any source into a vector store. Specifically, it helps:

- Avoid writing duplicated content into the vector store
- Avoid re-writing unchanged content
- Avoid re-computing embeddings over unchanged content

All of which should save you time and money, as well as improve your vector search results.

Crucially, the indexing API will work even with documents that have gone through several transformation steps (e.g., via text chunking) with respect to the original source documents.

## How it works

LangChain indexing makes use of a record manager (`RecordManager`) that keeps track of document writes into the vector store.

When indexing content, hashes are computed for each document, and the following information is stored in the record manager:

- the document hash (hash of both page content and metadata)
- write time
- the source ID - each document should include information in its metadata to allow us to determine the ultimate source of this document

## Deletion Modes

When indexing documents into a vector store, it's possible that some existing documents in the vector store should be deleted.
In certain situations you may want to remove any existing documents that are derived from the same sources as the new documents being indexed.
In others you may want to delete all existing documents wholesale. The indexing API deletion modes let you pick the behavior you want:

| Cleanup Mode | De-Duplicates Content | Parallelizable | Cleans Up Deleted Source Docs | Cleans Up Mutations of Source Docs and/or Derived Docs | Clean Up Timing    |
| ------------ | --------------------- | -------------- | ----------------------------- | ------------------------------------------------------ | ------------------ |
| None         | ✅                    | ✅             | ❌                            | ❌                                                     | -                  |
| Incremental  | ✅                    | ✅             | ❌                            | ✅                                                     | Continuously       |
| Full         | ✅                    | ❌             | ✅                            | ✅                                                     | At end of indexing |

`None` does not do any automatic clean up, allowing the user to manually do clean up of old content.

`incremental` and `full` offer the following automated clean up:

- If the content of the source document or derived documents has changed, both `incremental` or `full` modes will clean up (delete) previous versions of the content.
- If the source document has been deleted (meaning it is not included in the documents currently being indexed), the full cleanup mode will delete it from the vector store correctly, but the `incremental` mode will not.

When content is mutated (e.g., the source PDF file was revised) there will be a period of time during indexing when both the new and old versions may be returned to the user. This happens after the new content was written, but before the old version was deleted.

- `incremental` indexing minimizes this period of time as it is able to do clean up continuously, as it writes.
- `full` mode does the clean up after all batches have been written.

## Requirements

1. Do not use with a store that has been pre-populated with content independently of the indexing API, as the record manager will not know that records have been inserted previously.
2. Only works with LangChain `vectorstore`'s that support:
   a). document addition by id (`addDocuments` method with ids argument)
   b). delete by id (delete method with ids argument)

Compatible Vectorstores: [`PGVector`](/docs/integrations/vectorstores/pgvector), [`Chroma`](/docs/integrations/vectorstores/chroma), [`CloudflareVectorize`](/docs/integrations/vectorstores/cloudflare_vectorize),
[`ElasticVectorSearch`](/docs/integrations/vectorstores/elasticsearch), [`FAISS`](/docs/integrations/vectorstores/faiss), [`MariaDB`](/docs/integrations/vectorstores/mariadb), [`MomentoVectorIndex`](/docs/integrations/vectorstores/momento_vector_index),
[`Pinecone`](/docs/integrations/vectorstores/pinecone), [`SupabaseVectorStore`](/docs/integrations/vectorstores/supabase), [`VercelPostgresVectorStore`](/docs/integrations/vectorstores/vercel_postgres),
[`Weaviate`](/docs/integrations/vectorstores/weaviate), [`Xata`](/docs/integrations/vectorstores/xata)

## Caution

The record manager relies on a time-based mechanism to determine what content can be cleaned up (when using `full` or `incremental` cleanup modes).

If two tasks run back-to-back, and the first task finishes before the clock time changes, then the second task may not be able to clean up content.

This is unlikely to be an issue in actual settings for the following reasons:

1. The `RecordManager` uses higher resolution timestamps.
2. The data would need to change between the first and the second tasks runs, which becomes unlikely if the time interval between the tasks is small.
3. Indexing tasks typically take more than a few ms.

## Quickstart

import CodeBlock from "@theme/CodeBlock";
import QuickStartExample from "@examples/indexes/indexing_api/indexing.ts";

<CodeBlock language="typescript">{QuickStartExample}</CodeBlock>

## Next steps

You've now learned how to use indexing in your RAG pipelines.

Next, check out some of the other sections on retrieval.
