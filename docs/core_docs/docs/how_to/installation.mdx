---
sidebar_position: 1
---

# Installation

## Supported Environments

LangChain is written in TypeScript and can be used in:

- Node.js (ESM and CommonJS) - 18.x, 19.x, 20.x
- Cloudflare Workers
- Vercel / Next.js (Browser, Serverless and Edge functions)
- Supabase Edge Functions
- Browser
- Deno
- Bun

However, note that individual integrations may not be supported in all environments.

## Installation

To install the main `langchain` package, run:

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import CodeBlock from "@theme/CodeBlock";

```bash npm2yarn
npm install langchain @langchain/core
```

While this package acts as a sane starting point to using LangChain,
much of the value of LangChain comes when integrating it with various model providers, datastores, etc.
By default, the dependencies needed to do that are NOT installed. You will need to install the dependencies for specific integrations separately.
We'll show how to do that in the next sections of this guide.

Please also see the section on [installing integration packages](/docs/how_to/installation/#installing-integration-packages)
for some special considerations when installing LangChain packages.

## Ecosystem packages

With the exception of the `langsmith` SDK, all packages in the LangChain ecosystem depend on `@langchain/core`, which contains base
classes and abstractions that other packages use. The dependency graph below shows how the difference packages are related.
A directed arrow indicates that the source package depends on the target package:

![](/img/ecosystem_packages.png)

**Note:** It is important that your app only uses one version of `@langchain/core`. Common package managers may introduce additional versions
when resolving direct dependencies, even if you don't intend this. See [this section on installing integration packages](/docs/how_to/installation/#installing-integration-packages)
for more information and ways to remedy this.

### @langchain/community

The [@langchain/community](https://www.npmjs.com/package/@langchain/community) package contains a range of third-party integrations.
Install with:

```bash npm2yarn
npm install @langchain/community @langchain/core
```

There are also more granular packages containing LangChain integrations for individual providers.

### @langchain/core

The [@langchain/core](https://www.npmjs.com/package/@langchain/core) package contains base abstractions that the rest of the LangChain ecosystem uses, along with the LangChain Expression Language.
It should be installed separately:

```bash npm2yarn
npm install @langchain/core
```

### LangGraph

[LangGraph.js](https://langchain-ai.github.io/langgraphjs/) is a library for building stateful, multi-actor applications with LLMs.
It integrates smoothly with LangChain, but can be used without it.

Install with:

```bash npm2yarn
npm install @langchain/langgraph @langchain/core
```

### LangSmith SDK

The LangSmith SDK is automatically installed by LangChain.
If you're not using it with LangChain, install with:

```bash npm2yarn
npm install langsmith
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

## Installing integration packages

LangChain supports packages that contain module integrations with individual third-party providers.
They can be as specific as [`@langchain/anthropic`](/docs/integrations/platforms/anthropic/), which contains integrations just for Anthropic models,
or as broad as [`@langchain/community`](https://www.npmjs.com/package/@langchain/community), which contains broader variety of community contributed integrations.

These packages, as well as the main LangChain package, all have [`@langchain/core`](https://www.npmjs.com/package/@langchain/core) as a peer dependency
to avoid package managers installing multiple versions of the same package. It contains the base abstractions that these integration packages extend.

To ensure that all integrations and their types interact with each other properly, it is important that they all use the same version of `@langchain/core`.
If you encounter type errors around base classes, you may need to guarantee that your package manager is resolving a single version of `@langchain/core`. To do so,
you can add a `"resolutions"` or `"overrides"` field like the following in your project's `package.json`. The name will depend on your package manager:

:::tip
The `resolutions` or `pnpm.overrides` fields for `yarn` or `pnpm` must be set in the root `package.json` file.
:::

If you are using `yarn`:

```json title="yarn package.json"
{
  "name": "your-project",
  "version": "0.0.0",
  "private": true,
  "engines": {
    "node": ">=18"
  },
  "dependencies": {
    "@langchain/anthropic": "^0.0.2",
    "@langchain/core": "^0.3.0",
    "langchain": "0.0.207"
  },
  "resolutions": {
    "@langchain/core": "0.3.0"
  }
}
```

You can also try running the [`yarn dedupe`](https://yarnpkg.com/cli/dedupe) command if you are on `yarn` version 2 or higher.

Or for `npm`:

```json title="npm package.json"
{
  "name": "your-project",
  "version": "0.0.0",
  "private": true,
  "engines": {
    "node": ">=18"
  },
  "dependencies": {
    "@langchain/anthropic": "^0.0.2",
    "@langchain/core": "^0.3.0",
    "langchain": "0.0.207"
  },
  "overrides": {
    "@langchain/core": "0.3.0"
  }
}
```

You can also try the [`npm dedupe`](https://docs.npmjs.com/cli/commands/npm-dedupe) command.

Or for `pnpm`:

```json title="pnpm package.json"
{
  "name": "your-project",
  "version": "0.0.0",
  "private": true,
  "engines": {
    "node": ">=18"
  },
  "dependencies": {
    "@langchain/anthropic": "^0.0.2",
    "@langchain/core": "^0.3.0",
    "langchain": "0.0.207"
  },
  "pnpm": {
    "overrides": {
      "@langchain/core": "0.3.0"
    }
  }
}
```

You can also try the [`pnpm dedupe`](https://pnpm.io/cli/dedupe) command.

## Loading the library

### TypeScript

LangChain is written in TypeScript and provides type definitions for all of its public APIs.

### ESM

LangChain provides an ESM build targeting Node.js environments. You can import it using the following syntax:

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

```typescript
import { ChatOpenAI } from "@langchain/openai";
```

If you are using TypeScript in an ESM project we suggest updating your `tsconfig.json` to include the following:

```json title="tsconfig.json"
{
  "compilerOptions": {
    ...
    "target": "ES2020", // or higher
    "module": "nodenext",
  }
}
```

### CommonJS

LangChain provides a CommonJS build targeting Node.js environments. You can import it using the following syntax:

```typescript
const { ChatOpenAI } = require("@langchain/openai");
```

### Cloudflare Workers

LangChain can be used in Cloudflare Workers. You can import it using the following syntax:

```typescript
import { ChatOpenAI } from "@langchain/openai";
```

### Vercel / Next.js

LangChain can be used in Vercel / Next.js. We support using LangChain in frontend components, in Serverless functions and in Edge functions. You can import it using the following syntax:

```typescript
import { ChatOpenAI } from "@langchain/openai";
```

### Deno / Supabase Edge Functions

LangChain can be used in Deno / Supabase Edge Functions. You can import it using the following syntax:

```typescript
import { ChatOpenAI } from "https://esm.sh/@langchain/openai";
```

or

```typescript
import { ChatOpenAI } from "npm:@langchain/openai";
```

### Browser

LangChain can be used in the browser. In our CI we test bundling LangChain with Webpack and Vite, but other bundlers should work too. You can import it using the following syntax:

```typescript
import { ChatOpenAI } from "@langchain/openai";
```

## Unsupported: Node.js 16

We do not support Node.js 16, but if you still want to run LangChain on Node.js 16, you will need to follow the instructions in this section. We do not guarantee that these instructions will continue to work in the future.

You will have to make `fetch` available globally, either:

- run your application with `NODE_OPTIONS='--experimental-fetch' node ...`, or
- install `node-fetch` and follow the instructions [here](https://github.com/node-fetch/node-fetch#providing-global-access)

You'll also need to [polyfill `ReadableStream`](https://www.npmjs.com/package/web-streams-polyfill) by installing:

```bash npm2yarn
npm i web-streams-polyfill@4
```

And then adding it to the global namespace in your main entrypoint:

```typescript
import "web-streams-polyfill/polyfill";
```

Additionally you'll have to polyfill `structuredClone`, eg. by installing `core-js` and following the instructions [here](https://github.com/zloirock/core-js).

If you are running Node.js 18+, you do not need to do anything.
