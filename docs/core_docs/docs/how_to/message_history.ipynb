{"cells": [{"cell_type": "raw", "id": "8165bd4c", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "keywords: [memory]\n", "---"]}, {"cell_type": "markdown", "id": "f47033eb", "metadata": {}, "source": ["# How to add message history\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Chaining runnables](/docs/how_to/sequence/)\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "- [Chat Messages](/docs/concepts/messages)\n", "\n", ":::\n", "\n", "```{=mdx}\n", ":::note\n", "\n", "This guide previously covered the [RunnableWithMessageHistory](https://api.js.langchain.com/classes/_langchain_core.runnables.RunnableWithMessageHistory.html) abstraction. You can access this version of the guide in the [v0.2 docs](https://js.langchain.com/v0.2/docs/how_to/message_history/).\n", "\n", "The LangGraph implementation offers a number of advantages over `RunnableWithMessageHistory`, including the ability to persist arbitrary components of an application's state (instead of only messages).\n", "\n", ":::\n", "```\n", "\n", "\n", "Passing conversation state into and out a chain is vital when building a chatbot. LangGraph implements a built-in persistence layer, allowing chain states to be automatically persisted in memory, or external backends such as SQLite, Postgres or Redis. Details can be found in the LangGraph persistence documentation.\n", "\n", "In this guide we demonstrate how to add persistence to arbitrary LangChain runnables by wrapping them in a minimal LangGraph application. This lets us persist the message history and other elements of the chain's state, simplifying the development of multi-turn applications. It also supports multiple threads, enabling a single application to interact separately with multiple users.\n", "\n", "## Setup\n", "\n", "```{=mdx}\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<Npm2Yarn>\n", "  @langchain/core @langchain/langgraph\n", "</Npm2Yarn>\n", "```\n", "\n", "Let’s also set up a chat model that we’ll use for the below examples.\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```\n"]}, {"cell_type": "code", "execution_count": 30, "id": "8a4e4708", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o\",\n", "  temperature: 0,\n", "});"]}, {"cell_type": "markdown", "id": "1f6121bc-2080-4ccc-acf0-f77de4bc951d", "metadata": {}, "source": ["## Example: message inputs\n", "\n", "Adding memory to a [chat model](/docs/concepts/chat_models) provides a simple example. Chat models accept a list of messages as input and output a message. LangGraph includes a built-in `MessagesState` that we can use for this purpose.\n", "\n", "Below, we:\n", "1. Define the graph state to be a list of messages;\n", "2. Add a single node to the graph that calls a chat model;\n", "3. Compile the graph with an in-memory checkpointer to store messages between runs.\n", "\n", ":::info\n", "\n", "The output of a LangGraph application is its [state](https://langchain-ai.github.io/langgraphjs/concepts/low_level/).\n", "\n", ":::"]}, {"cell_type": "code", "execution_count": 31, "id": "f691a73a-a866-4354-9fff-8315605e2b8f", "metadata": {}, "outputs": [], "source": ["import { START, END, MessagesAnnotation, StateGraph, MemorySaver } from \"@langchain/langgraph\";\n", "\n", "// Define the function that calls the model\n", "const callModel = async (state: typeof MessagesAnnotation.State) => {\n", "  const response = await llm.invoke(state.messages);\n", "  // Update message history with response:\n", "  return { messages: response };\n", "};\n", "\n", "// Define a new graph\n", "const workflow = new StateGraph(MessagesAnnotation)\n", "  // Define the (single) node in the graph\n", "  .addNode(\"model\", callModel)\n", "  .addEdge(START, \"model\")\n", "  .addEdge(\"model\", END);\n", "\n", "// Add memory\n", "const memory = new MemorySaver();\n", "const app = workflow.compile({ checkpointer: memory });"]}, {"cell_type": "markdown", "id": "c0b396a8-f81e-4139-b4b2-75adf61d8179", "metadata": {}, "source": ["When we run the application, we pass in a configuration object that specifies a `thread_id`. This ID is used to distinguish conversational threads (e.g., between different users)."]}, {"cell_type": "code", "execution_count": 32, "id": "e4309511-2140-4d91-8f5f-ea3661e6d179", "metadata": {}, "outputs": [], "source": ["import { v4 as uuidv4 } from \"uuid\";\n", "\n", "const config = { configurable: { thread_id: uuidv4() } }"]}, {"cell_type": "markdown", "id": "108c45a2-4971-4120-ba64-9a4305a414bb", "metadata": {}, "source": ["We can then invoke the application:"]}, {"cell_type": "code", "execution_count": 33, "id": "72a5ff6c-501f-4151-8dd9-f600f70554be", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-ABTqCeKnMQmG9IH8dNF5vPjsgXtcM\",\n", "  \"content\": \"<PERSON> <PERSON>! How can I assist you today?\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 10,\n", "      \"promptTokens\": 12,\n", "      \"totalTokens\": 22\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"system_fingerprint\": \"fp_e375328146\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 12,\n", "    \"output_tokens\": 10,\n", "    \"total_tokens\": 22\n", "  }\n", "}\n"]}], "source": ["const input = [\n", "  {\n", "    role: \"user\",\n", "    content: \"Hi! I'm <PERSON>.\",\n", "  }\n", "]\n", "const output = await app.invoke({ messages: input }, config)\n", "// The output contains all messages in the state.\n", "// This will log the last message in the conversation.\n", "console.log(output.messages[output.messages.length - 1]);"]}, {"cell_type": "code", "execution_count": 34, "id": "5931fb35-0fac-40e7-8ac6-b14cb4e926cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-ABTqD5jrJXeKCpvoIDp47fvgw2OPn\",\n", "  \"content\": \"Your name is <PERSON>. How can I help you today, <PERSON>?\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 14,\n", "      \"promptTokens\": 34,\n", "      \"totalTokens\": 48\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"system_fingerprint\": \"fp_e375328146\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 34,\n", "    \"output_tokens\": 14,\n", "    \"total_tokens\": 48\n", "  }\n", "}\n"]}], "source": ["const input2 = [\n", "  {\n", "    role: \"user\",\n", "    content: \"What's my name?\",\n", "  }\n", "]\n", "const output2 = await app.invoke({ messages: input2 }, config)\n", "console.log(output2.messages[output2.messages.length - 1]);"]}, {"cell_type": "markdown", "id": "91de6d12-881d-4d23-a421-f2e3bf829b79", "metadata": {}, "source": ["Note that states are separated for different threads. If we issue the same query to a thread with a new `thread_id`, the model indicates that it does not know the answer:"]}, {"cell_type": "code", "execution_count": 35, "id": "6f12c26f-8913-4484-b2c5-b49eda2e6d7d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-ABTqDkctxwmXjeGOZpK6Km8jdCqdl\",\n", "  \"content\": \"I'm sorry, but I don't have access to personal information about users. How can I assist you today?\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 21,\n", "      \"promptTokens\": 11,\n", "      \"totalTokens\": 32\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 11,\n", "    \"output_tokens\": 21,\n", "    \"total_tokens\": 32\n", "  }\n", "}\n"]}], "source": ["const config2 = { configurable: { thread_id: uuidv4() } }\n", "const input3 = [\n", "  {\n", "    role: \"user\",\n", "    content: \"What's my name?\",\n", "  }\n", "]\n", "const output3 = await app.invoke({ messages: input3 }, config2)\n", "console.log(output3.messages[output3.messages.length - 1]);"]}, {"cell_type": "markdown", "id": "6749ea95-3382-4843-bb96-cfececb9e4e5", "metadata": {}, "source": ["## Example: object inputs\n", "\n", "LangChain runnables often accept multiple inputs via separate keys in a single object argument. A common example is a prompt template with multiple parameters.\n", "\n", "Whereas before our runnable was a chat model, here we chain together a prompt template and chat model."]}, {"cell_type": "code", "execution_count": 36, "id": "6e7a402a-0994-4fc5-a607-fb990a248aa4", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate, MessagesPlaceholder } from \"@langchain/core/prompts\";\n", "\n", "const prompt = ChatPromptTemplate.fromMessages([\n", "  [\"system\", \"Answer in {language}.\"],\n", "  new MessagesPlaceholder(\"messages\"),\n", "])\n", "\n", "const runnable = prompt.pipe(llm);"]}, {"cell_type": "markdown", "id": "f83107bd-ae61-45e1-a57e-94ab043aad4b", "metadata": {}, "source": ["For this scenario, we define the graph state to include these parameters (in addition to the message history). We then define a single-node graph in the same way as before.\n", "\n", "Note that in the below state:\n", "- Updates to the `messages` list will append messages;\n", "- Updates to the `language` string will overwrite the string."]}, {"cell_type": "code", "execution_count": 37, "id": "267429ea-be0f-4f80-8daf-c63d881a1436", "metadata": {}, "outputs": [], "source": ["import { START, END, StateGraph, MemorySaver, MessagesAnnotation, Annotation } from \"@langchain/langgraph\";\n", "\n", "// Define the State\n", "// highlight-next-line\n", "const GraphAnnotation = Annotation.Root({\n", "  // highlight-next-line\n", "  language: Annotation<string>(),\n", "  // Spread `MessagesAnnotation` into the state to add the `messages` field.\n", "  // highlight-next-line\n", "  ...MessagesAnnotation.spec,\n", "})\n", "\n", "\n", "// Define the function that calls the model\n", "const callModel2 = async (state: typeof GraphAnnotation.State) => {\n", "  const response = await runnable.invoke(state);\n", "  // Update message history with response:\n", "  return { messages: [response] };\n", "};\n", "\n", "const workflow2 = new StateGraph(GraphAnnotation)\n", "  .addNode(\"model\", callModel2)\n", "  .addEdge(START, \"model\")\n", "  .addEdge(\"model\", END);\n", "\n", "const app2 = workflow2.compile({ checkpointer: new MemorySaver() });"]}, {"cell_type": "code", "execution_count": 38, "id": "f3844fb4-58d7-43c8-b427-6d9f64d7411b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-ABTqFnCASRB5UhZ7XAbbf5T0Bva4U\",\n", "  \"content\": \"Lo siento, pero no tengo suficiente información para saber tu nombre. ¿Cómo te llamas?\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 19,\n", "      \"promptTokens\": 19,\n", "      \"totalTokens\": 38\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"system_fingerprint\": \"fp_e375328146\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 19,\n", "    \"output_tokens\": 19,\n", "    \"total_tokens\": 38\n", "  }\n", "}\n"]}], "source": ["const config3 = { configurable: { thread_id: uuidv4() } }\n", "const input4 = {\n", "  messages: [\n", "    {\n", "      role: \"user\",\n", "      content: \"What's my name?\",\n", "    }\n", "  ],\n", "  language: \"Spanish\",\n", "} \n", "const output4 = await app2.invoke(input4, config3)\n", "console.log(output4.messages[output4.messages.length - 1]);"]}, {"cell_type": "markdown", "id": "7df47824-ef18-4a6e-a416-345ec9203f88", "metadata": {}, "source": ["## Managing message history\n", "\n", "The message history (and other elements of the application state) can be accessed via `.getState`:"]}, {"cell_type": "code", "execution_count": 39, "id": "1cbd6d82-43c1-4d11-98af-5c3ad9cd9b3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Language: Spanish\n", "[\n", "  HumanMessage {\n", "    \"content\": \"What's my name?\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {}\n", "  },\n", "  AIMessage {\n", "    \"id\": \"chatcmpl-ABTqFnCASRB5UhZ7XAbbf5T0Bva4U\",\n", "    \"content\": \"Lo siento, pero no tengo suficiente información para saber tu nombre. ¿Cómo te llamas?\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {\n", "      \"tokenUsage\": {\n", "        \"completionTokens\": 19,\n", "        \"promptTokens\": 19,\n", "        \"totalTokens\": 38\n", "      },\n", "      \"finish_reason\": \"stop\",\n", "      \"system_fingerprint\": \"fp_e375328146\"\n", "    },\n", "    \"tool_calls\": [],\n", "    \"invalid_tool_calls\": []\n", "  }\n", "]\n"]}], "source": ["const state = (await app2.getState(config3)).values\n", "\n", "console.log(`Language: ${state.language}`);\n", "console.log(state.messages)"]}, {"cell_type": "markdown", "id": "acfbccda-0bd6-4c4d-ae6e-8118520314e1", "metadata": {}, "source": ["We can also update the state via `.updateState`. For example, we can manually append a new message:"]}, {"cell_type": "code", "execution_count": 40, "id": "e98310d7-8ab1-461d-94a7-dd419494ab8d", "metadata": {}, "outputs": [], "source": ["const _ = await app2.updateState(config3, { messages: [{ role: \"user\", content: \"test\" }]})"]}, {"cell_type": "code", "execution_count": 41, "id": "74ab3691-6f3b-49c5-aad0-2a90fc2a1e6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Language: Spanish\n", "[\n", "  HumanMessage {\n", "    \"content\": \"What's my name?\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {}\n", "  },\n", "  AIMessage {\n", "    \"id\": \"chatcmpl-ABTqFnCASRB5UhZ7XAbbf5T0Bva4U\",\n", "    \"content\": \"Lo siento, pero no tengo suficiente información para saber tu nombre. ¿Cómo te llamas?\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {\n", "      \"tokenUsage\": {\n", "        \"completionTokens\": 19,\n", "        \"promptTokens\": 19,\n", "        \"totalTokens\": 38\n", "      },\n", "      \"finish_reason\": \"stop\",\n", "      \"system_fingerprint\": \"fp_e375328146\"\n", "    },\n", "    \"tool_calls\": [],\n", "    \"invalid_tool_calls\": []\n", "  },\n", "  HumanMessage {\n", "    \"content\": \"test\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {}\n", "  }\n", "]\n"]}], "source": ["const state2 = (await app2.getState(config3)).values\n", "\n", "console.log(`Language: ${state2.language}`);\n", "console.log(state2.messages)"]}, {"cell_type": "markdown", "id": "e4a1ea00-d7ff-4f18-b9ec-9aec5909d027", "metadata": {}, "source": ["For details on managing state, including deleting messages, see the LangGraph documentation:\n", "\n", "- [How to delete messages](https://langchain-ai.github.io/langgraphjs/how-tos/delete-messages/)\n", "- [How to view and update past graph state](https://langchain-ai.github.io/langgraphjs/how-tos/time-travel/)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}