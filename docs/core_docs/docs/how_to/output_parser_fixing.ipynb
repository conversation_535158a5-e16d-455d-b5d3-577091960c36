{"cells": [{"cell_type": "markdown", "id": "0fee7096", "metadata": {}, "source": ["# How to try to fix errors in output parsing\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Output parsers](/docs/concepts/output_parsers)\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "- [Chaining runnables together](/docs/how_to/sequence/)\n", "\n", ":::\n", "\n", "LLMs aren't perfect, and sometimes fail to produce output that perfectly matches a the desired format. To help handle errors, we can use the [`OutputFixingParser`](https://api.js.langchain.com/classes/langchain.output_parsers.OutputFixingParser.html) This output parser wraps another output parser, and in the event that the first one fails, it calls out to another LLM in an attempt to fix any errors.\n", "\n", "Specifically, we can pass the misformatted output, along with the formatted instructions, to the model and ask it to fix it.\n", "\n", "For this example, we'll use the [`StructuredOutputParser`](https://api.js.langchain.com/classes/langchain_core.output_parsers.StructuredOutputParser.html), which can validate output according to a Zod schema. Here's what happens if we pass it a result that does not comply with the schema:"]}, {"cell_type": "code", "execution_count": 1, "id": "15283e0b", "metadata": {}, "outputs": [{"ename": "Error", "evalue": "Failed to parse. Text: \"{'name': '<PERSON>', 'film_names': ['<PERSON>']}\". Error: SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)", "output_type": "error", "traceback": ["Stack trace:", "Error: Failed to parse. Text: \"{'name': '<PERSON>', 'film_names': ['<PERSON>']}\". Error: SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)", "    at StructuredOutputParser.parse (file:///Users/<USER>/Library/Caches/deno/npm/registry.npmjs.org/@langchain/core/0.1.63/dist/output_parsers/structured.js:86:19)", "    at <anonymous>:11:14"]}], "source": ["import { z } from \"zod\";\n", "import { RunnableSequence } from \"@langchain/core/runnables\";\n", "import { StructuredOutputParser } from \"@langchain/core/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const zodSchema = z.object({\n", "  name: z.string().describe(\"name of an actor\"),\n", "  film_names: z.array(z.string()).describe(\"list of names of films they starred in\"),\n", "});\n", "\n", "const parser = StructuredOutputParser.fromZodSchema(zodSchema);\n", "\n", "const misformatted = \"{'name': '<PERSON>', 'film_names': ['<PERSON>']}\";\n", "\n", "await parser.parse(misformatted);"]}, {"cell_type": "markdown", "id": "723c559d", "metadata": {}, "source": ["Now we can construct and use a `OutputFixingParser`. This output parser takes as an argument another output parser but also an LLM with which to try to correct any formatting mistakes."]}, {"cell_type": "code", "execution_count": 3, "id": "4aaccbf1", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  name: \u001b[32m\"<PERSON>\"\u001b[39m,\n", "  film_names: [\n", "    \u001b[32m\"<PERSON>\"\u001b[39m,\n", "    \u001b[32m\"Saving Private <PERSON>\"\u001b[39m,\n", "    \u001b[32m\"Cast Away\"\u001b[39m,\n", "    \u001b[32m\"Catch Me If You Can\"\u001b[39m\n", "  ]\n", "}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import { ChatAnthropic } from \"@langchain/anthropic\";\n", "\n", "import { OutputFixingParser } from \"langchain/output_parsers\";\n", "\n", "const model = new ChatAnthropic({\n", "  model: \"claude-3-sonnet-20240229\",\n", "  maxTokens: 512,\n", "  temperature: 0.1,\n", "});\n", "\n", "const parserWithFix = OutputFixingParser.fromLLM(model, parser);\n", "\n", "await parserWithFix.parse(misformatted);"]}, {"cell_type": "markdown", "id": "84498e02", "metadata": {}, "source": ["For more about different parameters and options, check out our [API reference docs](https://api.js.langchain.com/classes/langchain.output_parsers.OutputFixingParser.html)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}