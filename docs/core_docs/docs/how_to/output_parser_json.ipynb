{"cells": [{"cell_type": "markdown", "id": "72b1b316", "metadata": {}, "source": ["# How to parse JSON output\n", "\n", "While some model providers support [built-in ways to return structured output](/docs/how_to/structured_output), not all do. We can use an output parser to help users to specify an arbitrary JSON schema via the prompt, query a model for outputs that conform to that schema, and finally parse that schema as JSON.\n", "\n", ":::{.callout-note}\n", "Keep in mind that large language models are leaky abstractions! You'll have to use an LLM with sufficient capacity to generate well-formed JSON.\n", ":::\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Output parsers](/docs/concepts/output_parsers)\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "- [Structured output](/docs/how_to/structured_output)\n", "- [Chaining runnables together](/docs/how_to/sequence/)\n", "\n", ":::"]}, {"cell_type": "markdown", "id": "ae909b7a", "metadata": {}, "source": ["The [`JsonOutputParser`](https://api.js.langchain.com/classes/langchain_core.output_parsers.JsonOutputParser.html) is one built-in option for prompting for and then parsing JSON output."]}, {"cell_type": "markdown", "id": "6c667607", "metadata": {}, "source": ["```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs />\n", "```"]}, {"cell_type": "code", "execution_count": 2, "id": "4ccf45a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  setup: \u001b[32m\"Why don't scientists trust atoms?\"\u001b[39m,\n", "  punchline: \u001b[32m\"Because they make up everything!\"\u001b[39m\n", "}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import { ChatOpenAI } from \"@langchain/openai\";\n", "const model = new ChatOpenAI({\n", "  model: \"gpt-4o\",\n", "  temperature: 0,\n", "})\n", "\n", "import { JsonOutputParser } from \"@langchain/core/output_parsers\"\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "// Define your desired data structure. Only used for typing the parser output.\n", "interface Joke {\n", "  setup: string\n", "  punchline: string\n", "}\n", "\n", "// A query and format instructions used to prompt a language model.\n", "const jokeQuery = \"Tell me a joke.\";\n", "const formatInstructions = \"Respond with a valid JSON object, containing two fields: 'setup' and 'punchline'.\"\n", "\n", "// Set up a parser + inject instructions into the prompt template.\n", "const parser = new JsonOutputParser<Joke>()\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(\n", "  \"Answer the user query.\\n{format_instructions}\\n{query}\\n\"\n", ");\n", "\n", "const partialedPrompt = await prompt.partial({\n", "  format_instructions: formatInstructions\n", "});\n", "\n", "const chain = partialedPrompt.pipe(model).pipe(parser);\n", "\n", "await chain.invoke({ query: jokeQuery });"]}, {"cell_type": "markdown", "id": "37d801be", "metadata": {}, "source": ["## Streaming\n", "\n", "The `JsonOutputParser` also supports streaming partial chunks. This is useful when the model returns partial JSON output in multiple chunks. The parser will keep track of the partial chunks and return the final JSON output when the model finishes generating the output."]}, {"cell_type": "code", "execution_count": 3, "id": "0309256d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{}\n", "{ setup: \"\" }\n", "{ setup: \"Why\" }\n", "{ setup: \"Why don't\" }\n", "{ setup: \"Why don't scientists\" }\n", "{ setup: \"Why don't scientists trust\" }\n", "{ setup: \"Why don't scientists trust atoms\" }\n", "{ setup: \"Why don't scientists trust atoms?\", punchline: \"\" }\n", "{ setup: \"Why don't scientists trust atoms?\", punchline: \"Because\" }\n", "{\n", "  setup: \"Why don't scientists trust atoms?\",\n", "  punchline: \"Because they\"\n", "}\n", "{\n", "  setup: \"Why don't scientists trust atoms?\",\n", "  punchline: \"Because they make\"\n", "}\n", "{\n", "  setup: \"Why don't scientists trust atoms?\",\n", "  punchline: \"Because they make up\"\n", "}\n", "{\n", "  setup: \"Why don't scientists trust atoms?\",\n", "  punchline: \"Because they make up everything\"\n", "}\n", "{\n", "  setup: \"Why don't scientists trust atoms?\",\n", "  punchline: \"Because they make up everything!\"\n", "}\n"]}], "source": ["for await (const s of await chain.stream({ query: jokeQuery })) {\n", "    console.log(s)\n", "}"]}, {"cell_type": "markdown", "id": "1eefe12b", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now learned one way to prompt a model to return structured JSON. Next, check out the [broader guide on obtaining structured output](/docs/how_to/structured_output) for other techniques."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}