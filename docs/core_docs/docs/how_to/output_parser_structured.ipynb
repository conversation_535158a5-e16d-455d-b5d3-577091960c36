{"cells": [{"cell_type": "raw", "id": "38831021-76ed-48b3-9f62-d1241a68b6ad", "metadata": {}, "source": ["---\n", "sidebar_position: 3\n", "---"]}, {"cell_type": "markdown", "id": "a745f98b-c495-44f6-a882-757c38992d76", "metadata": {}, "source": ["# How to use output parsers to parse an LLM response into structured format\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Output parsers](/docs/concepts/output_parsers)\n", "- [Chat models](/docs/concepts/chat_models)\n", "\n", ":::\n", "\n", "Language models output text. But there are times where you want to get more structured information than just text back. While some model providers support [built-in ways to return structured output](/docs/how_to/structured_output), not all do. For these providers, you must use prompting to encourage the model to return structured data in the desired format.\n", "\n", "<PERSON><PERSON><PERSON><PERSON> has [output parsers](/docs/concepts/output_parsers) which can help parse model outputs into usable objects. We'll go over a few examples below.\n", "\n", "## Get started\n", "\n", "The primary type of output parser for working with structured data in model responses is the [`StructuredOutputParser`](https://api.js.langchain.com/classes/langchain_core.output_parsers.StructuredOutputParser.html). In the below example, we define a schema for the type of output we expect from the model using [`zod`](https://zod.dev).\n", "\n", "First, let's see the default formatting instructions we'll plug into the prompt:"]}, {"cell_type": "markdown", "id": "b62367da", "metadata": {}, "source": ["```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs />\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "1594b2bf-2a6f-47bb-9a81-38930f8e606b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You must format your output as a JSON value that adheres to a given \"JSON Schema\" instance.\n", "\n", "\"JSON Schema\" is a declarative language that allows you to annotate and validate JSON documents.\n", "\n", "For example, the example \"JSON Schema\" instance {{\"properties\": {{\"foo\": {{\"description\": \"a list of test words\", \"type\": \"array\", \"items\": {{\"type\": \"string\"}}}}}}, \"required\": [\"foo\"]}}}}\n", "would match an object with one required property, \"foo\". The \"type\" property specifies \"foo\" must be an \"array\", and the \"description\" property semantically describes it as \"a list of test words\". The items within \"foo\" must be strings.\n", "Thus, the object {{\"foo\": [\"bar\", \"baz\"]}} is a well-formatted instance of this example \"JSON Schema\". The object {{\"properties\": {{\"foo\": [\"bar\", \"baz\"]}}}} is not well-formatted.\n", "\n", "Your output will be parsed and type-checked according to the provided schema instance, so make sure all fields in your output match the schema exactly and there are no trailing commas!\n", "\n", "Here is the JSON Schema instance your output must adhere to. Include the enclosing markdown codeblock:\n", "```json\n", "{\"type\":\"object\",\"properties\":{\"answer\":{\"type\":\"string\",\"description\":\"answer to the user's question\"},\"source\":{\"type\":\"string\",\"description\":\"source used to answer the user's question, should be a website.\"}},\"required\":[\"answer\",\"source\"],\"additionalProperties\":false,\"$schema\":\"http://json-schema.org/draft-07/schema#\"}\n", "```\n", "\n"]}], "source": ["import { z } from \"zod\";\n", "import { RunnableSequence } from \"@langchain/core/runnables\";\n", "import { StructuredOutputParser } from \"@langchain/core/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const zodSchema = z.object({\n", "  answer: z.string().describe(\"answer to the user's question\"),\n", "  source: z.string().describe(\"source used to answer the user's question, should be a website.\"),\n", "})\n", "\n", "const parser = StructuredOutputParser.fromZodSchema(zodSchema);\n", "\n", "const chain = RunnableSequence.from([\n", "  ChatPromptTemplate.fromTemplate(\n", "    \"Answer the users question as best as possible.\\n{format_instructions}\\n{question}\"\n", "  ),\n", "  model,\n", "  parser,\n", "]);\n", "\n", "console.log(parser.getFormatInstructions());\n"]}, {"cell_type": "markdown", "id": "2bd357c5", "metadata": {}, "source": ["Next, let's invoke the chain:"]}, {"cell_type": "code", "execution_count": 2, "id": "301471a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  answer: \"The capital of France is Paris.\",\n", "  source: \"https://en.wikipedia.org/wiki/Paris\"\n", "}\n"]}], "source": ["const response = await chain.invoke({\n", "  question: \"What is the capital of France?\",\n", "  format_instructions: parser.getFormatInstructions(),\n", "});\n", "\n", "console.log(response);"]}, {"cell_type": "markdown", "id": "75976cd6-78e2-458b-821f-3ddf3683466b", "metadata": {}, "source": ["Output parsers implement the [Runnable interface](/docs/how_to/#langchain-expression-language-lcel), the basic building block of [LangChain Expression Language (LCEL)](/docs/how_to/#langchain-expression-language-lcel). This means they support `invoke`, `stream`, `batch`, `streamLog` calls.\n", "\n", "## Validation\n", "\n", "One feature of the `StructuredOutputParser` is that it supports stricter Zod validations. For example, if you pass a simulated model output that does not conform to the schema, we get a detailed type error:"]}, {"cell_type": "code", "execution_count": 3, "id": "475f1ae5", "metadata": {}, "outputs": [{"ename": "Error", "evalue": "Failed to parse. Text: \"{\"badfield\": \"foo\"}\". Error: [\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"string\",\n    \"received\": \"undefined\",\n    \"path\": [\n      \"answer\"\n    ],\n    \"message\": \"Required\"\n  },\n  {\n    \"code\": \"invalid_type\",\n    \"expected\": \"string\",\n    \"received\": \"undefined\",\n    \"path\": [\n      \"source\"\n    ],\n    \"message\": \"Required\"\n  }\n]", "output_type": "error", "traceback": ["Stack trace:", "Error: Failed to parse. Text: \"{\"badfield\": \"foo\"}\". Error: [", "  {", "    \"code\": \"invalid_type\",", "    \"expected\": \"string\",", "    \"received\": \"undefined\",", "    \"path\": [", "      \"answer\"", "    ],", "    \"message\": \"Required\"", "  },", "  {", "    \"code\": \"invalid_type\",", "    \"expected\": \"string\",", "    \"received\": \"undefined\",", "    \"path\": [", "      \"source\"", "    ],", "    \"message\": \"Required\"", "  }", "]", "    at StructuredOutputParser.parse (file:///Users/<USER>/Library/Caches/deno/npm/registry.npmjs.org/@langchain/core/0.1.63/dist/output_parsers/structured.js:86:19)", "    at async StructuredOutputParser._callWithConfig (file:///Users/<USER>/Library/Caches/deno/npm/registry.npmjs.org/@langchain/core/0.1.63/dist/runnables/base.js:203:22)", "    at async <anonymous>:2:1"]}], "source": ["import { AIMessage } from \"@langchain/core/messages\";\n", "\n", "await parser.invoke(new AIMessage(`{\"badfield\": \"foo\"}`));"]}, {"cell_type": "markdown", "id": "653a0236", "metadata": {}, "source": ["Compared to:"]}, {"cell_type": "code", "execution_count": 4, "id": "5f39d45e", "metadata": {}, "outputs": [{"data": {"text/plain": ["{ answer: \u001b[32m\"Paris\"\u001b[39m, source: \u001b[32m\"I made it up\"\u001b[39m }"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["await parser.invoke(new AIMessage(`{\"answer\": \"Paris\", \"source\": \"I made it up\"}`));"]}, {"cell_type": "markdown", "id": "d289aa9c", "metadata": {}, "source": ["More advanced Zod validations are supported as well. To learn more, check out the [Zod documentation](https://zod.dev)."]}, {"cell_type": "markdown", "id": "d88590a0-f36b-4ad5-8a56-d300971a6440", "metadata": {}, "source": ["## Streaming\n", "\n", "While all parsers are runnables and support the streaming interface, only certain parsers can stream through partially parsed objects, since this is highly dependent on the output type. The `StructuredOutputParser` does not support partial streaming because it validates the output at each step. If you try to stream using a chain with this output parser, the chain will simply yield the fully parsed output:"]}, {"cell_type": "code", "execution_count": 5, "id": "fac8f714", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  answer: \"The capital of France is Paris.\",\n", "  source: \"https://en.wikipedia.org/wiki/Paris\"\n", "}\n"]}], "source": ["const stream = await chain.stream({\n", "  question: \"What is the capital of France?\",\n", "  format_instructions: parser.getFormatInstructions(),\n", "});\n", "\n", "for await (const s of stream) {\n", "  console.log(s)\n", "}"]}, {"cell_type": "markdown", "id": "a3a40f19", "metadata": {}, "source": ["The simpler [`JsonOutputParser`](https://api.js.langchain.com/classes/langchain_core.output_parsers.JsonOutputParser.html), however, supports streaming through partial outputs:"]}, {"cell_type": "code", "execution_count": 8, "id": "d7ecfe4d-dae8-4452-98ea-e48bdc498788", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{}\n", "{ answer: \"\" }\n", "{ answer: \"The\" }\n", "{ answer: \"The invention\" }\n", "{ answer: \"The invention of\" }\n", "{ answer: \"The invention of the\" }\n", "{ answer: \"The invention of the microscope\" }\n", "{ answer: \"The invention of the microscope is\" }\n", "{ answer: \"The invention of the microscope is attributed\" }\n", "{ answer: \"The invention of the microscope is attributed to\" }\n", "{ answer: \"The invention of the microscope is attributed to <PERSON>\" }\n", "{ answer: \"The invention of the microscope is attributed to <PERSON>\" }\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>,\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>,\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 4 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 8 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 12 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 13 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 18 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 20 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 26 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 29 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 33 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 38 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 43 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 48 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 51 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 52 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 57 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 63 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 73 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 80 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 81 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 85 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 94 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 99 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 108 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 112 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 118 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 127 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 138 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 145 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 149 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 150 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 151 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 157 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 159 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 163 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 167 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 171 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 175 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 176 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 181 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 186 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 190 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 202 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 203 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 209 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 214 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 226 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 239 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 242 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 246 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 253 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 257 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 262 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 265 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 268 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 273 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 288 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 300 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 303 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 311 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 316 more characters\n", "}\n", "{\n", "  answer: \"The invention of the microscope is attributed to <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>\"... 317 more characters\n", "}\n"]}], "source": ["import { JsonOutputParser } from \"@langchain/core/output_parsers\";\n", "\n", "const template = `Return a JSON object with a single key named \"answer\" that answers the following question: {question}.\n", "Do not wrap the JSON output in markdown blocks.`\n", "\n", "const jsonPrompt = ChatPromptTemplate.fromTemplate(template);\n", "const jsonParser = new JsonOutputParser();\n", "const jsonChain = jsonPrompt.pipe(model).pipe(jsonParser);\n", "\n", "const stream = await jsonChain.stream({\n", "  question: \"Who invented the microscope?\",\n", "});\n", "\n", "for await (const s of stream) {\n", "  console.log(s)\n", "}"]}, {"cell_type": "markdown", "id": "605b8dd1", "metadata": {}, "source": ["## Next steps\n", "\n", "You've learned about using output parsers to parse structured outputs from prompted model outputs.\n", "\n", "Next, check out the [guide on tool calling](/docs/how_to/tool_calling), a more built-in way of obtaining structured output that some model providers support, or read more about output parsers for other types of structured data like [XML](/docs/how_to/output_parser_xml)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}