{"cells": [{"cell_type": "raw", "id": "d35de667-0352-4bfb-a890-cebe7f676fe7", "metadata": {}, "source": ["---\n", "sidebar_position: 5\n", "keywords: [RunnablePassthrough, LCEL]\n", "---"]}, {"cell_type": "markdown", "id": "b022ab74-794d-4c54-ad47-ff9549ddb9d2", "metadata": {}, "source": ["# How to pass through arguments from one step to the next\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)\n", "- [Chaining runnables](/docs/how_to/sequence/)\n", "- [Calling runnables in parallel](/docs/how_to/parallel/)\n", "- [Custom functions](/docs/how_to/functions/)\n", "\n", ":::\n", "\n", "\n", "When composing chains with several steps, sometimes you will want to pass data from previous steps unchanged for use as input to a later step. The [`RunnablePassthrough`](https://api.js.langchain.com/classes/langchain_core.runnables.RunnablePassthrough.html) class allows you to do just this, and is typically is used in conjuction with a [RunnableParallel](/docs/how_to/parallel/) to pass data through to a later step in your constructed chains.\n", "\n", "Let's look at an example:"]}, {"cell_type": "code", "execution_count": 1, "id": "03988b8d-d54c-4492-8707-1594372cf093", "metadata": {}, "outputs": [{"data": {"text/plain": ["{ passed: { num: \u001b[33m1\u001b[39m }, modified: \u001b[33m2\u001b[39m }"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import { RunnableParallel, RunnablePassthrough } from \"@langchain/core/runnables\";\n", "\n", "const runnable = RunnableParallel.from({\n", "  passed: new RunnablePassthrough<{ num: number }>(),\n", "  modified: (input: { num: number }) => input.num + 1,\n", "});\n", "\n", "await runnable.invoke({ num: 1 });"]}, {"cell_type": "markdown", "id": "702c7acc-cd31-4037-9489-647df192fd7c", "metadata": {}, "source": ["As seen above, `passed` key was called with `RunnablePassthrough()` and so it simply passed on `{'num': 1}`. \n", "\n", "We also set a second key in the map with `modified`. This uses a lambda to set a single value adding 1 to the num, which resulted in `modified` key with the value of `2`."]}, {"cell_type": "markdown", "id": "15187a3b-d666-4b9b-a258-672fc51fe0e2", "metadata": {}, "source": ["## Retrieval Example\n", "\n", "In the example below, we see a more real-world use case where we use `RunnablePassthrough` along with `RunnableParallel` in a chain to properly format inputs to a prompt:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/openai @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "code", "execution_count": 3, "id": "267d1460-53c1-4fdb-b2c3-b6a1eb7fccff", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"<PERSON> worked at Kensho.\"\u001b[39m"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { RunnablePassthrough, RunnableSequence } from \"@langchain/core/runnables\";\n", "import { ChatOpenAI, OpenAIEmbeddings } from \"@langchain/openai\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const vectorstore = await MemoryVectorStore.fromDocuments([\n", "  { pageContent: \"ha<PERSON><PERSON> worked at kens<PERSON>\", metadata: {} }\n", "], new OpenAIEmbeddings());\n", "\n", "const retriever = vectorstore.asRetriever();\n", "\n", "const template = `Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "`;\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(template);\n", "\n", "const model = new ChatOpenAI({ model: \"gpt-4o\" });\n", "\n", "const retrievalChain = RunnableSequence.from([\n", "  {\n", "    context: retriever.pipe((docs) => docs[0].pageContent),\n", "    question: new RunnablePassthrough()\n", "  },\n", "  prompt,\n", "  model,\n", "  new StringOutputParser(),\n", "]);\n", "\n", "await retrieval<PERSON><PERSON><PERSON>.invoke(\"where did harrison work?\");"]}, {"cell_type": "markdown", "id": "392cd4c4-e7ed-4ab8-934d-f7a4eca55ee1", "metadata": {}, "source": ["Here the input to prompt is expected to be a map with keys `\"context\"` and `\"question\"`. The user input is just the question. So we need to get the context using our retriever and passthrough the user input under the `\"question\"` key. The `RunnablePassthrough` allows us to pass on the user's question to the prompt and model.\n", "\n", "## Next steps\n", "\n", "Now you've learned how to pass data through your chains to help to help format the data flowing through your chains.\n", "\n", "To learn more, see the other how-to guides on runnables in this section."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}