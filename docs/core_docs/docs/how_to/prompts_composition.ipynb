{"cells": [{"cell_type": "raw", "id": "02a1c8fb", "metadata": {}, "source": ["---\n", "sidebar_position: 5\n", "---"]}, {"cell_type": "markdown", "id": "4de4e022", "metadata": {}, "source": ["# How to compose prompts together\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "\n", ":::\n", "\n", "LangChain provides a user friendly interface for composing different parts of prompts together. You can do this with either string prompts or chat prompts. Constructing prompts this way allows for easy reuse of components."]}, {"cell_type": "markdown", "id": "c3190650", "metadata": {}, "source": ["## String prompt composition\n", "\n", "When working with string prompts, each template is joined together. You can work with either prompts directly or strings (the first element in the list needs to be a prompt)."]}, {"cell_type": "code", "execution_count": 3, "id": "69b17f05", "metadata": {}, "outputs": [{"data": {"text/plain": ["PromptTemplate {\n", "  lc_serializable: \u001b[33mtrue\u001b[39m,\n", "  lc_kwargs: {\n", "    inputVariables: [ \u001b[32m\"topic\"\u001b[39m, \u001b[32m\"language\"\u001b[39m ],\n", "    templateFormat: \u001b[32m\"f-string\"\u001b[39m,\n", "    template: \u001b[32m\"Tell me a joke about {topic}, make it funny and in {language}\"\u001b[39m\n", "  },\n", "  lc_runnable: \u001b[33mtrue\u001b[39m,\n", "  name: \u001b[90mundefined\u001b[39m,\n", "  lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"prompts\"\u001b[39m, \u001b[32m\"prompt\"\u001b[39m ],\n", "  inputVariables: [ \u001b[32m\"topic\"\u001b[39m, \u001b[32m\"language\"\u001b[39m ],\n", "  outputParser: \u001b[90mundefined\u001b[39m,\n", "  partialVariables: \u001b[90mundefined\u001b[39m,\n", "  templateFormat: \u001b[32m\"f-string\"\u001b[39m,\n", "  template: \u001b[32m\"Tell me a joke about {topic}, make it funny and in {language}\"\u001b[39m,\n", "  validateTemplate: \u001b[33mtrue\u001b[39m\n", "}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const prompt = PromptTemplate.fromTemplate(`Tell me a joke about {topic}, make it funny and in {language}`)\n", "\n", "prompt"]}, {"cell_type": "code", "execution_count": 4, "id": "dbba24ba", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"Tell me a joke about sports, make it funny and in spanish\"\u001b[39m"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["await prompt.format({ topic: \"sports\", language: \"spanish\" })"]}, {"cell_type": "markdown", "id": "4e4f6a8a", "metadata": {}, "source": ["## Chat prompt composition"]}, {"cell_type": "markdown", "id": "8554bae5", "metadata": {}, "source": ["A chat prompt is made up a of a list of messages. Similarly to the above example, we can concatenate chat prompt templates. Each new element is a new message in the final prompt.\n", "\n", "First, let's initialize the a [`ChatPromptTemplate`](https://api.python.langchain.com/en/latest/prompts/langchain_core.prompts.chat.ChatPromptTemplate.html) with a [`SystemMessage`](https://api.python.langchain.com/en/latest/messages/langchain_core.messages.system.SystemMessage.html)."]}, {"cell_type": "code", "execution_count": 5, "id": "cab8dd65", "metadata": {}, "outputs": [], "source": ["import { AIMessage, HumanMessage, SystemMessage} from \"@langchain/core/messages\"\n", "\n", "const prompt = new SystemMessage(\"You are a nice pirate\")"]}, {"cell_type": "markdown", "id": "30656ef8", "metadata": {}, "source": ["You can then easily create a pipeline combining it with other messages *or* message templates.\n", "Use a `BaseMessage` when there are no variables to be formatted, use a `MessageTemplate` when there are variables to be formatted. You can also use just a string (note: this will automatically get inferred as a [`HumanMessagePromptTemplate`](https://api.js.langchain.com/classes/langchain_core.prompts.HumanMessagePromptTemplate.html).)"]}, {"cell_type": "code", "execution_count": 6, "id": "a2ddd0a1", "metadata": {}, "outputs": [], "source": ["import { HumanMessagePromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const newPrompt = HumanMessagePromptTemplate.fromTemplate([prompt, new HumanMessage(\"Hi\"), new AIMessage(\"what?\"), \"{input}\"])"]}, {"cell_type": "markdown", "id": "72294e1b", "metadata": {}, "source": ["Under the hood, this creates an instance of the ChatPromptTemplate class, so you can use it just as you did before!"]}, {"cell_type": "code", "execution_count": 8, "id": "297932de", "metadata": {}, "outputs": [{"data": {"text/plain": ["[\n", "  HumanMessage {\n", "    lc_serializable: \u001b[33mtrue\u001b[39m,\n", "    lc_kwargs: {\n", "      content: [\n", "        { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"You are a nice pirate\"\u001b[39m },\n", "        { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"Hi\"\u001b[39m },\n", "        { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"what?\"\u001b[39m },\n", "        { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"i said hi\"\u001b[39m }\n", "      ],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "    content: [\n", "      { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"You are a nice pirate\"\u001b[39m },\n", "      { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"Hi\"\u001b[39m },\n", "      { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"what?\"\u001b[39m },\n", "      { type: \u001b[32m\"text\"\u001b[39m, text: \u001b[32m\"i said hi\"\u001b[39m }\n", "    ],\n", "    name: \u001b[90mundefined\u001b[39m,\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  }\n", "]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["await newPrompt.formatMessages({ input: \"i said hi\" })"]}, {"cell_type": "markdown", "id": "0e1d47e3-b05a-4aef-a58c-3057fa628c1c", "metadata": {}, "source": ["## Using PipelinePrompt"]}, {"cell_type": "markdown", "id": "0a5892f9-e4d8-4b7c-b6a5-4651539b9734", "metadata": {}, "source": ["LangChain includes a class called [`PipelinePromptTemplate`](https://api.js.langchain.com/classes/_langchain_core.prompts.PipelinePromptTemplate.html), which can be useful when you want to reuse parts of prompts. A PipelinePrompt consists of two main parts:\n", "\n", "- Final prompt: The final prompt that is returned\n", "- Pipeline prompts: A list of tuples, consisting of a string name and a prompt template. Each prompt template will be formatted and then passed to future prompt templates as a variable with the same name."]}, {"cell_type": "code", "execution_count": 1, "id": "4face631-74d7-49ca-93b1-1e6e66fa58e2", "metadata": {}, "outputs": [], "source": ["import {\n", "    PromptTemplate,\n", "    PipelinePromptTemplate,\n", "  } from \"@langchain/core/prompts\";\n", "  \n", "const fullPrompt = PromptTemplate.fromTemplate(`{introduction}\n", "\n", "{example}\n", "\n", "{start}`);\n", "\n", "const introductionPrompt = PromptTemplate.fromTemplate(\n", "`You are impersonating {person}.`\n", ");\n", "\n", "const examplePrompt =\n", "PromptTemplate.fromTemplate(`Here's an example of an interaction:\n", "Q: {example_q}\n", "A: {example_a}`);\n", "\n", "const startPrompt = PromptTemplate.fromTemplate(`Now, do this for real!\n", "Q: {input}\n", "A:`);\n", "\n", "const composedPrompt = new PipelinePromptTemplate({\n", "pipelinePrompts: [\n", "    {\n", "    name: \"introduction\",\n", "    prompt: introduction<PERSON><PERSON><PERSON>,\n", "    },\n", "    {\n", "    name: \"example\",\n", "    prompt: example<PERSON>rompt,\n", "    },\n", "    {\n", "    name: \"start\",\n", "    prompt: start<PERSON>rompt,\n", "    },\n", "],\n", "finalPrompt: fullPrompt,\n", "});\n", "  \n", "\n", "  "]}, {"cell_type": "code", "execution_count": 2, "id": "c6cabb16-ea30-4de0-8548-dcce84df8421", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are impersonating <PERSON><PERSON>.\n", "\n", "Here's an example of an interaction:\n", "Q: What's your favorite car?\n", "A: Telsa\n", "\n", "Now, do this for real!\n", "Q: What's your favorite social media site?\n", "A:\n"]}], "source": ["const formattedPrompt = await composedPrompt.format({\n", "    person: \"<PERSON><PERSON>\",\n", "    example_q: `What's your favorite car?`,\n", "    example_a: \"Telsa\",\n", "    input: `What's your favorite social media site?`,\n", "  });\n", "  \n", "  \n", "console.log(formattedPrompt);\n"]}, {"cell_type": "markdown", "id": "96922030", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now learned how to compose prompts together.\n", "\n", "Next, check out the other how-to guides on prompt templates in this section, like [adding few-shot examples to your prompt templates](/docs/how_to/few_shot_examples_chat)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}