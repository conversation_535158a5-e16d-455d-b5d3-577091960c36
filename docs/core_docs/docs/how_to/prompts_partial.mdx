# How to partially format prompt templates

:::info Prerequisites

This guide assumes familiarity with the following concepts:

- [Prompt templates](/docs/concepts/prompt_templates)

:::

Like partially binding arguments to a function, it can make sense to "partial" a prompt template - e.g. pass in
a subset of the required values, as to create a new prompt template which expects only the remaining subset of values.

LangChain supports this in two ways:

1. Partial formatting with string values.
2. Partial formatting with functions that return string values.

In the examples below, we go over the motivations for both use cases as well as how to do it in LangChain.

## Partial with strings

One common use case for wanting to partial a prompt template is if you get access to some of the variables in a
prompt before others. For example, suppose you have a prompt template that requires two variables, `foo` and `baz`.
If you get the `foo` value early on in your chain, but the `baz` value later, it can be inconvenient to pass both variables all the way through the chain.
Instead, you can partial the prompt template with the `foo` value, and then pass the partialed prompt template along and just use that.
Below is an example of doing this:

```typescript
import { PromptTemplate } from "langchain/prompts";

const prompt = new PromptTemplate({
  template: "{foo}{bar}",
  inputVariables: ["foo", "bar"],
});

const partialPrompt = await prompt.partial({
  foo: "foo",
});

const formattedPrompt = await partialPrompt.format({
  bar: "baz",
});

console.log(formattedPrompt);

// foobaz
```

You can also just initialize the prompt with the partialed variables.

```typescript
const prompt = new PromptTemplate({
  template: "{foo}{bar}",
  inputVariables: ["bar"],
  partialVariables: {
    foo: "foo",
  },
});

const formattedPrompt = await prompt.format({
  bar: "baz",
});

console.log(formattedPrompt);

// foobaz
```

## Partial With Functions

You can also partial with a function. The use case for this is when you have a variable you know that you always want to fetch in a common way. A prime example of this is with date or time. Imagine you have a prompt which you always want to have the current date. You can't hard code it in the prompt, and passing it along with the other input variables can be tedious. In this case, it's very handy to be able to partial the prompt with a function that always returns the current date.

```typescript
const getCurrentDate = () => {
  return new Date().toISOString();
};

const prompt = new PromptTemplate({
  template: "Tell me a {adjective} joke about the day {date}",
  inputVariables: ["adjective", "date"],
});

const partialPrompt = await prompt.partial({
  date: getCurrentDate,
});

const formattedPrompt = await partialPrompt.format({
  adjective: "funny",
});

console.log(formattedPrompt);

// Tell me a funny joke about the day 2023-07-13T00:54:59.287Z
```

You can also just initialize the prompt with the partialed variables:

```typescript
const prompt = new PromptTemplate({
  template: "Tell me a {adjective} joke about the day {date}",
  inputVariables: ["adjective"],
  partialVariables: {
    date: getCurrentDate,
  },
});

const formattedPrompt = await prompt.format({
  adjective: "funny",
});

console.log(formattedPrompt);

// Tell me a funny joke about the day 2023-07-13T00:54:59.287Z
```

## Next steps

You've now learned how to partially apply variables to your prompt templates.

Next, check out the other how-to guides on prompt templates in this section, like [adding few-shot examples to your prompt templates](/docs/how_to/few_shot_examples_chat).
