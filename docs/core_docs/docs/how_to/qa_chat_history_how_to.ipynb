{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to add chat history\n", "\n", "\n", ":::note\n", "\n", "This tutorial previously built a chatbot using [RunnableWithMessageHistory](https://api.js.langchain.com/classes/_langchain_core.runnables.RunnableWithMessageHistory.html). You can access this version of the tutorial in the [v0.2 docs](https://js.langchain.com/v0.2/docs/how_to/qa_chat_history_how_to/).\n", "\n", "The LangGraph implementation offers a number of advantages over `RunnableWithMessageHistory`, including the ability to persist arbitrary components of an application's state (instead of only messages).\n", "\n", ":::\n", "\n", "In many Q&A applications we want to allow the user to have a back-and-forth conversation, meaning the application needs some sort of \"memory\" of past questions and answers, and some logic for incorporating those into its current thinking.\n", "\n", "In this guide we focus on **adding logic for incorporating historical messages.**\n", "\n", "This is largely a condensed version of the [Conversational RAG tutorial](/docs/tutorials/qa_chat_history).\n", "\n", "We will cover two approaches:\n", "\n", "1. [Chains](/docs/how_to/qa_chat_history_how_to#chains), in which we always execute a retrieval step;\n", "2. [Agents](/docs/how_to/qa_chat_history_how_to#agents), in which we give an LLM discretion over whether and how to execute a retrieval step (or multiple steps).\n", "\n", "For the external knowledge source, we will use the same [LLM Powered Autonomous Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post by <PERSON><PERSON> from the [RAG tutorial](/docs/tutorials/rag)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "### Dependencies\n", "\n", "We’ll use an OpenAI chat model and embeddings and a Memory vector store in this walkthrough, but everything shown here works with any [ChatModel](/docs/concepts/chat_models) or [LLM](/docs/concepts/text_llms), [Embeddings](/docs/concepts/embedding_models), and [VectorStore](/docs/concepts/vectorstores) or [Retriever](/docs/concepts/retrievers).\n", "\n", "We’ll use the following packages:\n", "\n", "```bash\n", "npm install --save langchain @langchain/openai langchain cheerio uuid\n", "```\n", "\n", "We need to set environment variable `OPENAI_API_KEY`:\n", "\n", "```bash\n", "export OPENAI_API_KEY=YOUR_KEY\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls. As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent. The best way to do this is with [LangSmith](https://docs.smith.langchain.com).\n", "\n", "Note that Lang<PERSON>mith is not needed, but it is helpful. If you do want to use LangSmith, after you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "\n", "```bash\n", "export LANGSMITH_TRACING=true\n", "export LANGSMITH_API_KEY=YOUR_KEY\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# export LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chains {#chains}\n", "\n", "In a conversational RAG application, queries issued to the retriever should be informed by the context of the conversation. <PERSON><PERSON><PERSON><PERSON> provides a [createHistoryAwareRetriever](https://api.js.langchain.com/functions/langchain.chains_history_aware_retriever.createHistoryAwareRetriever.html) constructor to simplify this. It constructs a chain that accepts keys `input` and `chat_history` as input, and has the same output schema as a retriever. `createHistoryAwareRetriever` requires as inputs:  \n", "\n", "1. LLM;\n", "2. Retriever;\n", "3. Prompt.\n", "\n", "First we obtain these objects:\n", "\n", "### LLM\n", "\n", "We can use any supported chat model:\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\"\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({ model: \"gpt-4o\" });"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Initial setup"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "import { RecursiveCharacterTextSplitter } from \"langchain/text_splitter\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\"\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "const loader = new CheerioWebBaseLoader(\n", "  \"https://lilianweng.github.io/posts/2023-06-23-agent/\"\n", ");\n", "\n", "const docs = await loader.load();\n", "\n", "const textSplitter = new RecursiveCharacterTextSplitter({ chunkSize: 1000, chunkOverlap: 200 });\n", "const splits = await textSplitter.splitDocuments(docs);\n", "const vectorStore = await MemoryVectorStore.fromDocuments(splits, new OpenAIEmbeddings());\n", "\n", "// Retrieve and generate using the relevant snippets of the blog.\n", "const retriever = vectorStore.asRetriever();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Prompt\n", "\n", "We'll use a prompt that includes a `MessagesPlaceholder` variable under the name \"chat_history\". This allows us to pass in a list of Messages to the prompt using the \"chat_history\" input key, and these messages will be inserted after the system message and before the human message containing the latest question."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate, MessagesPlaceholder } from \"@langchain/core/prompts\";\n", "\n", "const contextualizeQSystemPrompt = (\n", "  \"Given a chat history and the latest user question \" +\n", "  \"which might reference context in the chat history, \" +\n", "  \"formulate a standalone question which can be understood \" +\n", "  \"without the chat history. Do NOT answer the question, \" +\n", "  \"just reformulate it if needed and otherwise return it as is.\"\n", ")\n", "\n", "const contextualizeQPrompt = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\"system\", contextualizeQSystemPrompt],\n", "    new MessagesPlaceholder(\"chat_history\"),\n", "    [\"human\", \"{input}\"],\n", "  ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Assembling the chain\n", "\n", "We can then instantiate the history-aware retriever:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import { createHistoryAwareRetriever } from \"langchain/chains/history_aware_retriever\";\n", "\n", "const historyAwareRetriever = await createHistoryAwareRetriever({\n", "  llm,\n", "  retriever,\n", "  rephrasePrompt: contextualizeQPrompt\n", "});\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This chain prepends a rephrasing of the input query to our retriever, so that the retrieval incorporates the context of the conversation.\n", "\n", "Now we can build our full QA chain.\n", "\n", "As in the [RAG tutorial](/docs/tutorials/rag), we will use [createStuffDocumentsChain](https://api.js.langchain.com/functions/langchain.chains_combine_documents.createStuffDocumentsChain.html) to generate a `questionAnswer<PERSON>hain`, with input keys `context`, `chat_history`, and `input`-- it accepts the retrieved context alongside the conversation history and query to generate an answer.\n", "\n", "We build our final `rag<PERSON>hain` with [createRetrievalChain](https://api.js.langchain.com/functions/langchain.chains_retrieval.createRetrievalChain.html). This chain applies the `historyAwareRetriever` and `questionAnswerChain` in sequence, retaining intermediate outputs such as the retrieved context for convenience. It has input keys `input` and `chat_history`, and includes `input`, `chat_history`, `context`, and `answer` in its output."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import { createStuffDocumentsChain } from \"langchain/chains/combine_documents\";\n", "import { createRetrievalChain } from \"langchain/chains/retrieval\";\n", "\n", "const systemPrompt = \n", "  \"You are an assistant for question-answering tasks. \" +\n", "  \"Use the following pieces of retrieved context to answer \" +\n", "  \"the question. If you don't know the answer, say that you \" +\n", "  \"don't know. Use three sentences maximum and keep the \" +\n", "  \"answer concise.\" +\n", "  \"\\n\\n\" +\n", "  \"{context}\";\n", "\n", "const qaPrompt = ChatPromptTemplate.fromMessages([\n", "  [\"system\", systemPrompt],\n", "  new MessagesPlaceholder(\"chat_history\"),\n", "  [\"human\", \"{input}\"],\n", "]);\n", "\n", "const questionAnswerChain = await createStuffDocumentsChain({\n", "  llm,\n", "  prompt: qa<PERSON><PERSON><PERSON>,\n", "});\n", "\n", "const ragChain = await createRetrievalChain({\n", "  retriever: historyAwareRetriever,\n", "  combineDocsChain: questionAnswerChain,\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stateful Management of chat history\n", "\n", "We have added application logic for incorporating chat history, but we are still manually plumbing it through our application. In production, the Q&A application we usually persist the chat history into a database, and be able to read and update it appropriately.\n", "\n", "[LangGraph](https://langchain-ai.github.io/langgraphjs/) implements a built-in [persistence layer](https://langchain-ai.github.io/langgraphjs/concepts/persistence/), making it ideal for chat applications that support multiple conversational turns.\n", "\n", "Wrapping our chat model in a minimal LangGraph application allows us to automatically persist the message history, simplifying the development of multi-turn applications.\n", "\n", "LangGraph comes with a simple [in-memory checkpointer](https://langchain-ai.github.io/langgraphjs/reference/classes/checkpoint.MemorySaver.html), which we use below. See its documentation for more detail, including how to use different persistence backends (e.g., SQLite or Postgres).\n", "\n", "For a detailed walkthrough of how to manage message history, head to the How to add message history (memory) guide."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import { AIMessage, BaseMessage, HumanMessage } from \"@langchain/core/messages\";\n", "import { StateGraph, START, END, MemorySaver, messagesStateReducer, Annotation } from \"@langchain/langgraph\";\n", "\n", "// Define the State interface\n", "const GraphAnnotation = Annotation.Root({\n", "  input: Annotation<string>(),\n", "  chat_history: Annotation<BaseMessage[]>({\n", "    reducer: messagesStateReducer,\n", "    default: () => [],\n", "  }),\n", "  context: Annotation<string>(),\n", "  answer: Annotation<string>(),\n", "})\n", "\n", "// Define the call_model function\n", "async function callModel(state: typeof GraphAnnotation.State) {\n", "  const response = await rag<PERSON>hain.invoke(state);\n", "  return {\n", "    chat_history: [\n", "      new HumanMessage(state.input),\n", "      new AIMessage(response.answer),\n", "    ],\n", "    context: response.context,\n", "    answer: response.answer,\n", "  };\n", "}\n", "\n", "// Create the workflow\n", "const workflow = new StateGraph(GraphAnnotation)\n", "  .addNode(\"model\", callModel)\n", "  .addEdge(START, \"model\")\n", "  .addEdge(\"model\", END);\n", "\n", "// Compile the graph with a checkpointer object\n", "const memory = new MemorySaver();\n", "const app = workflow.compile({ checkpointer: memory });"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task Decomposition is the process of breaking down a complicated task into smaller, simpler, and more manageable steps. Techniques like Chain of Thought (CoT) and Tree of Thoughts expand on this by enabling agents to think step by step or explore multiple reasoning possibilities at each step. This allows for a more structured and interpretable approach to handling complex tasks.\n"]}], "source": ["import { v4 as uuidv4 } from \"uuid\";\n", "\n", "const threadId = uuidv4();\n", "const config = { configurable: { thread_id: threadId } };\n", "\n", "const result = await app.invoke(\n", "  { input: \"What is Task Decomposition?\" },\n", "  config,\n", ")\n", "console.log(result.answer);"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["One way of doing task decomposition is by using an LLM with simple prompting, such as asking \"Steps for XYZ.\\n1.\" or \"What are the subgoals for achieving XYZ?\" This method leverages direct prompts to guide the model in breaking down tasks.\n"]}], "source": ["const result2 = await app.invoke(\n", "  { input: \"What is one way of doing it?\" },\n", "  config,\n", ")\n", "console.log(result2.answer);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The conversation history can be inspected via the state of the application:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HumanMessage {\n", "  \"content\": \"What is Task Decomposition?\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {}\n", "}\n", "AIMessage {\n", "  \"content\": \"Task Decomposition is the process of breaking down a complicated task into smaller, simpler, and more manageable steps. Techniques like Chain of Thought (CoT) and Tree of Thoughts expand on this by enabling agents to think step by step or explore multiple reasoning possibilities at each step. This allows for a more structured and interpretable approach to handling complex tasks.\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {},\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": []\n", "}\n", "HumanMessage {\n", "  \"content\": \"What is one way of doing it?\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {}\n", "}\n", "AIMessage {\n", "  \"content\": \"One way of doing task decomposition is by using an LLM with simple prompting, such as asking \\\"Steps for XYZ.\\\\n1.\\\" or \\\"What are the subgoals for achieving XYZ?\\\" This method leverages direct prompts to guide the model in breaking down tasks.\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {},\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": []\n", "}\n"]}], "source": ["const chatHistory = (await app.getState(config)).values.chat_history;\n", "for (const message of chat<PERSON><PERSON>ory) {\n", "  console.log(message);\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tying it together\n", "\n", "![](../../static/img/conversational_retrieval_chain.png)\n", "\n", "For convenience, we tie together all of the necessary steps in a single code cell:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Task Decomposition is the process of breaking a complicated task into smaller, simpler steps to enhance model performance on complex tasks. Techniques like Chain of Thought (CoT) and Tree of Thoughts (ToT) are used for this, with CoT focusing on step-by-step thinking and ToT exploring multiple reasoning possibilities at each step. Decomposition can be carried out by the LLM itself, using task-specific instructions, or through human inputs.\n", "One way of doing task decomposition is by prompting the LLM with simple instructions such as \"Steps for XYZ.\\n1.\" or \"What are the subgoals for achieving XYZ?\" This encourages the model to break down the task into smaller, manageable steps on its own.\n"]}], "source": ["import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "import { RecursiveCharacterTextSplitter } from \"langchain/text_splitter\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\"\n", "import { OpenAIEmbeddings, ChatOpenAI } from \"@langchain/openai\";\n", "import { ChatPromptTemplate, MessagesPlaceholder } from \"@langchain/core/prompts\";\n", "import { createHistoryAwareRetriever } from \"langchain/chains/history_aware_retriever\";\n", "import { createStuffDocumentsChain } from \"langchain/chains/combine_documents\";\n", "import { createRetrievalChain } from \"langchain/chains/retrieval\";\n", "import { AIMessage, BaseMessage, HumanMessage } from \"@langchain/core/messages\";\n", "import { StateGraph, START, END, MemorySaver, messagesStateReducer, Annotation } from \"@langchain/langgraph\";\n", "import { v4 as uuidv4 } from \"uuid\";\n", "\n", "const llm2 = new ChatOpenAI({ model: \"gpt-4o\" });\n", "\n", "const loader2 = new CheerioWebBaseLoader(\n", "  \"https://lilianweng.github.io/posts/2023-06-23-agent/\"\n", ");\n", "\n", "const docs2 = await loader2.load();\n", "\n", "const textSplitter2 = new RecursiveCharacterTextSplitter({ chunkSize: 1000, chunkOverlap: 200 });\n", "const splits2 = await textSplitter2.splitDocuments(docs2);\n", "const vectorStore2 = await MemoryVectorStore.fromDocuments(splits2, new OpenAIEmbeddings());\n", "\n", "// Retrieve and generate using the relevant snippets of the blog.\n", "const retriever2 = vectorStore2.asRetriever();\n", "\n", "const contextualizeQSystemPrompt2 =\n", "  \"Given a chat history and the latest user question \" +\n", "  \"which might reference context in the chat history, \" +\n", "  \"formulate a standalone question which can be understood \" +\n", "  \"without the chat history. Do NOT answer the question, \" +\n", "  \"just reformulate it if needed and otherwise return it as is.\";\n", "\n", "const contextualizeQPrompt2 = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\"system\", contextualizeQSystemPrompt2],\n", "    new MessagesPlaceholder(\"chat_history\"),\n", "    [\"human\", \"{input}\"],\n", "  ]\n", ")\n", "\n", "const historyAwareRetriever2 = await createHistoryAwareRetriever({\n", "  llm: llm2,\n", "  retriever: retriever2,\n", "  rephrasePrompt: contextualizeQPrompt2\n", "});\n", "\n", "const systemPrompt2 = \n", "  \"You are an assistant for question-answering tasks. \" +\n", "  \"Use the following pieces of retrieved context to answer \" +\n", "  \"the question. If you don't know the answer, say that you \" +\n", "  \"don't know. Use three sentences maximum and keep the \" +\n", "  \"answer concise.\" +\n", "  \"\\n\\n\" +\n", "  \"{context}\";\n", "\n", "const qaPrompt2 = ChatPromptTemplate.fromMessages([\n", "  [\"system\", systemPrompt2],\n", "  new MessagesPlaceholder(\"chat_history\"),\n", "  [\"human\", \"{input}\"],\n", "]);\n", "\n", "const questionAnswerChain2 = await createStuffDocumentsChain({\n", "  llm: llm2,\n", "  prompt: qaPrompt2,\n", "});\n", "\n", "const ragChain2 = await createRetrievalChain({\n", "  retriever: historyAwareRetriever2,\n", "  combineDocsChain: questionAnswerChain2,\n", "});\n", "\n", "// Define the State interface\n", "const GraphAnnotation2 = Annotation.Root({\n", "  input: Annotation<string>(),\n", "  chat_history: Annotation<BaseMessage[]>({\n", "    reducer: messagesStateReducer,\n", "    default: () => [],\n", "  }),\n", "  context: Annotation<string>(),\n", "  answer: Annotation<string>(),\n", "})\n", "\n", "// Define the call_model function\n", "async function callModel2(state: typeof GraphAnnotation2.State) {\n", "  const response = await ragChain2.invoke(state);\n", "  return {\n", "    chat_history: [\n", "      new HumanMessage(state.input),\n", "      new AIMessage(response.answer),\n", "    ],\n", "    context: response.context,\n", "    answer: response.answer,\n", "  };\n", "}\n", "\n", "// Create the workflow\n", "const workflow2 = new StateGraph(GraphAnnotation2)\n", "  .addNode(\"model\", callModel2)\n", "  .addEdge(START, \"model\")\n", "  .addEdge(\"model\", END);\n", "\n", "// Compile the graph with a checkpointer object\n", "const memory2 = new MemorySaver();\n", "const app2 = workflow2.compile({ checkpointer: memory2 });\n", "\n", "const threadId2 = uuidv4();\n", "const config2 = { configurable: { thread_id: threadId2 } };\n", "\n", "const result3 = await app2.invoke(\n", "  { input: \"What is Task Decomposition?\" },\n", "  config2,\n", ")\n", "console.log(result3.answer);\n", "\n", "const result4 = await app2.invoke(\n", "  { input: \"What is one way of doing it?\" },\n", "  config2,\n", ")\n", "console.log(result4.answer);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Agents {#agents}\n", "\n", "Agents leverage the reasoning capabilities of LLMs to make decisions during execution. Using agents allow you to offload some discretion over the retrieval process. Although their behavior is less predictable than chains, they offer some advantages in this context:\n", "- Agents generate the input to the retriever directly, without necessarily needing us to explicitly build in contextualization, as we did above;\n", "- Agents can execute multiple retrieval steps in service of a query, or refrain from executing a retrieval step altogether (e.g., in response to a generic greeting from a user).\n", "\n", "### Retrieval tool\n", "\n", "Agents can access \"tools\" and manage their execution. In this case, we will convert our retriever into a LangChain tool to be wielded by the agent:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import { createRetrieverTool } from \"langchain/tools/retriever\";\n", "\n", "const tool =  createRetrieverTool(\n", "    retriever,\n", "    {\n", "      name: \"blog_post_retriever\",\n", "      description: \"Searches and returns excerpts from the Autonomous Agents blog post.\",\n", "    }\n", ")\n", "const tools = [tool]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Agent constructor\n", "\n", "Now that we have defined the tools and the LLM, we can create the agent. We will be using [LangGraph](https://langchain-ai.github.io/langgraphjs) to construct the agent. \n", "Currently we are using a high level interface to construct the agent, but the nice thing about LangGraph is that this high-level interface is backed by a low-level, highly controllable API in case you want to modify the agent logic."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["import { createReactAgent } from \"@langchain/langgraph/prebuilt\";\n", "\n", "const agentExecutor = createReactAgent({ llm, tools })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now try it out. Note that so far it is not stateful (we still need to add in memory)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  agent: {\n", "    messages: [\n", "      AIMessage {\n", "        \"id\": \"chatcmpl-AB7xlcJBGSKSp1GvgDY9FP8KvXxwB\",\n", "        \"content\": \"\",\n", "        \"additional_kwargs\": {\n", "          \"tool_calls\": [\n", "            {\n", "              \"id\": \"call_Ev0nA6nzGwOeMC5upJUUxTuw\",\n", "              \"type\": \"function\",\n", "              \"function\": \"[Object]\"\n", "            }\n", "          ]\n", "        },\n", "        \"response_metadata\": {\n", "          \"tokenUsage\": {\n", "            \"completionTokens\": 19,\n", "            \"promptTokens\": 66,\n", "            \"totalTokens\": 85\n", "          },\n", "          \"finish_reason\": \"tool_calls\",\n", "          \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "        },\n", "        \"tool_calls\": [\n", "          {\n", "            \"name\": \"blog_post_retriever\",\n", "            \"args\": {\n", "              \"query\": \"Task Decomposition\"\n", "            },\n", "            \"type\": \"tool_call\",\n", "            \"id\": \"call_Ev0nA6nzGwOeMC5upJUUxTuw\"\n", "          }\n", "        ],\n", "        \"invalid_tool_calls\": [],\n", "        \"usage_metadata\": {\n", "          \"input_tokens\": 66,\n", "          \"output_tokens\": 19,\n", "          \"total_tokens\": 85\n", "        }\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n", "{\n", "  tools: {\n", "    messages: [\n", "      ToolMessage {\n", "        \"content\": \"Fig. 1. Overview of a LLM-powered autonomous agent system.\\nComponent One: Planning#\\nA complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\\nTask Decomposition#\\nChain of thought (CoT; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\\nTree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\\n\\nTask decomposition can be done (1) by LLM with simple prompting like \\\"Steps for XYZ.\\\\n1.\\\", \\\"What are the subgoals for achieving XYZ?\\\", (2) by using task-specific instructions; e.g. \\\"Write a story outline.\\\" for writing a novel, or (3) with human inputs.\\nAnother quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain Definition Language (PDDL) as an intermediate interface to describe the planning problem. In this process, LLM (1) translates the problem into “Problem PDDL”, then (2) requests a classical planner to generate a PDDL plan based on an existing “Domain PDDL”, and finally (3) translates the PDDL plan back into natural language. Essentially, the planning step is outsourced to an external tool, assuming the availability of domain-specific PDDL and a suitable planner which is common in certain robotic setups but not in many other domains.\\nSelf-Reflection#\\n\\nAgent System Overview\\n                \\n                    Component One: Planning\\n                        \\n                \\n                    Task Decomposition\\n                \\n                    Self-Reflection\\n                \\n                \\n                    Component Two: Memory\\n                        \\n                \\n                    Types of Memory\\n                \\n                    Maximum Inner Product Search (MIPS)\\n                \\n                \\n                    Component Three: Tool Use\\n                \\n                    Case Studies\\n                        \\n                \\n                    Scientific Discovery Agent\\n                \\n                    Generative Agents Simulation\\n                \\n                    Proof-of-Concept Examples\\n                \\n                \\n                    Challenges\\n                \\n                    Citation\\n                \\n                    References\\n\\n(3) Task execution: Expert models execute on the specific tasks and log results.\\nInstruction:\\n\\nWith the input and the inference results, the AI assistant needs to describe the process and results. The previous stages can be formed as - User Input: {{ User Input }}, Task Planning: {{ Tasks }}, Model Selection: {{ Model Assignment }}, Task Execution: {{ Predictions }}. You must first answer the user's request in a straightforward manner. Then describe the task process and show your analysis and model inference results to the user in the first person. If inference results contain a file path, must tell the user the complete file path.\",\n", "        \"name\": \"blog_post_retriever\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {},\n", "        \"tool_call_id\": \"call_Ev0nA6nzGwOeMC5upJUUxTuw\"\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n", "{\n", "  agent: {\n", "    messages: [\n", "      AIMessage {\n", "        \"id\": \"chatcmpl-AB7xmiPNPbMX2KvZKHM2oPfcoFMnY\",\n", "        \"content\": \"**Task Decomposition** involves breaking down a complicated or large task into smaller, more manageable subtasks. Here are some insights based on current techniques and research:\\n\\n1. **Chain of Thought (CoT)**:\\n   - Introduced by <PERSON> et al. (2022), this technique prompts the model to \\\"think step by step\\\".\\n   - It helps decompose hard tasks into several simpler steps.\\n   - Enhances the interpretability of the model's thought process.\\n\\n2. **Tree of Thoughts (ToT)**:\\n   - An extension of CoT by <PERSON> et al. (2023).\\n   - Decomposes problems into multiple thought steps and generates several possibilities at each step.\\n   - Utilizes tree structures through BFS (Breadth-First Search) or DFS (Depth-First Search) with evaluation by a classifier or majority vote.\\n\\n3. **Methods of Task Decomposition**:\\n   - **Simple Prompting**: Asking the model directly, e.g., \\\"Steps for XYZ.\\\\n1.\\\" or \\\"What are the subgoals for achieving XYZ?\\\".\\n   - **Task-Specific Instructions**: Tailoring instructions to the task, such as \\\"Write a story outline\\\" for writing a novel.\\n   - **Human Inputs**: Receiving inputs from humans to refine the process.\\n\\n4. **LLM+P Approach**:\\n   - Suggested by <PERSON> et al. (2023), combines language models with an external classical planner.\\n   - Uses Planning Domain Definition Language (PDDL) for long-horizon planning:\\n     1. Translates the problem into a PDDL problem.\\n     2. Requests an external planner to generate a PDDL plan.\\n     3. Translates the PDDL plan back into natural language.\\n   - This method offloads the planning complexity to a specialized tool, especially relevant for domains utilizing robotic setups.\\n\\nTask Decomposition is a fundamental component of planning in autonomous agent systems, aiding in the efficient accomplishment of complex tasks by breaking them into smaller, actionable steps.\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {\n", "          \"tokenUsage\": {\n", "            \"completionTokens\": 411,\n", "            \"promptTokens\": 732,\n", "            \"totalTokens\": 1143\n", "          },\n", "          \"finish_reason\": \"stop\",\n", "          \"system_fingerprint\": \"fp_e375328146\"\n", "        },\n", "        \"tool_calls\": [],\n", "        \"invalid_tool_calls\": [],\n", "        \"usage_metadata\": {\n", "          \"input_tokens\": 732,\n", "          \"output_tokens\": 411,\n", "          \"total_tokens\": 1143\n", "        }\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n"]}], "source": ["const query = \"What is Task Decomposition?\"\n", "\n", "for await (const s of await agentExecutor.stream(\n", "  { messages: [{ role: \"user\", content: query }] },\n", ")){\n", "  console.log(s)\n", "  console.log(\"----\")\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["LangGraph comes with built in persistence, so we don't need to use `ChatMessageHistory`! Rather, we can pass in a checkpointer to our LangGraph agent directly.\n", "\n", "Distinct conversations are managed by specifying a key for a conversation thread in the config object, as shown below."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["import { MemorySaver } from \"@langchain/langgraph\";\n", "\n", "const memory3 = new MemorySaver();\n", "\n", "const agentExecutor2 = createReactAgent({ llm, tools, checkpointSaver: memory3 })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is all we need to construct a conversational RAG agent.\n", "\n", "Let's observe its behavior. Note that if we input a query that does not require a retrieval step, the agent does not execute one:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  agent: {\n", "    messages: [\n", "      AIMessage {\n", "        \"id\": \"chatcmpl-AB7y8P8AGHkxOwKpwMc3qj6r0skYr\",\n", "        \"content\": \"Hello, <PERSON>! How can I assist you today?\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {\n", "          \"tokenUsage\": {\n", "            \"completionTokens\": 12,\n", "            \"promptTokens\": 64,\n", "            \"totalTokens\": 76\n", "          },\n", "          \"finish_reason\": \"stop\",\n", "          \"system_fingerprint\": \"fp_e375328146\"\n", "        },\n", "        \"tool_calls\": [],\n", "        \"invalid_tool_calls\": [],\n", "        \"usage_metadata\": {\n", "          \"input_tokens\": 64,\n", "          \"output_tokens\": 12,\n", "          \"total_tokens\": 76\n", "        }\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n"]}], "source": ["const threadId3 = uuidv4();\n", "const config3 = { configurable: { thread_id: threadId3 } };\n", "\n", "for await (const s of await agentExecutor2.stream({ messages: [{ role: \"user\", content: \"Hi! I'm bob\" }] }, config3)) {\n", "  console.log(s)\n", "  console.log(\"----\")\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Further, if we input a query that does require a retrieval step, the agent generates the input to the tool:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  agent: {\n", "    messages: [\n", "      AIMessage {\n", "        \"id\": \"chatcmpl-AB7y8Do2IHJ2rnUvvMU3pTggmuZud\",\n", "        \"content\": \"\",\n", "        \"additional_kwargs\": {\n", "          \"tool_calls\": [\n", "            {\n", "              \"id\": \"call_3tSaOZ3xdKY4miIJdvBMR80V\",\n", "              \"type\": \"function\",\n", "              \"function\": \"[Object]\"\n", "            }\n", "          ]\n", "        },\n", "        \"response_metadata\": {\n", "          \"tokenUsage\": {\n", "            \"completionTokens\": 19,\n", "            \"promptTokens\": 89,\n", "            \"totalTokens\": 108\n", "          },\n", "          \"finish_reason\": \"tool_calls\",\n", "          \"system_fingerprint\": \"fp_e375328146\"\n", "        },\n", "        \"tool_calls\": [\n", "          {\n", "            \"name\": \"blog_post_retriever\",\n", "            \"args\": {\n", "              \"query\": \"Task Decomposition\"\n", "            },\n", "            \"type\": \"tool_call\",\n", "            \"id\": \"call_3tSaOZ3xdKY4miIJdvBMR80V\"\n", "          }\n", "        ],\n", "        \"invalid_tool_calls\": [],\n", "        \"usage_metadata\": {\n", "          \"input_tokens\": 89,\n", "          \"output_tokens\": 19,\n", "          \"total_tokens\": 108\n", "        }\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n", "{\n", "  tools: {\n", "    messages: [\n", "      ToolMessage {\n", "        \"content\": \"Fig. 1. Overview of a LLM-powered autonomous agent system.\\nComponent One: Planning#\\nA complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\\nTask Decomposition#\\nChain of thought (CoT; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\\nTree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\\n\\nTask decomposition can be done (1) by LLM with simple prompting like \\\"Steps for XYZ.\\\\n1.\\\", \\\"What are the subgoals for achieving XYZ?\\\", (2) by using task-specific instructions; e.g. \\\"Write a story outline.\\\" for writing a novel, or (3) with human inputs.\\nAnother quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain Definition Language (PDDL) as an intermediate interface to describe the planning problem. In this process, LLM (1) translates the problem into “Problem PDDL”, then (2) requests a classical planner to generate a PDDL plan based on an existing “Domain PDDL”, and finally (3) translates the PDDL plan back into natural language. Essentially, the planning step is outsourced to an external tool, assuming the availability of domain-specific PDDL and a suitable planner which is common in certain robotic setups but not in many other domains.\\nSelf-Reflection#\\n\\nAgent System Overview\\n                \\n                    Component One: Planning\\n                        \\n                \\n                    Task Decomposition\\n                \\n                    Self-Reflection\\n                \\n                \\n                    Component Two: Memory\\n                        \\n                \\n                    Types of Memory\\n                \\n                    Maximum Inner Product Search (MIPS)\\n                \\n                \\n                    Component Three: Tool Use\\n                \\n                    Case Studies\\n                        \\n                \\n                    Scientific Discovery Agent\\n                \\n                    Generative Agents Simulation\\n                \\n                    Proof-of-Concept Examples\\n                \\n                \\n                    Challenges\\n                \\n                    Citation\\n                \\n                    References\\n\\n(3) Task execution: Expert models execute on the specific tasks and log results.\\nInstruction:\\n\\nWith the input and the inference results, the AI assistant needs to describe the process and results. The previous stages can be formed as - User Input: {{ User Input }}, Task Planning: {{ Tasks }}, Model Selection: {{ Model Assignment }}, Task Execution: {{ Predictions }}. You must first answer the user's request in a straightforward manner. Then describe the task process and show your analysis and model inference results to the user in the first person. If inference results contain a file path, must tell the user the complete file path.\",\n", "        \"name\": \"blog_post_retriever\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {},\n", "        \"tool_call_id\": \"call_3tSaOZ3xdKY4miIJdvBMR80V\"\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n", "{\n", "  agent: {\n", "    messages: [\n", "      AIMessage {\n", "        \"id\": \"chatcmpl-AB7y9tpoTvM3lsrhoxCWkkerk9fb2\",\n", "        \"content\": \"Task decomposition is a methodology used to break down complex tasks into smaller, more manageable steps. Here’s an overview of various approaches to task decomposition:\\n\\n1. **Chain of Thought (CoT)**: This technique prompts a model to \\\"think step by step,\\\" which aids in transforming big tasks into multiple smaller tasks. This method enhances the model’s performance on complex tasks by making the problem more manageable and interpretable.\\n\\n2. **Tree of Thoughts (ToT)**: An extension of Chain of Thought, this approach explores multiple reasoning possibilities at each step, effectively creating a tree structure. The search process can be carried out using Breadth-First Search (BFS) or Depth-First Search (DFS), with each state evaluated by either a classifier or a majority vote.\\n\\n3. **Simple Prompting**: Involves straightforward instructions to decompose a task, such as starting with \\\"Steps for XYZ. 1.\\\" or asking \\\"What are the subgoals for achieving XYZ?\\\". This can also include task-specific instructions like \\\"Write a story outline\\\" for writing a novel.\\n\\n4. **LLM+P**: Combines Large Language Models (LLMs) with an external classical planner. The problem is translated into a Planning Domain Definition Language (PDDL) format, an external planner generates a plan, and then the plan is translated back into natural language. This approach highlights a synergy between modern AI techniques and traditional planning strategies.\\n\\nThese approaches allow complex problems to be approached and solved more efficiently by focusing on manageable sub-tasks.\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {\n", "          \"tokenUsage\": {\n", "            \"completionTokens\": 311,\n", "            \"promptTokens\": 755,\n", "            \"totalTokens\": 1066\n", "          },\n", "          \"finish_reason\": \"stop\",\n", "          \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "        },\n", "        \"tool_calls\": [],\n", "        \"invalid_tool_calls\": [],\n", "        \"usage_metadata\": {\n", "          \"input_tokens\": 755,\n", "          \"output_tokens\": 311,\n", "          \"total_tokens\": 1066\n", "        }\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n"]}], "source": ["const query2 = \"What is Task Decomposition?\"\n", "\n", "for await (const s of await agentExecutor2.stream({ messages: [{ role: \"user\", content: query2 }] }, config3)) {\n", "  console.log(s)\n", "  console.log(\"----\")\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Above, instead of inserting our query verbatim into the tool, the agent stripped unnecessary words like \"what\" and \"is\".\n", "\n", "This same principle allows the agent to use the context of the conversation when necessary:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  agent: {\n", "    messages: [\n", "      AIMessage {\n", "        \"id\": \"chatcmpl-AB7yDE4rCOXTPZ3595GknUgVzASmt\",\n", "        \"content\": \"\",\n", "        \"additional_kwargs\": {\n", "          \"tool_calls\": [\n", "            {\n", "              \"id\": \"call_cWnDZq2aloVtMB4KjZlTxHmZ\",\n", "              \"type\": \"function\",\n", "              \"function\": \"[Object]\"\n", "            }\n", "          ]\n", "        },\n", "        \"response_metadata\": {\n", "          \"tokenUsage\": {\n", "            \"completionTokens\": 21,\n", "            \"promptTokens\": 1089,\n", "            \"totalTokens\": 1110\n", "          },\n", "          \"finish_reason\": \"tool_calls\",\n", "          \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "        },\n", "        \"tool_calls\": [\n", "          {\n", "            \"name\": \"blog_post_retriever\",\n", "            \"args\": {\n", "              \"query\": \"common ways of task decomposition\"\n", "            },\n", "            \"type\": \"tool_call\",\n", "            \"id\": \"call_cWnDZq2aloVtMB4KjZlTxHmZ\"\n", "          }\n", "        ],\n", "        \"invalid_tool_calls\": [],\n", "        \"usage_metadata\": {\n", "          \"input_tokens\": 1089,\n", "          \"output_tokens\": 21,\n", "          \"total_tokens\": 1110\n", "        }\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n", "{\n", "  tools: {\n", "    messages: [\n", "      ToolMessage {\n", "        \"content\": \"Fig. 1. Overview of a LLM-powered autonomous agent system.\\nComponent One: Planning#\\nA complicated task usually involves many steps. An agent needs to know what they are and plan ahead.\\nTask Decomposition#\\nChain of thought (CoT; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.\\nTree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.\\n\\nTask decomposition can be done (1) by LLM with simple prompting like \\\"Steps for XYZ.\\\\n1.\\\", \\\"What are the subgoals for achieving XYZ?\\\", (2) by using task-specific instructions; e.g. \\\"Write a story outline.\\\" for writing a novel, or (3) with human inputs.\\nAnother quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain Definition Language (PDDL) as an intermediate interface to describe the planning problem. In this process, LLM (1) translates the problem into “Problem PDDL”, then (2) requests a classical planner to generate a PDDL plan based on an existing “Domain PDDL”, and finally (3) translates the PDDL plan back into natural language. Essentially, the planning step is outsourced to an external tool, assuming the availability of domain-specific PDDL and a suitable planner which is common in certain robotic setups but not in many other domains.\\nSelf-Reflection#\\n\\nAgent System Overview\\n                \\n                    Component One: Planning\\n                        \\n                \\n                    Task Decomposition\\n                \\n                    Self-Reflection\\n                \\n                \\n                    Component Two: Memory\\n                        \\n                \\n                    Types of Memory\\n                \\n                    Maximum Inner Product Search (MIPS)\\n                \\n                \\n                    Component Three: Tool Use\\n                \\n                    Case Studies\\n                        \\n                \\n                    Scientific Discovery Agent\\n                \\n                    Generative Agents Simulation\\n                \\n                    Proof-of-Concept Examples\\n                \\n                \\n                    Challenges\\n                \\n                    Citation\\n                \\n                    References\\n\\nResources:\\n1. Internet access for searches and information gathering.\\n2. Long Term memory management.\\n3. GPT-3.5 powered Agents for delegation of simple tasks.\\n4. File output.\\n\\nPerformance Evaluation:\\n1. Continuously review and analyze your actions to ensure you are performing to the best of your abilities.\\n2. Constructively self-criticize your big-picture behavior constantly.\\n3. Reflect on past decisions and strategies to refine your approach.\\n4. Every command has a cost, so be smart and efficient. Aim to complete tasks in the least number of steps.\",\n", "        \"name\": \"blog_post_retriever\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {},\n", "        \"tool_call_id\": \"call_cWnDZq2aloVtMB4KjZlTxHmZ\"\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n", "{\n", "  agent: {\n", "    messages: [\n", "      AIMessage {\n", "        \"id\": \"chatcmpl-AB7yGASxz0Z0g2jiCxwx4gYHYJTi4\",\n", "        \"content\": \"According to the blog post, there are several common methods of task decomposition:\\n\\n1. **Simple Prompting by LLMs**: This involves straightforward instructions to decompose a task. Examples include:\\n   - \\\"Steps for XYZ. 1.\\\"\\n   - \\\"What are the subgoals for achieving XYZ?\\\"\\n   - Task-specific instructions like \\\"Write a story outline\\\" for writing a novel.\\n\\n2. **Human Inputs**: Decomposition can be guided by human insights and instructions.\\n\\n3. **Chain of Thought (CoT)**: This technique prompts a model to think step-by-step, enabling it to break down complex tasks into smaller, more manageable tasks. CoT has become a standard method to enhance model performance on intricate tasks.\\n\\n4. **Tree of Thoughts (ToT)**: An extension of CoT, this approach decomposes the problem into multiple thought steps and generates several thoughts per step, forming a tree structure. The search process can be performed using Breadth-First Search (BFS) or Depth-First Search (DFS), with each state evaluated by a classifier or through a majority vote.\\n\\n5. **LLM+P (Large Language Model plus Planner)**: This method integrates LLMs with an external classical planner. It involves:\\n   - Translating the problem into “Problem PDDL” (Planning Domain Definition Language).\\n   - Using an external planner to generate a PDDL plan based on an existing “Domain PDDL”.\\n   - Translating the PDDL plan back into natural language.\\n  \\nBy utilizing these methods, tasks can be effectively decomposed into more manageable parts, allowing for more efficient problem-solving and planning.\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {\n", "          \"tokenUsage\": {\n", "            \"completionTokens\": 334,\n", "            \"promptTokens\": 1746,\n", "            \"totalTokens\": 2080\n", "          },\n", "          \"finish_reason\": \"stop\",\n", "          \"system_fingerprint\": \"fp_52a7f40b0b\"\n", "        },\n", "        \"tool_calls\": [],\n", "        \"invalid_tool_calls\": [],\n", "        \"usage_metadata\": {\n", "          \"input_tokens\": 1746,\n", "          \"output_tokens\": 334,\n", "          \"total_tokens\": 2080\n", "        }\n", "      }\n", "    ]\n", "  }\n", "}\n", "----\n"]}], "source": ["const query3 = \"What according to the blog post are common ways of doing it? redo the search\"\n", "\n", "for await (const s of await agentExecutor2.stream({ messages: [{ role: \"user\", content: query3 }] }, config3)) {\n", "  console.log(s)\n", "  console.log(\"----\")\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the agent was able to infer that \"it\" in our query refers to \"task decomposition\", and generated a reasonable search query as a result-- in this case, \"common ways of task decomposition\"."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Tying it together\n", "\n", "For convenience, we tie together all of the necessary steps in a single code cell:"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import { createRetrieverTool } from \"langchain/tools/retriever\";\n", "import { createReactAgent } from \"@langchain/langgraph/prebuilt\";\n", "import { MemorySaver } from \"@langchain/langgraph\";\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "import { RecursiveCharacterTextSplitter } from \"langchain/text_splitter\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\"\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "const llm3 = new ChatOpenAI({ model: \"gpt-4o\" });\n", "\n", "const loader3 = new CheerioWebBaseLoader(\n", "  \"https://lilianweng.github.io/posts/2023-06-23-agent/\"\n", ");\n", "\n", "const docs3 = await loader3.load();\n", "\n", "const textSplitter3 = new RecursiveCharacterTextSplitter({ chunkSize: 1000, chunkOverlap: 200 });\n", "const splits3 = await textSplitter3.splitDocuments(docs3);\n", "const vectorStore3 = await MemoryVectorStore.fromDocuments(splits3, new OpenAIEmbeddings());\n", "\n", "// Retrieve and generate using the relevant snippets of the blog.\n", "const retriever3 = vectorStore3.asRetriever();\n", "\n", "const tool2 = createRetrieverTool(\n", "    retriever3,\n", "    {\n", "      name: \"blog_post_retriever\",\n", "      description: \"Searches and returns excerpts from the Autonomous Agents blog post.\",\n", "    }\n", ")\n", "const tools2 = [tool2]\n", "const memory4 = new MemorySaver();\n", "\n", "const agentExecutor3 = createReactAgent({ llm: llm3, tools: tools2, checkpointSaver: memory4 })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "We've covered the steps to build a basic conversational Q&A application:\n", "\n", "- We used chains to build a predictable application that generates search queries for each user input;\n", "- We used agents to build an application that \"decides\" when and how to generate search queries.\n", "\n", "To explore different types of retrievers and retrieval strategies, visit the [retrievers](/docs/how_to#retrievers) section of the how-to guides.\n", "\n", "For a detailed walkthrough of <PERSON><PERSON><PERSON><PERSON>'s conversation memory abstractions, visit the [How to add message history (memory)](/docs/how_to/message_history) LCEL page.\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}