{"cells": [{"cell_type": "raw", "id": "0e77c293-4049-43be-ba49-ff9daeefeee7", "metadata": {}, "source": ["---\n", "sidebar_position: 4\n", "---"]}, {"cell_type": "markdown", "id": "14d3fd06", "metadata": {}, "source": ["# How to do per-user retrieval\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following:\n", "\n", "- [Retrieval-augmented generation](/docs/tutorials/rag/)\n", "\n", ":::\n", "\n", "When building a retrieval app, you often have to build it with multiple users in\n", "mind. This means that you may be storing data not just for one user, but for\n", "many different users, and they should not be able to see each other's data. This\n", "means that you need to be able to configure your retrieval chain to only\n", "retrieve certain information. This generally involves two steps.\n", "\n", "**Step 1: Make sure the retriever you are using supports multiple users**\n", "\n", "At the moment, there is no unified flag or filter for this in LangChain. Rather,\n", "each vectorstore and retriever may have their own, and may be called different\n", "things (namespaces, multi-tenancy, etc). For vectorstores, this is generally\n", "exposed as a keyword argument that is passed in during `similaritySearch`. By\n", "reading the documentation or source code, figure out whether the retriever you\n", "are using supports multiple users, and, if so, how to use it.\n", "\n", "**Step 2: Add that parameter as a configurable field for the chain**\n", "\n", "The LangChain `config` object is passed through to every Run<PERSON>ble. Here you can\n", "add any fields you'd like to the `configurable` object. Later, inside the chain\n", "we can extract these fields.\n", "\n", "**Step 3: Call the chain with that configurable field**\n", "\n", "Now, at runtime you can call this chain with configurable field.\n", "\n", "## Code Example\n", "\n", "Let's see a concrete example of what this looks like in code. We will use\n", "Pinecone for this example."]}, {"cell_type": "markdown", "id": "c8ccbef7", "metadata": {}, "source": ["## Setup\n", "\n", "### Install dependencies\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/pinecone @langchain/openai @langchain/core @pinecone-database/pinecone\n", "</Npm2Yarn>\n", "```\n", "\n", "### Set environment variables\n", "\n", "We'll use OpenAI and Pinecone in this example:\n", "\n", "```env\n", "OPENAI_API_KEY=your-api-key\n", "\n", "PINECONE_API_KEY=your-api-key\n", "PINECONE_INDEX=your-index-name\n", "\n", "# Optional, use <PERSON><PERSON><PERSON> for best-in-class observability\n", "LANGSMITH_API_KEY=your-api-key\n", "LANGSMITH_TRACING=true\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "7345de3c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[ \u001b[32m\"77b8f174-9d89-4c6c-b2ab-607fe3913b2d\"\u001b[39m ]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "import { PineconeStore } from \"@langchain/pinecone\";\n", "import { Pinecone } from \"@pinecone-database/pinecone\";\n", "import { Document } from \"@langchain/core/documents\";\n", "\n", "const embeddings = new OpenAIEmbeddings();\n", "\n", "const pinecone = new Pinecone();\n", "\n", "const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX);\n", "\n", "/**\n", " * Pinecone allows you to partition the records in an index into namespaces. \n", " * Queries and other operations are then limited to one namespace, \n", " * so different requests can search different subsets of your index.\n", " * Read more about namespaces here: https://docs.pinecone.io/guides/indexes/use-namespaces\n", " * \n", " * NOTE: If you have namespace enabled in your Pinecone index, you must provide the namespace when creating the PineconeStore.\n", " */\n", "const namespace = \"pinecone\";\n", "\n", "const vectorStore = await PineconeStore.fromExistingIndex(\n", "  new OpenAIEmbeddings(),\n", "  { pineconeIndex, namespace },\n", ");\n", "\n", "await vectorStore.addDocuments(\n", "  [new Document({ pageContent: \"i worked at kensho\" })],\n", "  { namespace: \"harrison\" },\n", ");\n", "\n", "await vectorStore.addDocuments(\n", "  [new Document({ pageContent: \"i worked at facebook\" })],\n", "  { namespace: \"ankush\" },\n", ");"]}, {"cell_type": "markdown", "id": "39c11920", "metadata": {}, "source": ["The pinecone kwarg for `namespace` can be used to separate documents"]}, {"cell_type": "code", "execution_count": 2, "id": "3c2a39fa", "metadata": {}, "outputs": [{"data": {"text/plain": ["[ Document { pageContent: \u001b[32m\"i worked at facebook\"\u001b[39m, metadata: {} } ]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["// This will only get documents for Ankush\n", "const ankushRetriever = vectorStore.asRetriever({\n", "  filter: {\n", "    namespace: \"ankush\",\n", "  },\n", "});\n", "\n", "await ankushRetriever.invoke(\n", "  \"where did i work?\",\n", ");"]}, {"cell_type": "code", "execution_count": 3, "id": "56393baa", "metadata": {}, "outputs": [{"data": {"text/plain": ["[ Document { pageContent: \u001b[32m\"i worked at kensho\"\u001b[39m, metadata: {} } ]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["// This will only get documents for <PERSON>\n", "const harrisonRetriever = vectorStore.asRetriever({\n", "  filter: {\n", "    namespace: \"harrison\",\n", "  },\n", "});\n", "\n", "await harrisonRetriever.invoke(\n", "  \"where did i work?\",\n", ");"]}, {"cell_type": "markdown", "id": "88ae97ed", "metadata": {}, "source": ["We can now create the chain that we will use to perform question-answering."]}, {"cell_type": "code", "execution_count": 4, "id": "44a865f6", "metadata": {}, "outputs": [], "source": ["import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import {\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "  RunnablePassthrough,\n", "} from \"@langchain/core/runnables\";\n", "import { ChatOpenAI, OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "const template = `Answer the question based only on the following context:\n", "{context}\n", "Question: {question}`;\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(template);\n", "\n", "const model = new ChatOpenAI({\n", "  model: \"gpt-3.5-turbo-0125\",\n", "  temperature: 0,\n", "});"]}, {"cell_type": "markdown", "id": "2d481b70", "metadata": {}, "source": ["We can now create the chain using our configurable retriever. It is configurable\n", "because we can define any object which will be passed to the chain. From there,\n", "we extract the configurable object and pass it to the vectorstore."]}, {"cell_type": "code", "execution_count": 8, "id": "210b0446", "metadata": {}, "outputs": [], "source": ["import { RunnablePassthrough, RunnableSequence } from \"@langchain/core/runnables\";\n", "\n", "const chain = RunnableSequence.from([\n", "  RunnablePassthrough.assign({\n", "    context: async (input: { question: string }, config) => {\n", "      if (!config || !(\"configurable\" in config)) {\n", "        throw new Error(\"No config\");\n", "      }\n", "      const { configurable } = config;\n", "      const documents = await vectorStore.asRetriever(configurable).invoke(\n", "        input.question,\n", "        config,\n", "      );\n", "      return documents.map((doc) => doc.pageContent).join(\"\\n\\n\");\n", "    },\n", "  }),\n", "  prompt,\n", "  model,\n", "  new StringOutputParser(),\n", "]);"]}, {"cell_type": "markdown", "id": "7f6458c3", "metadata": {}, "source": ["We can now invoke the chain with configurable options. `search_kwargs` is the id\n", "of the configurable field. The value is the search kwargs to use for Pinecone"]}, {"cell_type": "code", "execution_count": 9, "id": "a38037b2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"The user worked at Kensho.\"\u001b[39m"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["await chain.invoke(\n", "  { question: \"where did the user work?\"},\n", "  { configurable: { filter: { namespace: \"harrison\" } } },\n", ");"]}, {"cell_type": "code", "execution_count": 11, "id": "0ff4f5f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"The user worked at Facebook.\"\u001b[39m"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["await chain.invoke(\n", "  { question: \"where did the user work?\"},\n", "  { configurable: { filter: { namespace: \"ankush\" } } },\n", ");"]}, {"cell_type": "markdown", "id": "7fb27b941602401d91542211134fc71a", "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}, "source": ["For more vector store implementations that can support multiple users, please refer to specific\n", "pages, such as [Milvus](/docs/integrations/vectorstores/milvus).\n", "\n", "## Next steps\n", "\n", "You've now seen one approach for supporting retrieval with data from multiple users.\n", "\n", "Next, check out some of the other how-to guides on RAG, such as [returning sources](/docs/how_to/qa_sources)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}