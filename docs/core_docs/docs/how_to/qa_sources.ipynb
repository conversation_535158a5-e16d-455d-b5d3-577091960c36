{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to return sources\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following:\n", "\n", "- [Retrieval-augmented generation](/docs/tutorials/rag/)\n", "\n", ":::\n", "\n", "Often in Q&A applications it’s important to show users the sources that were used to generate the answer. The simplest way to do this is for the chain to return the Documents that were retrieved in each generation.\n", "\n", "We'll be using the [LLM Powered Autonomous Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post by <PERSON><PERSON> for retrieval content this notebook."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "### Dependencies\n", "\n", "We’ll use an OpenAI chat model and embeddings and a Memory vector store in this walkthrough, but everything shown here works with any [ChatModel](/docs/concepts/chat_models) or [LLM](/docs/concepts/text_llms), [Embeddings](/docs/concepts/embedding_models), and [VectorStore](/docs/concepts/vectorstores) or [Retriever](/docs/concepts/retrievers).\n", "\n", "We’ll use the following packages:\n", "\n", "```bash\n", "npm install --save langchain @langchain/openai cheerio\n", "```\n", "\n", "We need to set environment variable `OPENAI_API_KEY`:\n", "\n", "```bash\n", "export OPENAI_API_KEY=YOUR_KEY\n", "```\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls. As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent. The best way to do this is with [LangSmith](https://smith.langchain.com/).\n", "\n", "Note that Lang<PERSON>mith is not needed, but it is helpful. If you do want to use LangSmith, after you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "\n", "```bash\n", "export LANGSMITH_TRACING=true\n", "export LANGSMITH_API_KEY=YOUR_KEY\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# export LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chain without sources\n", "\n", "Here is the Q&A app we built over the [LLM Powered Autonomous Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post by <PERSON><PERSON> in the [Quickstart](/docs/tutorials/qa_chat_history/)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import \"cheerio\";\n", "import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "import { RecursiveCharacterTextSplitter } from \"langchain/text_splitter\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\"\n", "import { OpenAIEmbeddings, ChatOpenAI } from \"@langchain/openai\";\n", "import { pull } from \"langchain/hub\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { formatDocumentsAsString } from \"langchain/util/document\";\n", "import { RunnableSequence, RunnablePassthrough } from \"@langchain/core/runnables\";\n", "import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "\n", "const loader = new CheerioWebBaseLoader(\n", "  \"https://lilianweng.github.io/posts/2023-06-23-agent/\"\n", ");\n", "\n", "const docs = await loader.load();\n", "\n", "const textSplitter = new RecursiveCharacterTextSplitter({ chunkSize: 1000, chunkOverlap: 200 });\n", "const splits = await textSplitter.splitDocuments(docs);\n", "const vectorStore = await MemoryVectorStore.fromDocuments(splits, new OpenAIEmbeddings());\n", "\n", "// Retrieve and generate using the relevant snippets of the blog.\n", "const retriever = vectorStore.asRetriever();\n", "const prompt = await pull<ChatPromptTemplate>(\"rlm/rag-prompt\");\n", "const llm = new ChatOpenAI({ model: \"gpt-3.5-turbo\", temperature: 0 });\n", "\n", "const ragChain = RunnableSequence.from([\n", "  {\n", "    context: retriever.pipe(formatDocumentsAsString),\n", "    question: new RunnablePassthrough(),\n", "  },\n", "  prompt,\n", "  llm,\n", "  new StringOutputParser()\n", "]);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see what this prompt actually looks like:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\n", "Question: {question} \n", "Context: {context} \n", "Answer:\n"]}], "source": ["console.log(prompt.promptMessages.map((msg) => msg.prompt.template).join(\"\\n\"));"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"Task decomposition is a technique used to break down complex tasks into smaller and simpler steps. T\"\u001b[39m... 254 more characters"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["await rag<PERSON><PERSON><PERSON>.invoke(\"What is task decomposition?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding sources\n", "\n", "With LCEL, we can easily pass the retrieved documents through the chain and return them in the final response:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  question: \u001b[32m\"What is Task Decomposition\"\u001b[39m,\n", "  context: [\n", "    Document {\n", "      pageContent: \u001b[32m\"Fig. 1. Overview of a LLM-powered autonomous agent system.\\n\"\u001b[39m +\n", "        \u001b[32m\"Component One: Planning#\\n\"\u001b[39m +\n", "        \u001b[32m\"A complicated ta\"\u001b[39m... 898 more characters,\n", "      metadata: {\n", "        source: \u001b[32m\"https://lilianweng.github.io/posts/2023-06-23-agent/\"\u001b[39m,\n", "        loc: { lines: \u001b[36m[Object]\u001b[39m }\n", "      }\n", "    },\n", "    Document {\n", "      pageContent: \u001b[32m'Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\\\n1.\", \"What are'\u001b[39m... 887 more characters,\n", "      metadata: {\n", "        source: \u001b[32m\"https://lilianweng.github.io/posts/2023-06-23-agent/\"\u001b[39m,\n", "        loc: { lines: \u001b[36m[Object]\u001b[39m }\n", "      }\n", "    },\n", "    Document {\n", "      pageContent: \u001b[32m\"Agent System Overview\\n\"\u001b[39m +\n", "        \u001b[32m\"                \\n\"\u001b[39m +\n", "        \u001b[32m\"                    Component One: Planning\\n\"\u001b[39m +\n", "        \u001b[32m\"                 \"\u001b[39m... 850 more characters,\n", "      metadata: {\n", "        source: \u001b[32m\"https://lilianweng.github.io/posts/2023-06-23-agent/\"\u001b[39m,\n", "        loc: { lines: \u001b[36m[Object]\u001b[39m }\n", "      }\n", "    },\n", "    Document {\n", "      pageContent: \u001b[32m\"Resources:\\n\"\u001b[39m +\n", "        \u001b[32m\"1. Internet access for searches and information gathering.\\n\"\u001b[39m +\n", "        \u001b[32m\"2. Long Term memory management\"\u001b[39m... 456 more characters,\n", "      metadata: {\n", "        source: \u001b[32m\"https://lilianweng.github.io/posts/2023-06-23-agent/\"\u001b[39m,\n", "        loc: { lines: \u001b[36m[Object]\u001b[39m }\n", "      }\n", "    }\n", "  ],\n", "  answer: \u001b[32m\"Task decomposition is a technique used to break down complex tasks into smaller and simpler steps fo\"\u001b[39m... 230 more characters\n", "}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import {\n", "  RunnableMap,\n", "  RunnablePassthrough,\n", "  RunnableSequence\n", "} from \"@langchain/core/runnables\";\n", "import { formatDocumentsAsString } from \"langchain/util/document\";\n", "\n", "const ragChainWithSources = RunnableMap.from({\n", "  // Return raw documents here for now since we want to return them at\n", "  // the end - we'll format in the next step of the chain\n", "  context: retriever,\n", "  question: new RunnablePassthrough(),\n", "}).assign({\n", "  answer: RunnableSequence.from([\n", "    (input) => {\n", "      return {\n", "        // Now we format the documents as strings for the prompt\n", "        context: formatDocumentsAsString(input.context),\n", "        question: input.question\n", "      };\n", "    },\n", "    prompt,\n", "    llm,\n", "    new StringOutputParser()\n", "  ]),\n", "})\n", "\n", "await ragChainWithSources.invoke(\"What is Task Decomposition\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Check out the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/c3753531-563c-40d4-a6bf-21bfe8741d10/r) here to see the internals of the chain.\n", "\n", "## Next steps\n", "\n", "You've now learned how to return sources from your QA chains.\n", "\n", "Next, check out some of the other guides around RAG, such as [how to stream responses](/docs/how_to/qa_streaming)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 2}