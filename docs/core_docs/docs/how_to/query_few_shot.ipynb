{"cells": [{"cell_type": "markdown", "id": "f2195672-0cab-4967-ba8a-c6544635547d", "metadata": {}, "source": ["# How to add examples to the prompt\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following:\n", "\n", "- [Query analysis](/docs/tutorials/rag#query-analysis)\n", "\n", ":::\n", "\n", "As our query analysis becomes more complex, the LLM may struggle to understand how exactly it should respond in certain scenarios. In order to improve performance here, we can add examples to the prompt to guide the LLM.\n", "\n", "Let's take a look at how we can add examples for the LangChain YouTube video query analyzer we built in the [query analysis tutorial](/docs/tutorials/rag#query-analysis)."]}, {"cell_type": "markdown", "id": "a4079b57-4369-49c9-b2ad-c809b5408d7e", "metadata": {}, "source": ["## Setup\n", "\n", "### Install dependencies\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/core zod uuid\n", "</Npm2Yarn>\n", "```\n", "\n", "### Set environment variables\n", "\n", "```\n", "# Optional, use <PERSON><PERSON><PERSON> for best-in-class observability\n", "LANGSMITH_API_KEY=your-api-key\n", "LANGSMITH_TRACING=true\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "87ee608e", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from '@langchain/openai';\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "markdown", "id": "57396e23-c192-4d97-846b-5eacea4d6b8d", "metadata": {}, "source": ["## Query schema\n", "\n", "We'll define a query schema that we want our model to output. To make our query analysis a bit more interesting, we'll add a `subQueries` field that contains more narrow questions derived from the top level question."]}, {"cell_type": "code", "execution_count": 21, "id": "0b51dd76-820d-41a4-98c8-893f6fe0d1ea", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "\n", "const subQueriesDescription = `\n", "If the original question contains multiple distinct sub-questions,\n", "or if there are more generic questions that would be helpful to answer in\n", "order to answer the original question, write a list of all relevant sub-questions.\n", "Make sure this list is comprehensive and covers all parts of the original question.\n", "It's ok if there's redundancy in the sub-questions, it's better to cover all the bases than to miss some.\n", "Make sure the sub-questions are as narrowly focused as possible in order to get the most relevant results.`\n", "\n", "const searchSchema = z.object({\n", "    query: z.string().describe(\"Primary similarity search query applied to video transcripts.\"),\n", "    subQueries: z.array(z.string()).optional().describe(subQueriesDescription),\n", "    publishYear: z.number().optional().describe(\"Year video was published\")\n", "})"]}, {"cell_type": "markdown", "id": "f8b08c52-1ce9-4d8b-a779-cbe8efde51d1", "metadata": {}, "source": ["## Query generation"]}, {"cell_type": "markdown", "id": "86f5ee5d", "metadata": {}, "source": ["```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "783c03c3-8c72-4f88-9cf4-5829ce6745d6", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "import { RunnablePassthrough, RunnableSequence } from \"@langchain/core/runnables\"\n", "\n", "const system = `You are an expert at converting user questions into database queries.\n", "You have access to a database of tutorial videos about a software library for building LLM-powered applications.\n", "Given a question, return a list of database queries optimized to retrieve the most relevant results.\n", "\n", "If there are acronyms or words you are not familiar with, do not try to rephrase them.`\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "[\n", "    [\"system\", system],\n", "    [\"placeholder\", \"{examples}\"],\n", "    [\"human\", \"{question}\"],\n", "]\n", ")\n", "const llmWithTools = llm.withStructuredOutput(searchSchema, {\n", "  name: \"<PERSON>\",\n", "})\n", "const queryAnalyzer = RunnableSequence.from([\n", "  {\n", "    question: new RunnablePassthrough(),\n", "  },\n", "  prompt,\n", "  llmWithTools\n", "]);"]}, {"cell_type": "markdown", "id": "f403517a-b8e3-44ac-b0a6-02f8305635a2", "metadata": {}, "source": ["Let's try out our query analyzer without any examples in the prompt:"]}, {"cell_type": "code", "execution_count": 34, "id": "0bcfce06-6f0c-4f9d-a1fc-dc29342d2aae", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  query: \u001b[32m\"difference between Web Voyager and Reflection Agents\"\u001b[39m,\n", "  subQueries: [ \u001b[32m\"Do Web Voyager and Reflection Agents use LangGraph?\"\u001b[39m ]\n", "}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["await queryAnalyzer.invoke(\n", "  \"what's the difference between web voyager and reflection agents? do both use langgraph?\"\n", ")"]}, {"cell_type": "markdown", "id": "00962b08-899c-465c-9a41-6459b207e0f2", "metadata": {}, "source": ["## Adding examples and tuning the prompt\n", "\n", "This works pretty well, but we probably want it to decompose the question even further to separate the queries about Web Voyager and Reflection Agents.\n", "\n", "To tune our query generation results, we can add some examples of inputs questions and gold standard output queries to our prompt."]}, {"cell_type": "code", "execution_count": 35, "id": "15b4923d-a08e-452d-8889-9a09a57d1095", "metadata": {}, "outputs": [], "source": ["const examples = []"]}, {"cell_type": "code", "execution_count": 36, "id": "da5330e6-827a-40e5-982b-b23b6286b758", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[33m1\u001b[39m"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["const question = \"What's chat langchain, is it a langchain template?\"\n", "const query = {\n", "  query: \"What is chat langchain and is it a langchain template?\",\n", "  subQueries: [\"What is chat langchain\", \"What is a langchain template\"],\n", "}\n", "examples.push({ \"input\": question, \"toolCalls\": [query] })"]}, {"cell_type": "code", "execution_count": 37, "id": "580e857a-27df-4ecf-a19c-458dc9244ec8", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[33m2\u001b[39m"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["const question2 = \"How to build multi-agent system and stream intermediate steps from it\"\n", "const query2 = {\n", "  query: \"How to build multi-agent system and stream intermediate steps from it\",\n", "  subQueries: [\n", "    \"How to build multi-agent system\",\n", "    \"How to stream intermediate steps from multi-agent system\",\n", "    \"How to stream intermediate steps\",\n", "  ],\n", "}\n", "\n", "examples.push({ \"input\": question2, \"toolCalls\": [query2] })"]}, {"cell_type": "code", "execution_count": 38, "id": "fa63310d-69e3-4701-825c-fbb01f8a5a16", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[33m3\u001b[39m"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["const question3 = \"LangChain agents vs LangGraph?\"\n", "const query3 = {\n", "  query: \"What's the difference between LangChain agents and LangGraph? How do you deploy them?\",\n", "  subQueries: [\n", "    \"What are LangChain agents\",\n", "    \"What is LangGrap<PERSON>\",\n", "    \"How do you deploy LangChain agents\",\n", "    \"How do you deploy LangGraph\",\n", "  ],\n", "}\n", "examples.push({ \"input\": question3, \"toolCalls\": [query3] });"]}, {"cell_type": "markdown", "id": "bd21389c-f862-44e6-9d51-92db10979525", "metadata": {}, "source": ["Now we need to update our prompt template and chain so that the examples are included in each prompt. Since we're working with LLM model function-calling, we'll need to do a bit of extra structuring to send example inputs and outputs to the model. We'll create a `toolExampleToMessages` helper function to handle this for us:"]}, {"cell_type": "code", "execution_count": 44, "id": "68b03709-9a60-4acf-b96c-cafe1056c6f3", "metadata": {}, "outputs": [], "source": ["import {\n", "    AIMessage,\n", "    BaseMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    ToolMessage,\n", "  } from \"@langchain/core/messages\";\n", "  import { v4 as uuidV4 } from \"uuid\";\n", "  \n", "  const toolExampleToMessages = (example: Record<string, any>): Array<BaseMessage> => {\n", "    const messages: Array<BaseMessage> = [new HumanMessage({ content: example.input })];\n", "    const openaiToolCalls = example.toolCalls.map((toolCall) => {\n", "      return {\n", "        id: uuidV4(),\n", "        type: \"function\" as const,\n", "        function: {\n", "          name: \"search\",\n", "          arguments: JSON.stringify(toolCall),\n", "        },\n", "      };\n", "    });\n", "  \n", "    messages.push(new AIMessage({ content: \"\", additional_kwargs: { tool_calls: openaiToolCalls } }));\n", "  \n", "    const toolOutputs = \"toolOutputs\" in example ? example.toolOutputs : Array(openaiToolCalls.length).fill(\"You have correctly called this tool.\");\n", "    toolOutputs.forEach((output, index) => {\n", "      messages.push(new ToolMessage({ content: output, tool_call_id: openaiToolCalls[index].id }));\n", "    });\n", "  \n", "    return messages;\n", "  }\n", "  \n", "  const exampleMessages = examples.map((ex) => toolExampleToMessages(ex)).flat();"]}, {"cell_type": "code", "execution_count": 40, "id": "d9bf9f87-3e6b-4fc2-957b-949b077fab54", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate, MessagesPlaceholder } from \"@langchain/core/prompts\";\n", "import { RunnableSequence } from \"@langchain/core/runnables\";\n", "\n", "const queryAnalyzerWithExamples = RunnableSequence.from([\n", "  {\n", "    question: new RunnablePassthrough(),\n", "    examples: () => exampleMessages,\n", "  },\n", "  prompt,\n", "  llmWithTools\n", "]);"]}, {"cell_type": "code", "execution_count": 41, "id": "e565ccb0-3530-4782-b56b-d1f6d0a8e559", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  query: \u001b[32m\"Difference between Web Voyager and Reflection agents, do they both use LangGraph?\"\u001b[39m,\n", "  subQueries: [\n", "    \u001b[32m\"Difference between Web Voyager and Reflection agents\"\u001b[39m,\n", "    \u001b[32m\"Do Web Voyager and Reflection agents use LangGraph\"\u001b[39m\n", "  ]\n", "}"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["await queryAnalyzerWithExamples.invoke(\n", "    \"what's the difference between web voyager and reflection agents? do both use langgraph?\"\n", ")"]}, {"cell_type": "markdown", "id": "e5ea49ff-be53-4072-8c25-08682bb31a19", "metadata": {}, "source": ["Thanks to our examples we get a slightly more decomposed search query. With some more prompt engineering and tuning of our examples we could improve query generation even more.\n", "\n", "You can see that the examples are passed to the model as messages in the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/102829c3-69fc-4cb7-b28b-399ae2c9c008/r).\n", "\n", "## Next steps\n", "\n", "You've now learned some techniques for combining few-shotting with query analysis.\n", "\n", "Next, check out some of the other query analysis guides in this section, like [how to deal with high cardinality data](/docs/how_to/query_high_cardinality)."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}