{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "keywords: [chain, chaining, runnablesequence]\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to chain runnables\n", "\n", "One point about [LangChain Expression Language](/docs/concepts/lcel) is that any two runnables can be \"chained\" together into sequences. The output of the previous runnable's `.invoke()` call is passed as input to the next runnable. This can be done using the `.pipe()` method.\n", "\n", "The resulting [`RunnableSequence`](https://api.js.langchain.com/classes/langchain_core.runnables.RunnableSequence.html) is itself a runnable, which means it can be invoked, streamed, or further chained just like any other runnable. Advantages of chaining runnables in this way are efficient streaming (the sequence will stream output as soon as it is available), and debugging and tracing with tools like [LangSmith](/docs/how_to/debugging).\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)\n", "- [Prompt templates](/docs/concepts/prompt_templates)\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Output parser](/docs/concepts/output_parsers)\n", "\n", ":::\n", "\n", "## The pipe method\n", "\n", "To show off how this works, let's go through an example. We'll walk through a common pattern in LangChain: using a [prompt template](/docs/concepts/prompt_templates) to format input into a [chat model](/docs/concepts/chat_models), and finally converting the chat message output into a string with an [output parser](/docs/concepts/output_parsers).\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs\n", "  customVarName=\"model\"\n", "/>\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from '@langchain/openai';\n", "\n", "const model = new ChatOpenAI({\n", "  model: \"gpt-4o\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(\"tell me a joke about {topic}\")\n", "\n", "const chain = prompt.pipe(model).pipe(new StringOutputParser())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prompts and models are both runnable, and the output type from the prompt call is the same as the input type of the chat model, so we can chain them together. We can then invoke the resulting sequence like any other runnable:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Here's a bear joke for you:\\n\\nWhy did the bear dissolve in water?\\nBecause it was a polar bear!\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["await chain.invoke({ topic: \"bears\" })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Co<PERSON>cion\n", "\n", "We can even combine this chain with more runnables to create another chain. This may involve some input/output formatting using other types of runnables, depending on the required inputs and outputs of the chain components.\n", "\n", "For example, let's say we wanted to compose the joke generating chain with another chain that evaluates whether or not the generated joke was funny.\n", "\n", "We would need to be careful with how we format the input into the next chain. In the below example, the dict in the chain is automatically parsed and converted into a [`RunnableParallel`](/docs/how_to/parallel), which runs all of its values in parallel and returns a dict with the results.\n", "\n", "This happens to be the same format the next prompt template expects. Here it is in action:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Haha, that\\'s a clever play on words! Using \"polar\" to imply the bear dissolved or became polar/polarized when put in water. Not the most hilarious joke ever, but it has a cute, groan-worthy pun that makes it mildly amusing. I appreciate a good pun or wordplay joke.'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import { RunnableLambda } from \"@langchain/core/runnables\";\n", "\n", "const analysisPrompt = ChatPromptTemplate.fromTemplate(\"is this a funny joke? {joke}\")\n", "\n", "const composedChain = new RunnableLambda({\n", "  func: async (input: { topic: string }) => {\n", "    const result = await chain.invoke(input);\n", "    return { joke: result };\n", "  }\n", "}).pipe(analysisPrompt).pipe(model).pipe(new StringOutputParser())\n", "\n", "await composed<PERSON><PERSON><PERSON>.invoke({ topic: \"bears\" })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Functions will also be coerced into runnables, so you can add custom logic to your chains too. The below chain results in the same logical flow as before:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Haha, that's a cute and punny joke! I like how it plays on the idea of beets blushing or turning red like someone blushing. Food puns can be quite amusing. While not a total knee-slapper, it's a light-hearted, groan-worthy dad joke that would make me chuckle and shake my head. Simple vegetable humor!\""]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import { RunnableSequence } from \"@langchain/core/runnables\";\n", "\n", "const composedChainWithLambda = RunnableSequence.from([\n", "    chain,\n", "    (input) => ({ joke: input }),\n", "    analysisPrompt,\n", "    model,\n", "    new StringOutputParser()\n", "])\n", "\n", "await composedChainWithLambda.invoke({ topic: \"beets\" })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> See the <PERSON><PERSON><PERSON> trace for the run above [here](https://smith.langchain.com/public/ef1bf347-a243-4da6-9be6-54f5d73e6da2/r)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["However, keep in mind that using functions like this may interfere with operations like streaming. See [this section](/docs/how_to/functions) for more information."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "You now know some ways to chain two runnables together.\n", "\n", "To learn more, see the other how-to guides on runnables in [this section](/docs/how_to/#langchain-expression-language-lcel)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "typescript", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.1"}}, "nbformat": 4, "nbformat_minor": 2}