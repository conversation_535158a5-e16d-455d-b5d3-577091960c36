{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to stream\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [LangChain Expression Language](/docs/concepts/lcel)\n", "- [Output parsers](/docs/concepts/output_parsers)\n", "\n", ":::\n", "\n", "Streaming is critical in making applications based on LLMs feel responsive to end-users.\n", "\n", "Important LangChain primitives like LLMs, parsers, prompts, retrievers, and agents implement the LangChain Runnable Interface.\n", "\n", "This interface provides two general approaches to stream content:\n", "\n", "- `.stream()`: a default implementation of streaming that streams the final output from the chain.\n", "- `streamEvents()` and `streamLog()`: these provide a way to stream both intermediate steps and final output from the chain.\n", "\n", "Let’s take a look at both approaches!\n", "\n", ":::info\n", "For a higher-level overview of streaming techniques in LangChain, see [this section of the conceptual guide](/docs/concepts/streaming).\n", ":::\n", "\n", "# Using Stream\n", "\n", "All `Runnable` objects implement a method called stream.\n", "\n", "These methods are designed to stream the final output in chunks, yielding each chunk as soon as it is available.\n", "\n", "Streaming is only possible if all steps in the program know how to process an **input stream**; i.e., process an input chunk one at a time, and yield a corresponding output chunk.\n", "\n", "The complexity of this processing can vary, from straightforward tasks like emitting tokens produced by an LLM, to more challenging ones like streaming parts of JSON results before the entire JSON is complete.\n", "\n", "The best place to start exploring streaming is with the single most important components in LLM apps – the models themselves!\n", "\n", "## LLMs and Chat Models\n", "\n", "Large language models can take several seconds to generate a complete response to a query. This is far slower than the **~200-300 ms** threshold at which an application feels responsive to an end user.\n", "\n", "The key strategy to make the application feel more responsive is to show intermediate progress; e.g., to stream the output from the model token by token."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import \"dotenv/config\";"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs />\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const model = new ChatOpenAI({\n", "  model: \"gpt-4o\",\n", "  temperature: 0,\n", "});"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|\n", "Hello|\n", "!|\n", " I'm|\n", " a|\n", " large|\n", " language|\n", " model|\n", " developed|\n", " by|\n", " Open|\n", "AI|\n", " called|\n", " GPT|\n", "-|\n", "4|\n", ",|\n", " based|\n", " on|\n", " the|\n", " Gener|\n", "ative|\n", " Pre|\n", "-trained|\n", " Transformer|\n", " architecture|\n", ".|\n", " I'm|\n", " designed|\n", " to|\n", " understand|\n", " and|\n", " generate|\n", " human|\n", "-like|\n", " text|\n", " based|\n", " on|\n", " the|\n", " input|\n", " I|\n", " receive|\n", ".|\n", " My|\n", " primary|\n", " function|\n", " is|\n", " to|\n", " assist|\n", " with|\n", " answering|\n", " questions|\n", ",|\n", " providing|\n", " information|\n", ",|\n", " and|\n", " engaging|\n", " in|\n", " various|\n", " types|\n", " of|\n", " conversations|\n", ".|\n", " While|\n", " I|\n", " don't|\n", " have|\n", " personal|\n", " experiences|\n", " or|\n", " emotions|\n", ",|\n", " I'm|\n", " trained|\n", " on|\n", " diverse|\n", " datasets|\n", " that|\n", " enable|\n", " me|\n", " to|\n", " provide|\n", " useful|\n", " and|\n", " relevant|\n", " information|\n", " across|\n", " a|\n", " wide|\n", " array|\n", " of|\n", " topics|\n", ".|\n", " How|\n", " can|\n", " I|\n", " assist|\n", " you|\n", " today|\n", "?|\n", "|\n", "|\n"]}], "source": ["const stream = await model.stream(\"Hello! Tell me about yourself.\");\n", "const chunks = [];\n", "for await (const chunk of stream) {\n", "  chunks.push(chunk);\n", "  console.log(`${chunk.content}|`)\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's have a look at one of the raw chunks:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessageChunk {\n", "  lc_serializable: true,\n", "  lc_kwargs: {\n", "    content: '',\n", "    tool_call_chunks: [],\n", "    additional_kwargs: {},\n", "    id: 'chatcmpl-9lO8YUEcX7rqaxxevelHBtl1GaWoo',\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ 'langchain_core', 'messages' ],\n", "  content: '',\n", "  name: undefined,\n", "  additional_kwargs: {},\n", "  response_metadata: { prompt: 0, completion: 0, finish_reason: null },\n", "  id: 'chatcmpl-9lO8YUEcX7rqaxxevelHBtl1GaWoo',\n", "  tool_calls: [],\n", "  invalid_tool_calls: [],\n", "  tool_call_chunks: [],\n", "  usage_metadata: undefined\n", "}\n"]}], "source": ["chunks[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We got back something called an `AIMessageChunk`. This chunk represents a part of an `AIMessage`.\n", "\n", "Message chunks are additive by design – one can simply add them up using the `.concat()` method to get the state of the response so far!"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessageChunk {\n", "  lc_serializable: true,\n", "  lc_kwargs: {\n", "    content: \"Hello! I'm a\",\n", "    additional_kwargs: {},\n", "    response_metadata: { prompt: 0, completion: 0, finish_reason: null },\n", "    tool_call_chunks: [],\n", "    id: 'chatcmpl-9lO8YUEcX7rqaxxevelHBtl1GaWoo',\n", "    tool_calls: [],\n", "    invalid_tool_calls: []\n", "  },\n", "  lc_namespace: [ 'langchain_core', 'messages' ],\n", "  content: \"Hello! I'm a\",\n", "  name: undefined,\n", "  additional_kwargs: {},\n", "  response_metadata: { prompt: 0, completion: 0, finish_reason: null },\n", "  id: 'chatcmpl-9lO8YUEcX7rqaxxevelHBtl1GaWoo',\n", "  tool_calls: [],\n", "  invalid_tool_calls: [],\n", "  tool_call_chunks: [],\n", "  usage_metadata: undefined\n", "}\n"]}], "source": ["let finalChunk = chunks[0];\n", "\n", "for (const chunk of chunks.slice(1, 5)) {\n", "  finalChunk = finalChunk.concat(chunk);\n", "}\n", "\n", "finalChunk"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chains\n", "\n", "Virtually all LLM applications involve more steps than just a call to a language model.\n", "\n", "Let’s build a simple chain using `LangChain Expression Language` (`LCEL`) that combines a prompt, model and a parser and verify that streaming works.\n", "\n", "We will use `StringOutputParser` to parse the output from the model. This is a simple parser that extracts the content field from an `AIMessageChunk`, giving us the `token` returned by the model.\n", "\n", ":::{.callout-tip}\n", "LCEL is a declarative way to specify a “program” by chainining together different LangChain primitives. Chains created using LCEL benefit from an automatic implementation of stream, allowing streaming of the final output. In fact, chains created with LCEL implement the entire standard Runnable interface.\n", ":::"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|\n", "Sure|\n", ",|\n", " here's|\n", " a|\n", " joke|\n", " for|\n", " you|\n", ":\n", "\n", "|\n", "Why|\n", " did|\n", " the|\n", " par|\n", "rot|\n", " sit|\n", " on|\n", " the|\n", " stick|\n", "?\n", "\n", "|\n", "Because|\n", " it|\n", " wanted|\n", " to|\n", " be|\n", " a|\n", " \"|\n", "pol|\n", "ly|\n", "-stick|\n", "-al|\n", "\"|\n", " observer|\n", "!|\n", "|\n", "|\n"]}], "source": ["import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(\"Tell me a joke about {topic}\");\n", "\n", "const parser = new StringOutputParser();\n", "\n", "const chain = prompt.pipe(model).pipe(parser);\n", "\n", "const stream = await chain.stream({\n", "  topic: \"parrot\",\n", "});\n", "\n", "for await (const chunk of stream) {\n", "  console.log(`${chunk}|`)\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": [":::{.callout-note}\n", "You do not have to use the `LangChain Expression Language` to use LangChain and can instead rely on a standard **imperative** programming approach by\n", "caling `invoke`, `batch` or `stream` on each component individually, assigning the results to variables and then using them downstream as you see fit.\n", "\n", "If that works for your needs, then that's fine by us 👌!\n", ":::\n", "\n", "### Working with Input Streams\n", "\n", "What if you wanted to stream JSON from the output as it was being generated?\n", "\n", "If you were to rely on `JSON.parse` to parse the partial json, the parsing would fail as the partial json wouldn't be valid json.\n", "\n", "You'd likely be at a complete loss of what to do and claim that it wasn't possible to stream JSON.\n", "\n", "Well, turns out there is a way to do it - the parser needs to operate on the **input stream**, and attempt to \"auto-complete\" the partial json into a valid state.\n", "\n", "Let's see such a parser in action to understand what this means."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  countries: [\n", "    { name: 'France', population: 67390000 },\n", "    { name: 'Spain', population: 47350000 },\n", "    { name: 'Japan', population: 125800000 }\n", "  ]\n", "}\n"]}], "source": ["import { JsonOutputParser } from \"@langchain/core/output_parsers\"\n", "\n", "const chain = model.pipe(new JsonOutputParser());\n", "const stream = await chain.stream(\n", "  `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`\n", ");\n", "\n", "for await (const chunk of stream) {\n", "  console.log(chunk);\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's **break** streaming. We'll use the previous example and append an extraction function at the end that extracts the country names from the finalized JSON. Since this new last step is just a function call with no defined streaming behavior, the streaming output from previous steps is aggregated, then passed as a single input to the function.\n", "\n", ":::{.callout-warning}\n", "Any steps in the chain that operate on **finalized inputs** rather than on **input streams** can break streaming functionality via `stream`.\n", ":::\n", "\n", ":::{.callout-tip}\n", "Later, we will discuss the `streamEvents` API which streams results from intermediate steps. This API will stream results from intermediate steps even if the chain contains steps that only operate on **finalized inputs**.\n", ":::"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\"France\",\"Spain\",\"Japan\"]\n"]}], "source": ["// A function that operates on finalized inputs\n", "// rather than on an input_stream\n", "\n", "// A function that does not operates on input streams and breaks streaming.\n", "const extractCountryNames = (inputs: Record<string, any>) => {\n", "  if (!Array.isArray(inputs.countries)) {\n", "    return \"\";\n", "  }\n", "  return JSON.stringify(inputs.countries.map((country) => country.name));\n", "}\n", "\n", "const chain = model.pipe(new JsonOutputParser()).pipe(extractCountryNames);\n", "\n", "const stream = await chain.stream(\n", "  `output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`\n", ");\n", "\n", "for await (const chunk of stream) {\n", "  console.log(chunk);\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Non-streaming components\n", "\n", "Like the above example, some built-in components like Retrievers do not offer any streaming. What happens if we try to `stream` them?"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  [\n", "    Document {\n", "      pageContent: 'mitochondria is the powerhouse of the cell',\n", "      metadata: {},\n", "      id: undefined\n", "    },\n", "    Document {\n", "      pageContent: 'buildings are made of brick',\n", "      metadata: {},\n", "      id: undefined\n", "    }\n", "  ]\n", "]\n"]}], "source": ["import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const template = `Answer the question based only on the following context:\n", "{context}\n", "\n", "Question: {question}\n", "`;\n", "const prompt = ChatPromptTemplate.fromTemplate(template);\n", "\n", "const vectorstore = await MemoryVectorStore.fromTexts(\n", "  [\"mitochondria is the powerhouse of the cell\", \"buildings are made of brick\"],\n", "  [{}, {}],\n", "  new OpenAIEmbeddings(),\n", ");\n", "\n", "const retriever = vectorstore.asRetriever();\n", "\n", "const chunks = [];\n", "\n", "for await (const chunk of await retriever.stream(\"What is the powerhouse of the cell?\")) {\n", "  chunks.push(chunk);\n", "}\n", "\n", "console.log(chunks);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Stream just yielded the final result from that component. \n", "\n", "This is OK! Not all components have to implement streaming -- in some cases streaming is either unnecessary, difficult or just doesn't make sense.\n", "\n", ":::{.callout-tip}\n", "An LCEL chain constructed using some non-streaming components will still be able to stream in a lot of cases, with streaming of partial output starting after the last non-streaming step in the chain.\n", ":::\n", "\n", "Here's an example of this:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|\n", "M|\n", "ito|\n", "ch|\n", "ond|\n", "ria|\n", " is|\n", " the|\n", " powerhouse|\n", " of|\n", " the|\n", " cell|\n", ".|\n", "|\n", "|\n"]}], "source": ["import { RunnablePassthrough, RunnableSequence } from \"@langchain/core/runnables\";\n", "import type { Document } from \"@langchain/core/documents\";\n", "import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "\n", "const formatDocs = (docs: Document[]) => {\n", "  return docs.map((doc) => doc.pageContent).join(\"\\n-----\\n\")\n", "}\n", "\n", "const retrievalChain = RunnableSequence.from([\n", "  {\n", "    context: retriever.pipe(formatDocs),\n", "    question: new RunnablePassthrough()\n", "  },\n", "  prompt,\n", "  model,\n", "  new StringOutputParser(),\n", "]);\n", "\n", "const stream = await retrievalChain.stream(\"What is the powerhouse of the cell?\");\n", "\n", "for await (const chunk of stream) {\n", "  console.log(`${chunk}|`);\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we've seen how the `stream` method works, let's venture into the world of streaming events!\n", "\n", "## Using Stream Events\n", "\n", "Event Streaming is a **beta** API. This API may change a bit based on feedback.\n", "\n", ":::{.callout-note}\n", "Introduced in @langchain/core **0.1.27**.\n", ":::\n", "\n", "For the `streamEvents` method to work properly:\n", "\n", "* Any custom functions / runnables must propragate callbacks \n", "* Set proper parameters on models to force the LLM to stream tokens.\n", "* Let us know if anything doesn't work as expected!\n", "\n", "### Event Reference\n", "\n", "Below is a reference table that shows some events that might be emitted by the various Runnable objects.\n", "\n", ":::{.callout-note}\n", "When streaming is implemented properly, the inputs to a runnable will not be known until after the input stream has been entirely consumed. This means that `inputs` will often be included only for `end` events and rather than for `start` events.\n", ":::\n", "\n", "| event                | name             | chunk                           | input                                         | output                                          |\n", "|----------------------|------------------|---------------------------------|-----------------------------------------------|-------------------------------------------------|\n", "| on_llm_start         | [model name]     |                                 | {'input': 'hello'}                            |                                                 |\n", "| on_llm_stream        | [model name]     | 'Hello' `or` AIMessageChunk(content=\"hello\")  |                                               |                                   |\n", "| on_llm_end           | [model name]     |                                 | 'Hello human!'                                | {\"generations\": [...], \"llmOutput\": None, ...}  |\n", "| on_chain_start       | format_docs      |                                 |                                               |                                                 |\n", "| on_chain_stream      | format_docs      | \"hello world!, goodbye world!\"  |                                               |                                                 |\n", "| on_chain_end         | format_docs      |                                 | [Document(...)]                               | \"hello world!, goodbye world!\"                  |\n", "| on_tool_start        | some_tool        |                                 | {\"x\": 1, \"y\": \"2\"}                            |                                                 |\n", "| on_tool_stream       | some_tool        | {\"x\": 1, \"y\": \"2\"}              |                                               |                                                 |\n", "| on_tool_end          | some_tool        |                                 |                                               | {\"x\": 1, \"y\": \"2\"}                              |\n", "| on_retriever_start   | [retriever name] |                                 | {\"query\": \"hello\"}                            |                                                 |\n", "| on_retriever_chunk   | [retriever name] | {documents: [...]}              |                                               |                                                 |\n", "| on_retriever_end     | [retriever name] |                                 | {\"query\": \"hello\"}                            | {documents: [...]}                              |\n", "| on_prompt_start      | [template_name]  |                                 | {\"question\": \"hello\"}                         |                                                 |\n", "| on_prompt_end        | [template_name]  |                                 | {\"question\": \"hello\"}                         | ChatPromptValue(messages: [SystemMessage, ...]) |\n", "\n", "`streamEvents` will also emit dispatched custom events in `v2`. Please see [this guide](/docs/how_to/callbacks_custom_events/) for more.\n", "\n", "### Chat Model\n", "\n", "Let's start off by looking at the events produced by a chat model."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25\n"]}], "source": ["const events = [];\n", "\n", "const eventStream = await model.streamEvents(\"hello\", { version: \"v2\" });\n", "\n", "for await (const event of eventStream) {\n", "  events.push(event);\n", "}\n", "\n", "console.log(events.length)"]}, {"cell_type": "markdown", "metadata": {}, "source": [":::{.callout-note}\n", "\n", "Hey what's that funny version=\"v2\" parameter in the API?! 😾\n", "\n", "This is a **beta API**, and we're almost certainly going to make some changes to it.\n", "\n", "This version parameter will allow us to minimize such breaking changes to your code. \n", "\n", "In short, we are annoying you now, so we don't have to annoy you later.\n", ":::"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's take a look at the few of the start event and a few of the end events."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    event: 'on_chat_model_start',\n", "    data: { input: 'hello' },\n", "    name: 'ChatOpenAI',\n", "    tags: [],\n", "    run_id: 'c983e634-9f1d-4916-97d8-63c3a86102c2',\n", "    metadata: {\n", "      ls_provider: 'openai',\n", "      ls_model_name: 'gpt-4o',\n", "      ls_model_type: 'chat',\n", "      ls_temperature: 1,\n", "      ls_max_tokens: undefined,\n", "      ls_stop: undefined\n", "    }\n", "  },\n", "  {\n", "    event: 'on_chat_model_stream',\n", "    data: { chunk: [AIMessageChunk] },\n", "    run_id: 'c983e634-9f1d-4916-97d8-63c3a86102c2',\n", "    name: 'ChatOpenAI',\n", "    tags: [],\n", "    metadata: {\n", "      ls_provider: 'openai',\n", "      ls_model_name: 'gpt-4o',\n", "      ls_model_type: 'chat',\n", "      ls_temperature: 1,\n", "      ls_max_tokens: undefined,\n", "      ls_stop: undefined\n", "    }\n", "  },\n", "  {\n", "    event: 'on_chat_model_stream',\n", "    run_id: 'c983e634-9f1d-4916-97d8-63c3a86102c2',\n", "    name: 'ChatOpenAI',\n", "    tags: [],\n", "    metadata: {\n", "      ls_provider: 'openai',\n", "      ls_model_name: 'gpt-4o',\n", "      ls_model_type: 'chat',\n", "      ls_temperature: 1,\n", "      ls_max_tokens: undefined,\n", "      ls_stop: undefined\n", "    },\n", "    data: { chunk: [AIMessageChunk] }\n", "  }\n", "]\n"]}], "source": ["events.slice(0, 3);"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    event: 'on_chat_model_stream',\n", "    run_id: 'c983e634-9f1d-4916-97d8-63c3a86102c2',\n", "    name: 'ChatOpenAI',\n", "    tags: [],\n", "    metadata: {\n", "      ls_provider: 'openai',\n", "      ls_model_name: 'gpt-4o',\n", "      ls_model_type: 'chat',\n", "      ls_temperature: 1,\n", "      ls_max_tokens: undefined,\n", "      ls_stop: undefined\n", "    },\n", "    data: { chunk: [AIMessageChunk] }\n", "  },\n", "  {\n", "    event: 'on_chat_model_end',\n", "    data: { output: [AIMessageChunk] },\n", "    run_id: 'c983e634-9f1d-4916-97d8-63c3a86102c2',\n", "    name: 'ChatOpenAI',\n", "    tags: [],\n", "    metadata: {\n", "      ls_provider: 'openai',\n", "      ls_model_name: 'gpt-4o',\n", "      ls_model_type: 'chat',\n", "      ls_temperature: 1,\n", "      ls_max_tokens: undefined,\n", "      ls_stop: undefined\n", "    }\n", "  }\n", "]\n"]}], "source": ["events.slice(-2);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Chain\n", "\n", "Let's revisit the example chain that parsed streaming JSON to explore the streaming events API."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["83\n"]}], "source": ["const chain = model.pipe(new JsonOutputParser());\n", "const eventStream = await chain.streamEvents(\n", "  `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`,\n", "  { version: \"v2\" },\n", ");\n", "\n", "\n", "const events = [];\n", "for await (const event of eventStream) {\n", "  events.push(event);\n", "}\n", "\n", "console.log(events.length)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you examine at the first few events, you'll notice that there are **3** different start events rather than **2** start events.\n", "\n", "The three start events correspond to:\n", "\n", "1. The chain (model + parser)\n", "2. The model\n", "3. The parser"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    event: 'on_chain_start',\n", "    data: {\n", "      input: 'Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"'\n", "    },\n", "    name: 'RunnableSequence',\n", "    tags: [],\n", "    run_id: '5dd960b8-4341-4401-8993-7d04d49fcc08',\n", "    metadata: {}\n", "  },\n", "  {\n", "    event: 'on_chat_model_start',\n", "    data: { input: [Object] },\n", "    name: 'ChatOpenAI',\n", "    tags: [ 'seq:step:1' ],\n", "    run_id: '5d2917b1-886a-47a1-807d-8a0ba4cb4f65',\n", "    metadata: {\n", "      ls_provider: 'openai',\n", "      ls_model_name: 'gpt-4o',\n", "      ls_model_type: 'chat',\n", "      ls_temperature: 1,\n", "      ls_max_tokens: undefined,\n", "      ls_stop: undefined\n", "    }\n", "  },\n", "  {\n", "    event: 'on_parser_start',\n", "    data: {},\n", "    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "    tags: [ 'seq:step:2' ],\n", "    run_id: '756c57d6-d455-484f-a556-79a82c4e1d40',\n", "    metadata: {}\n", "  }\n", "]\n"]}], "source": ["events.slice(0, 3);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What do you think you'd see if you looked at the last 3 events? what about the middle?\n", "\n", "Let's use this API to take output the stream events from the model and the parser. We're ignoring start events, end events and events from the chain."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chat model chunk: \n", "Chat model chunk: ```\n", "Chat model chunk: json\n", "Chat model chunk: \n", "\n", "Chat model chunk: {\n", "\n", "Chat model chunk:    \n", "Chat model chunk:  \"\n", "Chat model chunk: countries\n", "Chat model chunk: \":\n", "Chat model chunk:  [\n", "\n", "Chat model chunk:        \n", "Chat model chunk:  {\n", "\n", "Chat model chunk:            \n", "Chat model chunk:  \"\n", "Chat model chunk: name\n", "Chat model chunk: \":\n", "Chat model chunk:  \"\n", "Chat model chunk: France\n", "Chat model chunk: \",\n", "\n", "Chat model chunk:            \n", "Chat model chunk:  \"\n", "Chat model chunk: population\n", "Chat model chunk: \":\n", "Chat model chunk:  \n", "Chat model chunk: 652\n", "Chat model chunk: 735\n", "Chat model chunk: 11\n", "Chat model chunk: \n", "\n"]}], "source": ["let eventCount = 0;\n", "\n", "const eventStream = await chain.streamEvents(\n", "  `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`,\n", "  { version: \"v1\" },\n", ");\n", "\n", "for await (const event of eventStream) {\n", "  // Truncate the output\n", "  if (eventCount > 30) {\n", "    continue;\n", "  }\n", "  const eventType = event.event;\n", "  if (eventType === \"on_llm_stream\") {\n", "    console.log(`Chat model chunk: ${event.data.chunk.message.content}`);\n", "  } else if (eventType === \"on_parser_stream\") {\n", "    console.log(`Parser chunk: ${JSON.stringify(event.data.chunk)}`);\n", "  }\n", "  eventCount += 1;\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Because both the model and the parser support streaming, we see streaming events from both components in real time! Neat! 🦜\n", "\n", "### Filtering Events\n", "\n", "Because this API produces so many events, it is useful to be able to filter on events.\n", "\n", "You can filter by either component `name`, component `tags` or component `type`.\n", "\n", "#### By Name\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  event: 'on_parser_start',\n", "  data: {\n", "    input: 'Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"'\n", "  },\n", "  name: 'my_parser',\n", "  tags: [ 'seq:step:2' ],\n", "  run_id: '0a605976-a8f8-4259-8ef6-b3d7e52b3d4e',\n", "  metadata: {}\n", "}\n", "{\n", "  event: 'on_parser_stream',\n", "  run_id: '0a605976-a8f8-4259-8ef6-b3d7e52b3d4e',\n", "  name: 'my_parser',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {},\n", "  data: { chunk: { countries: [Array] } }\n", "}\n", "{\n", "  event: 'on_parser_end',\n", "  data: { output: { countries: [Array] } },\n", "  run_id: '0a605976-a8f8-4259-8ef6-b3d7e52b3d4e',\n", "  name: 'my_parser',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {}\n", "}\n"]}], "source": ["const chain = model.withConfig({ runName: \"model\" })\n", "  .pipe(\n", "    new JsonOutputParser().withConfig({ runName: \"my_parser\" })\n", "  );\n", "\n", "\n", "const eventStream = await chain.streamEvents(\n", "  `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`,\n", "  { version: \"v2\" },\n", "  { includeNames: [\"my_parser\"] },\n", ");\n", "\n", "let eventCount = 0;\n", "\n", "for await (const event of eventStream) {\n", "  // Truncate the output\n", "  if (eventCount > 10) {\n", "    continue;\n", "  }\n", "  console.log(event);\n", "  eventCount += 1;\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### By type"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  event: 'on_chat_model_start',\n", "  data: {\n", "    input: 'Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"'\n", "  },\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '```',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: 'json',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '\\n',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '{\\n',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' ',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' \"',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: 'countries',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '\":',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' [\\n',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO98p55iuqUNwx4GZ6j2BkDak6Rr',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'fb6351eb-9537-445d-a1bd-24c2e11efd8e',\n", "  name: 'model',\n", "  tags: [ 'seq:step:1' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n"]}], "source": ["const chain = model.withConfig({ runName: \"model\" })\n", "  .pipe(\n", "    new JsonOutputParser().withConfig({ runName: \"my_parser\" })\n", "  );\n", "\n", "\n", "const eventStream = await chain.streamEvents(\n", "  `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`,\n", "  { version: \"v2\" },\n", "  { includeTypes: [\"chat_model\"] },\n", ");\n", "\n", "let eventCount = 0;\n", "\n", "for await (const event of eventStream) {\n", "  // Truncate the output\n", "  if (eventCount > 10) {\n", "    continue;\n", "  }\n", "  console.log(event);\n", "  eventCount += 1;\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### By Tags\n", "\n", ":::{.callout-caution}\n", "\n", "Tags are inherited by child components of a given runnable. \n", "\n", "If you're using tags to filter, make sure that this is what you want.\n", ":::"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  event: 'on_chain_start',\n", "  data: {\n", "    input: 'Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"'\n", "  },\n", "  name: 'RunnableSequence',\n", "  tags: [ 'my_chain' ],\n", "  run_id: '1fed60d6-e0b7-4d5e-8ec7-cd7d3ee5c69f',\n", "  metadata: {}\n", "}\n", "{\n", "  event: 'on_chat_model_start',\n", "  data: { input: { messages: [Array] } },\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_parser_start',\n", "  data: {},\n", "  name: 'my_parser',\n", "  tags: [ 'seq:step:2', 'my_chain' ],\n", "  run_id: 'caf24a1e-255c-4937-9f38-6e46275d854a',\n", "  metadata: {}\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: 'Certainly',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '!',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: \" Here's\",\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' the',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' JSON',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' format',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' output',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: [Object],\n", "      id: 'chatcmpl-9lO99nzUvCsZWCiq6vNtS1Soa1qNp',\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: 'ecb99d6e-ce03-445f-aadf-73e6cbbc52fe',\n", "  name: 'ChatOpenAI',\n", "  tags: [ 'seq:step:1', 'my_chain' ],\n", "  metadata: {\n", "    ls_provider: 'openai',\n", "    ls_model_name: 'gpt-4o',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 1,\n", "    ls_max_tokens: undefined,\n", "    ls_stop: undefined\n", "  }\n", "}\n"]}], "source": ["const chain = model\n", "  .pipe(new JsonOutputParser().withConfig({ runName: \"my_parser\" }))\n", "  .withConfig({ tags: [\"my_chain\"] });\n", "\n", "\n", "const eventStream = await chain.streamEvents(\n", "  `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`,\n", "  { version: \"v2\" },\n", "  { includeTags: [\"my_chain\"] },\n", ");\n", "\n", "let eventCount = 0;\n", "\n", "for await (const event of eventStream) {\n", "  // Truncate the output\n", "  if (eventCount > 10) {\n", "    continue;\n", "  }\n", "  console.log(event);\n", "  eventCount += 1;\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Streaming events over HTTP\n", "\n", "For convenience, `streamEvents` supports encoding streamed intermediate events as HTTP [server-sent events](https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events), encoded as bytes. Here's what that looks like (using a [`TextDecoder`](https://developer.mozilla.org/en-US/docs/Web/API/TextDecoder) to reconvert the binary data back into a human readable string):"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["event: data\n", "data: {\"event\":\"on_chain_start\",\"data\":{\"input\":\"Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \\\"countries\\\" which contains a list of countries. Each country should have the key \\\"name\\\" and \\\"population\\\"\"},\"name\":\"RunnableSequence\",\"tags\":[\"my_chain\"],\"run_id\":\"41cd92f8-9b8c-4365-8aa0-fda3abdae03d\",\"metadata\":{}}\n", "\n", "\n", "event: data\n", "data: {\"event\":\"on_chat_model_start\",\"data\":{\"input\":{\"messages\":[[{\"lc\":1,\"type\":\"constructor\",\"id\":[\"langchain_core\",\"messages\",\"HumanMessage\"],\"kwargs\":{\"content\":\"Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \\\"countries\\\" which contains a list of countries. Each country should have the key \\\"name\\\" and \\\"population\\\"\",\"additional_kwargs\":{},\"response_metadata\":{}}}]]}},\"name\":\"ChatOpenAI\",\"tags\":[\"seq:step:1\",\"my_chain\"],\"run_id\":\"a6c2bc61-c868-4570-a143-164e64529ee0\",\"metadata\":{\"ls_provider\":\"openai\",\"ls_model_name\":\"gpt-4o\",\"ls_model_type\":\"chat\",\"ls_temperature\":1}}\n", "\n", "\n", "event: data\n", "data: {\"event\":\"on_parser_start\",\"data\":{},\"name\":\"my_parser\",\"tags\":[\"seq:step:2\",\"my_chain\"],\"run_id\":\"402533c5-0e4e-425d-a556-c30a350972d0\",\"metadata\":{}}\n", "\n", "\n", "event: data\n", "data: {\"event\":\"on_chat_model_stream\",\"data\":{\"chunk\":{\"lc\":1,\"type\":\"constructor\",\"id\":[\"langchain_core\",\"messages\",\"AIMessageChunk\"],\"kwargs\":{\"content\":\"\",\"tool_call_chunks\":[],\"additional_kwargs\":{},\"id\":\"chatcmpl-9lO9BAQwbKDy2Ou2RNFUVi0VunAsL\",\"tool_calls\":[],\"invalid_tool_calls\":[],\"response_metadata\":{\"prompt\":0,\"completion\":0,\"finish_reason\":null}}}},\"run_id\":\"a6c2bc61-c868-4570-a143-164e64529ee0\",\"name\":\"ChatOpenAI\",\"tags\":[\"seq:step:1\",\"my_chain\"],\"metadata\":{\"ls_provider\":\"openai\",\"ls_model_name\":\"gpt-4o\",\"ls_model_type\":\"chat\",\"ls_temperature\":1}}\n", "\n", "\n"]}], "source": ["const chain = model\n", "  .pipe(new JsonOutputParser().withConfig({ runName: \"my_parser\" }))\n", "  .withConfig({ tags: [\"my_chain\"] });\n", "\n", "\n", "const eventStream = await chain.streamEvents(\n", "  `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`,\n", "  {\n", "    version: \"v2\",\n", "    encoding: \"text/event-stream\",\n", "  },\n", ");\n", "\n", "let eventCount = 0;\n", "\n", "const textDecoder = new TextDecoder();\n", "\n", "for await (const event of eventStream) {\n", "  // Truncate the output\n", "  if (eventCount > 3) {\n", "    continue;\n", "  }\n", "  console.log(textDecoder.decode(event));\n", "  eventCount += 1;\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A nice feature of this format is that you can pass the resulting stream directly into a native [HTTP response object](https://developer.mozilla.org/en-US/docs/Web/API/Response) with the correct headers (commonly used by frameworks like [Hono](https://hono.dev/) and [Next.js](https://nextjs.org/)), then parse that stream on the frontend. Your server-side handler would look something like this:"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["const handler = async () => {\n", "  const eventStream = await chain.streamEvents(\n", "    `Output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`,\n", "    {\n", "      version: \"v2\",\n", "      encoding: \"text/event-stream\",\n", "    },\n", "  );\n", "  return new Response(eventStream, {\n", "    headers: {\n", "      \"content-type\": \"text/event-stream\",\n", "    }\n", "  });\n", "};"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And your frontend could look like this (using the [`@microsoft/fetch-event-source`](https://www.npmjs.com/package/@microsoft/fetch-event-source) pacakge to fetch and parse the event source):"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import { fetchEventSource } from \"@microsoft/fetch-event-source\";\n", "\n", "const makeChainRequest = async () => {\n", "  await fetchEventSource(\"https://your_url_here\", {\n", "    method: \"POST\",\n", "    body: JSON.stringify({\n", "      foo: 'bar'\n", "    }),\n", "    onmessage: (message) => {\n", "      if (message.event === \"data\") {\n", "        console.log(message.data);\n", "      }\n", "    },\n", "    onerror: (err) => {\n", "      console.log(err);\n", "    }\n", "  });\n", "};"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Non-streaming components\n", "\n", "Remember how some components don't stream well because they don't operate on **input streams**?\n", "\n", "While such components can break streaming of the final output when using `stream`, `streamEvents` will still yield streaming events from intermediate steps that support streaming!"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\"France\",\"Spain\",\"Japan\"]\n"]}], "source": ["// A function that operates on finalized inputs\n", "// rather than on an input_stream\n", "import { JsonOutputParser } from \"@langchain/core/output_parsers\"\n", "import { RunnablePassthrough } from \"@langchain/core/runnables\";\n", "\n", "// A function that does not operates on input streams and breaks streaming.\n", "const extractCountryNames = (inputs: Record<string, any>) => {\n", "  if (!Array.isArray(inputs.countries)) {\n", "    return \"\";\n", "  }\n", "  return JSON.stringify(inputs.countries.map((country) => country.name));\n", "}\n", "\n", "const chain = model.pipe(new JsonOutputParser()).pipe(extractCountryNames);\n", "\n", "const stream = await chain.stream(\n", "  `output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key \"name\" and \"population\"`\n", ");\n", "\n", "for await (const chunk of stream) {\n", "  console.log(chunk);\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As expected, the `stream` API doesn't work correctly because `extractCountryNames` doesn't operate on streams.\n", "\n", "Now, let's confirm that with `streamEvents` we're still seeing streaming output from the model and the parser."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["const eventStream = await chain.streamEvents(\n", "  `output a list of the countries france, spain and japan and their populations in JSON format.\n", "Use a dict with an outer key of \"countries\" which contains a list of countries.\n", "Each country should have the key \"name\" and \"population\"\n", "Your output should ONLY contain valid JSON data. Do not include any other text or content in your output.`,\n", "  { version: \"v2\" },\n", ");\n", "\n", "let eventCount = 0;\n", "\n", "for await (const event of eventStream) {\n", "  // Truncate the output\n", "  if (eventCount > 30) {\n", "    continue;\n", "  }\n", "  const eventType = event.event;\n", "  if (eventType === \"on_chat_model_stream\") {\n", "    console.log(`Chat model chunk: ${event.data.chunk.message.content}`);\n", "  } else if (eventType === \"on_parser_stream\") {\n", "    console.log(`Parser chunk: ${JSON.stringify(event.data.chunk)}`);\n", "  } else {\n", "    console.log(eventType)\n", "  }\n", "  eventCount += 1;\n", "}"]}, {"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["Chat model chunk:\n", "Chat model chunk: Here's\n", "Chat model chunk:  how\n", "Chat model chunk:  you\n", "Chat model chunk:  can\n", "Chat model chunk:  represent\n", "Chat model chunk:  the\n", "Chat model chunk:  countries\n", "Chat model chunk:  France\n", "Chat model chunk: ,\n", "Chat model chunk:  Spain\n", "Chat model chunk: ,\n", "Chat model chunk:  and\n", "Chat model chunk:  Japan\n", "Chat model chunk: ,\n", "Chat model chunk:  along\n", "Chat model chunk:  with\n", "Chat model chunk:  their\n", "Chat model chunk:  populations\n", "Chat model chunk: ,\n", "Chat model chunk:  in\n", "Chat model chunk:  JSON\n", "Chat model chunk:  format\n", "Chat model chunk: :\n", "\n", "\n", "Chat model chunk: ```\n", "Chat model chunk: json\n", "Chat model chunk:\n", "\n", "Chat model chunk: {"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Related\n", "\n", "- [Dispatching custom events](/docs/how_to/callbacks_custom_events)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}