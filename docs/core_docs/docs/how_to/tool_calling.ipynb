{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "keywords: [function, function calling, tool, tool call, tool calling]\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# How to use chat models to call tools\n", "\n", "```{=mdx}\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [Tool calling](/docs/concepts/tool_calling)\n", "\n", ":::\n", "```\n", "\n", "[Tool calling](/docs/concepts/tool_calling) allows a chat model to respond to a given prompt by \"calling a tool\".\n", "\n", "Remember, while the name \"tool calling\" implies that the model is directly performing some action, this is actually not the case! The model only generates the arguments to a tool, and actually running the tool (or not) is up to the user.\n", "\n", "Tool calling is a general technique that generates structured output from a model, and you can use it even when you don't intend to invoke any tools. An example use-case of that is [extraction from unstructured text](/docs/tutorials/extraction/).\n", "\n", "![](../../static/img/tool_call.png)\n", "\n", "If you want to see how to use the model-generated tool call to actually run a tool function [check out this guide](/docs/how_to/tool_results_pass_to_model/).\n", "\n", "```{=mdx}\n", ":::note Supported models\n", "\n", "Tool calling is not universal, but is supported by many popular LLM providers, including [Anthropic](/docs/integrations/chat/anthropic/), \n", "[Cohere](/docs/integrations/chat/cohere/), [Google](/docs/integrations/chat/google_vertex_ai/), \n", "[Mistral](/docs/integrations/chat/mistral/), [OpenAI](/docs/integrations/chat/openai/), and even for locally-running models via [Ollama](/docs/integrations/chat/ollama/).\n", "\n", "You can find a [list of all models that support tool calling here](/docs/integrations/chat/).\n", "\n", ":::\n", "```\n", "\n", "LangChain implements standard interfaces for defining tools, passing them to LLMs, and representing tool calls.\n", "This guide will cover how to bind tools to an LLM, then invoke the LLM to generate these arguments.\n", "\n", "LangChain implements standard interfaces for defining tools, passing them to LLMs, \n", "and representing tool calls. This guide will show you how to use them."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Passing tools to chat models\n", "\n", "Chat models that support tool calling features implement a [`.bindTools()`](https://api.js.langchain.com/classes/langchain_core.language_models_chat_models.BaseChatModel.html#bindTools) method, which \n", "receives a list of Lang<PERSON><PERSON>n [tool objects](https://api.js.langchain.com/classes/langchain_core.tools.StructuredTool.html)\n", "and binds them to the chat model in its expected format. Subsequent invocations of the \n", "chat model will include tool schemas in its calls to the LLM.\n", "\n", "```{=mdx}\n", ":::note\n", "As of `@langchain/core` version `0.2.9`, all chat models with tool calling capabilities now support [OpenAI-formatted tools](https://api.js.langchain.com/interfaces/langchain_core.language_models_base.ToolDefinition.html).\n", ":::\n", "```\n", "\n", "Let's walk through an example:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" providers={[\"anthropic\", \"openai\", \"mistral\", \"fireworks\"]} additionalDependencies=\"@langchain/core\" />\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can use the `.bindTools()` method to handle the conversion from LangChain tool to our model provider's specific format and bind it to the model (i.e., passing it in each time the model is invoked). A number of models implement helper methods that will take care of formatting and binding different function-like objects to the model.\n", "Let's create a new tool implementing a Zod schema, then bind it to the model:\n", "\n", "```{=mdx}\n", ":::note\n", "The `tool` function is available in `@langchain/core` version 0.2.7 and above.\n", "\n", "If you are on an older version of core, you should use instantiate and use [`DynamicStructuredTool`](https://api.js.langchain.com/classes/langchain_core.tools.DynamicStructuredTool.html) instead.\n", ":::\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import { tool } from \"@langchain/core/tools\";\n", "import { z } from \"zod\";\n", "\n", "/**\n", " * Note that the descriptions here are crucial, as they will be passed along\n", " * to the model along with the class name.\n", " */\n", "const calculatorSchema = z.object({\n", "  operation: z\n", "    .enum([\"add\", \"subtract\", \"multiply\", \"divide\"])\n", "    .describe(\"The type of operation to execute.\"),\n", "  number1: z.number().describe(\"The first number to operate on.\"),\n", "  number2: z.number().describe(\"The second number to operate on.\"),\n", "});\n", "\n", "const calculatorTool = tool(async ({ operation, number1, number2 }) => {\n", "  // Functions must return strings\n", "  if (operation === \"add\") {\n", "    return `${number1 + number2}`;\n", "  } else if (operation === \"subtract\") {\n", "    return `${number1 - number2}`;\n", "  } else if (operation === \"multiply\") {\n", "    return `${number1 * number2}`;\n", "  } else if (operation === \"divide\") {\n", "    return `${number1 / number2}`;\n", "  } else {\n", "    throw new Error(\"Invalid operation.\");\n", "  }\n", "}, {\n", "  name: \"calculator\",\n", "  description: \"Can perform mathematical operations.\",\n", "  schema: calculatorSchema,\n", "});\n", "\n", "const llmWithTools = llm.bindTools([calculatorTool]);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's invoke it! We expect the model to use the calculator to answer the question:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-9p1Ib4xfxV4yahv2ZWm1IRb1fRVD7\",\n", "  \"content\": \"\",\n", "  \"additional_kwargs\": {\n", "    \"tool_calls\": [\n", "      {\n", "        \"id\": \"call_CrZkMP0AvUrz7w9kim0splbl\",\n", "        \"type\": \"function\",\n", "        \"function\": \"[Object]\"\n", "      }\n", "    ]\n", "  },\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 24,\n", "      \"promptTokens\": 93,\n", "      \"totalTokens\": 117\n", "    },\n", "    \"finish_reason\": \"tool_calls\",\n", "    \"system_fingerprint\": \"fp_400f27fa1f\"\n", "  },\n", "  \"tool_calls\": [\n", "    {\n", "      \"name\": \"calculator\",\n", "      \"args\": {\n", "        \"operation\": \"multiply\",\n", "        \"number1\": 3,\n", "        \"number2\": 12\n", "      },\n", "      \"type\": \"tool_call\",\n", "      \"id\": \"call_CrZkMP0AvUrz7w9kim0splbl\"\n", "    }\n", "  ],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 93,\n", "    \"output_tokens\": 24,\n", "    \"total_tokens\": 117\n", "  }\n", "}\n"]}], "source": ["const res = await llmWithTools.invoke(\"What is 3 * 12\");\n", "\n", "console.log(res);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we can see our LLM generated arguments to a tool!\n", "\n", "**Note:** If you are finding that the model does not call a desired tool for a given prompt, you can see [this guide on how to force the LLM to call a tool](/docs/how_to/tool_choice/) rather than letting it decide.\n", "\n", "```{=mdx}\n", ":::tip\n", "See a LangSmith trace for the above [here](https://smith.langchain.com/public/b2222205-7da9-4a5a-8efe-6bc62347705d/r).\n", ":::\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON> calls\n", "\n", "If tool calls are included in a LLM response, they are attached to the corresponding \n", "[message](https://api.js.langchain.com/classes/langchain_core.messages.AIMessage.html) \n", "or [message chunk](https://api.js.langchain.com/classes/langchain_core.messages.AIMessageChunk.html) \n", "as a list of [tool call](https://api.js.langchain.com/types/langchain_core.messages_tool.ToolCall.html) \n", "objects in the `.tool_calls` attribute.\n", "\n", "A `ToolCall` is a typed dict that includes a \n", "tool name, dict of argument values, and (optionally) an identifier. Messages with no \n", "tool calls default to an empty list for this attribute.\n", "\n", "Chat models can call multiple tools at once. Here's an example:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'calculator',\n", "    args: { operation: 'multiply', number1: 3, number2: 12 },\n", "    type: 'tool_call',\n", "    id: 'call_01lvdk2COLV2hTjRUNAX8XWH'\n", "  },\n", "  {\n", "    name: 'calculator',\n", "    args: { operation: 'add', number1: 11, number2: 49 },\n", "    type: 'tool_call',\n", "    id: 'call_fB0vo8VC2HRojZcj120xIBxM'\n", "  }\n", "]\n"]}], "source": ["const res = await llmWithTools.invoke(\"What is 3 * 12? Also, what is 11 + 49?\");\n", "\n", "res.tool_calls;"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `.tool_calls` attribute should contain valid tool calls. Note that on occasion, \n", "model providers may output malformed tool calls (e.g., arguments that are not \n", "valid JSON). When parsing fails in these cases, instances \n", "of [`InvalidToolCall`](https://api.js.langchain.com/types/langchain_core.messages_tool.InvalidToolCall.html) \n", "are populated in the `.invalid_tool_calls` attribute. An `InvalidToolCall` can have \n", "a name, string arguments, identifier, and error message."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Binding model-specific formats (advanced)\n", "\n", "Providers adopt different conventions for formatting tool schemas. For instance, OpenAI uses a format like this:\n", "\n", "- `type`: The type of the tool. At the time of writing, this is always \"function\".\n", "- `function`: An object containing tool parameters.\n", "- `function.name`: The name of the schema to output.\n", "- `function.description`: A high level description of the schema to output.\n", "- `function.parameters`: The nested details of the schema you want to extract, formatted as a [JSON schema](https://json-schema.org/) object.\n", "\n", "We can bind this model-specific format directly to the model if needed. Here's an example:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-9p1IeP7mIp3jPn1wgsP92zxEfNo7k\",\n", "  \"content\": \"\",\n", "  \"additional_kwargs\": {\n", "    \"tool_calls\": [\n", "      {\n", "        \"id\": \"call_P5Xgyi0Y7IfisaUmyapZYT7d\",\n", "        \"type\": \"function\",\n", "        \"function\": \"[Object]\"\n", "      }\n", "    ]\n", "  },\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 24,\n", "      \"promptTokens\": 85,\n", "      \"totalTokens\": 109\n", "    },\n", "    \"finish_reason\": \"tool_calls\",\n", "    \"system_fingerprint\": \"fp_400f27fa1f\"\n", "  },\n", "  \"tool_calls\": [\n", "    {\n", "      \"name\": \"calculator\",\n", "      \"args\": {\n", "        \"operation\": \"multiply\",\n", "        \"number1\": 119,\n", "        \"number2\": 8\n", "      },\n", "      \"type\": \"tool_call\",\n", "      \"id\": \"call_P5Xgyi0Y7IfisaUmyapZYT7d\"\n", "    }\n", "  ],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 85,\n", "    \"output_tokens\": 24,\n", "    \"total_tokens\": 109\n", "  }\n", "}\n"]}], "source": ["import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const model = new ChatOpenAI({ model: \"gpt-4o\" });\n", "\n", "const modelWithTools = model.bind({\n", "  tools: [{\n", "    \"type\": \"function\",\n", "    \"function\": {\n", "      \"name\": \"calculator\",\n", "      \"description\": \"Can perform mathematical operations.\",\n", "      \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "          \"operation\": {\n", "            \"type\": \"string\",\n", "            \"description\": \"The type of operation to execute.\",\n", "            \"enum\": [\"add\", \"subtract\", \"multiply\", \"divide\"]\n", "          },\n", "          \"number1\": {\"type\": \"number\", \"description\": \"First integer\"},\n", "          \"number2\": {\"type\": \"number\", \"description\": \"Second integer\"},\n", "        },\n", "        \"required\": [\"number1\", \"number2\"],\n", "      },\n", "    },\n", "  }],\n", "});\n", "\n", "await modelWithTools.invoke(`Whats 119 times 8?`);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This is functionally equivalent to the `bind_tools()` calls above."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "Now you've learned how to bind tool schemas to a chat model and have the model call the tool.\n", "\n", "Next, check out this guide on actually using the tool by invoking the function and passing the results back to the model:\n", "\n", "- Pass [tool results back to model](/docs/how_to/tool_results_pass_to_model)\n", "\n", "You can also check out some more specific uses of tool calling:\n", "\n", "- Few shot prompting [with tools](/docs/how_to/tools_few_shot/)\n", "- Stream [tool calls](/docs/how_to/tool_streaming/)\n", "- Pass [runtime values to tools](/docs/how_to/tool_runtime)\n", "- Getting [structured outputs](/docs/how_to/structured_output/) from models"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}