{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to pass run time values to tools\n", "\n", "```{=mdx}\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [How to create tools](/docs/how_to/custom_tools)\n", "- [How to use a model to call tools](/docs/how_to/tool_calling/)\n", ":::\n", "\n", ":::info Supported models\n", "\n", "This how-to guide uses models with native tool calling capability.\n", "You can find a [list of all models that support tool calling](/docs/integrations/chat/).\n", "\n", ":::\n", "```\n", "\n", "You may need to bind values to a tool that are only known at runtime. For example, the tool logic may require using the ID of the user who made the request.\n", "\n", "Most of the time, such values should not be controlled by the LLM. In fact, allowing the LLM to control the user ID may lead to a security risk.\n", "\n", "Instead, the LLM should only control the parameters of the tool that are meant to be controlled by the LLM, while other parameters (such as user ID) should be fixed by the application logic."]}, {"cell_type": "markdown", "metadata": {}, "source": ["```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs\n", "  customVarName=\"llm\"\n", "/>\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({ model: \"gpt-4o-mini\" })"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using context variables\n", "\n", "```{=mdx}\n", ":::caution Compatibility\n", "This functionality was added in `@langchain/core>=0.3.10`. If you are using the LangSmith SDK separately in your project, we also recommend upgrading to `langsmith>=0.1.65`. Please make sure your packages are up to date.\n", "\n", "It also requires [`async_hooks`](https://nodejs.org/api/async_hooks.html) support, which is not supported in all environments.\n", ":::\n", "```\n", "\n", "One way to solve this problem is by using **context variables**. Context variables are a powerful feature that allows you to set values at a higher level of your application, then access them within child runnable (such as tools) called from that level.\n", "\n", "They work outside of traditional scoping rules, so you don't need to have a direct reference to the declared variable to access its value.\n", "\n", "Below, we declare a tool that updates a central `userToPets` state based on a context variable called `userId`. Note that this `userId` is not part of the tool schema or input:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { tool } from \"@langchain/core/tools\";\n", "import { getContextVariable } from \"@langchain/core/context\";\n", "\n", "let userToPets: Record<string, string[]> = {};\n", "\n", "const updateFavoritePets = tool(async (input) => {\n", "  const userId = getContextVariable(\"userId\");\n", "  if (userId === undefined) {\n", "    throw new Error(`No \"userId\" found in current context. Remember to call \"setContextVariable('userId', value)\";`);\n", "  }\n", "  userToPets[userId] = input.pets;\n", "  return \"update_favorite_pets called.\"\n", "}, {\n", "  name: \"update_favorite_pets\",\n", "  description: \"add to the list of favorite pets.\",\n", "  schema: z.object({\n", "    pets: z.array(z.string())\n", "  }),\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you were to invoke the above tool before setting a context variable at a higher level, `userId` would be `undefined`:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error: No \"userId\" found in current context. Remember to call \"setContextVariable('userId', value)\";\n", "    at updateFavoritePets.name (evalmachine.<anonymous>:14:15)\n", "    at /Users/<USER>/langchain/langchainjs/langchain-core/dist/tools/index.cjs:329:33\n", "    at AsyncLocalStorage.run (node:async_hooks:346:14)\n", "    at AsyncLocalStorageProvider.runWithConfig (/Users/<USER>/langchain/langchainjs/langchain-core/dist/singletons/index.cjs:58:24)\n", "    at /Users/<USER>/langchain/langchainjs/langchain-core/dist/tools/index.cjs:325:68\n", "    at new Promise (<anonymous>)\n", "    at DynamicStructuredTool.func (/Users/<USER>/langchain/langchainjs/langchain-core/dist/tools/index.cjs:321:20)\n", "    at DynamicStructuredTool._call (/Users/<USER>/langchain/langchainjs/langchain-core/dist/tools/index.cjs:283:21)\n", "    at DynamicStructuredTool.call (/Users/<USER>/langchain/langchainjs/langchain-core/dist/tools/index.cjs:111:33)\n", "    at async evalmachine.<anonymous>:3:22\n"]}], "source": ["await updateFavoritePets.invoke({ pets: [\"cat\", \"dog\" ]})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Instead, set a context variable with a parent of where the tools are invoked:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import { setContextVariable } from \"@langchain/core/context\";\n", "import { BaseChatModel } from \"@langchain/core/language_models/chat_models\";\n", "import { RunnableLambda } from \"@langchain/core/runnables\";\n", "\n", "const handleRunTimeRequestRunnable = RunnableLambda.from(async (params: {\n", "  userId: string;\n", "  query: string;\n", "  llm: BaseChatModel;\n", "}) => {\n", "  const { userId, query, llm } = params;\n", "  if (!llm.bindTools) {\n", "    throw new Error(\"Language model does not support tools.\");\n", "  }\n", "  // Set a context variable accessible to any child runnables called within this one.\n", "  // You can also set context variables at top level that act as globals.\n", "  setContextVariable(\"userId\", userId);\n", "  const tools = [updateFavoritePets];\n", "  const llmWithTools = llm.bindTools(tools);\n", "  const modelResponse = await llmWithTools.invoke(query);\n", "  // For simplicity, skip checking the tool call's name field and assume\n", "  // that the model is calling the \"updateFavoritePets\" tool\n", "  if (modelResponse.tool_calls.length > 0) {\n", "    return updateFavoritePets.invoke(modelResponse.tool_calls[0]);\n", "  } else {\n", "    return \"No tool invoked.\";\n", "  }\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And when our method invokes the tools, you will see that the tool properly access the previously set `userId` context variable and runs successfully:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ToolMessage {\n", "  \"content\": \"update_favorite_pets called.\",\n", "  \"name\": \"update_favorite_pets\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {},\n", "  \"tool_call_id\": \"call_vsD2DbSpDquOtmFlOtbUME6h\"\n", "}\n"]}], "source": ["await handleRunTimeRequestRunnable.invoke({\n", "  userId: \"brace\",\n", "  query: \"my favorite animals are cats and parrots.\",\n", "  llm: llm\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And have additionally updated the `userToPets` object with a key matching the `userId` we passed, `\"brace\"`:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ brace: [ 'cats', 'parrots' ] }\n"]}], "source": ["console.log(userToPets);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Without context variables\n", "\n", "If you are on an earlier version of core or an environment that does not support `async_hooks`, you can use the following design pattern that creates the tool dynamically at run time and binds to them appropriate values.\n", "\n", "The idea is to create the tool dynamically at request time, and bind to it the appropriate information. For example,\n", "this information may be the user ID as resolved from the request itself."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { tool } from \"@langchain/core/tools\";\n", "\n", "userToPets = {};\n", "\n", "function generateToolsForUser(userId: string) {\n", "  const updateFavoritePets = tool(async (input) => {\n", "    userToPets[userId] = input.pets;\n", "    return \"update_favorite_pets called.\"\n", "  }, {\n", "    name: \"update_favorite_pets\",\n", "    description: \"add to the list of favorite pets.\",\n", "    schema: z.object({\n", "      pets: z.array(z.string())\n", "    }),\n", "  });\n", "  // You can declare and return additional tools as well:\n", "  return [updateFavoritePets];\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Verify that the tool works correctly"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ cobb: [ 'tiger', 'wolf' ] }\n"]}], "source": ["const [updatePets] = generateToolsForUser(\"cobb\");\n", "\n", "await updatePets.invoke({ pets: [\"tiger\", \"wolf\"] });\n", "\n", "console.log(userToPets);"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import { BaseChatModel } from \"@langchain/core/language_models/chat_models\";\n", "\n", "async function handleRunTimeRequest(userId: string, query: string, llm: BaseChatModel): Promise<any> {\n", "  if (!llm.bindTools) {\n", "    throw new Error(\"Language model does not support tools.\");\n", "  }\n", "  const tools = generateToolsForUser(userId);\n", "  const llmWithTools = llm.bindTools(tools);\n", "  return llmWithTools.invoke(query);\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This code will allow the LLM to invoke the tools, but the LLM is **unaware** of the fact that a **user ID** even exists. You can see that `user_id` is not among the params the LLM generates:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  name: 'update_favorite_pets',\n", "  args: { pets: [ 'tigers', 'wolves' ] },\n", "  type: 'tool_call',\n", "  id: 'call_FBF4D51SkVK2clsLOQHX6wTv'\n", "}\n"]}], "source": ["const aiMessage = await handleRunTimeRequest(\n", "  \"cobb\", \"my favorite pets are tigers and wolves.\", llm,\n", ");\n", "console.log(aiMessage.tool_calls[0]);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```{=mdx}\n", ":::tip\n", "Click [here](https://smith.langchain.com/public/3d766ecc-8f28-400b-8636-632e6f1598c7/r) to see the <PERSON><PERSON><PERSON> trace for the above run.\n", ":::\n", "\n", ":::tip\n", "Chat models only output requests to invoke tools. They don't actually invoke the underlying tools.\n", "\n", "To see how to invoke the tools, please refer to [how to use a model to call tools](/docs/how_to/tool_calling/).\n", ":::\n", "```"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}