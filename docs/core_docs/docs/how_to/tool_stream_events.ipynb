{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to stream events from a tool\n", "\n", "```{=mdx}\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [Custom tools](/docs/how_to/custom_tools)\n", "- [Using stream events](/docs/how_to/streaming/#using-stream-events)\n", "- [Accessing RunnableConfig within a custom tool](/docs/how_to/tool_configure/)\n", "\n", ":::\n", "```\n", "\n", "If you have tools that call chat models, retrievers, or other runnables, you may want to access internal events from those runnables or configure them with additional properties. This guide shows you how to manually pass parameters properly so that you can do this using the [`.streamEvents()`](/docs/how_to/streaming/#using-stream-events) method.\n", "\n", "```{=mdx}\n", ":::caution Compatibility\n", "\n", "In order to support a wider variety of JavaScript environments, the base LangChain package does not automatically propagate configuration to child runnables by default. This includes callbacks necessary for `.streamEvents()`. This is a common reason why you may fail to see events being emitted from custom runnables or tools.\n", "\n", "You will need to manually propagate the `RunnableConfig` object to the child runnable. For an example of how to manually propagate the config, see the implementation of the `bar` RunnableLambda below.\n", "\n", "This guide also requires `@langchain/core>=0.2.16`.\n", ":::\n", "```\n", "\n", "Say you have a custom tool that calls a chain that condenses its input by prompting a chat model to return only 10 words, then reversing the output. First, define it in a naive way:\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"model\" />\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import { ChatAnthropic } from \"@langchain/anthropic\";\n", "const model = new ChatAnthropic({\n", "  model: \"claude-3-5-sonnet-20240620\",\n", "  temperature: 0,\n", "});"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { tool } from \"@langchain/core/tools\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "\n", "const specialSummarizationTool = tool(async (input) => {\n", "  const prompt = ChatPromptTemplate.fromTemplate(\n", "    \"You are an expert writer. Summarize the following text in 10 words or less:\\n\\n{long_text}\"\n", "  );\n", "  const reverse = (x: string) => {\n", "    return x.split(\"\").reverse().join(\"\");\n", "  };\n", "  const chain = prompt\n", "    .pipe(model)\n", "    .pipe(new StringOutputParser())\n", "    .pipe(reverse);\n", "  const summary = await chain.invoke({ long_text: input.long_text });\n", "  return summary;\n", "}, {\n", "  name: \"special_summarization_tool\",\n", "  description: \"A tool that summarizes input text using advanced techniques.\",\n", "  schema: z.object({\n", "    long_text: z.string(),\n", "  }),\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Invoking the tool directly works just fine:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [".yad noitaudarg rof tiftuo sesoohc yrraB ;scisy<PERSON> seifed eeB\n"]}], "source": ["const LONG_TEXT = `\n", "NARRATOR:\n", "(Black screen with text; The sound of buzzing bees can be heard)\n", "According to all known laws of aviation, there is no way a bee should be able to fly. Its wings are too small to get its fat little body off the ground. The bee, of course, flies anyway because bees don't care what humans think is impossible.\n", "BARRY BENSON:\n", "(<PERSON> is picking out a shirt)\n", "Yellow, black. Yellow, black. Yellow, black. Yellow, black. Ooh, black and yellow! Let's shake it up a little.\n", "JANET BENSON:\n", "<PERSON>! Breakfast is ready!\n", "BARRY:\n", "Coming! Hang on a second.`;\n", "\n", "await specialSummarizationTool.invoke({ long_text: LONG_TEXT });"]}, {"cell_type": "markdown", "metadata": {}, "source": ["But if you wanted to access the raw output from the chat model rather than the full tool, you might try to use the [`.streamEvents()`](/docs/how_to/streaming/#using-stream-events) method and look for an `on_chat_model_end` event. Here's what happens:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["const stream = await specialSummarizationTool.streamEvents(\n", "  { long_text: LONG_TEXT },\n", "  { version: \"v2\" },\n", ");\n", "\n", "for await (const event of stream) {\n", "  if (event.event === \"on_chat_model_end\") {\n", "    // Never triggers!\n", "    console.log(event);\n", "  }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You'll notice that there are no chat model events emitted from the child run!\n", "\n", "This is because the example above does not pass the tool's config object into the internal chain. To fix this, redefine your tool to take a special parameter typed as `RunnableConfig` (see [this guide](/docs/how_to/tool_configure) for more details). You'll also need to pass that parameter through into the internal chain when executing it:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["const specialSummarizationToolWithConfig = tool(async (input, config) => {\n", "  const prompt = ChatPromptTemplate.fromTemplate(\n", "    \"You are an expert writer. Summarize the following text in 10 words or less:\\n\\n{long_text}\"\n", "  );\n", "  const reverse = (x: string) => {\n", "    return x.split(\"\").reverse().join(\"\");\n", "  };\n", "  const chain = prompt\n", "    .pipe(model)\n", "    .pipe(new StringOutputParser())\n", "    .pipe(reverse);\n", "  // Pass the \"config\" object as an argument to any executed runnables\n", "  const summary = await chain.invoke({ long_text: input.long_text }, config);\n", "  return summary;\n", "}, {\n", "  name: \"special_summarization_tool\",\n", "  description: \"A tool that summarizes input text using advanced techniques.\",\n", "  schema: z.object({\n", "    long_text: z.string(),\n", "  }),\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now try the same `.streamEvents()` call as before with your new tool:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  event: 'on_chat_model_end',\n", "  data: {\n", "    output: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '<PERSON> defies physics; <PERSON> chooses outfit for graduation day.',\n", "      name: undefined,\n", "      additional_kwargs: [Object],\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: [Object]\n", "    },\n", "    input: { messages: [Array] }\n", "  },\n", "  run_id: '27ac7b2e-591c-4adc-89ec-64d96e233ec8',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n"]}], "source": ["const stream = await specialSummarizationToolWithConfig.streamEvents(\n", "  { long_text: LONG_TEXT },\n", "  { version: \"v2\" },\n", ");\n", "\n", "for await (const event of stream) {\n", "  if (event.event === \"on_chat_model_end\") {\n", "    // Never triggers!\n", "    console.log(event);\n", "  }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Awesome! This time there's an event emitted.\n", "\n", "For streaming, `.streamEvents()` automatically calls internal runnables in a chain with streaming enabled if possible, so if you wanted to a stream of tokens as they are generated from the chat model, you could simply filter to look for `on_chat_model_stream` events with no other changes:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: 'Bee',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' def',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: 'ies physics',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ';',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' <PERSON>',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' cho',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: 'oses outfit',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' for',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' graduation',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: ' day',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n", "{\n", "  event: 'on_chat_model_stream',\n", "  data: {\n", "    chunk: AIMessageChunk {\n", "      lc_serializable: true,\n", "      lc_kwargs: [Object],\n", "      lc_namespace: [Array],\n", "      content: '.',\n", "      name: undefined,\n", "      additional_kwargs: {},\n", "      response_metadata: {},\n", "      id: undefined,\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      tool_call_chunks: [],\n", "      usage_metadata: undefined\n", "    }\n", "  },\n", "  run_id: '938c0469-83c6-4dbd-862e-cd73381165de',\n", "  name: 'ChatAnthropic',\n", "  tags: [ 'seq:step:2' ],\n", "  metadata: {\n", "    ls_provider: 'anthropic',\n", "    ls_model_name: 'claude-3-5-sonnet-20240620',\n", "    ls_model_type: 'chat',\n", "    ls_temperature: 0,\n", "    ls_max_tokens: 2048,\n", "    ls_stop: undefined\n", "  }\n", "}\n"]}], "source": ["const stream = await specialSummarizationToolWithConfig.streamEvents(\n", "  { long_text: LONG_TEXT },\n", "  { version: \"v2\" },\n", ");\n", "\n", "for await (const event of stream) {\n", "  if (event.event === \"on_chat_model_stream\") {\n", "    // Never triggers!\n", "    console.log(event);\n", "  }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automatically passing config (Advanced)\n", "\n", "If you've used [LangGraph](https://langchain-ai.github.io/langgraphjs/), you may have noticed that you don't need to pass config in nested calls. This is because LangGraph takes advantage of an API called [`async_hooks`](https://nodejs.org/api/async_hooks.html), which is not supported in many, but not all environments.\n", "\n", "If you wish, you can enable automatic configuration passing by running the following code to import and enable `AsyncLocalStorage` globally:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import { AsyncLocalStorageProviderSingleton } from \"@langchain/core/singletons\";\n", "import { AsyncLocalStorage } from \"async_hooks\";\n", "\n", "AsyncLocalStorageProviderSingleton.initializeGlobalInstance(\n", "  new AsyncLocalStorage()\n", ");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next steps\n", "\n", "You've now seen how to stream events from within a tool. Next, check out the following guides for more on using tools:\n", "\n", "- Pass [runtime values to tools](/docs/how_to/tool_runtime)\n", "- Pass [tool results back to a model](/docs/how_to/tool_results_pass_to_model)\n", "- [Dispatch custom callback events](/docs/how_to/callbacks_custom_events)\n", "\n", "You can also check out some more specific uses of tool calling:\n", "\n", "- Building [tool-using chains and agents](/docs/how_to#tools)\n", "- Getting [structured outputs](/docs/how_to/structured_output/) from models"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}