{"cells": [{"cell_type": "markdown", "id": "15780a65", "metadata": {}, "source": ["# How to use LangChain tools\n", "\n", "Tools are interfaces that an agent, chain, or LLM can use to interact with the world.\n", "They combine a few things:\n", "\n", "1. The name of the tool\n", "2. A description of what the tool is\n", "3. JSON schema of what the inputs to the tool are\n", "4. The function to call \n", "5. Whether the result of a tool should be returned directly to the user\n", "\n", "It is useful to have all this information because this information can be used to build action-taking systems! The name, description, and schema can be used to prompt the LLM so it knows how to specify what action to take, and then the function to call is equivalent to taking that action.\n", "\n", "The simpler the input to a tool is, the easier it is for an LLM to be able to use it.\n", "Many agents will only work with tools that have a single string input.\n", "\n", "Importantly, the name, description, and schema (if used) are all used in the prompt. Therefore, it is vitally important that they are clear and describe exactly how the tool should be used.\n", "\n", "## <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Let's take a look at how to work with tools. To do this, we'll work with a built in tool."]}, {"cell_type": "code", "execution_count": 1, "id": "19297004", "metadata": {}, "outputs": [], "source": ["import { WikipediaQueryRun } from \"@langchain/community/tools/wikipedia_query_run\";\n", "\n", "const tool = new WikipediaQueryRun({\n", "  topKResults: 1,\n", "  maxDocContentLength: 100,\n", "});"]}, {"cell_type": "markdown", "id": "7db48439", "metadata": {}, "source": ["This is the default name:"]}, {"cell_type": "code", "execution_count": 2, "id": "50f1ece1", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"wikipedia-api\"\u001b[39m"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.name;"]}, {"cell_type": "markdown", "id": "075499b1", "metadata": {}, "source": ["This is the default description:"]}, {"cell_type": "code", "execution_count": 3, "id": "e9be09e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"A tool for interacting with and fetching data from the Wikipedia API.\"\u001b[39m"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.description;"]}, {"cell_type": "markdown", "id": "89c86b00", "metadata": {}, "source": ["This is the default schema of the inputs. This is a [Zod](https://zod.dev) schema on the tool class. We convert it to JSON schema for display purposes:"]}, {"cell_type": "code", "execution_count": 4, "id": "963a2e8c", "metadata": {}, "outputs": [{"data": {"text/plain": ["{\n", "  type: \u001b[32m\"object\"\u001b[39m,\n", "  properties: { input: { type: \u001b[32m\"string\"\u001b[39m } },\n", "  additionalProperties: \u001b[33mfalse\u001b[39m,\n", "  \u001b[32m\"$schema\"\u001b[39m: \u001b[32m\"http://json-schema.org/draft-07/schema#\"\u001b[39m\n", "}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import { zodToJsonSchema } from \"zod-to-json-schema\";\n", "\n", "zodToJsonSchema(tool.schema);"]}, {"cell_type": "markdown", "id": "5c467a35", "metadata": {}, "source": ["We can see if the tool should return directly to the user"]}, {"cell_type": "code", "execution_count": 5, "id": "039334b3", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[33mfalse\u001b[39m"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["tool.returnDirect;"]}, {"cell_type": "markdown", "id": "fc421b02", "metadata": {}, "source": ["We can invoke this tool with an object input:"]}, {"cell_type": "code", "execution_count": 6, "id": "6669a13c", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"Page: <PERSON><PERSON><PERSON><PERSON>\\n\"\u001b[39m +\n", "  \u001b[32m\"Summary: LangChain is a framework designed to simplify the creation of applications \"\u001b[39m"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["await tool.invoke({ input: \"langchain\" })"]}, {"cell_type": "markdown", "id": "587d6a58", "metadata": {}, "source": ["We can also invoke this tool with a single string input. \n", "We can do this because this tool expects only a single input.\n", "If it required multiple inputs, we would not be able to do that."]}, {"cell_type": "code", "execution_count": 7, "id": "8cb23935", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"Page: <PERSON><PERSON><PERSON><PERSON>\\n\"\u001b[39m +\n", "  \u001b[32m\"Summary: LangChain is a framework designed to simplify the creation of applications \"\u001b[39m"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["await tool.invoke(\"langchain\")"]}, {"cell_type": "markdown", "id": "450ac963", "metadata": {}, "source": ["## How to use built-in toolkits\n", "\n", "Toolkits are collections of tools that are designed to be used together for specific tasks. They have convenient loading methods.\n", "\n", "For a complete list of available ready-made toolkits, visit [Integrations](/docs/integrations/toolkits/).\n", "\n", "All Toolkits expose a `getTools()` method which returns a list of tools.\n", "\n", "You're usually meant to use them this way:\n", "\n", "```ts\n", "// Initialize a toolkit\n", "const toolkit = new ExampleTookit(...);\n", "\n", "// Get list of tools\n", "const tools = toolkit.getTools();\n", "```"]}, {"cell_type": "markdown", "id": "c5b8b6bc", "metadata": {}, "source": ["## More Topics\n", "\n", "This was a quick introduction to tools in LangChain, but there is a lot more to learn\n", "\n", "**[Built-In Tools](/docs/integrations/tools/)**: For a list of all built-in tools, see [this page](/docs/integrations/tools/)\n", "    \n", "**[Custom Tools](/docs/how_to/custom_tools)**: Although built-in tools are useful, it's highly likely that you'll have to define your own tools. See [this guide](/docs/how_to/custom_tools) for instructions on how to do so."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}