{"cells": [{"cell_type": "markdown", "id": "5d60cbb9-2a6a-43ea-a9e9-f67b16ddd2b2", "metadata": {}, "source": ["# How to handle tool errors\n", "\n", "```{=mdx}\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [<PERSON><PERSON><PERSON><PERSON>](/docs/concepts/tools)\n", "- [How to use a model to call tools](/docs/how_to/tool_calling)\n", "\n", ":::\n", "```\n", "\n", "Calling tools with an LLM isn't perfect. The model may try to call a tool that doesn't exist or fail to return arguments that match the requested schema. Strategies like keeping schemas simple, reducing the number of tools you pass at once, and having good names and descriptions can help mitigate this risk, but aren't foolproof.\n", "\n", "This guide covers some ways to build error handling into your chains to mitigate these failure modes."]}, {"cell_type": "markdown", "id": "0a50f93a-5d6f-4691-8f98-27239a1c2f95", "metadata": {}, "source": ["## Chain\n", "\n", "Suppose we have the following (dummy) tool and tool-calling chain. We'll make our tool intentionally convoluted to try and trip up the model."]}, {"cell_type": "code", "execution_count": 1, "id": "1d20604e-c4d1-4d21-841b-23e4f61aec36", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "import { tool } from \"@langchain/core/tools\";\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-3.5-turbo-0125\",\n", "  temperature: 0,\n", "});\n", "\n", "const complexTool = tool(async (params) => {\n", "  return params.int_arg * params.float_arg;\n", "}, {\n", "  name: \"complex_tool\",\n", "  description: \"Do something complex with a complex tool.\",\n", "  schema: z.object({\n", "    int_arg: z.number(),\n", "    float_arg: z.number(),\n", "    number_arg: z.object({}),\n", "  })\n", "});\n", "\n", "const llmWithTools = llm.bindTools([complexTool]);\n", "\n", "const chain = llmWithTools\n", "  .pipe((message) => message.tool_calls?.[0].args)\n", "  .pipe(complexTool);"]}, {"cell_type": "markdown", "id": "c34f005e-63f0-4841-9461-ca36c36607fc", "metadata": {}, "source": ["We can see that when we try to invoke this chain the model fails to correctly call the tool:"]}, {"cell_type": "code", "execution_count": 2, "id": "d354664c-ac44-4967-a35f-8912b3ad9477", "metadata": {}, "outputs": [{"ename": "Error", "evalue": "Received tool input did not match expected schema", "output_type": "error", "traceback": ["Stack trace:", "Error: Received tool input did not match expected schema", "    at DynamicStructuredTool.call (file:///Users/<USER>/Library/Caches/deno/npm/registry.npmjs.org/@langchain/core/0.2.16/dist/tools/index.js:100:19)", "    at eventLoopTick (ext:core/01_core.js:63:7)", "    at async RunnableSequence.invoke (file:///Users/<USER>/Library/Caches/deno/npm/registry.npmjs.org/@langchain/core/0.2.16_1/dist/runnables/base.js:1139:27)", "    at async <anonymous>:1:22"]}], "source": ["await chain.invoke(\n", "  \"use complex tool. the args are 5, 2.1, potato\"\n", ");"]}, {"cell_type": "markdown", "id": "890d989d-2d39-4571-9a55-d3496b9b5d27", "metadata": {}, "source": ["## Try/except tool call\n", "\n", "The simplest way to more gracefully handle errors is to try/except the tool-calling step and return a helpful message on errors:"]}, {"cell_type": "code", "execution_count": 3, "id": "8fedb550-683d-45ae-8876-ae7acb332019", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Calling tool with arguments:\n", "\n", "{\"int_arg\":5,\"float_arg\":2.1,\"number_arg\":\"potato\"}\n", "\n", "raised the following error:\n", "\n", "Error: Received tool input did not match expected schema\n"]}], "source": ["const tryExceptToolWrapper = async (input, config) => {\n", "  try {\n", "    const result = await complexTool.invoke(input);\n", "    return result;\n", "  } catch (e) {\n", "    return `Calling tool with arguments:\\n\\n${JSON.stringify(input)}\\n\\nraised the following error:\\n\\n${e}`\n", "  }\n", "}\n", "\n", "const chainWithTools = llmWithTools\n", "  .pipe((message) => message.tool_calls?.[0].args)\n", "  .pipe(tryExceptToolWrapper);\n", "\n", "const res = await chainWithTools.invoke(\"use complex tool. the args are 5, 2.1, potato\");\n", "\n", "console.log(res);"]}, {"cell_type": "markdown", "id": "3b2f6393-cb47-49d0-921c-09550a049fe4", "metadata": {}, "source": ["## Fallbacks\n", "\n", "We can also try to fallback to a better model in the event of a tool invocation error. In this case we'll fall back to an identical chain that uses `gpt-4-1106-preview` instead of `gpt-3.5-turbo`."]}, {"cell_type": "code", "execution_count": 4, "id": "02cc4223-35fa-4240-976a-012299ca703c", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[33m10.5\u001b[39m"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["const bad<PERSON>hain = llmWithTools\n", "  .pipe((message) => message.tool_calls?.[0].args)\n", "  .pipe(complexTool);\n", "\n", "const betterModel = new ChatOpenAI({\n", "  model: \"gpt-4-1106-preview\",\n", "  temperature: 0,\n", "}).bindTools([complexTool]);\n", "\n", "const betterChain = betterModel\n", "  .pipe((message) => message.tool_calls?.[0].args)\n", "  .pipe(complexTool);\n", "\n", "const chainWithFallback = badChain.withFallbacks([betterChain]);\n", "\n", "await chainWithFallback.invoke(\"use complex tool. the args are 5, 2.1, potato\");"]}, {"cell_type": "markdown", "id": "412f8c4e-cc83-4d87-84a1-5ba2f8edb1e9", "metadata": {}, "source": ["Looking at the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/ea31e7ca-4abc-48e3-9943-700100c86622/r) for this chain run, we can see that the first chain call fails as expected and it's the fallback that succeeds."]}, {"cell_type": "markdown", "id": "6b97af9f", "metadata": {}, "source": ["## Next steps\n", "\n", "Now you've seen some strategies how to handle tool calling errors. Next, you can learn more about how to use tools:\n", "\n", "- Few shot prompting [with tools](/docs/how_to/tool_calling#few-shotting-with-tools)\n", "- Stream [tool calls](/docs/how_to/tool_streaming/)\n", "- Pass [runtime values to tools](/docs/how_to/tool_runtime)\n", "\n", "You can also check out some more specific uses of tool calling:\n", "\n", "- Getting [structured outputs](/docs/how_to/structured_output/) from models"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}