{"cells": [{"cell_type": "raw", "id": "3243cb05-8243-421f-99fa-98201abb3094", "metadata": {}, "source": ["---\n", "sidebar_position: 3\n", "---"]}, {"cell_type": "markdown", "id": "14b94240", "metadata": {}, "source": ["# How to add ad-hoc tool calling capability to LLMs and Chat Models\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [LangChain Expression Language (LCEL)](/docs/concepts/lcel)\n", "- [Chaining runnables](/docs/how_to/sequence/)\n", "- [Tool calling](/docs/how_to/tool_calling/)\n", "\n", ":::\n", "\n", "In this guide we'll build a Chain that does not rely on any special model APIs (like tool calling, which we showed in the [Quickstart](/docs/how_to/tool_calling)) and instead just prompts the model directly to invoke tools."]}, {"cell_type": "markdown", "id": "a0a22cb8-19e7-450a-9d1b-6848d2c81cd1", "metadata": {}, "source": ["## Setup\n", "\n", "We'll need to install the following packages:\n", "\n", "```{=mdx}\n", "import Npm2Yarn from '@theme/Npm2Yarn';\n", "\n", "<Npm2Yarn>\n", "  @langchain/core zod\n", "</Npm2Yarn>\n", "```\n", "\n", "#### Set environment variables\n", "\n", "```\n", "# Optional, use <PERSON><PERSON><PERSON> for best-in-class observability\n", "LANGSMITH_API_KEY=your-api-key\n", "LANGSMITH_TRACING=true\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "markdown", "id": "68946881", "metadata": {}, "source": ["## Create a tool\n", "\n", "First, we need to create a tool to call. For this example, we will create a custom tool from a function. For more information on all details related to creating custom tools, please see [this guide](/docs/how_to/custom_tools)."]}, {"cell_type": "code", "execution_count": 13, "id": "90187d07", "metadata": {}, "outputs": [], "source": ["import { tool } from \"@langchain/core/tools\";\n", "import { z } from \"zod\";\n", "\n", "const multiplyTool = tool((input) => {\n", "    return (input.first_int * input.second_int).toString()\n", "}, {\n", "    name: \"multiply\",\n", "    description: \"Multiply two integers together.\",\n", "    schema: z.object({\n", "        first_int: z.number(),\n", "        second_int: z.number(),\n", "    })\n", "})\n"]}, {"cell_type": "code", "execution_count": 14, "id": "d7009e1a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["multiply\n", "Multiply two integers together.\n"]}], "source": ["console.log(multiplyTool.name)\n", "console.log(multiplyTool.description)"]}, {"cell_type": "code", "execution_count": 15, "id": "be77e780", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20\n"]}], "source": ["await multiplyTool.invoke({ first_int: 4, second_int: 5 })"]}, {"cell_type": "markdown", "id": "15dd690e-e54d-4209-91a4-181f69a452ac", "metadata": {}, "source": ["## Creating our prompt\n", "\n", "We'll want to write a prompt that specifies the tools the model has access to, the arguments to those tools, and the desired output format of the model. In this case we'll instruct it to output a JSON blob of the form `{\"name\": \"...\", \"arguments\": {...}}`.\n", "\n", "```{=mdx}\n", ":::tip\n", "As of `langchain` version `0.2.8`, the `renderTextDescription` function now supports [OpenAI-formatted tools](https://api.js.langchain.com/interfaces/langchain_core.language_models_base.ToolDefinition.html).\n", ":::\n", "```"]}, {"cell_type": "code", "execution_count": 16, "id": "c64818f0-9364-423c-922e-bdfb8f01e726", "metadata": {}, "outputs": [], "source": ["import { renderTextDescription } from \"langchain/tools/render\";\n", "\n", "const renderedTools = renderTextDescription([multiplyTool])"]}, {"cell_type": "code", "execution_count": 17, "id": "63552d4d-8bd6-4aca-8805-56e236f6552d", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const systemPrompt = `You are an assistant that has access to the following set of tools. Here are the names and descriptions for each tool:\n", "\n", "{rendered_tools}\n", "\n", "Given the user input, return the name and input of the tool to use. Return your response as a JSON blob with 'name' and 'arguments' keys.`;\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "    [[\"system\", systemPrompt], [\"user\", \"{input}\"]]\n", ")"]}, {"cell_type": "markdown", "id": "14df2cd5-b6fa-4b10-892d-e8692c7931e5", "metadata": {}, "source": ["## Adding an output parser\n", "\n", "We'll use the `JsonOutputParser` for parsing our models output to JSON.\n", "\n", "```{=mdx}\n", "import ChatModelTabs from '@theme/ChatModelTabs';\n", "\n", "<ChatModelTabs />\n", "```"]}, {"cell_type": "code", "execution_count": 18, "id": "f129f5bd-127c-4c95-8f34-8f437da7ca8f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ name: 'multiply', arguments: [ 13, 4 ] }\n"]}], "source": ["import { JsonOutputParser } from \"@langchain/core/output_parsers\";\n", "const chain = prompt.pipe(model).pipe(new JsonOutputParser())\n", "await chain.invoke({ input: \"what's thirteen times 4\", rendered_tools: renderedTools })"]}, {"cell_type": "markdown", "id": "8e29dd4c-8eb5-457f-92d1-8add076404dc", "metadata": {}, "source": ["## Invoking the tool\n", "\n", "We can invoke the tool as part of the chain by passing along the model-generated \"arguments\" to it:"]}, {"cell_type": "code", "execution_count": 19, "id": "0555b384-fde6-4404-86e0-7ea199003d58", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["52\n"]}], "source": ["import { RunnableLambda, RunnablePick } from \"@langchain/core/runnables\"\n", "\n", "const chain = prompt.pipe(model).pipe(new JsonOutputParser()).pipe(new RunnablePick(\"arguments\")).pipe(new RunnableLambda({ func: (input) => multiplyTool.invoke({\n", "  first_int: input[0],\n", "  second_int: input[1]\n", "}) }))\n", "await chain.invoke({ input: \"what's thirteen times 4\", rendered_tools: renderedTools })"]}, {"cell_type": "markdown", "id": "8d60b2cb-6ce0-48fc-8d18-d2337161a53d", "metadata": {}, "source": ["## Choosing from multiple tools\n", "\n", "Suppose we have multiple tools we want the chain to be able to choose from:"]}, {"cell_type": "code", "execution_count": 20, "id": "95c86d32-ee45-4c87-a28c-14eff19b49e9", "metadata": {}, "outputs": [], "source": ["const addTool = tool((input) => {\n", "    return (input.first_int + input.second_int).toString()\n", "}, {\n", "    name: \"add\",\n", "    description: \"Add two integers together.\",\n", "    schema: z.object({\n", "        first_int: z.number(),\n", "        second_int: z.number(),\n", "    }),\n", "});\n", "\n", "const exponentiateTool = tool((input) => {\n", "    return Math.pow(input.first_int, input.second_int).toString()\n", "}, {\n", "    name: \"exponentiate\",\n", "    description: \"Exponentiate the base to the exponent power.\",\n", "    schema: z.object({\n", "        first_int: z.number(),\n", "        second_int: z.number(),\n", "    }),\n", "});\n", "\n"]}, {"cell_type": "markdown", "id": "748405ff-4c85-4bd7-82e1-30458b5a4106", "metadata": {}, "source": ["With function calling, we can do this like so:"]}, {"cell_type": "markdown", "id": "eb3aa89e-40e1-45ec-b1f3-ab28cfc8e42d", "metadata": {}, "source": ["If we want to run the model selected tool, we can do so using a function that returns the tool based on the model output. Specifically, our function will action return it's own subchain that gets the \"arguments\" part of the model output and passes it to the chosen tool:"]}, {"cell_type": "code", "execution_count": 21, "id": "db254773-5b8e-43d0-aabe-c21566c154cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1135\n"]}], "source": ["import { StructuredToolInterface } from \"@langchain/core/tools\"\n", "\n", "const tools = [addTool, exponentiateTool, multiplyTool]\n", "\n", "const toolChain = (modelOutput) => {\n", "    const toolMap: Record<string, StructuredToolInterface> = Object.fromEntries(tools.map(tool => [tool.name, tool]))\n", "    const chosenTool = toolMap[modelOutput.name]\n", "    return new RunnablePick(\"arguments\").pipe(new RunnableLambda({ func: (input) => chosenTool.invoke({\n", "        first_int: input[0],\n", "        second_int: input[1]\n", "      }) }))\n", "}\n", "const toolChainRunnable = new RunnableLambda({\n", "  func: tool<PERSON><PERSON>n\n", "})\n", "\n", "const renderedTools = renderTextDescription(tools)\n", "const systemPrompt = `You are an assistant that has access to the following set of tools. Here are the names and descriptions for each tool:\n", "\n", "{rendered_tools}\n", "\n", "Given the user input, return the name and input of the tool to use. Return your response as a JSON blob with 'name' and 'arguments' keys.`\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "    [[\"system\", systemPrompt], [\"user\", \"{input}\"]]\n", ")\n", "const chain = prompt.pipe(model).pipe(new JsonOutputParser()).pipe(toolChainRunnable)\n", "await chain.invoke({ input: \"what's 3 plus 1132\", rendered_tools: renderedTools })"]}, {"cell_type": "markdown", "id": "b4a9c5aa-f60a-4017-af6f-1ff6e04bfb61", "metadata": {}, "source": ["## Returning tool inputs\n", "\n", "It can be helpful to return not only tool outputs but also tool inputs. We can easily do this with LCEL by `RunnablePassthrough.assign`-ing the tool output. This will take whatever the input is to the RunnablePassrthrough components (assumed to be a dictionary) and add a key to it while still passing through everything that's currently in the input:"]}, {"cell_type": "code", "execution_count": 22, "id": "45404406-859d-4caa-8b9d-5838162c80a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ name: 'add', arguments: [ 3, 1132 ], output: '1135' }\n"]}], "source": ["import { RunnablePassthrough } from \"@langchain/core/runnables\"\n", "\n", "const chain = prompt.pipe(model).pipe(new JsonOutputParser()).pipe(RunnablePassthrough.assign({ output: toolChainRunnable }))\n", "await chain.invoke({ input: \"what's 3 plus 1132\", rendered_tools: renderedTools })\n"]}, {"cell_type": "markdown", "id": "775252dc", "metadata": {}, "source": ["## What's next?\n", "\n", "This how-to guide shows the \"happy path\" when the model correctly outputs all the required tool information.\n", "\n", "In reality, if you're using more complex tools, you will start encountering errors from the model, especially for models that have not been fine tuned for tool calling and for less capable models.\n", "\n", "You will need to be prepared to add strategies to improve the output from the model; e.g.,\n", "\n", "- Provide few shot examples.\n", "- Add error handling (e.g., catch the exception and feed it back to the LLM to ask it to correct its previous output)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}