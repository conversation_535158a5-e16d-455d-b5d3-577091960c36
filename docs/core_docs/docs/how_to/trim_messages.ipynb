{"cells": [{"cell_type": "markdown", "id": "b5ee5b75-6876-4d62-9ade-5a7a808ae5a2", "metadata": {}, "source": ["# How to trim messages\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Messages](/docs/concepts/messages)\n", "- [Chat models](/docs/concepts/chat_models)\n", "- [Chaining](/docs/how_to/sequence/)\n", "- [Chat history](/docs/concepts/chat_history)\n", "\n", "The methods in this guide also require `@langchain/core>=0.2.8`.\n", "Please see here for a [guide on upgrading](/docs/how_to/installation/#installing-integration-packages).\n", "\n", ":::\n", "\n", "All models have finite context windows, meaning there's a limit to how many tokens they can take as input. If you have very long messages or a chain/agent that accumulates a long message is history, you'll need to manage the length of the messages you're passing in to the model.\n", "\n", "The `trimMessages` util provides some basic strategies for trimming a list of messages to be of a certain token length.\n", "\n", "## Getting the last `maxTokens` tokens\n", "\n", "To get the last `maxTokens` in the list of Messages we can set `strategy: \"last\"`. Notice that for our `tokenCounter` we can pass in a function (more on that below) or a language model (since language models have a message token counting method). It makes sense to pass in a model when you're trimming your messages to fit into the context window of that specific model:"]}, {"cell_type": "code", "execution_count": 2, "id": "c974633b-3bd0-4844-8a8f-85e3e25f13fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"role\": \"human\",\n", "  \"content\": \"and who is harrison chasing anyways\"\n", "}\n", "\n", "{\n", "  \"role\": \"ai\",\n", "  \"content\": \"Hmmm let me think.\\n\\nWhy, he's probably chasing after the last cup of coffee in the office!\"\n", "}\n", "\n", "{\n", "  \"role\": \"human\",\n", "  \"content\": \"what do you call a speechless parrot\"\n", "}\n"]}], "source": ["import { AIMessage, HumanMessage, SystemMessage, trimMessages } from \"@langchain/core/messages\";\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const messages = [\n", "    new SystemMessage(\"you're a good assistant, you always respond with a joke.\"),\n", "    new HumanMessage(\"i wonder why it's called langchain\"),\n", "    new AIMessage(\n", "        'Well, I guess they thought \"WordRope\" and \"SentenceString\" just didn\\'t have the same ring to it!'\n", "    ),\n", "    new HumanMessage(\"and who is harrison chasing anyways\"),\n", "    new AIMessage(\n", "        \"Hmmm let me think.\\n\\nWhy, he's probably chasing after the last cup of coffee in the office!\"\n", "    ),\n", "    new HumanMessage(\"what do you call a speechless parrot\"),\n", "];\n", "\n", "const trimmed = await trimMessages(\n", "    messages,\n", "    {\n", "        maxTokens: 45,\n", "        strategy: \"last\",\n", "        tokenCounter: new ChatOpenAI({ modelName: \"gpt-4\" }),\n", "    }\n", ");\n", "\n", "console.log(trimmed.map((x) => JSON.stringify({\n", "    role: x._getType(),\n", "    content: x.content,\n", "}, null, 2)).join(\"\\n\\n\"));"]}, {"cell_type": "markdown", "id": "d3f46654-c4b2-4136-b995-91c3febe5bf9", "metadata": {}, "source": ["If we want to always keep the initial system message we can specify `includeSystem: true`:"]}, {"cell_type": "code", "execution_count": 5, "id": "589b0223-3a73-44ec-8315-2dba3ee6117d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"you're a good assistant, you always respond with a joke.\",\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: \"you're a good assistant, you always respond with a joke.\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'Hmmm let me think.\\n' +\n", "        '\\n' +\n", "        \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'Hmmm let me think.\\n' +\n", "      '\\n' +\n", "      \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    usage_metadata: undefined\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'what do you call a speechless parrot',\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'what do you call a speechless parrot',\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["await trimMessages(\n", "    messages,\n", "    {\n", "        maxTokens: 45,\n", "        strategy: \"last\",\n", "        tokenCounter: new ChatOpenAI({ modelName: \"gpt-4\" }),\n", "        includeSystem: true\n", "    }\n", ");"]}, {"cell_type": "markdown", "id": "8a8b542c-04d1-4515-8d82-b999ea4fac4f", "metadata": {}, "source": ["If we want to allow splitting up the contents of a message we can specify `allowPartial: true`:"]}, {"cell_type": "code", "execution_count": 10, "id": "8c46a209-dddd-4d01-81f6-f6ae55d3225c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"you're a good assistant, you always respond with a joke.\",\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: \"you're a good assistant, you always respond with a joke.\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'Hmmm let me think.\\n' +\n", "        '\\n' +\n", "        \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'Hmmm let me think.\\n' +\n", "      '\\n' +\n", "      \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    usage_metadata: undefined\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'what do you call a speechless parrot',\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'what do you call a speechless parrot',\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["await trimMessages(\n", "    messages,\n", "    {\n", "        maxTokens: 50,\n", "        strategy: \"last\",\n", "        tokenCounter: new ChatOpenAI({ modelName: \"gpt-4\" }),\n", "        includeSystem: true,\n", "        allowPartial: true\n", "    }\n", ");"]}, {"cell_type": "markdown", "id": "306adf9c-41cd-495c-b4dc-e4f43dd7f8f8", "metadata": {}, "source": ["If we need to make sure that our first message (excluding the system message) is always of a specific type, we can specify `startOn`:"]}, {"cell_type": "code", "execution_count": 12, "id": "878a730b-fe44-4e9d-ab65-7b8f7b069de8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"you're a good assistant, you always respond with a joke.\",\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: \"you're a good assistant, you always respond with a joke.\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'and who is harrison chasing anyways',\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'and who is harrison chasing anyways',\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'Hmmm let me think.\\n' +\n", "        '\\n' +\n", "        \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'Hmmm let me think.\\n' +\n", "      '\\n' +\n", "      \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    usage_metadata: undefined\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'what do you call a speechless parrot',\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'what do you call a speechless parrot',\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["await trimMessages(\n", "    messages,\n", "    {\n", "        maxTokens: 60,\n", "        strategy: \"last\",\n", "        tokenCounter: new ChatOpenAI({ modelName: \"gpt-4\" }),\n", "        includeSystem: true,\n", "        startOn: \"human\"\n", "    }\n", ");"]}, {"cell_type": "markdown", "id": "7f5d391d-235b-4091-b2de-c22866b478f3", "metadata": {}, "source": ["## Getting the first `maxTokens` tokens\n", "\n", "We can perform the flipped operation of getting the *first* `maxTokens` by specifying `strategy: \"first\"`:"]}, {"cell_type": "code", "execution_count": 13, "id": "5f56ae54-1a39-4019-9351-3b494c003d5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"you're a good assistant, you always respond with a joke.\",\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: \"you're a good assistant, you always respond with a joke.\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"i wonder why it's called langchain\",\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: \"i wonder why it's called langchain\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["await trimMessages(\n", "    messages,\n", "    {\n", "        maxTokens: 45,\n", "        strategy: \"first\",\n", "        tokenCounter: new ChatOpenAI({ modelName: \"gpt-4\" }),\n", "    }\n", ");"]}, {"cell_type": "markdown", "id": "ab70bf70-1e5a-4d51-b9b8-a823bf2cf532", "metadata": {}, "source": ["## Writing a custom token counter\n", "\n", "We can write a custom token counter function that takes in a list of messages and returns an int."]}, {"cell_type": "code", "execution_count": 16, "id": "1c1c3b1e-2ece-49e7-a3b6-e69877c1633b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'Hmmm let me think.\\n' +\n", "        '\\n' +\n", "        \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'Hmmm let me think.\\n' +\n", "      '\\n' +\n", "      \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    usage_metadata: undefined\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'what do you call a speechless parrot',\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'what do you call a speechless parrot',\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["import { encodingForModel } from '@langchain/core/utils/tiktoken';\n", "import { BaseMessage, HumanMessage, AIMessage, ToolMessage, SystemMessage, MessageContent, MessageContentText } from '@langchain/core/messages';\n", "\n", "async function strTokenCounter(messageContent: MessageContent): Promise<number> {\n", "    if (typeof messageContent === 'string') {\n", "        return (\n", "            await encodingForModel(\"gpt-4\")\n", "          ).encode(messageContent).length;\n", "    } else {\n", "        if (messageContent.every((x) => x.type === \"text\" && x.text)) {\n", "            return (\n", "                await encodingForModel(\"gpt-4\")\n", "              ).encode((messageContent as MessageContentText[]).map(({ text }) => text).join(\"\")).length;\n", "        }\n", "        throw new Error(`Unsupported message content ${JSON.stringify(messageContent)}`);\n", "    }\n", "}\n", "\n", "async function tiktokenCounter(messages: BaseMessage[]): Promise<number> {\n", "  let numTokens = 3; // every reply is primed with <|start|>assistant<|message|>\n", "  const tokensPerMessage = 3;\n", "  const tokensPerName = 1;\n", "\n", "  for (const msg of messages) {\n", "    let role: string;\n", "    if (msg instanceof HumanMessage) {\n", "      role = 'user';\n", "    } else if (msg instanceof AIMessage) {\n", "      role = 'assistant';\n", "    } else if (msg instanceof ToolMessage) {\n", "      role = 'tool';\n", "    } else if (msg instanceof SystemMessage) {\n", "      role = 'system';\n", "    } else {\n", "      throw new Error(`Unsupported message type ${msg.constructor.name}`);\n", "    }\n", "\n", "    numTokens += tokensPerMessage + (await strTokenCounter(role)) + (await strTokenCounter(msg.content));\n", "\n", "    if (msg.name) {\n", "      numTokens += tokensPerName + (await strTokenCounter(msg.name));\n", "    }\n", "  }\n", "\n", "  return numTokens;\n", "}\n", "\n", "await trimMessages(messages, {\n", "  maxTokens: 45,\n", "  strategy: 'last',\n", "  tokenCounter: t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "});"]}, {"cell_type": "markdown", "id": "4b2a672b-c007-47c5-9105-617944dc0a6a", "metadata": {}, "source": ["## Chaining\n", "\n", "`trimMessages` can be used in an imperatively (like above) or declaratively, making it easy to compose with other components in a chain"]}, {"cell_type": "code", "execution_count": 17, "id": "96aa29b2-01e0-437c-a1ab-02fb0141cb57", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  lc_serializable: true,\n", "  lc_kwargs: {\n", "    content: 'Thanks! I do try to keep things light. But for a more serious answer, \"LangChain\" is likely named to reflect its focus on language processing and the way it connects different components or models together—essentially forming a \"chain\" of linguistic operations. The \"Lang\" part emphasizes its focus on language, while \"Chain\" highlights the interconnected workflows it aims to facilitate.',\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    additional_kwargs: { function_call: undefined, tool_calls: undefined },\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ 'langchain_core', 'messages' ],\n", "  content: 'Thanks! I do try to keep things light. But for a more serious answer, \"LangChain\" is likely named to reflect its focus on language processing and the way it connects different components or models together—essentially forming a \"chain\" of linguistic operations. The \"Lang\" part emphasizes its focus on language, while \"Chain\" highlights the interconnected workflows it aims to facilitate.',\n", "  name: undefined,\n", "  additional_kwargs: { function_call: undefined, tool_calls: undefined },\n", "  response_metadata: {\n", "    tokenUsage: { completionTokens: 77, promptTokens: 59, totalTokens: 136 },\n", "    finish_reason: 'stop'\n", "  },\n", "  id: undefined,\n", "  tool_calls: [],\n", "  invalid_tool_calls: [],\n", "  usage_metadata: { input_tokens: 59, output_tokens: 77, total_tokens: 136 }\n", "}\n"]}], "source": ["import { ChatOpenAI } from \"@langchain/openai\";\n", "import { trimMessages } from \"@langchain/core/messages\";\n", "\n", "const llm = new ChatOpenAI({ model: \"gpt-4o\" })\n", "\n", "// Notice we don't pass in messages. This creates\n", "// a RunnableLambda that takes messages as input\n", "const trimmer = trimMessages({\n", "    maxTokens: 45,\n", "    strategy: \"last\",\n", "    tokenCounter: llm,\n", "    includeSystem: true,\n", "})\n", "\n", "const chain = trimmer.pipe(llm);\n", "await chain.invoke(messages)"]}, {"cell_type": "markdown", "id": "4d91d390-e7f7-467b-ad87-d100411d7a21", "metadata": {}, "source": ["Looking at [the <PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/3793312c-a74b-4e77-92b4-f91b3d74ac5f/r) we can see that before the messages are passed to the model they are first trimmed.\n", "\n", "Looking at just the trimmer, we can see that it's a Runnable object that can be invoked like all Runnables:"]}, {"cell_type": "code", "execution_count": 18, "id": "1ff02d0a-353d-4fac-a77c-7c2c5262abd9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: \"you're a good assistant, you always respond with a joke.\",\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: \"you're a good assistant, you always respond with a joke.\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  },\n", "  AIMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'Hmmm let me think.\\n' +\n", "        '\\n' +\n", "        \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "      tool_calls: [],\n", "      invalid_tool_calls: [],\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'Hmmm let me think.\\n' +\n", "      '\\n' +\n", "      \"Why, he's probably chasing after the last cup of coffee in the office!\",\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    usage_metadata: undefined\n", "  },\n", "  HumanMessage {\n", "    lc_serializable: true,\n", "    lc_kwargs: {\n", "      content: 'what do you call a speechless parrot',\n", "      additional_kwargs: {},\n", "      response_metadata: {}\n", "    },\n", "    lc_namespace: [ 'langchain_core', 'messages' ],\n", "    content: 'what do you call a speechless parrot',\n", "    name: undefined,\n", "    additional_kwargs: {},\n", "    response_metadata: {},\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["await trimmer.invoke(messages)"]}, {"cell_type": "markdown", "id": "dc4720c8-4062-4ebc-9385-58411202ce6e", "metadata": {}, "source": ["## Using with ChatMessageHistory\n", "\n", "Trimming messages is especially useful when [working with chat histories](/docs/how_to/message_history/), which can get arbitrarily long:"]}, {"cell_type": "code", "execution_count": 19, "id": "a9517858-fc2f-4dc3-898d-bf98a0e905a0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  lc_serializable: true,\n", "  lc_kwargs: {\n", "    content: 'A \"polly-no-want-a-cracker\"!',\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    additional_kwargs: { function_call: undefined, tool_calls: undefined },\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ 'langchain_core', 'messages' ],\n", "  content: 'A \"polly-no-want-a-cracker\"!',\n", "  name: undefined,\n", "  additional_kwargs: { function_call: undefined, tool_calls: undefined },\n", "  response_metadata: {\n", "    tokenUsage: { completionTokens: 11, promptTokens: 57, totalTokens: 68 },\n", "    finish_reason: 'stop'\n", "  },\n", "  id: undefined,\n", "  tool_calls: [],\n", "  invalid_tool_calls: [],\n", "  usage_metadata: { input_tokens: 57, output_tokens: 11, total_tokens: 68 }\n", "}\n"]}], "source": ["import { InMemoryChatMessageHistory } from \"@langchain/core/chat_history\";\n", "import { RunnableWithMessageHistory } from \"@langchain/core/runnables\";\n", "import { HumanMessage, trimMessages } from \"@langchain/core/messages\";\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const chatHistory = new InMemoryChatMessageHistory(messages.slice(0, -1))\n", "\n", "const dummyGetSessionHistory = async (sessionId: string) => {\n", "    if (sessionId !== \"1\") {\n", "        throw new Error(\"Session not found\");\n", "      }\n", "      return chatHistory;\n", "  }\n", "\n", "  const llm = new ChatOpenAI({ model: \"gpt-4o\" });\n", "\n", "  const trimmer = trimMessages({\n", "    maxTokens: 45,\n", "    strategy: \"last\",\n", "    tokenCounter: llm,\n", "    includeSystem: true,\n", "  });\n", "\n", "const chain = trimmer.pipe(llm);\n", "const chainWithHistory = new RunnableWithMessageHistory({\n", "    runnable: chain,\n", "    getMessageHistory: dummyGetSessionHistory,\n", "})\n", "await chainWithHistory.invoke(\n", "    [new HumanMessage(\"what do you call a speechless parrot\")],\n", "    { configurable: { sessionId: \"1\"} },\n", ")"]}, {"cell_type": "markdown", "id": "556b7b4c-43cb-41de-94fc-1a41f4ec4d2e", "metadata": {}, "source": ["Looking at [the <PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/cfc76880-5895-4852-b7d0-12916448bdb2/r) we can see that we retrieve all of our messages but before the messages are passed to the model they are trimmed to be just the system message and last human message."]}, {"cell_type": "markdown", "id": "75dc7b84-b92f-44e7-8beb-ba22398e4efb", "metadata": {}, "source": ["## API reference\n", "\n", "For a complete description of all arguments head to the [API reference](https://api.js.langchain.com/functions/langchain_core.messages.trimMessages.html)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}