---
keywords: [similaritySearchWithScore]
---

# How to create and query vector stores

:::info
Head to [Integrations](/docs/integrations/vectorstores) for documentation on built-in integrations with vectorstore providers.
:::

:::info Prerequisites

This guide assumes familiarity with the following concepts:

- [Vector stores](/docs/concepts/#vectorstores)
- [Embeddings](/docs/concepts/embedding_models)
- [Document loaders](/docs/concepts/document_loaders)

:::

One of the most common ways to store and search over unstructured data is to embed it and store the resulting embedding
vectors, and then at query time to embed the unstructured query and retrieve the embedding vectors that are
'most similar' to the embedded query. A vector store takes care of storing embedded data and performing vector search
for you.

This walkthrough uses a basic, unoptimized implementation called [`MemoryVectorStore`](https://api.js.langchain.com/classes/langchain.vectorstores_memory.MemoryVectorStore.html) that stores embeddings in-memory and does an exact, linear search for the most similar embeddings.
LangChain contains many built-in integrations - see [this section](/docs/how_to/vectorstores/#which-one-to-pick) for more, or the [full list of integrations](/docs/integrations/vectorstores/).

## Creating a new index

Most of the time, you'll need to load and prepare the data you want to search over. Here's an example that loads a recent speech from a file:

import ExampleLoader from "@examples/indexes/vector_stores/memory_fromdocs.ts";

<CodeBlock language="typescript">{ExampleLoader}</CodeBlock>

Most of the time, you'll need to split the loaded text as a preparation step. See [this section](/docs/concepts/text_splitters) to learn more about text splitters.

## Creating a new index from texts

If you have already prepared the data you want to search over, you can initialize a vector store directly from text chunks:

import CodeBlock from "@theme/CodeBlock";
import ExampleTexts from "@examples/indexes/vector_stores/memory.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{ExampleTexts}</CodeBlock>

## Which one to pick?

Here's a quick guide to help you pick the right vector store for your use case:

- If you're after something that can just run inside your Node.js application, in-memory, without any other servers to stand up, then go for [HNSWLib](/docs/integrations/vectorstores/hnswlib), [Faiss](/docs/integrations/vectorstores/faiss), [LanceDB](/docs/integrations/vectorstores/lancedb) or [CloseVector](/docs/integrations/vectorstores/closevector)
- If you're looking for something that can run in-memory in browser-like environments, then go for [MemoryVectorStore](/docs/integrations/vectorstores/memory) or [CloseVector](/docs/integrations/vectorstores/closevector)
- If you come from Python and you were looking for something similar to FAISS, try [HNSWLib](/docs/integrations/vectorstores/hnswlib) or [Faiss](/docs/integrations/vectorstores/faiss)
- If you're looking for an open-source full-featured vector database that you can run locally in a docker container, then go for [Chroma](/docs/integrations/vectorstores/chroma)
- If you're looking for an open-source vector database that offers low-latency, local embedding of documents and supports apps on the edge, then go for [Zep](/docs/integrations/vectorstores/zep)
- If you're looking for an open-source production-ready vector database that you can run locally (in a docker container) or hosted in the cloud, then go for [Weaviate](/docs/integrations/vectorstores/weaviate).
- If you're using Supabase already then look at the [Supabase](/docs/integrations/vectorstores/supabase) vector store to use the same Postgres database for your embeddings too
- If you're looking for a production-ready vector store you don't have to worry about hosting yourself, then go for [Pinecone](/docs/integrations/vectorstores/pinecone)
- If you are already utilizing SingleStore, or if you find yourself in need of a distributed, high-performance database, you might want to consider the [SingleStore](/docs/integrations/vectorstores/singlestore) vector store.
- If you are looking for an online MPP (Massively Parallel Processing) data warehousing service, you might want to consider the [AnalyticDB](/docs/integrations/vectorstores/analyticdb) vector store.
- If you're in search of a cost-effective vector database that allows run vector search with SQL, look no further than [MyScale](/docs/integrations/vectorstores/myscale).
- If you're in search of a vector database that you can load from both the browser and server side, check out [CloseVector](/docs/integrations/vectorstores/closevector). It's a vector database that aims to be cross-platform.
- If you're looking for a scalable, open-source columnar database with excellent performance for analytical queries, then consider [ClickHouse](/docs/integrations/vectorstores/clickhouse).

## Next steps

You've now learned how to load data into a vectorstore.

Next, check out the [full tutorial on retrieval-augmented generation](/docs/tutorials/rag).
