---
sidebar_class_name: beta
---

import CodeBlock from "@theme/CodeBlock";

# Datadog LLM Observability

:::warning
LLM Observability is in public beta, and its API is subject to change.
:::

With [Datadog LLM Observability](https://docs.datadoghq.com/llm_observability/), you can monitor, troubleshoot, and evaluate your LLM-powered applications, such as chatbots. You can investigate the root cause of issues, monitor operational performance, and evaluate the quality, privacy, and safety of your LLM applications.

This is an experimental community implementation, and it is not officially supported by Datadog. It is based on the [Datadog LLM Observability API](https://docs.datadoghq.com/llm_observability/api).

## Setup

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

## Usage

import UsageExample from "@examples/callbacks/datadog.ts";

<CodeBlock language="typescript">{UsageExample}</CodeBlock>
