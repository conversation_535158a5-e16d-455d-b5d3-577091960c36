---
sidebar_label: <PERSON><PERSON><PERSON>
---

import CodeBlock from "@theme/CodeBlock";

# ChatAlibabaTongyi

LangChain.js supports the Alibaba qwen family of models.

## Setup

You'll need to sign up for an Alibaba API key and set it as an environment variable named `ALIBABA_API_KEY`.

Then, you'll need to install the [`@langchain/community`](https://www.npmjs.com/package/@langchain/community) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

## Usage

Here's an example:

import Tongyi from "@examples/models/chat/integration_alitongyi.ts";

<CodeBlock language="typescript">{Tongyi}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
