{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Arcjet Redact\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# Arcjet Redact\n", "\n", "The [Arcjet](https://arcjet.com) redact integration allows you to redact sensitive user information from your prompts before sending it to a Chat Model.\n", "\n", "Arcjet Redact runs entirely on your own machine and never sends data anywhere else, ensuring best in class privacy and performance.\n", "\n", "The Arcjet Redact object is not a chat model itself, instead it wraps an LLM. It redacts the text that is inputted to it and then unredacts the output of the wrapped chat model before returning it. \n", "\n", "\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | PY Support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| Arcjet | @langchain/community | ❌ | ✅ | ❌ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "### Installation\n", "\n", "Install the Arcjet Redaction Library:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @arcjet/redact\n", "</Npm2Yarn>\n", "\n", "```\n", "\n", "And install LangChain Community:\n", "\n", "\n", "```{=mdx}\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "\n", "And now you're ready to start protecting your chat model calls with Arcjet Redaction!\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "code", "execution_count": 2, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import {\n", "  ArcjetRedact,\n", "  ArcjetSensitiveInfoType,\n", "} from \"@langchain/community/chat_models/arcjet\";\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "// Create an instance of another chat model for Arcjet to wrap\n", "const openai = new ChatOpenAI({\n", "  temperature: 0.8,\n", "  model: \"gpt-3.5-turbo-0125\",\n", "});\n", "\n", "const arcjetRedactOptions = {\n", "  // Specify a LLM that Arcjet Redact will call once it has redacted the input.\n", "  chatModel: openai,\n", "\n", "  // Specify the list of entities that should be redacted.\n", "  // If this isn't specified then all entities will be redacted.\n", "  entities: [\"email\", \"phone-number\", \"ip-address\", \"custom-entity\"] as ArcjetSensitiveInfoType[],\n", "\n", "  // You can provide a custom detect function to detect entities that we don't support yet.\n", "  // It takes a list of tokens and you return a list of identified types or undefined.\n", "  // The undefined types that you return should be added to the entities list if used.\n", "  detect: (tokens: string[]) => {\n", "    return tokens.map((t) => t === \"some-sensitive-info\" ? \"custom-entity\" : undefined)\n", "  },\n", "\n", "  // The number of tokens to provide to the custom detect function. This defaults to 1.\n", "  // It can be used to provide additional context when detecting custom entity types.\n", "  contextWindowSize: 1,\n", "\n", "  // This allows you to provide custom replacements when redacting. Please ensure\n", "  // that the replacements are unique so that unredaction works as expected.\n", "  replace: (identifiedType: string) => {\n", "    return identifiedType === \"email\" ? \"<EMAIL>\" : undefined;\n", "  },\n", "};\n", "\n", "const arcjetRedact = new ArcjetRedact(arcjetRedactOptions);\n", "\n", "const response = await arcjetRedact.invoke(\n", "  \"My email <NAME_EMAIL>, here is some-sensitive-info\"\n", ");"]}, {"cell_type": "code", "execution_count": null, "id": "165c0f4f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}