{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Azure OpenAI\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# AzureChatOpenAI\n", "\n", "Azure OpenAI is a Microsoft Azure service that provides powerful language models from OpenAI.\n", "\n", "This will help you getting started with AzureChatOpenAI [chat models](/docs/concepts/chat_models). For detailed documentation of all AzureChatOpenAI features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_openai.AzureChatOpenAI.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/chat/azure_chat_openai) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [AzureChatOpenAI](https://api.js.langchain.com/classes/langchain_openai.AzureChatOpenAI.html) | [`@langchain/openai`](https://www.npmjs.com/package/@langchain/openai) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/openai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/openai?style=flat-square&label=%20&) |\n", "\n", "### Model features\n", "\n", "See the links in the table headers below for guides on how to use specific features.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | ✅ | ✅ | ✅ | \n", "\n", "## Setup\n", "\n", "[Azure OpenAI](https://azure.microsoft.com/products/ai-services/openai-service/) is a cloud service to help you quickly develop generative AI experiences with a diverse set of prebuilt and curated models from OpenAI, Meta and beyond.\n", "\n", "LangChain.js supports integration with [Azure OpenAI](https://azure.microsoft.com/products/ai-services/openai-service/) using the new Azure integration in the [OpenAI SDK](https://github.com/openai/openai-node).\n", "\n", "You can learn more about Azure OpenAI and its difference with the OpenAI API on [this page](https://learn.microsoft.com/azure/ai-services/openai/overview).\n", "\n", "### Credentials\n", "\n", "If you don't have an Azure account, you can [create a free account](https://azure.microsoft.com/free/) to get started.\n", "\n", "You'll also need to have an Azure OpenAI instance deployed. You can deploy a version on Azure Portal following [this guide](https://learn.microsoft.com/azure/ai-services/openai/how-to/create-resource?pivots=web-portal).\n", "\n", "Once you have your instance running, make sure you have the name of your instance and key. You can find the key in the Azure Portal, under the \"Keys and Endpoint\" section of your instance. Then, if using Node.js, you can set your credentials as environment variables:\n", "\n", "```bash\n", "AZURE_OPENAI_API_INSTANCE_NAME=<YOUR_INSTANCE_NAME>\n", "AZURE_OPENAI_API_DEPLOYMENT_NAME=<YOUR_DEPLOYMENT_NAME>\n", "AZURE_OPENAI_API_KEY=<YOUR_KEY>\n", "AZURE_OPENAI_API_VERSION=\"2024-02-01\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain AzureChatOpenAI integration lives in the `@langchain/openai` package:\n", "\n", "```{=mdx}\n", "\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/openai @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 3, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { AzureChatOpenAI } from \"@langchain/openai\" \n", "\n", "const llm = new AzureChatOpenAI({\n", "    model: \"gpt-4o\",\n", "    temperature: 0,\n", "    maxTokens: undefined,\n", "    maxRetries: 2,\n", "    azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY, // In Node.js defaults to process.env.AZURE_OPENAI_API_KEY\n", "    azureOpenAIApiInstanceName: process.env.AZURE_OPENAI_API_INSTANCE_NAME, // In Node.js defaults to process.env.AZURE_OPENAI_API_INSTANCE_NAME\n", "    azureOpenAIApiDeploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME, // In Node.js defaults to process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME\n", "    azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION, // In Node.js defaults to process.env.AZURE_OPENAI_API_VERSION\n", "})"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 4, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-9qrWKByvVrzWMxSn8joRZAklHoB32\",\n", "  \"content\": \"J'adore la programmation.\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 8,\n", "      \"promptTokens\": 31,\n", "      \"totalTokens\": 39\n", "    },\n", "    \"finish_reason\": \"stop\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 31,\n", "    \"output_tokens\": 8,\n", "    \"total_tokens\": 39\n", "  }\n", "}\n"]}], "source": ["const aiMsg = await llm.invoke([\n", "    [\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ],\n", "    [\"human\", \"I love programming.\"],\n", "])\n", "aiMsg"]}, {"cell_type": "code", "execution_count": 5, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore la programmation.\n"]}], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 6, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-9qrWR7WiNjZ3leSG4Wd77cnKEVivv\",\n", "  \"content\": \"Ich liebe das Programmieren.\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"completionTokens\": 6,\n", "      \"promptTokens\": 26,\n", "      \"totalTokens\": 32\n", "    },\n", "    \"finish_reason\": \"stop\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 26,\n", "    \"output_tokens\": 6,\n", "    \"total_tokens\": 32\n", "  }\n", "}\n"]}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "    [\n", "        [\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ],\n", "        [\"human\", \"{input}\"],\n", "    ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "    {\n", "        input_language: \"English\",\n", "        output_language: \"German\",\n", "        input: \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "d1ee55bc-ffc8-4cfa-801c-993953a08cfd", "metadata": {}, "source": ["## Using Azure Managed Identity\n", "\n", "If you're using Azure Managed Identity, you can configure the credentials like this:"]}, {"cell_type": "code", "execution_count": 7, "id": "d7f47b2a", "metadata": {}, "outputs": [], "source": ["import {\n", "  DefaultAzureCredential,\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "} from \"@azure/identity\";\n", "import { AzureChatOpenAI } from \"@langchain/openai\";\n", "\n", "const credentials = new DefaultAzureCredential();\n", "const azureADTokenProvider = getBearerTokenProvider(\n", "  credentials,\n", "  \"https://cognitiveservices.azure.com/.default\"\n", ");\n", "\n", "const llmWithManagedIdentity = new AzureChatOpenAI({\n", "  azureADTokenProvider,\n", "  azureOpenAIApiInstanceName: \"<your_instance_name>\",\n", "  azureOpenAIApiDeploymentName: \"<your_deployment_name>\",\n", "  azureOpenAIApiVersion: \"<api_version>\",\n", "});"]}, {"cell_type": "markdown", "id": "6a889856", "metadata": {}, "source": ["## Using a different domain\n", "\n", "If your instance is hosted under a domain other than the default `openai.azure.com`, you'll need to use the alternate `AZURE_OPENAI_BASE_PATH` environment variable.\n", "For example, here's how you would connect to the domain `https://westeurope.api.microsoft.com/openai/deployments/{DEPLOYMENT_NAME}`:"]}, {"cell_type": "code", "execution_count": 8, "id": "ace7f876", "metadata": {}, "outputs": [], "source": ["import { AzureChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llmWithDifferentDomain = new AzureChatOpenAI({\n", "  temperature: 0.9,\n", "  azureOpenAIApiKey: \"<your_key>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_KEY\n", "  azureOpenAIApiDeploymentName: \"<your_deployment_name>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME\n", "  azureOpenAIApiVersion: \"<api_version>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_VERSION\n", "  azureOpenAIBasePath:\n", "    \"https://westeurope.api.microsoft.com/openai/deployments\", // In Node.js defaults to process.env.AZURE_OPENAI_BASE_PATH\n", "});\n"]}, {"cell_type": "markdown", "id": "092e7a38", "metadata": {}, "source": ["## Custom headers\n", "\n", "You can specify custom headers by passing in a `configuration` field:"]}, {"cell_type": "code", "execution_count": null, "id": "43503a94", "metadata": {}, "outputs": [], "source": ["import { AzureChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llmWithCustomHeaders = new AzureChatOpenAI({\n", "  azureOpenAIApiKey: process.env.AZURE_OPENAI_API_KEY, // In Node.js defaults to process.env.AZURE_OPENAI_API_KEY\n", "  azureOpenAIApiInstanceName: process.env.AZURE_OPENAI_API_INSTANCE_NAME, // In Node.js defaults to process.env.AZURE_OPENAI_API_INSTANCE_NAME\n", "  azureOpenAIApiDeploymentName: process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME, // In Node.js defaults to process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME\n", "  azureOpenAIApiVersion: process.env.AZURE_OPENAI_API_VERSION, // In Node.js defaults to process.env.AZURE_OPENAI_API_VERSION\n", "  configuration: {\n", "    defaultHeaders: {\n", "      \"x-custom-header\": `SOME_VALUE`,\n", "    },\n", "  },\n", "});\n", "\n", "await llmWithCustomHeaders.invoke(\"Hi there!\");"]}, {"cell_type": "markdown", "id": "1a6b849d", "metadata": {}, "source": ["The `configuration` field also accepts other `ClientOptions` parameters accepted by the official SDK.\n", "\n", "**Note:** The specific header `api-key` currently cannot be overridden in this manner and will pass through the value from `azureOpenAIApiKey`."]}, {"cell_type": "markdown", "id": "0ac0310c", "metadata": {}, "source": ["## Migration from Azure OpenAI SDK\n", "\n", "If you are using the deprecated Azure OpenAI SDK with the `@langchain/azure-openai` package, you can update your code to use the new Azure integration following these steps:\n", "\n", "1. Install the new `@langchain/openai` package and remove the previous `@langchain/azure-openai` package:\n", "\n", "```{=mdx}\n", "\n", "<Npm2Yarn>\n", "  @langchain/openai\n", "</Npm2Yarn>\n", "\n", "```\n", "\n", "```bash\n", "npm uninstall @langchain/azure-openai\n", "```\n", "\n", "   \n", "2. Update your imports to use the new `AzureChatOpenAI` class from the `@langchain/openai` package:\n", "   ```typescript\n", "   import { AzureChatOpenAI } from \"@langchain/openai\";\n", "   ```\n", "3. Update your code to use the new `AzureChatOpenAI` class and pass the required parameters:\n", "\n", "   ```typescript\n", "   const model = new AzureChatOpenAI({\n", "     azureOpenAIApiKey: \"<your_key>\",\n", "     azureOpenAIApiInstanceName: \"<your_instance_name>\",\n", "     azureOpenAIApiDeploymentName: \"<your_deployment_name>\",\n", "     azureOpenAIApiVersion: \"<api_version>\",\n", "   });\n", "   ```\n", "\n", "   Notice that the constructor now requires the `azureOpenAIApiInstanceName` parameter instead of the `azureOpenAIEndpoint` parameter, and adds the `azureOpenAIApiVersion` parameter to specify the API version.\n", "\n", "   - If you were using Azure Managed Identity, you now need to use the `azureADTokenProvider` parameter to the constructor instead of `credentials`, see the [Azure Managed Identity](#using-azure-managed-identity) section for more details.\n", "\n", "   - If you were using environment variables, you now have to set the `AZURE_OPENAI_API_INSTANCE_NAME` environment variable instead of `AZURE_OPENAI_API_ENDPOINT`, and add the `AZURE_OPENAI_API_VERSION` environment variable to specify the API version.\n"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all AzureChatOpenAI features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_openai.AzureChatOpenAI.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}