---
sidebar_label: <PERSON><PERSON>
---

import CodeBlock from "@theme/CodeBlock";

# ChatBaiduQianfan

## Setup

You'll first need to install the [`@langchain/baidu-qianfan`](https://www.npmjs.com/package/@langchain/baidu-qianfan) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/baidu-qianfan @langchain/core
```

Available models: `ERNIE-Bot`,`ERNIE-Lite-8K`,`ERNIE-Bot-4`,`ERNIE-Speed-8K`,`ERNIE-Speed-128K`,`ERNIE-4.0-8K`,
`ERNIE-4.0-8K-Preview`,`ERNIE-3.5-8K`,`ERNIE-3.5-8K-Preview`,`ERNIE-Lite-8K`,`ERNIE-Tiny-8K`,`ERNIE-Character-8K`,
`ERNIE Speed-AppBuilder`

Abandoned models: `ERNIE-Bot-turbo`

## Usage

import ChatBaiduQianfanExample from "@examples/models/chat/chat_baidu_qianfan.ts";

<CodeBlock language="typescript">{ChatBaiduQianfanExample}</CodeBlock>

## Streaming

Qianfan's API also supports streaming token responses. The example below demonstrates how to use this feature.

import ChatBaiduQianfanStreamExample from "@examples/models/chat/chat_stream_baidu_qianfan.ts";

<CodeBlock language="typescript">{ChatBaiduQianfanStreamExample}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
