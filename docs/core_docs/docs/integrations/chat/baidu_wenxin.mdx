---
sidebar_label: <PERSON><PERSON> Wenxin
sidebar_class_name: hidden
---

import CodeBlock from "@theme/CodeBlock";

# ChatBaiduWenxin

:::warning
This class has been deprecated.

Use the [`@langchain/baidu-qianfan`](/docs/integrations/chat/baidu_qianfan/) package instead.
:::

LangChain.js supports Baidu's ERNIE-bot family of models. Here's an example:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

Available models: `ERNIE-Bot`,`ERNIE-Bot-turbo`,`ERNIE-Bot-4`,`ERNIE-Speed-8K`,`ERNIE-Speed-128K`,`ERNIE-4.0-8K`,
`ERNIE-4.0-8K-Preview`,`ERNIE-3.5-8K`,`ERNIE-3.5-8K-Preview`,`ERNIE-Lite-8K`,`ERNIE-Tiny-8K`,`ERNIE-Character-8K`,
`ERNIE Speed-AppBuilder`

Abandoned models: `ERNIE-Bot-turbo`

import Wenxin from "@examples/models/chat/integration_baiduwenxin.ts";

<CodeBlock language="typescript">{Wenxin}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
