{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Amazon Bedrock\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# BedrockChat\n", "\n", "[Amazon Bedrock](https://aws.amazon.com/bedrock/) is a fully managed service that offers a choice of high-performing foundation models (FMs) from leading AI companies like AI21 Labs, Anthropic, Cohere, Meta, Stability AI, and Amazon via a single API, along with a broad set of capabilities you need to build generative AI applications with security, privacy, and responsible AI. \n", "\n", "This will help you getting started with Amazon Bedrock [chat models](/docs/concepts/chat_models). For detailed documentation of all `BedrockChat` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_chat_models_bedrock.BedrockChat.html).\n", "\n", "```{=mdx}\n", ":::tip\n", "The newer [`ChatBedrockConverse` chat model is now available via the dedicated `@langchain/aws`](/docs/integrations/chat/bedrock_converse) integration package. Use [tool calling](/docs/concepts/tool_calling) with more models with this package.\n", ":::\n", "```\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/chat/bedrock/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [`BedrockChat`](https://api.js.langchain.com/classes/langchain_community_chat_models_bedrock.BedrockChat.html) | [`@langchain/community`](https://npmjs.com/@langchain/community) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "### Model features\n", "\n", "See the links in the table headers below for guides on how to use specific features.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ✅ | ❌ | ❌ | ✅ | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "To access Bedrock models you'll need to create an AWS account, set up the Bedrock API service, get an access key ID and secret key, and install the `@langchain/community` integration package.\n", "\n", "### Credentials\n", "\n", "Head to the [AWS docs](https://docs.aws.amazon.com/bedrock/latest/userguide/getting-started.html) to sign up for AWS and setup your credentials. You'll also need to turn on model access for your account, which you can do by [following these instructions](https://docs.aws.amazon.com/bedrock/latest/userguide/model-access.html).\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain `BedrockChat` integration lives in the `@langchain/community` package. You'll also need to install several official AWS packages as peer dependencies:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core @aws-crypto/sha256-js @aws-sdk/credential-provider-node @smithy/protocol-http @smithy/signature-v4 @smithy/eventstream-codec @smithy/util-utf8 @aws-sdk/types\n", "</Npm2Yarn>\n", "```\n", "\n", "You can also use BedrockChat in web environments such as Edge functions or Cloudflare Workers by omitting the @aws-sdk/credential-provider-node dependency and using the web entrypoint:\n", "\n", "```{=mdx}\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core @aws-crypto/sha256-js @smithy/protocol-http @smithy/signature-v4 @smithy/eventstream-codec @smithy/util-utf8 @aws-sdk/types\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Currently, only Anthropic, Cohere, and Mistral models are supported with the chat model integration. For foundation models from AI21 or Amazon, see the [text generation Bedrock variant](/docs/integrations/llms/bedrock/).\n", "\n", "There are a few different ways to authenticate with AWS - the below examples rely on an access key, secret access key and region set in your environment variables:"]}, {"cell_type": "code", "execution_count": 1, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { BedrockChat } from \"@langchain/community/chat_models/bedrock\";\n", "\n", "const llm = new BedrockChat({\n", "  model: \"anthropic.claude-3-5-sonnet-20240620-v1:0\",\n", "  region: process.env.BEDROCK_AWS_REGION,\n", "  credentials: {\n", "    accessKeyId: process.env.BEDROCK_AWS_ACCESS_KEY_ID!,\n", "    secretAccessKey: process.env.BEDROCK_AWS_SECRET_ACCESS_KEY!,\n", "  },\n", "  // endpointUrl: \"custom.amazonaws.com\",\n", "  // modelKwargs: {\n", "  //   anthropic_version: \"bedrock-2023-05-31\",\n", "  // },\n", "});"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"content\": \"J'adore la programmation.\",\n", "  \"additional_kwargs\": {\n", "    \"id\": \"msg_bdrk_01RwhfuWkLLcp7ks1X3u8bwd\"\n", "  },\n", "  \"response_metadata\": {\n", "    \"type\": \"message\",\n", "    \"role\": \"assistant\",\n", "    \"model\": \"claude-3-5-sonnet-20240620\",\n", "    \"stop_reason\": \"end_turn\",\n", "    \"stop_sequence\": null,\n", "    \"usage\": {\n", "      \"input_tokens\": 29,\n", "      \"output_tokens\": 11\n", "    }\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": []\n", "}\n"]}], "source": ["const aiMsg = await llm.invoke([\n", "  [\n", "    \"system\",\n", "    \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "  ],\n", "  [\"human\", \"I love programming.\"],\n", "])\n", "aiMsg"]}, {"cell_type": "code", "execution_count": 3, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore la programmation.\n"]}], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 4, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"content\": \"Here's the German translation:\\n\\nIch liebe Programmieren.\",\n", "  \"additional_kwargs\": {\n", "    \"id\": \"msg_bdrk_01RtUH3qrYJPUdutYoxphFkv\"\n", "  },\n", "  \"response_metadata\": {\n", "    \"type\": \"message\",\n", "    \"role\": \"assistant\",\n", "    \"model\": \"claude-3-5-sonnet-20240620\",\n", "    \"stop_reason\": \"end_turn\",\n", "    \"stop_sequence\": null,\n", "    \"usage\": {\n", "      \"input_tokens\": 23,\n", "      \"output_tokens\": 18\n", "    }\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": []\n", "}\n"]}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\n", "      \"system\",\n", "      \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "    ],\n", "    [\"human\", \"{input}\"],\n", "  ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    input_language: \"English\",\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "d1ee55bc-ffc8-4cfa-801c-993953a08cfd", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "Tool calling with Bedrock models works in a similar way to [other models](/docs/how_to/tool_calling), but note that not all Bedrock models support tool calling. Please refer to the [AWS model documentation](https://docs.aws.amazon.com/bedrock/latest/APIReference/welcome.html) for more information."]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `BedrockChat` features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_community_chat_models_bedrock.BedrockChat.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}