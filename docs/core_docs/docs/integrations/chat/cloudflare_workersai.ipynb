{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Cloudflare Workers AI\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatCloudflareWorkersAI\n", "\n", "[Workers AI](https://developers.cloudflare.com/workers-ai/) allows you to run machine learning models, on the Cloudflare network, from your own code.\n", "\n", "This will help you getting started with Cloudflare Workers AI [chat models](/docs/concepts/chat_models). For detailed documentation of all `ChatCloudflareWorkersAI` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_cloudflare.ChatCloudflareWorkersAI.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | PY support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [`ChatCloudflareWorkersAI`](https://api.js.langchain.com/classes/langchain_cloudflare.ChatCloudflareWorkersAI.html) | [`@langchain/cloudflare`](https://npmjs.com/@langchain/cloudflare) | ❌ | ✅ | ❌ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/cloudflare?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/cloudflare?style=flat-square&label=%20&) |\n", "\n", "### Model features\n", "\n", "See the links in the table headers below for guides on how to use specific features.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: |\n", "| ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ✅ | ❌ | ❌ | \n", "\n", "## Setup\n", "\n", "To access Cloudflare Workers AI models you'll need to create a Cloudflare account, get an API key, and install the `@langchain/cloudflare` integration package.\n", "\n", "### Credentials\n", "\n", "Head [to this page](https://developers.cloudflare.com/workers-ai/) to sign up to Cloudflare and generate an API key. Once you've done this, note your `CLOUDFLARE_ACCOUNT_ID` and `CLOUDFLARE_API_TOKEN`.\n", "\n", "Passing a binding within a Cloudflare Worker is not yet supported.\n", "\n", "### Installation\n", "\n", "The LangChain ChatCloudflareWorkersAI integration lives in the `@langchain/cloudflare` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/cloudflare @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "fb6997f0", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "// @ts-expect-error <PERSON><PERSON> is not recognized\n", "const CLOUDFLARE_ACCOUNT_ID = Deno.env.get(\"CLOUDFLARE_ACCOUNT_ID\");\n", "// @ts-expect-error <PERSON><PERSON> is not recognized\n", "const CLOUDFLARE_API_TOKEN = Deno.env.get(\"CLOUDFLARE_API_TOKEN\");"]}, {"cell_type": "code", "execution_count": 2, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { ChatCloudflareWorkersAI } from \"@langchain/cloudflare\";\n", "\n", "const llm = new ChatCloudflareWorkersAI({\n", "  model: \"@cf/meta/llama-2-7b-chat-int8\", // Default value\n", "  cloudflareAccountId: CLOUDFLARE_ACCOUNT_ID,\n", "  cloudflareApiToken: CLOUDFLARE_API_TOKEN,\n", "  // Pass a custom base URL to use Cloudflare AI Gateway\n", "  // baseUrl: `https://gateway.ai.cloudflare.com/v1/{YOUR_ACCOUNT_ID}/{GATEWAY_NAME}/workers-ai/`,\n", "});"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 3, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["AIMessage {\n", "  lc_serializable: \u001b[33mtrue\u001b[39m,\n", "  lc_kwargs: {\n", "    content: \u001b[32m'I can help with that! The translation of \"I love programming\" in French is:\\n'\u001b[39m +\n", "      \u001b[32m\"\\n\"\u001b[39m +\n", "      \u001b[32m`\"<PERSON>'adore le programmati`\u001b[39m... 4 more characters,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "  content: \u001b[32m'I can help with that! The translation of \"I love programming\" in French is:\\n'\u001b[39m +\n", "    \u001b[32m\"\\n\"\u001b[39m +\n", "    \u001b[32m`\"<PERSON>'adore le programmati`\u001b[39m... 4 more characters,\n", "  name: \u001b[90mundefined\u001b[39m,\n", "  additional_kwargs: {},\n", "  response_metadata: {},\n", "  tool_calls: [],\n", "  invalid_tool_calls: []\n", "}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["const aiMsg = await llm.invoke([\n", "  [\n", "    \"system\",\n", "    \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "  ],\n", "  [\"human\", \"I love programming.\"],\n", "])\n", "aiMsg"]}, {"cell_type": "code", "execution_count": 4, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I can help with that! The translation of \"I love programming\" in French is:\n", "\n", "\"J'adore le programmation.\"\n"]}], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 5, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage {\n", "  lc_serializable: \u001b[33mtrue\u001b[39m,\n", "  lc_kwargs: {\n", "    content: \u001b[32m\"Das Programmieren ist für mich sehr Valent sein!\"\u001b[39m,\n", "    tool_calls: [],\n", "    invalid_tool_calls: [],\n", "    additional_kwargs: {},\n", "    response_metadata: {}\n", "  },\n", "  lc_namespace: [ \u001b[32m\"langchain_core\"\u001b[39m, \u001b[32m\"messages\"\u001b[39m ],\n", "  content: \u001b[32m\"Das Programmieren ist für mich sehr Valent sein!\"\u001b[39m,\n", "  name: \u001b[90mundefined\u001b[39m,\n", "  additional_kwargs: {},\n", "  response_metadata: {},\n", "  tool_calls: [],\n", "  invalid_tool_calls: []\n", "}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\n", "      \"system\",\n", "      \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "    ],\n", "    [\"human\", \"{input}\"],\n", "  ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    input_language: \"English\",\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `ChatCloudflareWorkersAI` features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_cloudflare.ChatCloudflareWorkersAI.html"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}