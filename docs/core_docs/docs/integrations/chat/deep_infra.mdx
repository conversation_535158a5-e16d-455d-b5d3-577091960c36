---
sidebar_label: Deep Infra
---

import CodeBlock from "@theme/CodeBlock";

# ChatDeepInfra

LangChain supports chat models hosted by [Deep Infra](https://deepinfra.com/) through the `ChatDeepInfra` wrapper.
First, you'll need to install the `@langchain/community` package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

You'll need to obtain an API key and set it as an environment variable named `DEEPINFRA_API_TOKEN`
(or pass it into the constructor), then call the model as shown below:

import Example from "@examples/models/chat/integration_deepinfra.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
