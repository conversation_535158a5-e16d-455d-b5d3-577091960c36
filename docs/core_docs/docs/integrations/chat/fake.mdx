# Fake LLM

LangChain provides a fake LLM chat model for testing purposes. This allows you to mock out calls to the LLM and and simulate what would happen if the LLM responded in a certain way.

## Usage

import CodeBlock from "@theme/CodeBlock";
import FakeListChatExample from "@examples/models/chat/integration_fake.ts";

<CodeBlock language="typescript">{FakeListChatExample}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
