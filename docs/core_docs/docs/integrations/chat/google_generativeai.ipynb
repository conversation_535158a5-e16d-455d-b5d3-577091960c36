{"cells": [{"cell_type": "raw", "id": "46f7ac07", "metadata": {}, "source": ["---\n", "sidebar_label: Google GenAI\n", "keywords: [gemini, gemini-pro, ChatGoogleGenerativeAI]\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatGoogleGenerativeAI\n", "\n", "[Google AI](https://ai.google.dev/) offers a number of different chat models, including the powerful Gemini series. For information on the latest models, their features, context windows, etc. head to the [Google AI docs](https://ai.google.dev/gemini-api/docs/models/gemini).\n", "\n", "This will help you getting started with `ChatGoogleGenerativeAI` [chat models](/docs/concepts/chat_models). For detailed documentation of all `ChatGoogleGenerativeAI` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_google_genai.ChatGoogleGenerativeAI.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/chat/google_generative_ai) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatGoogleGenerativeAI](https://api.js.langchain.com/classes/langchain_google_genai.ChatGoogleGenerativeAI.html) | [@langchain/google-genai](https://api.js.langchain.com/modules/langchain_google_genai.html) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/google-genai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/google-genai?style=flat-square&label=%20&) |\n", "\n", "### Model features\n", "\n", "See the links in the table headers below for guides on how to use specific features.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | \n", "\n", "## Setup\n", "\n", "You can access Google's `gemini` and `gemini-vision` models, as well as other\n", "generative models in LangChain through `ChatGoogleGenerativeAI` class in the\n", "`@langchain/google-genai` integration package.\n", "\n", "```{=mdx}\n", "\n", ":::tip\n", "You can also access Google's `gemini` family of models via the LangChain VertexAI and VertexAI-web integrations.\n", "\n", "Click [here](/docs/integrations/chat/google_vertex_ai) to read the docs.\n", ":::\n", "\n", "```\n", "\n", "### Credentials\n", "\n", "Get an API key here: [https://ai.google.dev/tutorials/setup](https://ai.google.dev/tutorials/setup)\n", "\n", "Then set the `GOOGLE_API_KEY` environment variable:\n", "\n", "```bash\n", "export GOOGLE_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain `ChatGoogleGenerativeAI` integration lives in the `@langchain/google-genai` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/google-genai @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { ChatGoogleGenerativeAI } from \"@langchain/google-genai\"\n", "\n", "const llm = new ChatGoogleGenerativeAI({\n", "    model: \"gemini-1.5-pro\",\n", "    temperature: 0,\n", "    maxRetries: 2,\n", "    // other params...\n", "})"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"content\": \"J'adore programmer. \\n\",\n", "  \"additional_kwargs\": {\n", "    \"finishReason\": \"STOP\",\n", "    \"index\": 0,\n", "    \"safetyRatings\": [\n", "      {\n", "        \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      }\n", "    ]\n", "  },\n", "  \"response_metadata\": {\n", "    \"finishReason\": \"STOP\",\n", "    \"index\": 0,\n", "    \"safetyRatings\": [\n", "      {\n", "        \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      }\n", "    ]\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 21,\n", "    \"output_tokens\": 5,\n", "    \"total_tokens\": 26\n", "  }\n", "}\n"]}], "source": ["const aiMsg = await llm.invoke([\n", "    [\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ],\n", "    [\"human\", \"I love programming.\"],\n", "])\n", "aiMsg"]}, {"cell_type": "code", "execution_count": 3, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore programmer. \n", "\n"]}], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 4, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"content\": \"Ich liebe das Programmieren. \\n\",\n", "  \"additional_kwargs\": {\n", "    \"finishReason\": \"STOP\",\n", "    \"index\": 0,\n", "    \"safetyRatings\": [\n", "      {\n", "        \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      }\n", "    ]\n", "  },\n", "  \"response_metadata\": {\n", "    \"finishReason\": \"STOP\",\n", "    \"index\": 0,\n", "    \"safetyRatings\": [\n", "      {\n", "        \"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HATE_SPEECH\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_HARASSMENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      },\n", "      {\n", "        \"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n", "        \"probability\": \"NEGLIGIBLE\"\n", "      }\n", "    ]\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 16,\n", "    \"output_tokens\": 7,\n", "    \"total_tokens\": 23\n", "  }\n", "}\n"]}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "    [\n", "        [\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ],\n", "        [\"human\", \"{input}\"],\n", "    ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "    {\n", "        input_language: \"English\",\n", "        output_language: \"German\",\n", "        input: \"I love programming.\",\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "6a44de56", "metadata": {}, "source": ["## Safety Settings\n", "\n", "Gemini models have default safety settings that can be overridden. If you are receiving lots of \"Safety Warnings\" from your models, you can try tweaking the safety_settings attribute of the model. For example, to turn off safety blocking for dangerous content, you can import enums from the `@google/generative-ai` package, then construct your LLM as follows:"]}, {"cell_type": "code", "execution_count": 1, "id": "92db2f25", "metadata": {}, "outputs": [], "source": ["import { ChatGoogleGenerativeAI } from \"@langchain/google-genai\";\n", "import { HarmBlockThreshold, HarmCategory } from \"@google/generative-ai\";\n", "\n", "const llmWithSafetySettings = new ChatGoogleGenerativeAI({\n", "  model: \"gemini-1.5-pro\",\n", "  temperature: 0,\n", "  safetySettings: [\n", "    {\n", "      category: HarmCategory.HARM_CATEGORY_HARASSMENT,\n", "      threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,\n", "    },\n", "  ],\n", "  // other params...\n", "});"]}, {"cell_type": "markdown", "id": "d1ee55bc-ffc8-4cfa-801c-993953a08cfd", "metadata": {}, "source": ["## <PERSON><PERSON> calling\n", "\n", "Tool calling with Google AI is mostly the same [as tool calling with other models](/docs/how_to/tool_calling), but has a few restrictions on schema.\n", "\n", "The Google AI API does not allow tool schemas to contain an object with unknown properties. For example, the following Zod schemas will throw an error:\n", "\n", "`const invalidSchema = z.object({ properties: z.record(z.unknown()) });`\n", "\n", "and\n", "\n", "`const invalidSchema2 = z.record(z.unknown());`\n", "\n", "Instead, you should explicitly define the properties of the object field. Here's an example:"]}, {"cell_type": "code", "execution_count": 5, "id": "d6805c40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'browser_tool',\n", "    args: {\n", "      url: 'https://www.weather.com',\n", "      query: 'weather tonight in new york'\n", "    },\n", "    type: 'tool_call'\n", "  }\n", "]\n"]}], "source": ["import { tool } from \"@langchain/core/tools\";\n", "import { ChatGoogleGenerativeAI } from \"@langchain/google-genai\";\n", "import { z } from \"zod\";\n", "\n", "// Define your tool\n", "const fakeBrowserTool = tool((_) => {\n", "  return \"The search result is xyz...\"\n", "}, {\n", "  name: \"browser_tool\",\n", "  description: \"Useful for when you need to find something on the web or summarize a webpage.\",\n", "  schema: z.object({\n", "    url: z.string().describe(\"The URL of the webpage to search.\"),\n", "    query: z.string().optional().describe(\"An optional search query to use.\"),\n", "  }),\n", "})\n", "\n", "const llmWithTool = new ChatGoogleGenerativeAI({\n", "  model: \"gemini-pro\",\n", "}).bindTools([fakeBrowserTool]) // Bind your tools to the model\n", "\n", "const toolRes = await llmWithTool.invoke([\n", "  [\n", "    \"human\",\n", "    \"Search the web and tell me what the weather will be like tonight in new york. use a popular weather website\",\n", "  ],\n", "]);\n", "\n", "console.log(toolRes.tool_calls);"]}, {"cell_type": "markdown", "id": "9049ee37", "metadata": {}, "source": ["### Built in Google Search Retrieval\n", "\n", "Google also offers a built in search tool which you can use to ground content generation in real-world information. Here's an example of how to use it:"]}, {"cell_type": "code", "execution_count": 3, "id": "43da673d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Los Angeles Dodgers won the 2024 World Series, defeating the New York Yankees in Game 5 on October 30, 2024, by a score of 7-6. This victory marks the Dodgers' eighth World Series title and their first in a full season since 1988.  They achieved this win by overcoming a 5-0 deficit, making them the first team in World Series history to win a clinching game after being behind by such a margin.  The Dodgers also became the first team in MLB postseason history to overcome a five-run deficit, fall behind again, and still win.  <PERSON> earned the save in the final game, securing the championship for the Dodgers.\n", "\n"]}], "source": ["import { DynamicRetrievalMode, GoogleSearchRetrievalTool } from \"@google/generative-ai\";\n", "import { ChatGoogleGenerativeAI } from \"@langchain/google-genai\";\n", "\n", "const searchRetrievalTool: GoogleSearchRetrievalTool = {\n", "  googleSearchRetrieval: {\n", "    dynamicRetrievalConfig: {\n", "      mode: DynamicRetrievalMode.MODE_DYNAMIC,\n", "      dynamicThreshold: 0.7, // default is 0.7\n", "    }\n", "  }\n", "};\n", "const searchRetrievalModel = new ChatGoogleGenerativeAI({\n", "  model: \"gemini-1.5-pro\",\n", "  temperature: 0,\n", "  maxRetries: 0,\n", "}).bindTools([searchRetrievalTool]);\n", "\n", "const searchRetrievalResult = await searchRetrievalModel.invoke(\"Who won the 2024 MLB World Series?\");\n", "\n", "console.log(searchRetrievalResult.content);"]}, {"cell_type": "markdown", "id": "6cc5f529", "metadata": {}, "source": ["The response also includes metadata about the search result:"]}, {"cell_type": "code", "execution_count": 4, "id": "ae5ab86c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  searchEntryPoint: {\n", "    renderedContent: '<style>\\n' +\n", "      '.container {\\n' +\n", "      '  align-items: center;\\n' +\n", "      '  border-radius: 8px;\\n' +\n", "      '  display: flex;\\n' +\n", "      '  font-family: Google Sans, Roboto, sans-serif;\\n' +\n", "      '  font-size: 14px;\\n' +\n", "      '  line-height: 20px;\\n' +\n", "      '  padding: 8px 12px;\\n' +\n", "      '}\\n' +\n", "      '.chip {\\n' +\n", "      '  display: inline-block;\\n' +\n", "      '  border: solid 1px;\\n' +\n", "      '  border-radius: 16px;\\n' +\n", "      '  min-width: 14px;\\n' +\n", "      '  padding: 5px 16px;\\n' +\n", "      '  text-align: center;\\n' +\n", "      '  user-select: none;\\n' +\n", "      '  margin: 0 8px;\\n' +\n", "      '  -webkit-tap-highlight-color: transparent;\\n' +\n", "      '}\\n' +\n", "      '.carousel {\\n' +\n", "      '  overflow: auto;\\n' +\n", "      '  scrollbar-width: none;\\n' +\n", "      '  white-space: nowrap;\\n' +\n", "      '  margin-right: -12px;\\n' +\n", "      '}\\n' +\n", "      '.headline {\\n' +\n", "      '  display: flex;\\n' +\n", "      '  margin-right: 4px;\\n' +\n", "      '}\\n' +\n", "      '.gradient-container {\\n' +\n", "      '  position: relative;\\n' +\n", "      '}\\n' +\n", "      '.gradient {\\n' +\n", "      '  position: absolute;\\n' +\n", "      '  transform: translate(3px, -9px);\\n' +\n", "      '  height: 36px;\\n' +\n", "      '  width: 9px;\\n' +\n", "      '}\\n' +\n", "      '@media (prefers-color-scheme: light) {\\n' +\n", "      '  .container {\\n' +\n", "      '    background-color: #fafafa;\\n' +\n", "      '    box-shadow: 0 0 0 1px #0000000f;\\n' +\n", "      '  }\\n' +\n", "      '  .headline-label {\\n' +\n", "      '    color: #1f1f1f;\\n' +\n", "      '  }\\n' +\n", "      '  .chip {\\n' +\n", "      '    background-color: #ffffff;\\n' +\n", "      '    border-color: #d2d2d2;\\n' +\n", "      '    color: #5e5e5e;\\n' +\n", "      '    text-decoration: none;\\n' +\n", "      '  }\\n' +\n", "      '  .chip:hover {\\n' +\n", "      '    background-color: #f2f2f2;\\n' +\n", "      '  }\\n' +\n", "      '  .chip:focus {\\n' +\n", "      '    background-color: #f2f2f2;\\n' +\n", "      '  }\\n' +\n", "      '  .chip:active {\\n' +\n", "      '    background-color: #d8d8d8;\\n' +\n", "      '    border-color: #b6b6b6;\\n' +\n", "      '  }\\n' +\n", "      '  .logo-dark {\\n' +\n", "      '    display: none;\\n' +\n", "      '  }\\n' +\n", "      '  .gradient {\\n' +\n", "      '    background: linear-gradient(90deg, #fafafa 15%, #fafafa00 100%);\\n' +\n", "      '  }\\n' +\n", "      '}\\n' +\n", "      '@media (prefers-color-scheme: dark) {\\n' +\n", "      '  .container {\\n' +\n", "      '    background-color: #1f1f1f;\\n' +\n", "      '    box-shadow: 0 0 0 1px #ffffff26;\\n' +\n", "      '  }\\n' +\n", "      '  .headline-label {\\n' +\n", "      '    color: #fff;\\n' +\n", "      '  }\\n' +\n", "      '  .chip {\\n' +\n", "      '    background-color: #2c2c2c;\\n' +\n", "      '    border-color: #3c4043;\\n' +\n", "      '    color: #fff;\\n' +\n", "      '    text-decoration: none;\\n' +\n", "      '  }\\n' +\n", "      '  .chip:hover {\\n' +\n", "      '    background-color: #353536;\\n' +\n", "      '  }\\n' +\n", "      '  .chip:focus {\\n' +\n", "      '    background-color: #353536;\\n' +\n", "      '  }\\n' +\n", "      '  .chip:active {\\n' +\n", "      '    background-color: #464849;\\n' +\n", "      '    border-color: #53575b;\\n' +\n", "      '  }\\n' +\n", "      '  .logo-light {\\n' +\n", "      '    display: none;\\n' +\n", "      '  }\\n' +\n", "      '  .gradient {\\n' +\n", "      '    background: linear-gradient(90deg, #1f1f1f 15%, #1f1f1f00 100%);\\n' +\n", "      '  }\\n' +\n", "      '}\\n' +\n", "      '</style>\\n' +\n", "      '<div class=\"container\">\\n' +\n", "      '  <div class=\"headline\">\\n' +\n", "      '    <svg class=\"logo-light\" width=\"18\" height=\"18\" viewBox=\"9 9 35 35\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n' +\n", "      '      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M42.8622 27.0064C42.8622 25.7839 42.7525 24.6084 42.5487 23.4799H26.3109V30.1568H35.5897C35.1821 32.3041 33.9596 34.1222 32.1258 35.3448V39.6864H37.7213C40.9814 36.677 42.8622 32.2571 42.8622 27.0064V27.0064Z\" fill=\"#4285F4\"/>\\n' +\n", "      '      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.3109 43.8555C30.9659 43.8555 34.8687 42.3195 37.7213 39.6863L32.1258 35.3447C30.5898 36.3792 28.6306 37.0061 26.3109 37.0061C21.8282 37.0061 18.0195 33.9811 16.6559 29.906H10.9194V34.3573C13.7563 39.9841 19.5712 43.8555 26.3109 43.8555V43.8555Z\" fill=\"#34A853\"/>\\n' +\n", "      '      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M16.6559 29.8904C16.3111 28.8559 16.1074 27.7588 16.1074 26.6146C16.1074 25.4704 16.3111 24.3733 16.6559 23.3388V18.8875H10.9194C9.74388 21.2072 9.06992 23.8247 9.06992 26.6146C9.06992 29.4045 9.74388 32.022 10.9194 34.3417L15.3864 30.8621L16.6559 29.8904V29.8904Z\" fill=\"#FBBC05\"/>\\n' +\n", "      '      <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M26.3109 16.2386C28.85 16.2386 31.107 17.1164 32.9095 18.8091L37.8466 13.8719C34.853 11.082 30.9659 9.3736 26.3109 9.3736C19.5712 9.3736 13.7563 13.245 10.9194 18.8875L16.6559 23.3388C18.0195 19.2636 21.8282 16.2386 26.3109 16.2386V16.2386Z\" fill=\"#EA4335\"/>\\n' +\n", "      '    </svg>\\n' +\n", "      '    <svg class=\"logo-dark\" width=\"18\" height=\"18\" viewBox=\"0 0 48 48\" xmlns=\"http://www.w3.org/2000/svg\">\\n' +\n", "      '      <circle cx=\"24\" cy=\"23\" fill=\"#FFF\" r=\"22\"/>\\n' +\n", "      '      <path d=\"M33.76 34.26c2.75-2.56 4.49-6.37 4.49-11.26 0-.89-.08-1.84-.29-3H24.01v5.99h8.03c-.4 2.02-1.5 3.56-3.07 4.56v.75l3.91 2.97h.88z\" fill=\"#4285F4\"/>\\n' +\n", "      '      <path d=\"M15.58 25.77A8.845 8.845 0 0 0 24 31.86c1.92 0 3.62-.46 4.97-1.31l4.79 3.71C31.14 36.7 27.65 38 24 38c-5.93 0-11.01-3.4-13.45-8.36l.17-1.01 4.06-2.85h.8z\" fill=\"#34A853\"/>\\n' +\n", "      '      <path d=\"M15.59 20.21a8.864 8.864 0 0 0 0 5.58l-5.03 3.86c-.98-2-1.53-4.25-1.53-6.64 0-2.39.55-4.64 1.53-6.64l1-.22 3.81 2.98.22 1.08z\" fill=\"#FBBC05\"/>\\n' +\n", "      '      <path d=\"M24 14.14c2.11 0 4.02.75 5.52 1.98l4.36-4.36C31.22 9.43 27.81 8 24 8c-5.93 0-11.01 3.4-13.45 8.36l5.03 3.85A8.86 8.86 0 0 1 24 14.14z\" fill=\"#EA4335\"/>\\n' +\n", "      '    </svg>\\n' +\n", "      '    <div class=\"gradient-container\"><div class=\"gradient\"></div></div>\\n' +\n", "      '  </div>\\n' +\n", "      '  <div class=\"carousel\">\\n' +\n", "      '    <a class=\"chip\" href=\"https://vertexaisearch.cloud.google.com/grounding-api-redirect/AZnLMfyXqJN3K4FKueRIZDY2Owjs5Rw4dqgDOc6ZjYKsFo4GgENxLktR2sPHtNUuEBIUeqmUYc3jz9pLRq2cgSpc-4EoGBwQSTTpKk71CX7revnXUa54r9LxcxKgYxrUNBm5HpEm6JDNeJykc6NacPYv43M2wgkrhHCHCzHRyjEP2YR0Pxq4JQMUuOrLeTAYWB9oUb87FE5ksfuB6gimqO5-6uS3psR6\">who won the 2024 mlb world series</a>\\n' +\n", "      '  </div>\\n' +\n", "      '</div>\\n'\n", "  },\n", "  groundingChunks: [\n", "    {\n", "      web: {\n", "        uri: 'https://vertexaisearch.cloud.google.com/grounding-api-redirect/AZnLMfwvs0gpiM4BbIcNXZnnp4d4ED_rLnIYz2ZwM-lwFnoUxXNlKzy7ZSbbs_E27yhARG6Gx2AuW7DsoqkWPfDFMqPdXfvG3n0qFOQxQ4MBQ9Ox9mTk3KH5KPRJ79m8V118RQRyhi6oK5qg5-fLQunXUVn_a42K7eMk7Kjb8VpZ4onl8Glv1lQQsAK7YWyYkQ7WkTHDHVGB-vrL2U2yRQ==',\n", "        title: 'foxsports.com'\n", "      }\n", "    },\n", "    {\n", "      web: {\n", "        uri: 'https://vertexaisearch.cloud.google.com/grounding-api-redirect/AZnLMfwxwBq8VYgKAhf3UC8U6U5D-i0lK4TwP-2Jf8ClqB-sI0iptm9GxgeaH1iHFbSi-j_C3UqYj8Ok0YDTyvg87S7JamU48pndrd467ZQbI2sI0yWxsCCZ_dosXHwemBHFL5TW2hbAqasq93CfJ09cp1jU',\n", "        title: 'mlb.com'\n", "      }\n", "    }\n", "  ],\n", "  groundingSupports: [\n", "    {\n", "      segment: {\n", "        endIndex: 131,\n", "        text: 'The Los Angeles Dodgers won the 2024 World Series, defeating the New York Yankees in Game 5 on October 30, 2024, by a score of 7-6.'\n", "      },\n", "      groundingChunkIndices: [ 0, 1 ],\n", "      confidenceScores: [ 0.7652759, 0.7652759 ]\n", "    },\n", "    {\n", "      segment: {\n", "        startIndex: 401,\n", "        endIndex: 531,\n", "        text: 'The Dodgers also became the first team in MLB postseason history to overcome a five-run deficit, fall behind again, and still win.'\n", "      },\n", "      groundingChunkIndices: [ 1 ],\n", "      confidenceScores: [ 0.8487609 ]\n", "    }\n", "  ],\n", "  retrievalMetadata: { googleSearchDynamicRetrievalScore: 0.93359375 },\n", "  webSearchQueries: [ 'who won the 2024 mlb world series' ]\n", "}\n"]}], "source": ["console.dir(searchRetrievalResult.response_metadata?.groundingMetadata, { depth: null });"]}, {"cell_type": "markdown", "id": "7696a4a5", "metadata": {}, "source": ["### Code Execution\n", "\n", "Google Generative AI also supports code execution. Using the built in `CodeExecutionTool`, you can make the model generate code, execute it, and use the results in a final completion:"]}, {"cell_type": "code", "execution_count": 1, "id": "08dde86b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    type: 'text',\n", "    text: \"Here's how to find the sum of the first and last three numbers in the given list using Python:\\n\" +\n", "      '\\n'\n", "  },\n", "  {\n", "    type: 'executableCode',\n", "    executableCode: {\n", "      language: 'PYTHON',\n", "      code: '\\n' +\n", "        'my_list = [1, 2, 3, 72638, 8, 727, 4, 5, 6]\\n' +\n", "        '\\n' +\n", "        'first_three_sum = sum(my_list[:3])\\n' +\n", "        'last_three_sum = sum(my_list[-3:])\\n' +\n", "        'total_sum = first_three_sum + last_three_sum\\n' +\n", "        '\\n' +\n", "        'print(f\"{first_three_sum=}\")\\n' +\n", "        'print(f\"{last_three_sum=}\")\\n' +\n", "        'print(f\"{total_sum=}\")\\n' +\n", "        '\\n'\n", "    }\n", "  },\n", "  {\n", "    type: 'codeExecutionResult',\n", "    codeExecutionResult: {\n", "      outcome: 'OUTCOME_OK',\n", "      output: 'first_three_sum=6\\nlast_three_sum=15\\ntotal_sum=21\\n'\n", "    }\n", "  },\n", "  {\n", "    type: 'text',\n", "    text: 'Therefore, the sum of the first three numbers (1, 2, 3) is 6, the sum of the last three numbers (4, 5, 6) is 15, and their total sum is 21.\\n'\n", "  }\n", "]\n"]}], "source": ["import { CodeExecutionTool } from \"@google/generative-ai\";\n", "import { ChatGoogleGenerativeAI } from \"@langchain/google-genai\";\n", "\n", "const codeExecutionTool: CodeExecutionTool = {\n", "  codeExecution: {}, // Simply pass an empty object to enable it.\n", "};\n", "const codeExecutionModel = new ChatGoogleGenerativeAI({\n", "  model: \"gemini-1.5-pro\",\n", "  temperature: 0,\n", "  maxRetries: 0,\n", "}).bindTools([codeExecutionTool]);\n", "\n", "const codeExecutionResult = await codeExecutionModel.invoke(\"Use code execution to find the sum of the first and last 3 numbers in the following list: [1, 2, 3, 72638, 8, 727, 4, 5, 6]\");\n", "\n", "console.dir(codeExecutionResult.content, { depth: null });"]}, {"cell_type": "markdown", "id": "9a76cf18", "metadata": {}, "source": ["You can also pass this generation back to the model as chat history:"]}, {"cell_type": "code", "execution_count": 2, "id": "b14518de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You asked for the sum of the first three and the last three numbers in the list `[1, 2, 3, 72638, 8, 727, 4, 5, 6]`.\n", "\n", "Here's a breakdown of the code:\n", "\n", "1. **`my_list = [1, 2, 3, 72638, 8, 727, 4, 5, 6]`**: This line defines the list of numbers you provided.\n", "\n", "2. **`first_three_sum = sum(my_list[:3])`**: This calculates the sum of the first three numbers.  `my_list[:3]` is a slice of the list that takes elements from the beginning up to (but not including) the index 3.  So, it takes elements at indices 0, 1, and 2, which are 1, 2, and 3. The `sum()` function then adds these numbers together.\n", "\n", "3. **`last_three_sum = sum(my_list[-3:])`**: This calculates the sum of the last three numbers. `my_list[-3:]` is a slice that takes elements starting from the third element from the end and goes to the end of the list. So it takes elements at indices -3, -2, and -1 which correspond to 4, 5, and 6. The `sum()` function adds these numbers.\n", "\n", "4. **`total_sum = first_three_sum + last_three_sum`**: This adds the sum of the first three numbers and the sum of the last three numbers to get the final result.\n", "\n", "5. **`print(f\"{first_three_sum=}\")`**, **`print(f\"{last_three_sum=}\")`**, and **`print(f\"{total_sum=}\")`**: These lines print the calculated sums in a clear and readable format.\n", "\n", "\n", "The output of the code was:\n", "\n", "* `first_three_sum=6`\n", "* `last_three_sum=15`\n", "* `total_sum=21`\n", "\n", "Therefore, the answer to your question is 21.\n", "\n"]}], "source": ["const codeExecutionExplanation = await codeExecutionModel.invoke([\n", "  codeExecutionResult,\n", "  {\n", "    role: \"user\",\n", "    content: \"Please explain the question I asked, the code you wrote, and the answer you got.\",\n", "  }\n", "])\n", "\n", "console.log(codeExecutionExplanation.content);"]}, {"cell_type": "markdown", "id": "a464c1a9", "metadata": {}, "source": ["## Context Caching\n", "\n", "Context caching allows you to pass some content to the model once, cache the input tokens, and then refer to the cached tokens for subsequent requests to reduce cost. You can create a `CachedContent` object using `GoogleAICacheManager` class and then pass the `CachedContent` object to your `ChatGoogleGenerativeAIModel` with `enableCachedContent()` method."]}, {"cell_type": "code", "execution_count": null, "id": "9a649be0", "metadata": {}, "outputs": [], "source": ["import { ChatGoogleGenerativeAI } from \"@langchain/google-genai\";\n", "import {\n", "  GoogleAICacheManager,\n", "  GoogleAIFileManager,\n", "} from \"@google/generative-ai/server\";\n", "\n", "const fileManager = new GoogleAIFileManager(process.env.GOOGLE_API_KEY);\n", "const cacheManager = new GoogleAICacheManager(process.env.GOOGLE_API_KEY);\n", "\n", "// uploads file for caching\n", "const pathToVideoFile = \"/path/to/video/file\";\n", "const displayName = \"example-video\";\n", "const fileResult = await fileManager.uploadFile(pathToVideoFile, {\n", "    displayName,\n", "    mimeType: \"video/mp4\",\n", "});\n", "\n", "// creates cached content AFTER uploading is finished\n", "const cachedContent = await cacheManager.create({\n", "    model: \"models/gemini-1.5-flash-001\",\n", "    displayName: displayName,\n", "    systemInstruction: \"You are an expert video analyzer, and your job is to answer \" +\n", "      \"the user's query based on the video file you have access to.\",\n", "    contents: [\n", "        {\n", "            role: \"user\",\n", "            parts: [\n", "                {\n", "                    fileData: {\n", "                        mimeType: fileResult.file.mimeType,\n", "                        fileUri: fileResult.file.uri,\n", "                    },\n", "                },\n", "            ],\n", "        },\n", "    ],\n", "    ttlSeconds: 300,\n", "});\n", "\n", "// passes cached video to model\n", "const model = new ChatGoogleGenerativeAI({});\n", "model.useCachedContent(cachedContent);\n", "\n", "// invokes model with cached video\n", "await model.invoke(\"Summarize the video\");"]}, {"cell_type": "markdown", "id": "12e978ff", "metadata": {}, "source": ["**Note**\n", "- Context caching supports both Gemini 1.5 Pro and Gemini 1.5 Flash. Context caching is only available for stable models with fixed versions (for example, gemini-1.5-pro-001). You must include the version postfix (for example, the -001 in gemini-1.5-pro-001).\n", "- The minimum input token count for context caching is 32,768, and the maximum is the same as the maximum for the given model."]}, {"cell_type": "markdown", "id": "0c6a950f", "metadata": {}, "source": ["## Gemini Prompting FAQs\n", "\n", "As of the time this doc was written (2023/12/12), Gemini has some restrictions on the types and structure of prompts it accepts. Specifically:\n", "\n", "1. When providing multimodal (image) inputs, you are restricted to at most 1 message of \"human\" (user) type. You cannot pass multiple messages (though the single human message may have multiple content entries)\n", "2. System messages are not natively supported, and will be merged with the first human message if present.\n", "3. For regular chat conversations, messages must follow the human/ai/human/ai alternating pattern. You may not provide 2 AI or human messages in sequence.\n", "4. Message may be blocked if they violate the safety checks of the LLM. In this case, the model will return an empty response.\n"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatGoogleGenerativeAI features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_google_genai.ChatGoogleGenerativeAI.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}