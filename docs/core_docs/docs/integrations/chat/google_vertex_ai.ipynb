{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Google Vertex AI\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatVertexAI\n", "\n", "[Google Vertex](https://cloud.google.com/vertex-ai) is a service that exposes all foundation models available in Google Cloud, like `gemini-1.5-pro`, `gemini-2.0-flash-exp`, etc.\n", "It also provides some non-Google models such as [<PERSON><PERSON><PERSON>'s <PERSON>](https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude).\n", "\n", "\n", "This will help you getting started with `ChatVertexAI` [chat models](/docs/concepts/chat_models). For detailed documentation of all `ChatVertexAI` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_google_vertexai.ChatVertexAI.html).\n", "\n", "## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/chat/google_vertex_ai_palm) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatVertexAI](https://api.js.langchain.com/classes/langchain_google_vertexai.ChatVertexAI.html) | [`@langchain/google-vertexai`](https://www.npmjs.com/package/@langchain/google-vertexai) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/google-vertexai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/google-vertexai?style=flat-square&label=%20&) |\n", "\n", "### Model features\n", "\n", "See the links in the table headers below for guides on how to use specific features.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ❌ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | \n", "\n", "Note that while logprobs are supported, Gemini has fairly restricted usage of them.\n", "\n", "## Setup\n", "\n", "LangChain.js supports two different authentication methods based on whether\n", "you're running in a Node.js environment or a web environment. It also supports\n", "the authentication method used by Vertex AI Express Mode using either package.\n", "\n", "To access `ChatVertexAI` models you'll need to setup Google VertexAI in your Google Cloud Platform (GCP) account, save the credentials file, and install the `@langchain/google-vertexai` integration package.\n", "\n", "### Credentials\n", "\n", "Head to your [GCP account](https://console.cloud.google.com/) and generate a credentials file. Once you've done this set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable:\n", "\n", "```bash\n", "export GOOGLE_APPLICATION_CREDENTIALS=\"path/to/your/credentials.json\"\n", "```\n", "\n", "If running in a web environment, you should set the `GOOGLE_VERTEX_AI_WEB_CREDENTIALS` environment variable as a JSON stringified object, and install the `@langchain/google-vertexai-web` package:\n", "\n", "```bash\n", "GOOGLE_VERTEX_AI_WEB_CREDENTIALS={\"type\":\"service_account\",\"project_id\":\"YOUR_PROJECT-12345\",...}\n", "```\n", "\n", "If you are using Vertex AI Express Mode, you can install either the `@langchain/google-vertexai` or `@langchain/google-vertexai-web` package.\n", "You can then go to the [Express Mode](https://console.cloud.google.com/vertex-ai/studio) API Key page and set your API Key in the `GOOGLE_API_KEY` environment variable:\n", "\n", "```bash\n", "export GOOGLE_API_KEY=\"api_key_value\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain `ChatVertexAI` integration lives in the `@langchain/google-vertexai` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/google-vertexai @langchain/core\n", "</Npm2Yarn>\n", "\n", "Or if using in a web environment like a [Vercel Edge function](https://vercel.com/blog/edge-functions-generally-available):\n", "\n", "<Npm2Yarn>\n", "  @langchain/google-vertexai-web @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { ChatVertexAI } from \"@langchain/google-vertexai\"\n", "// Uncomment the following line if you're running in a web environment:\n", "// import { ChatVertexAI } from \"@langchain/google-vertexai-web\"\n", "\n", "const llm = new ChatVertexAI({\n", "    model: \"gemini-2.0-flash-exp\",\n", "    temperature: 0,\n", "    maxRetries: 2,\n", "    // For web, authOptions.credentials\n", "    // authOptions: { ... }\n", "    // other params...\n", "})"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessageChunk {\n", "  \"content\": \"J'adore programmer. \\n\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {},\n", "  \"tool_calls\": [],\n", "  \"tool_call_chunks\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 20,\n", "    \"output_tokens\": 7,\n", "    \"total_tokens\": 27\n", "  }\n", "}\n"]}], "source": ["const aiMsg = await llm.invoke([\n", "    [\n", "        \"system\",\n", "        \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ],\n", "    [\"human\", \"I love programming.\"],\n", "])\n", "aiMsg"]}, {"cell_type": "code", "execution_count": 3, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore programmer. \n", "\n"]}], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "id": "de2480fa", "metadata": {}, "source": ["## Tool Calling with Google Search Retrieval\n", "\n", "It is possible to call the model with a Google search tool which you can use to [ground](https://cloud.google.com/vertex-ai/generative-ai/docs/model-reference/grounding) content generation with real-world information and reduce hallucinations.\n", "\n", "Grounding is currently not supported by `gemini-2.0-flash-exp`.\n", "\n", "You can choose to either ground using Google Search or by using a custom data store. Here are examples of both:  "]}, {"cell_type": "markdown", "id": "fd2091ba", "metadata": {}, "source": ["### Google Search Retrieval\n", "\n", "Grounding example that uses Google Search:\n"]}, {"cell_type": "code", "execution_count": null, "id": "65d019ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Boston Celtics won the 2024 NBA Finals, defeating the Dallas Mavericks 4-1 in the series to claim their 18th NBA championship. This victory marked their first title since 2008 and established them as the team with the most NBA championships, surpassing the Los Angeles Lakers' 17 titles.\n", "\n"]}], "source": ["import { ChatVertexAI } from \"@langchain/google-vertexai\"\n", "\n", "const searchRetrievalTool = {\n", "  googleSearchRetrieval: {\n", "    dynamicRetrievalConfig: {\n", "      mode: \"MODE_DYNAMIC\", // Use Dynamic Retrieval\n", "      dynamicThreshold: 0.7, // Default for Dynamic Retrieval threshold\n", "    },\n", "  },\n", "};\n", "\n", "const searchRetrievalModel = new ChatVertexAI({\n", "  model: \"gemini-1.5-pro\",\n", "  temperature: 0,\n", "  maxRetries: 0,\n", "}).bindTools([searchRetrievalTool]);\n", "\n", "const searchRetrievalResult = await searchRetrievalModel.invoke(\"Who won the 2024 NBA Finals?\");\n", "\n", "console.log(searchRetrievalResult.content);"]}, {"cell_type": "markdown", "id": "ac3a4a98", "metadata": {}, "source": ["### Google Search Retrieval with Data Store\n", "\n", "First, set up your data store (this is a schema of an example data store):\n", "\n", "|    ID   |     Date     |    Team 1   |   Score  |   Team 2   |\n", "|:-------:|:------------:|:-----------:|:--------:|:----------:|\n", "|  3001   |  2023-09-07  |  Argentina  |  1 - 0   |  Ecuador   |\n", "|  3002   |  2023-09-12  |  Venezuela  |  1 - 0   |  Paraguay  |\n", "|  3003   |  2023-09-12  |  Chile      |  0 - 0   |  Colombia  |\n", "|  3004   |  2023-09-12  |  Peru       |  0 - 1   |  Brazil    |\n", "|  3005   |  2024-10-15  |  Argentina  |  6 - 0   |  Bolivia   |\n", "\n", "Then, use this data store in the example provided below:\n", "\n", "(Note that you have to use your own variables for `projectId` and `datastoreId`)\n"]}, {"cell_type": "code", "execution_count": null, "id": "a6a539d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Argentina won against Bolivia with a score of 6-0 on October 15, 2024.\n", "\n"]}], "source": ["import { ChatVertexAI } from \"@langchain/google-vertexai\";\n", "\n", "const projectId = \"YOUR_PROJECT_ID\";\n", "const datastoreId = \"YOUR_DATASTORE_ID\";\n", "\n", "const searchRetrievalToolWithDataset = {\n", "  retrieval: {\n", "    vertexAiSearch: {\n", "      datastore: `projects/${projectId}/locations/global/collections/default_collection/dataStores/${datastoreId}`,\n", "    },\n", "    disableAttribution: false,\n", "  },\n", "};\n", "\n", "const searchRetrievalModelWithDataset = new ChatVertexAI({\n", "  model: \"gemini-1.5-pro\",\n", "  temperature: 0,\n", "  maxRetries: 0,\n", "}).bindTools([searchRetrievalToolWithDataset]);\n", "\n", "const searchRetrievalModelResult = await searchRetrievalModelWithDataset.invoke(\n", "  \"What is the score of Argentina vs Bolivia football game?\"\n", ");\n", "\n", "console.log(searchRetrievalModelResult.content);"]}, {"cell_type": "markdown", "id": "8d11f2be", "metadata": {}, "source": ["You should now get results that are grounded in the data from your provided data store."]}, {"cell_type": "markdown", "id": "57c8d02e", "metadata": {}, "source": ["## Context Caching\n", "\n", "Vertex AI offers context caching functionality, which helps optimize costs by storing and reusing long blocks of message content across multiple API requests. This is particularly useful when you have lengthy conversation histories or message segments that appear frequently in your interactions.\n", "\n", "To use this feature, first create a context cache by following [this official guide](https://cloud.google.com/vertex-ai/generative-ai/docs/context-cache/context-cache-create).\n", "\n", "Once you've created a cache, you can pass its id in as a runtime param as follows:"]}, {"cell_type": "code", "execution_count": null, "id": "f1b38971", "metadata": {}, "outputs": [], "source": ["import { ChatVertexAI } from \"@langchain/google-vertexai\";\n", "\n", "const modelWithCachedContent = new ChatVertexAI({\n", "  model: \"gemini-1.5-pro-002\",\n", "  location: \"us-east5\",\n", "});\n", "\n", "await modelWithCachedContent.invoke(\"What is in the content?\", {\n", "  cachedContent:\n", "    \"projects/PROJECT_NUMBER/locations/LOCATION/cachedContents/CACHE_ID\",\n", "});"]}, {"cell_type": "markdown", "id": "03411eab", "metadata": {}, "source": ["You can also bind this field directly onto the model instance:"]}, {"cell_type": "code", "execution_count": 5, "id": "0418df59", "metadata": {}, "outputs": [], "source": ["const modelWithBoundCachedContent = new ChatVertexAI({\n", "  model: \"gemini-1.5-pro-002\",\n", "  location: \"us-east5\",\n", "}).bind({\n", "  cachedContent:\n", "    \"projects/PROJECT_NUMBER/locations/LOCATION/cachedContents/CACHE_ID\",\n", "});\n"]}, {"cell_type": "markdown", "id": "e0af48c5", "metadata": {}, "source": ["Note that not all models currently support context caching."]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 4, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessageChunk {\n", "  \"content\": \"Ich liebe das Programmieren. \\n\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {},\n", "  \"tool_calls\": [],\n", "  \"tool_call_chunks\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"input_tokens\": 15,\n", "    \"output_tokens\": 9,\n", "    \"total_tokens\": 24\n", "  }\n", "}\n"]}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "    [\n", "        [\n", "            \"system\",\n", "            \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "        ],\n", "        [\"human\", \"{input}\"],\n", "    ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    input_language: \"English\",\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ");"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatVertexAI features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_google_vertexai.ChatVertexAI.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}