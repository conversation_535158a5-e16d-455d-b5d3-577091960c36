---
sidebar_position: 1
sidebar_class_name: hidden
hide_table_of_contents: true
---

# Chat models

[Chat models](/docs/concepts/chat_models) are language models that use a sequence of [messages](/docs/concepts/messages) as inputs and return messages as outputs (as opposed to using plain text). These are generally newer models.

:::info
If you'd like to write your own chat model, see [this how-to](/docs/how_to/custom_chat). If you'd like to contribute an integration, see [Contributing integrations](/docs/contributing).
:::

import ChatModelTabs from "@theme/ChatModelTabs";

<ChatModelTabs openaiParams={`{ model: "gpt-4o-mini" }`} />

```python
await model.invoke("Hello, world!")
```

## Featured providers

| Model                                                                    | Stream | JSON mode | [Tool Calling](/docs/how_to/tool_calling/) | [`withStructuredOutput()`](/docs/how_to/structured_output/#the-.withstructuredoutput-method) | [Multimodal](/docs/how_to/multimodal_inputs/) |
| :----------------------------------------------------------------------- | :----: | :-------: | :----------------------------------------: | :------------------------------------------------------------------------------------------: | :-------------------------------------------: |
| [BedrockChat](/docs/integrations/chat/bedrock/)                          |   ✅   |    ❌     |        🟡 (Bedrock Anthropic only)         |                                 🟡 (Bedrock Anthropic only)                                  |          🟡 (Bedrock Anthropic only)          |
| [ChatBedrockConverse](/docs/integrations/chat/bedrock_converse/)         |   ✅   |    ❌     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatAnthropic](/docs/integrations/chat/anthropic/)                      |   ✅   |    ❌     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatCloudflareWorkersAI](/docs/integrations/chat/cloudflare_workersai/) |   ✅   |    ❌     |                     ❌                     |                                              ❌                                              |                      ❌                       |
| [ChatCohere](/docs/integrations/chat/cohere/)                            |   ✅   |    ❌     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatFireworks](/docs/integrations/chat/fireworks/)                      |   ✅   |    ✅     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatGoogleGenerativeAI](/docs/integrations/chat/google_generativeai/)   |   ✅   |    ❌     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatVertexAI](/docs/integrations/chat/google_vertex_ai/)                |   ✅   |    ❌     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatGroq](/docs/integrations/chat/groq/)                                |   ✅   |    ✅     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatMistralAI](/docs/integrations/chat/mistral/)                        |   ✅   |    ✅     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatOllama](/docs/integrations/chat/ollama/)                            |   ✅   |    ✅     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatOpenAI](/docs/integrations/chat/openai/)                            |   ✅   |    ✅     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatTogetherAI](/docs/integrations/chat/togetherai/)                    |   ✅   |    ✅     |                     ✅                     |                                              ✅                                              |                      ✅                       |
| [ChatXAI](/docs/integrations/chat/xai/)                                  |   ✅   |    ✅     |                     ✅                     |                                              ✅                                              |                      ❌                       |

## All chat models

import { IndexTable } from "@theme/FeatureTables";

<IndexTable />
