---
sidebar_label: Moonshot
---

import CodeBlock from "@theme/CodeBlock";

# ChatMoonshot

LangChain.js supports the Moonshot AI family of models.

https://platform.moonshot.cn/docs/intro

## Setup

You'll need to sign up for an Moonshot API key and set it as an environment variable named `MOONSHOT_API_KEY`

https://platform.moonshot.cn/console

You'll also need to install the following dependencies:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

## Usage

Here's an example:

import Moonshot from "@examples/models/chat/integration_moonshot.ts";

<CodeBlock language="typescript">{Moonshot}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
