{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Novita AI\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ChatNovita\n", "\n", "Delivers an affordable, reliable, and simple inference platform for running top LLM models.\n", "\n", "You can find all the models we support here: [Novita AI Featured Models](https://novita.ai/models/llm?utm_source=github_langchain&utm_medium=github_readme&utm_campaign=link) or request the [Models API](https://novita.ai/docs/guides/llm-models?utm_source=github_langchain&utm_medium=github_readme&utm_campaign=link) to get all available models.\n", "\n", "Try the [Novita AI DeepSeek R1 API Demo](https://novita.ai/models/llm/deepseek-deepseek-r1?utm_source=github_langchain&utm_medium=github_readme&utm_campaign=link) today!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview\n", "\n", "### Model features\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | Native async | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: | :---: |\n", "| ❌ | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup\n", "\n", "To access Novita AI models you'll need to create a Novita account and get an API key.\n", "\n", "### Credentials\n", "\n", "Head to [this page](https://novita.ai/settings#key-management?utm_source=github_langchain&utm_medium=github_readme&utm_campaign=link) to sign up to Novita AI and generate an API key. Once you've done this set the NOVITA_API_KEY environment variable:\n", "\n", "```bash\n", "export NOVITA_API_KEY=\"your-api-key\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation\n", "\n", "The LangChain Novita integration lives in the `@langchain-community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions. Try the [Novita AI DeepSeek R1 API Demo](https://novita.ai/models/llm/deepseek-deepseek-r1?utm_source=github_langchain&utm_medium=github_readme&utm_campaign=link) today!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "javascript"}}, "outputs": [], "source": ["import { ChatNovitaAI } from \"@langchain/community/chat_models/novita\";\n", "\n", "const llm = new ChatNovitaAI({\n", "  model: \"deepseek/deepseek-r1\",\n", "  temperature: 0,\n", "  // other params...\n", "})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "javascript"}}, "outputs": [], "source": ["const aiMsg = await llm.invoke([\n", "  {\n", "    role: \"system\",\n", "    content: \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "  },\n", "  {\n", "    role: \"human\",\n", "    content: \"I love programming.\"\n", "  },\n", "]);"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "javascript"}}, "outputs": [], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "javascript"}}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\n", "      \"system\",\n", "      \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "    ],\n", "    [\"human\", \"{input}\"],\n", "  ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    input_language: \"English\",\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of Novita AI LLM APIs, head to [Novita AI LLM API reference](https://novita.ai/docs/guides/llm-api?utm_source=github_langchain&utm_medium=github_readme&utm_campaign=link)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}