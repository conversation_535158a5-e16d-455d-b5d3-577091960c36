{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Perplexity\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatPerplexity\n", "\n", "This guide will help you getting started with Perplexity [chat models](/docs/concepts/#chat-models). For detailed documentation of all `ChatPerplexity` features and configurations head to the [API reference](https://api.js.langchain.com/classes/_langchain_community.chat_models_perplexity.ChatPerplexity.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/chat/perplexity/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [`ChatPerplexity`](https://api.js.langchain.com/classes/_langchain_community.chat_models_perplexity.ChatPerplexity.html) | [`@langchain/community`](https://npmjs.com/@langchain/community) | ❌ | beta | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "### Model features\n", "\n", "See the links in the table headers below for guides on how to use specific features.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: |\n", "| ❌ | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ❌ | \n", "\n", "Note that at the time of writing, Perplexity only supports structured outputs on certain usage tiers.\n", "\n", "## Setup\n", "\n", "To access Perplexity models you'll need to create a Perplexity account, get an API key, and install the `@langchain/community` integration package.\n", "\n", "### Credentials\n", "\n", "Head to https://perplexity.ai to sign up for Perplexity and generate an API key. Once you've done this set the `PERPLEXITY_API_KEY` environment variable:\n", "\n", "```bash\n", "export PERPLEXITY_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain Perplexity integration lives in the `@langchain/community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { ChatPerplexity } from \"@langchain/community/chat_models/perplexity\"\n", "\n", "const llm = new ChatPerplexity({\n", "  model: \"sonar\",\n", "  temperature: 0,\n", "  maxTokens: undefined,\n", "  timeout: undefined,\n", "  maxRetries: 2,\n", "  // other params...\n", "})"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"run-71853938-aa30-4861-9019-f12323c09f9a\",\n", "  \"content\": \"J'adore la programmation.\",\n", "  \"additional_kwargs\": {\n", "    \"citations\": [\n", "      \"https://careersatagoda.com/blog/why-we-love-programming/\",\n", "      \"https://henrikwarne.com/2012/06/02/why-i-love-coding/\",\n", "      \"https://forum.freecodecamp.org/t/i-love-programming-but/497502\",\n", "      \"https://ilovecoding.org\",\n", "      \"https://thecodinglove.com\"\n", "    ]\n", "  },\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"promptTokens\": 20,\n", "      \"completionTokens\": 9,\n", "      \"totalTokens\": 29\n", "    }\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": []\n", "}\n"]}], "source": ["const aiMsg = await llm.invoke([\n", "  {\n", "    role: \"system\",\n", "    content: \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "  },\n", "  {\n", "    role: \"user\",\n", "    content: \"I love programming.\",\n", "  },\n", "])\n", "aiMsg"]}, {"cell_type": "code", "execution_count": 3, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore la programmation.\n"]}], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 4, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"run-a44dc452-4a71-423d-a4ee-50a2d7c90abd\",\n", "  \"content\": \"**English to German Translation:**\\n\\n\\\"I love programming\\\" translates to **\\\"Ich liebe das Programmieren.\\\"**\\n\\nIf you'd like to express your passion for programming in more detail, here are some additional translations:\\n\\n- **\\\"Programming is incredibly rewarding and fulfilling.\\\"** translates to **\\\"Das Programmieren ist unglaublich lohnend und erfüllend.\\\"**\\n- **\\\"I enjoy solving problems through coding.\\\"** translates to **\\\"Ich genieße es, Probleme durch Codieren zu lösen.\\\"**\\n- **\\\"I find the process of creating something from nothing very satisfying.\\\"** translates to **\\\"Ich finde den Prozess, etwas aus dem Nichts zu schaffen, sehr befriedigend.\\\"**\",\n", "  \"additional_kwargs\": {\n", "    \"citations\": [\n", "      \"https://careersatagoda.com/blog/why-we-love-programming/\",\n", "      \"https://henrikwarne.com/2012/06/02/why-i-love-coding/\",\n", "      \"https://dev.to/dvddpl/coding-is-boring-why-do-you-love-coding-cl0\",\n", "      \"https://forum.freecodecamp.org/t/i-love-programming-but/497502\",\n", "      \"https://ilovecoding.org\"\n", "    ]\n", "  },\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"promptTokens\": 15,\n", "      \"completionTokens\": 149,\n", "      \"totalTokens\": 164\n", "    }\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": []\n", "}\n"]}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\n", "      \"system\",\n", "      \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "    ],\n", "    [\"human\", \"{input}\"],\n", "  ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    input_language: \"English\",\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatPerplexity features and configurations head to the API reference: https://api.js.langchain.com/classes/_langchain_community.chat_models_perplexity.ChatPerplexity.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}