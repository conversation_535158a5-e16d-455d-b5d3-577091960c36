---
sidebar_label: PremAI
---

import CodeBlock from "@theme/CodeBlock";

# ChatPrem

## Setup

1. Create a Prem AI account and get your API key [here](https://app.premai.io/accounts/signup/).
2. Export or set your API key inline. The ChatPrem class defaults to `process.env.PREM_API_KEY`.

```bash
export PREM_API_KEY=your-api-key
```

You can use models provided by Prem AI as follows:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

import PremAI from "@examples/models/chat/integration_premai.ts";

<CodeBlock language="typescript">{PremAI}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
