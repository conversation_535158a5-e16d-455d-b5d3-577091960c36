---
sidebar_label: PromptLayer OpenAI
---

# PromptLayerChatOpenAI

You can pass in the optional `returnPromptLayerId` boolean to get a `promptLayerRequestId` like below. Here is an example of getting the PromptLayerChatOpenAI requestID:

```typescript
import { PromptLayerChatOpenAI } from "langchain/llms/openai";

const chat = new PromptLayerChatOpenAI({
  returnPromptLayerId: true,
});

const respA = await chat.generate([
  [
    new SystemMessage(
      "You are a helpful assistant that translates English to French."
    ),
  ],
]);

console.log(JSON.stringify(respA, null, 3));

/*
  {
    "generations": [
      [
        {
          "text": "Bonjour! Je suis un assistant utile qui peut vous aider à traduire de l'anglais vers le français. Que puis-je faire pour vous aujourd'hui?",
          "message": {
            "type": "ai",
            "data": {
              "content": "Bonjour! Je suis un assistant utile qui peut vous aider à traduire de l'anglais vers le français. Que puis-je faire pour vous aujourd'hui?"
            }
          },
          "generationInfo": {
            "promptLayerRequestId": 2300682
          }
        }
      ]
    ],
    "llmOutput": {
      "tokenUsage": {
        "completionTokens": 35,
        "promptTokens": 19,
        "totalTokens": 54
      }
    }
  }
*/
```

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
