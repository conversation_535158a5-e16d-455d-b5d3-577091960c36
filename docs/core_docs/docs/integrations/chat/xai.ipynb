{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: xAI\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ChatXAI\n", "\n", "[xAI](https://x.ai/) is an artificial intelligence company that develops large language models (LLMs). Their flagship model, Grok, is trained on real-time X (formerly Twitter) data and aims to provide witty, personality-rich responses while maintaining high capability on technical tasks.\n", "\n", "This guide will help you getting started with `ChatXAI` [chat models](/docs/concepts/chat_models). For detailed documentation of all `ChatXAI` features and configurations head to the [API reference](https://api.js.langchain.com/classes/_langchain_xai.ChatXAI.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | PY support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [ChatXAI](https://api.js.langchain.com/classes/_langchain_xai.ChatXAI.html) | [`@langchain/xai`](https://www.npmjs.com/package/@langchain/xai) | ❌ | ✅ | ❌ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/xai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/xai?style=flat-square&label=%20&) |\n", "\n", "### Model features\n", "\n", "See the links in the table headers below for guides on how to use specific features.\n", "\n", "| [Tool calling](/docs/how_to/tool_calling) | [Structured output](/docs/how_to/structured_output/) | JSON mode | [Image input](/docs/how_to/multimodal_inputs/) | Audio input | Video input | [Token-level streaming](/docs/how_to/chat_streaming/) | [Token usage](/docs/how_to/chat_token_usage_tracking/) | [Logprobs](/docs/how_to/logprobs/) |\n", "| :---: | :---: | :---: | :---: |  :---: | :---: | :---: | :---: | :---: |\n", "| ✅ | ✅ | ✅ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | \n", "\n", "## Setup\n", "\n", "To access `ChatXAI` models you'll need to create an xAI account, [get an API key](https://console.x.ai/), and install the `@langchain/xai` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [the xAI website](https://x.ai) to sign up to xAI and generate an API key. Once you've done this set the `XAI_API_KEY` environment variable:\n", "\n", "```bash\n", "export XAI_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain `ChatXAI` integration lives in the `@langchain/xai` package:\n", "\n", "```{=mdx}\n", "\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/xai @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 2, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { ChatXAI } from \"@langchain/xai\" \n", "\n", "const llm = new ChatXAI({\n", "    model: \"grok-beta\", // default\n", "    temperature: 0,\n", "    maxTokens: undefined,\n", "    maxRetries: 2,\n", "    // other params...\n", "})"]}, {"cell_type": "markdown", "id": "2b4f3e15", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 3, "id": "62e0dbc3", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"71d7e3d8-30dd-472c-8038-b6b283dcee63\",\n", "  \"content\": \"J'adore programmer.\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"promptTokens\": 30,\n", "      \"completionTokens\": 6,\n", "      \"totalTokens\": 36\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"usage\": {\n", "      \"prompt_tokens\": 30,\n", "      \"completion_tokens\": 6,\n", "      \"total_tokens\": 36\n", "    },\n", "    \"system_fingerprint\": \"fp_3e3898d4ce\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"output_tokens\": 6,\n", "    \"input_tokens\": 30,\n", "    \"total_tokens\": 36,\n", "    \"input_token_details\": {},\n", "    \"output_token_details\": {}\n", "  }\n", "}\n"]}], "source": ["const aiMsg = await llm.invoke([\n", "    [\n", "      \"system\",\n", "      \"You are a helpful assistant that translates English to French. Translate the user sentence.\",\n", "    ],\n", "    [\"human\", \"I love programming.\"],\n", "])\n", "console.log(aiMsg)"]}, {"cell_type": "code", "execution_count": 4, "id": "d86145b3-bfef-46e8-b227-4dda5c9c2705", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["J'adore programmer.\n"]}], "source": ["console.log(aiMsg.content)"]}, {"cell_type": "markdown", "id": "18e2bfc0-7e78-4528-a73f-499ac150dca8", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 5, "id": "e197d1d7-a070-4c96-9f8a-a0e86d046e0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"b2738008-8247-40e1-81dc-d9bf437a1a0c\",\n", "  \"content\": \"Ich liebe das Programmieren.\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"promptTokens\": 25,\n", "      \"completionTokens\": 7,\n", "      \"totalTokens\": 32\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"usage\": {\n", "      \"prompt_tokens\": 25,\n", "      \"completion_tokens\": 7,\n", "      \"total_tokens\": 32\n", "    },\n", "    \"system_fingerprint\": \"fp_3e3898d4ce\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"output_tokens\": 7,\n", "    \"input_tokens\": 25,\n", "    \"total_tokens\": 32,\n", "    \"input_token_details\": {},\n", "    \"output_token_details\": {}\n", "  }\n", "}\n"]}], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\n", "      \"system\",\n", "      \"You are a helpful assistant that translates {input_language} to {output_language}.\",\n", "    ],\n", "    [\"human\", \"{input}\"],\n", "  ]\n", ")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    input_language: \"English\",\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "d1ee55bc-ffc8-4cfa-801c-993953a08cfd", "metadata": {}, "source": ["Behind the scenes, xAI uses the OpenAI SDK and OpenAI compatible API."]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ChatXAI features and configurations head to the API reference: https://api.js.langchain.com/classes/_langchain_xai.ChatXAI.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}