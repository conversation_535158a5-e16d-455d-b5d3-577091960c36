---
sidebar_label: <PERSON>hipuAI
---

import CodeBlock from "@theme/CodeBlock";

# ChatZhipuAI

LangChain.js supports the Zhipu AI family of models.

https://open.bigmodel.cn/dev/howuse/model

## Setup

You'll need to sign up for an Zhipu API key and set it as an environment variable named `ZHIPUAI_API_KEY`

https://open.bigmodel.cn

You'll also need to install the following dependencies:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core jsonwebtoken
```

## Usage

Here's an example:

import ZhipuAI from "@examples/models/chat/integration_zhipuai.ts";

<CodeBlock language="typescript">{ZhipuAI}</CodeBlock>

## Related

- Chat model [conceptual guide](/docs/concepts/chat_models)
- Chat model [how-to guides](/docs/how_to/#chat-models)
