# Cohere Rerank

Reranking documents can greatly improve any RAG application and document retrieval system.

At a high level, a rerank API is a language model which analyzes documents and reorders them based on their relevance to a given query.

Cohere offers an API for reranking documents. In this example we'll show you how to use it.

## Setup

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/cohere @langchain/core
```

import CodeBlock from "@theme/CodeBlock";

import Example from "@examples/document_compressors/cohere_rerank.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

Here, we can see the `.rerank()` method returns just the index of the documents (matching the indexes of the input documents) and their relevancy scores.

If we'd like to have the documents returned from the method itself, we can use the `.compressDocuments()` method.

import ExampleCompressor from "@examples/document_compressors/cohere_rerank_compressor.ts";

<CodeBlock language="typescript">{ExampleCompressor}</CodeBlock>

From the results, we can see it returned the top 3 documents, and assigned a `relevanceScore` to each.

As expected, the document with the highest `relevanceScore` is the one that references Washington, D.C., with a score of `98.7%`!

### Usage with `CohereClient`

If you are using Cohere on Azure, AWS Bedrock or a standalone instance you can use the `CohereClient` to create a `CohereRerank` instance with your endpoint.

import ExampleClient from "@examples/document_compressors/cohere_rerank_custom_client.ts";

<CodeBlock language="typescript">{ExampleClient}</CodeBlock>
