{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: IBM watsonx.ai\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# IBM watsonx.ai\n", "\n", "## Overview\n", "\n", "This will help you getting started with the [Watsonx document compressor](/docs/concepts/#document_compressors). For detailed documentation of all Watsonx document compressor features and configurations head to the [API reference](https://api.js.langchain.com/modules/_langchain_community.document_compressors_ibm.html).\n", "\n", "### Integration details\n", "\n", "| Class | Package | [PY support](https://python.langchain.com/docs/integrations/retrievers/ibm_watsonx_ranker/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: | :---: |\n", "| [`WatsonxRerank`](https://api.js.langchain.com/classes/_langchain_community.document_compressors_ibm.WatsonxRerank.html) | [@langchain/community](https://www.npmjs.com/package/@langchain/community) |  ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access IBM WatsonxAI models you'll need to create an IBM watsonx.ai account, get an API key or any other type of credentials, and install the `@langchain/community` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [IBM Cloud](https://cloud.ibm.com/login) to sign up to IBM watsonx.ai and generate an API key or provide any other authentication form as presented below.\n", "\n", "#### IAM authentication\n", "\n", "```bash\n", "export WATSONX_AI_AUTH_TYPE=iam\n", "export WATSONX_AI_APIKEY=<YOUR-APIKEY>\n", "```\n", "\n", "#### Bearer token authentication\n", "\n", "```bash\n", "export WATSONX_AI_AUTH_TYPE=bearertoken\n", "export WATSONX_AI_BEARER_TOKEN=<YOUR-BEARER-TOKEN>\n", "```\n", "\n", "#### IBM watsonx.ai software authentication\n", "\n", "```bash\n", "export WATSONX_AI_AUTH_TYPE=cp4d\n", "export WATSONX_AI_USERNAME=<YOUR_USERNAME>\n", "export WATSONX_AI_PASSWORD=<YOUR_PASSWORD>\n", "export WATSONX_AI_URL=<URL>\n", "```\n", "\n", "Once these are placed in your environment variables and object is initialized authentication will proceed automatically.\n", "\n", "Authentication can also be accomplished by passing these values as parameters to a new instance.\n", "\n", "## IAM authentication\n", "\n", "```typescript\n", "import { WatsonxLLM } from \"@langchain/community/llms/ibm\";\n", "\n", "const props = {\n", "  version: \"YYYY-MM-DD\",\n", "  serviceUrl: \"<SERVICE_URL>\",\n", "  projectId: \"<PROJECT_ID>\",\n", "  watsonxAIAuthType: \"iam\",\n", "  watsonxAIApikey: \"<YOUR-APIKEY>\",\n", "};\n", "const instance = new WatsonxLLM(props);\n", "```\n", "\n", "## Bearer token authentication\n", "\n", "```typescript\n", "import { WatsonxLLM } from \"@langchain/community/llms/ibm\";\n", "\n", "const props = {\n", "  version: \"YYYY-MM-DD\",\n", "  serviceUrl: \"<SERVICE_URL>\",\n", "  projectId: \"<PROJECT_ID>\",\n", "  watsonxAIAuthType: \"<PERSON><PERSON>en\",\n", "  watsonx<PERSON><PERSON><PERSON><PERSON>Token: \"<YOUR-BEARERTOKEN>\",\n", "};\n", "const instance = new WatsonxLLM(props);\n", "```\n", "\n", "### IBM watsonx.ai software authentication\n", "\n", "```typescript\n", "import { WatsonxLLM } from \"@langchain/community/llms/ibm\";\n", "\n", "const props = {\n", "  version: \"YYYY-MM-DD\",\n", "  serviceUrl: \"<SERVICE_URL>\",\n", "  projectId: \"<PROJECT_ID>\",\n", "  watsonxAIAuthType: \"cp4d\",\n", "  watsonxAIUsername: \"<YOUR-USERNAME>\",\n", "  watsonxAIPassword: \"<YOUR-PASSWORD>\",\n", "  watsonxAIUrl: \"<url>\",\n", "};\n", "const instance = new WatsonxLLM(props);\n", "```\n", "\n", "If you want to get automated tracing from individual queries, you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "// process.env.LANGSMITH_API_KEY = \"<YOUR API KEY HERE>\";\n", "// process.env.LANGSMITH_TRACING = \"true\";\n", "```\n", "\n", "### Installation\n", "\n", "This document compressor lives in the `@langchain/community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our compressor:"]}, {"cell_type": "code", "execution_count": null, "id": "70cc8e65-2a02-408a-bbc6-8ef649057d82", "metadata": {}, "outputs": [], "source": ["import { WatsonxRerank } from \"@langchain/community/document_compressors/ibm\";\n", "\n", "const watsonxRerank = new WatsonxRerank({\n", "  version: \"2024-05-31\",\n", "  serviceUrl: process.env.WATSONX_AI_SERVICE_URL,\n", "  projectId: process.env.WATSONX_AI_PROJECT_ID,\n", "  model: \"cross-encoder/ms-marco-minilm-l-12-v2\",\n", "});"]}, {"cell_type": "markdown", "id": "5c5f2839-4020-424e-9fc9-07777eede442", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "markdown", "id": "b195b0c7", "metadata": {}, "source": ["First, set up a basic RAG ingest pipeline with embeddings, a text splitter and a vector store. We'll use this to and rerank some documents regarding the selected query:"]}, {"cell_type": "code", "execution_count": null, "id": "51a60dbe-9f2e-4e04-bb62-23968f17164a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  Document {\n", "    pageContent: 'And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.',\n", "    metadata: { loc: [Object] },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: 'I spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \\n' +\n", "      '\\n' +\n", "      'I’ve worked on these issues a long time. \\n' +\n", "      '\\n' +\n", "      'I know what works: Investing in crime preventionand community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety.',\n", "    metadata: { loc: [Object] },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: 'We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \\n' +\n", "      '\\n' +\n", "      'The only nation that can be defined by a single word: possibilities. \\n' +\n", "      '\\n' +\n", "      'So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \\n' +\n", "      '\\n' +\n", "      'And my report is this: the State of the Union is strong—because you, the American people, are strong.',\n", "    metadata: { loc: [Object] },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: 'And I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \\n' +\n", "      '\\n' +\n", "      'Tonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.',\n", "    metadata: { loc: [Object] },\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["import { readFileSync } from \"node:fs\";\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "import { WatsonxEmbeddings } from \"@langchain/community/embeddings/ibm\";\n", "import { CharacterTextSplitter } from \"@langchain/textsplitters\";\n", "\n", "const embeddings = new WatsonxEmbeddings({\n", " version: \"YYYY-MM-DD\",\n", " serviceUrl: process.env.API_URL,\n", " projectId: \"<PROJECT_ID>\",\n", " spaceId: \"<SPACE_ID>\",\n", " model: \"ibm/slate-125m-english-rtrvr\",\n", "});\n", "\n", "const textSplitter = new CharacterTextSplitter({\n", "  chunkSize: 400,\n", "  chunkOverlap: 0,\n", "});\n", "  \n", "const query = \"What did the president say about <PERSON><PERSON><PERSON>\";\n", "const text = readFileSync(\"state_of_the_union.txt\", \"utf8\");\n", "\n", "const docs = await textSplitter.createDocuments([text]);\n", "const vectorStore = await MemoryVectorStore.fromDocuments(docs, embeddings);\n", "const vectorStoreRetriever = vectorStore.asRetriever();\n", "\n", "const result = await vectorStoreRetriever.invoke(query);\n", "console.log(result);"]}, {"cell_type": "markdown", "id": "b13ebf96", "metadata": {}, "source": ["Pass selected documents to rerank and recive specific score for each"]}, {"cell_type": "code", "execution_count": null, "id": "fad30397", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  { index: 0, relevanceScore: 0.726995587348938 },\n", "  { index: 1, relevanceScore: 0.5758284330368042 },\n", "  { index: 2, relevanceScore: 0.5479092597961426 },\n", "  { index: 3, relevanceScore: 0.5468723773956299 }\n", "]\n"]}], "source": ["import { WatsonxRerank } from \"@langchain/community/document_compressors/ibm\";\n", "\n", "const reranker = new WatsonxRerank({\n", "  version: \"2024-05-31\",\n", "  serviceUrl: process.env.WATSONX_AI_SERVICE_URL,\n", "  projectId: process.env.WATSONX_AI_PROJECT_ID,\n", "  model: \"cross-encoder/ms-marco-minilm-l-12-v2\",\n", "});\n", "const compressed = await reranker.rerank(result, query);\n", "console.log(compressed);"]}, {"cell_type": "markdown", "id": "55f4ec18", "metadata": {}, "source": ["Or else you could have the documents returned with the result, for that use .compressDocuments() method as below."]}, {"cell_type": "code", "execution_count": null, "id": "e6cc39ac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  Document {\n", "    pageContent: 'And I did that 4 days ago, when I nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>. One of our nation’s top legal minds, who will continue <PERSON>’s legacy of excellence.',\n", "    metadata: { loc: [Object], relevanceScore: 0.726995587348938 },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: 'I spoke with their families and told them that we are forever in debt for their sacrifice, and we will carry on their mission to restore the trust and safety every community deserves. \\n' +\n", "      '\\n' +\n", "      'I’ve worked on these issues a long time. \\n' +\n", "      '\\n' +\n", "      'I know what works: Investing in crime preventionand community police officers who’ll walk the beat, who’ll know the neighborhood, and who can restore trust and safety.',\n", "    metadata: { loc: [Object], relevanceScore: 0.5758284330368042 },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: 'We are the only nation on Earth that has always turned every crisis we have faced into an opportunity. \\n' +\n", "      '\\n' +\n", "      'The only nation that can be defined by a single word: possibilities. \\n' +\n", "      '\\n' +\n", "      'So on this night, in our 245th year as a nation, I have come to report on the State of the Union. \\n' +\n", "      '\\n' +\n", "      'And my report is this: the State of the Union is strong—because you, the American people, are strong.',\n", "    metadata: { loc: [Object], relevanceScore: 0.5479092597961426 },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: 'And I’m taking robust action to make sure the pain of our sanctions  is targeted at Russia’s economy. And I will use every tool at our disposal to protect American businesses and consumers. \\n' +\n", "      '\\n' +\n", "      'Tonight, I can announce that the United States has worked with 30 other countries to release 60 Million barrels of oil from reserves around the world.',\n", "    metadata: { loc: [Object], relevanceScore: 0.5468723773956299 },\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["const compressedWithResults = await reranker.compressDocuments(result, query);\n", "console.log(compressedWithResults);"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all Watsonx document compressor features and configurations head to the [API reference](https://api.js.langchain.com/modules/_langchain_community.document_compressors_ibm.html)."]}], "metadata": {"kernelspec": {"display_name": "JavaScript (Node.js)", "language": "javascript", "name": "javascript"}, "language_info": {"file_extension": ".js", "mimetype": "application/javascript", "name": "javascript", "version": "20.17.0"}}, "nbformat": 4, "nbformat_minor": 5}