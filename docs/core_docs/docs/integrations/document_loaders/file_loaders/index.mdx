---
label: "File Loaders"
hide_table_of_contents: true
sidebar_class_name: node-only-category
---

# File Loaders

:::tip Compatibility
Only available on Node.js.
:::

These loaders are used to load files given a filesystem path or a Blob object.

:::info
If you'd like to write your own document loader, see [this how-to](/docs/how_to/document_loader_custom/). If you'd like to contribute an integration, see [Contributing integrations](/docs/contributing).
:::

import { CategoryTable, IndexTable } from "@theme/FeatureTables";

## All document loaders

<IndexTable />
