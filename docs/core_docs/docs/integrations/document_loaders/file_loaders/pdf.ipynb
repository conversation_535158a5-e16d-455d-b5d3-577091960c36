{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: PDFLoader\n", "sidebar_class_name: node-only\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# PDFLoader\n", "\n", "```{=mdx}\n", "\n", ":::tip Compatibility\n", "\n", "Only available on Node.js.\n", "\n", ":::\n", "\n", "```\n", "\n", "This notebook provides a quick overview for getting started with `PDFLoader` [document loaders](/docs/concepts/document_loaders). For detailed documentation of all `PDFLoader` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_document_loaders_fs_pdf.PDFLoader.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Compatibility | Local | PY support | \n", "| :--- | :--- | :---: | :---: |  :---: |\n", "| [PDFLoader](https://api.js.langchain.com/classes/langchain_community_document_loaders_fs_pdf.PDFLoader.html) | [@langchain/community](https://api.js.langchain.com/modules/langchain_community_document_loaders_fs_pdf.html) | Node-only | ✅ | 🟠 (See note below) |\n", "\n", "## Setup\n", "\n", "To access `PDFLoader` document loader you'll need to install the `@langchain/community` integration, along with the `pdf-parse` package.\n", "\n", "### Credentials\n", "\n", "### Installation\n", "\n", "The LangChain PDFLoader integration lives in the `@langchain/community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core pdf-parse\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and load documents:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import { PDFLoader } from \"@langchain/community/document_loaders/fs/pdf\"\n", "\n", "const nike10kPdfPath = \"../../../../data/nke-10k-2023.pdf\"\n", "\n", "const loader = new PDFLoader(nike10kPdfPath)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document {\n", "  pageContent: 'Table of Contents\\n' +\n", "    'UNITED STATES\\n' +\n", "    'SECURITIES AND EXCHANGE COMMISSION\\n' +\n", "    'Washington, D.C. 20549\\n' +\n", "    'FORM 10-K\\n' +\n", "    '(<PERSON>)\\n' +\n", "    '☑ ANNUAL REPORT PURSUANT TO SECTION 13 OR 15(D) OF THE SECURITIES EXCHANGE ACT OF 1934\\n' +\n", "    'FOR THE FISCAL YEAR ENDED MAY 31, 2023\\n' +\n", "    'OR\\n' +\n", "    '☐ TRANSITION REPORT PURSUANT TO SECTION 13 OR 15(D) OF THE SECURITIES EXCHANGE ACT OF 1934\\n' +\n", "    'FOR THE TRANSITION PERIOD FROM                         TO                         .\\n' +\n", "    'Commission File No. 1-10635\\n' +\n", "    'NIKE, Inc.\\n' +\n", "    '(Exact name of Registrant as specified in its charter)\\n' +\n", "    'Oregon93-0584541\\n' +\n", "    '(State or other jurisdiction of incorporation)(IRS Employer Identification No.)\\n' +\n", "    'One Bowerman Drive, Beaverton, Oregon 97005-6453\\n' +\n", "    '(Address of principal executive offices and zip code)\\n' +\n", "    '(*************\\n' +\n", "    \"(Registrant's telephone number, including area code)\\n\" +\n", "    'SECURITIES REGISTERED PURSUANT TO SECTION 12(B) OF THE ACT:\\n' +\n", "    'Class B Common StockNKENew York Stock Exchange\\n' +\n", "    '(Title of each class)(Trading symbol)(Name of each exchange on which registered)\\n' +\n", "    'SECURITIES REGISTERED PURSUANT TO SECTION 12(G) OF THE ACT:\\n' +\n", "    'NONE\\n' +\n", "    'Indicate by check mark:YESNO\\n' +\n", "    '•if the registrant is a well-known seasoned issuer, as defined in Rule 405 of the Securities Act.þ ̈\\n' +\n", "    '•if the registrant is not required to file reports pursuant to Section 13 or Section 15(d) of the Act. ̈þ\\n' +\n", "    '•whether the registrant (1) has filed all reports required to be filed by Section 13 or 15(d) of the Securities Exchange Act of 1934 during the preceding\\n' +\n", "    '12 months (or for such shorter period that the registrant was required to file such reports), and (2) has been subject to such filing requirements for the\\n' +\n", "    'past 90 days.\\n' +\n", "    'þ ̈\\n' +\n", "    '•whether the registrant has submitted electronically every Interactive Data File required to be submitted pursuant to Rule 405 of Regulation S-T\\n' +\n", "    '(§232.405 of this chapter) during the preceding 12 months (or for such shorter period that the registrant was required to submit such files).\\n' +\n", "    'þ ̈\\n' +\n", "    '•whether the registrant is a large accelerated filer, an accelerated filer, a non-accelerated filer, a smaller reporting company or an emerging growth company. See the definitions of “large accelerated filer,”\\n' +\n", "    '“accelerated filer,” “smaller reporting company,” and “emerging growth company” in Rule 12b-2 of the Exchange Act.\\n' +\n", "    'Large accelerated filerþAccelerated filer☐Non-accelerated filer☐Smaller reporting company☐Emerging growth company☐\\n' +\n", "    '•if an emerging growth company, if the registrant has elected not to use the extended transition period for complying with any new or revised financial\\n' +\n", "    'accounting standards provided pursuant to Section 13(a) of the Exchange Act.\\n' +\n", "    ' ̈\\n' +\n", "    \"•whether the registrant has filed a report on and attestation to its management's assessment of the effectiveness of its internal control over financial\\n\" +\n", "    'reporting under Section 404(b) of the Sarbanes-Oxley Act (15 U.S.C. 7262(b)) by the registered public accounting firm that prepared or issued its audit\\n' +\n", "    'report.\\n' +\n", "    'þ\\n' +\n", "    '•if securities are registered pursuant to Section 12(b) of the Act, whether the financial statements of the registrant included in the filing reflect the\\n' +\n", "    'correction of an error to previously issued financial statements.\\n' +\n", "    ' ̈\\n' +\n", "    '•whether any of those error corrections are restatements that required a recovery analysis of incentive-based compensation received by any of the\\n' +\n", "    \"registrant's executive officers during the relevant recovery period pursuant to § 240.10D-1(b).\\n\" +\n", "    ' ̈\\n' +\n", "    '•\\n' +\n", "    'whether the registrant is a shell company (as defined in Rule 12b-2 of the Act).☐þ\\n' +\n", "    \"As of November 30, 2022, the aggregate market values of the Registrant's Common Stock held by non-affiliates were:\\n\" +\n", "    'Class A$7,831,564,572 \\n' +\n", "    'Class B136,467,702,472 \\n' +\n", "    '$144,299,267,044 ',\n", "  metadata: {\n", "    source: '../../../../data/nke-10k-2023.pdf',\n", "    pdf: {\n", "      version: '1.10.100',\n", "      info: [Object],\n", "      metadata: null,\n", "      totalPages: 107\n", "    },\n", "    loc: { pageNumber: 1 }\n", "  },\n", "  id: undefined\n", "}\n"]}], "source": ["const docs = await loader.load()\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  source: '../../../../data/nke-10k-2023.pdf',\n", "  pdf: {\n", "    version: '1.10.100',\n", "    info: {\n", "      PDFFormatVersion: '1.4',\n", "      IsAcroFormPresent: false,\n", "      IsXFAPresent: false,\n", "      Title: '0000320187-23-000039',\n", "      Author: 'EDGAR Online, a division of Donnelley Financial Solutions',\n", "      Subject: 'Form 10-K filed on 2023-07-20 for the period ending 2023-05-31',\n", "      Keywords: '0000320187-23-000039; ; 10-K',\n", "      Creator: 'EDGAR Filing HTML Converter',\n", "      Producer: 'EDGRpdf Service w/ EO.Pdf 22.0.40.0',\n", "      CreationDate: \"D:20230720162200-04'00'\",\n", "      ModDate: \"D:20230720162208-04'00'\"\n", "    },\n", "    metadata: null,\n", "    totalPages: 107\n", "  },\n", "  loc: { pageNumber: 1 }\n", "}\n"]}], "source": ["console.log(docs[0].metadata)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage, one document per file"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Table of Contents\n", "UNITED STATES\n", "SECURITIES AND EXCHANGE COMMISSION\n", "Washington, D.C. 20549\n", "FORM 10-K\n", "\n"]}], "source": ["import { PDFLoader } from \"@langchain/community/document_loaders/fs/pdf\";\n", "\n", "const singleDocPerFileLoader = new PDFLoader(nike10kPdfPath, {\n", "  splitPages: false,\n", "});\n", "\n", "const singleDoc = await singleDocPerFileLoader.load();\n", "console.log(singleDoc[0].pageContent.slice(0, 100))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage, custom `pdfjs` build\n", "\n", "By default we use the `pdfjs` build bundled with `pdf-parse`, which is compatible with most environments, including Node.js and modern browsers. If you want to use a more recent version of `pdfjs-dist` or if you want to use a custom build of `pdfjs-dist`, you can do so by providing a custom `pdfjs` function that returns a promise that resolves to the `PDFJS` object.\n", "\n", "In the following example we use the \"legacy\" (see [pdfjs docs](https://github.com/mozilla/pdf.js/wiki/Frequently-Asked-Questions#which-browsersenvironments-are-supported)) build of `pdfjs-dist`, which includes several polyfills not included in the default build.\n", "\n", "```{=mdx}\n", "<Npm2Yarn>\n", "  pdfjs-dist\n", "</Npm2Yarn>\n", "\n", "```\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import { PDFLoader } from \"@langchain/community/document_loaders/fs/pdf\";\n", "\n", "const customBuildLoader = new PDFLoader(nike10kPdfPath, {\n", "  // you may need to add `.then(m => m.default)` to the end of the import\n", "  // @lc-ts-ignore\n", "  pdfjs: () => import(\"pdfjs-dist/legacy/build/pdf.js\"),\n", "});"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Eliminating extra spaces\n", "\n", "PDFs come in many varieties, which makes reading them a challenge. The loader parses individual text elements and joins them together with a space by default, but\n", "if you are seeing excessive spaces, this may not be the desired behavior. In that case, you can override the separator with an empty string like this:\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(<PERSON>)\n", "☑ ANNUAL REPORT PURSUANT TO SECTION 13 OR 15(D) OF THE SECURITIES EXCHANGE ACT OF 1934\n", "FOR THE FISCAL YEAR ENDED MAY 31, 2023\n", "OR\n", "☐ TRANSITI\n"]}], "source": ["import { PDFLoader } from \"@langchain/community/document_loaders/fs/pdf\";\n", "\n", "const noExtraSpacesLoader = new PDFLoader(nike10kPdfPath, {\n", "  parsedItemSeparator: \"\",\n", "});\n", "\n", "const noExtraSpacesDocs = await noExtraSpacesLoader.load();\n", "console.log(noExtraSpacesDocs[0].pageContent.slice(100, 250))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading directories"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unknown file type: Star_Wars_The_Clone_Wars_S06E07_Crisis_at_the_Heart.srt\n", "Unknown file type: example.txt\n", "Unknown file type: notion.md\n", "Unknown file type: bad_frontmatter.md\n", "Unknown file type: frontmatter.md\n", "Unknown file type: no_frontmatter.md\n", "Unknown file type: no_metadata.md\n", "Unknown file type: tags_and_frontmatter.md\n", "Unknown file type: test.mp3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Document {\n", "  pageContent: 'Bitcoin: A Peer-to-Peer Electronic Cash System\\n' +\n", "    '<PERSON><PERSON>\\n' +\n", "    '<EMAIL>\\n' +\n", "    'www.bitcoin.org\\n' +\n", "    'Abstract.   A  purely   peer-to-peer   version   of   electronic   cash   would   allow   online \\n' +\n", "    'payments   to   be   sent   directly   from   one   party   to   another   without   going   through   a \\n' +\n", "    'financial institution.   Digital signatures provide part of the solution, but the main \\n' +\n", "    'benefits are lost if a trusted third party is still required to prevent double-spending. \\n' +\n", "    'We propose a solution to the double-spending problem using a peer-to-peer network. \\n' +\n", "    'The   network   timestamps   transactions   by   hashing   them   into   an   ongoing   chain   of \\n' +\n", "    'hash-based proof-of-work, forming a record that cannot be changed without redoing \\n' +\n", "    'the proof-of-work.   The longest chain not only serves as proof of the sequence of \\n' +\n", "    'events witnessed, but proof that it came from the largest pool of CPU power.   As \\n' +\n", "    'long as a majority of CPU power is controlled by nodes that are not cooperating to \\n' +\n", "    \"attack the network,  they'll  generate the  longest  chain  and  outpace attackers.   The \\n\" +\n", "    'network itself requires minimal structure.   Messages are broadcast on a best effort \\n' +\n", "    'basis,   and   nodes   can   leave   and   rejoin   the   network   at   will,   accepting   the   longest \\n' +\n", "    'proof-of-work chain as proof of what happened while they were gone.\\n' +\n", "    '1.Introduction\\n' +\n", "    'Commerce on the Internet has come to rely almost exclusively on financial institutions serving as \\n' +\n", "    'trusted third  parties  to process electronic payments.   While the  system works  well enough for \\n' +\n", "    'most   transactions,   it   still   suffers   from   the   inherent   weaknesses   of   the   trust   based   model. \\n' +\n", "    'Completely non-reversible transactions are not really possible, since financial institutions cannot \\n' +\n", "    'avoid   mediating   disputes.     The   cost   of   mediation   increases   transaction   costs,   limiting   the \\n' +\n", "    'minimum practical transaction size and cutting off the possibility for small casual transactions, \\n' +\n", "    'and   there   is   a   broader   cost   in   the   loss   of   ability   to   make   non-reversible   payments   for   non-\\n' +\n", "    'reversible services.  With the possibility of reversal, the need for trust spreads.  Merchants must \\n' +\n", "    'be wary of their customers, hassling them for more information than they would otherwise need. \\n' +\n", "    'A certain percentage of fraud is accepted as unavoidable.  These costs and payment uncertainties \\n' +\n", "    'can be avoided in person by using physical currency, but no mechanism exists to make payments \\n' +\n", "    'over a communications channel without a trusted party.\\n' +\n", "    'What is needed is an electronic payment system based on cryptographic proof instead of trust, \\n' +\n", "    'allowing any two willing parties to transact directly with each other without the need for a trusted \\n' +\n", "    'third  party.    Transactions  that  are  computationally  impractical  to   reverse   would  protect  sellers \\n' +\n", "    'from fraud, and routine escrow mechanisms could easily be implemented to protect buyers.   In \\n' +\n", "    'this paper, we propose a solution to the double-spending problem using a peer-to-peer distributed \\n' +\n", "    'timestamp server to generate computational proof of the chronological order of transactions.  The \\n' +\n", "    'system   is   secure   as   long   as   honest   nodes   collectively   control   more   CPU   power   than   any \\n' +\n", "    'cooperating group of attacker nodes.\\n' +\n", "    '1',\n", "  metadata: {\n", "    source: '/Users/<USER>/code/lang-chain-ai/langchainjs/examples/src/document_loaders/example_data/bitcoin.pdf',\n", "    pdf: {\n", "      version: '1.10.100',\n", "      info: [Object],\n", "      metadata: null,\n", "      totalPages: 9\n", "    },\n", "    loc: { pageNumber: 1 }\n", "  },\n", "  id: undefined\n", "}\n", "Document {\n", "  pageContent: 'Bitcoin: A Peer-to-Peer Electronic Cash System\\n' +\n", "    '<PERSON><PERSON>\\n' +\n", "    '<EMAIL>\\n' +\n", "    'www.bitcoin.org\\n' +\n", "    'Abstract.   A  purely   peer-to-peer   version   of   electronic   cash   would   allow   online \\n' +\n", "    'payments   to   be   sent   directly   from   one   party   to   another   without   going   through   a \\n' +\n", "    'financial institution.   Digital signatures provide part of the solution, but the main \\n' +\n", "    'benefits are lost if a trusted third party is still required to prevent double-spending. \\n' +\n", "    'We propose a solution to the double-spending problem using a peer-to-peer network. \\n' +\n", "    'The   network   timestamps   transactions   by   hashing   them   into   an   ongoing   chain   of \\n' +\n", "    'hash-based proof-of-work, forming a record that cannot be changed without redoing \\n' +\n", "    'the proof-of-work.   The longest chain not only serves as proof of the sequence of \\n' +\n", "    'events witnessed, but proof that it came from the largest pool of CPU power.   As \\n' +\n", "    'long as a majority of CPU power is controlled by nodes that are not cooperating to',\n", "  metadata: {\n", "    source: '/Users/<USER>/code/lang-chain-ai/langchainjs/examples/src/document_loaders/example_data/bitcoin.pdf',\n", "    pdf: {\n", "      version: '1.10.100',\n", "      info: [Object],\n", "      metadata: null,\n", "      totalPages: 9\n", "    },\n", "    loc: { pageNumber: 1, lines: [Object] }\n", "  },\n", "  id: undefined\n", "}\n"]}], "source": ["import { DirectoryLoader } from \"langchain/document_loaders/fs/directory\";\n", "import { PDFLoader } from \"@langchain/community/document_loaders/fs/pdf\";\n", "import { RecursiveCharacterTextSplitter } from \"@langchain/textsplitters\";\n", "\n", "const exampleDataPath = \"../../../../../../examples/src/document_loaders/example_data/\";\n", "\n", "/* Load all PDFs within the specified directory */\n", "const directoryLoader = new DirectoryLoader(\n", "  exampleData<PERSON><PERSON>,\n", "  {\n", "    \".pdf\": (path: string) => new PDFLoader(path),\n", "  }\n", ");\n", "\n", "const directoryDocs = await directoryLoader.load();\n", "\n", "console.log(directoryDocs[0]);\n", "\n", "/* Additional steps : Split text into chunks with any TextSplitter. You can then use it as context or save it to memory afterwards. */\n", "const textSplitter = new RecursiveCharacterTextSplitter({\n", "  chunkSize: 1000,\n", "  chunkOverlap: 200,\n", "});\n", "\n", "const splitDocs = await textSplitter.splitDocuments(directoryDocs);\n", "console.log(splitDocs[0]);\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all PDFLoader features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_community_document_loaders_fs_pdf.PDFLoader.html"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}