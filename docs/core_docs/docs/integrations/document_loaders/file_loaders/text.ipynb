{"cells": [{"cell_type": "raw", "metadata": {}, "source": ["---\n", "sidebar_label: TextLoader\n", "sidebar_class_name: node-only\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# TextLoader\n", "\n", "```{=mdx}\n", "\n", ":::tip Compatibility\n", "\n", "Only available on Node.js.\n", "\n", ":::\n", "\n", "```\n", "\n", "This notebook provides a quick overview for getting started with `TextLoader` [document loaders](/docs/concepts/document_loaders). For detailed documentation of all `TextLoader` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain.document_loaders_fs_text.TextLoader.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Compatibility | Local | PY support | \n", "| :--- | :--- | :---: | :---: |  :---: |\n", "| [TextLoader](https://api.js.langchain.com/classes/langchain.document_loaders_fs_text.TextLoader.html) | [langchain](https://api.js.langchain.com/modules/langchain.document_loaders_fs_text.html) | Node-only | ✅ | ❌ |\n", "\n", "## Setup\n", "\n", "To access `TextLoader` document loader you'll need to install the `langchain` package.\n", "\n", "### Installation\n", "\n", "The LangChain TextLoader integration lives in the `langchain` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  langchain\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and load documents:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import { TextLoader } from \"langchain/document_loaders/fs/text\"\n", "\n", "const loader = new TextLoader(\"../../../../../../examples/src/document_loaders/example_data/example.txt\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document {\n", "  pageContent: 'Foo\\nBar\\nBaz\\n\\n',\n", "  metadata: {\n", "    source: '../../../../../../examples/src/document_loaders/example_data/example.txt'\n", "  },\n", "  id: undefined\n", "}\n"]}], "source": ["const docs = await loader.load()\n", "docs[0]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  source: '../../../../../../examples/src/document_loaders/example_data/example.txt'\n", "}\n"]}], "source": ["console.log(docs[0].metadata)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all TextLoader features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain.document_loaders_fs_text.TextLoader.html"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 4}