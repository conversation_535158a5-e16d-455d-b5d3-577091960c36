---
sidebar_position: 0
---

# Document loaders

[Document loaders](/docs/concepts/document_loaders) load data into LangChain's expected format for use-cases such as [retrieval-augmented generation (RAG)](/docs/tutorials/rag).

LangChain.js categorizes document loaders in two different ways:

- [File loaders](/docs/integrations/document_loaders/file_loaders/), which load data into LangChain formats from your local filesystem.
- [Web loaders](/docs/integrations/document_loaders/web_loaders/), which load data from remote sources.

See the individual pages for more on each category.

:::info
If you'd like to write your own document loader, see [this how-to](/docs/how_to/document_loader_custom/). If you'd like to contribute an integration, see [Contributing integrations](/docs/contributing).
:::
