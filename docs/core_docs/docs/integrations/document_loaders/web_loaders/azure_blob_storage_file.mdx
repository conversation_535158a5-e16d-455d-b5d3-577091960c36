---
hide_table_of_contents: true
sidebar_class_name: node-only
---

# Azure Blob Storage File

:::tip Compatibility
Only available on Node.js.
:::

This covers how to load an Azure File into LangChain documents.

## Setup

To use this loader, you'll need to have Unstructured already set up and ready to use at an available URL endpoint. It can also be configured to run locally.

See the docs [here](/docs/integrations/document_loaders/file_loaders/unstructured) for information on how to do that.

You'll also need to install the official Azure Storage Blob client library:

```bash npm2yarn
npm install @langchain/community @langchain/core @azure/storage-blob
```

## Usage

Once Unstructured is configured, you can use the Azure Blob Storage File loader to load files and then convert them into a Document.

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/azure_blob_storage_file.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
