# Browserbase Loader

## Description

[Browserbase](https://browserbase.com) is a developer platform to reliably run, manage, and monitor headless browsers.

Power your AI data retrievals with:

- [Serverless Infrastructure](https://docs.browserbase.com/under-the-hood) providing reliable browsers to extract data from complex UIs
- [Stealth Mode](https://docs.browserbase.com/features/stealth-mode) with included fingerprinting tactics and automatic captcha solving
- [Session Debugger](https://docs.browserbase.com/features/sessions) to inspect your Browser Session with networks timeline and logs
- [Live Debug](https://docs.browserbase.com/guides/session-debug-connection/browser-remote-control) to quickly debug your automation

## Installation

- Get an API key and Project ID from [browserbase.com](https://browserbase.com) and set it in environment variables (`BROWSERBASE_API_KEY`, `BROWSERBASE_PROJECT_ID`).
- Install the [Browserbase SDK](http://github.com/browserbase/js-sdk):

```bash npm2yarn
npm i @langchain/community @langchain/core @browserbasehq/sdk
```

## Example

Utilize the `BrowserbaseLoader` as follows to allow your agent to load websites:

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/browserbase.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Arguments

- `urls`: Required. List of URLs to load.

## Options

- `textContent` Retrieve only text content. Default is `false`.
- `sessionId` Optional. Provide an existing Session ID.
- `proxy` Optional. Enable/Disable Proxies.
