---
hide_table_of_contents: true
---

# College Confidential

This example goes over how to load data from the college confidential website, using Cheerio. One document will be created for each page.

## Setup

```bash npm2yarn
npm install @langchain/community @langchain/core cheerio
```

## Usage

```typescript
import { CollegeConfidentialLoader } from "@langchain/community/document_loaders/web/college_confidential";

const loader = new CollegeConfidentialLoader(
  "https://www.collegeconfidential.com/colleges/brown-university/"
);

const docs = await loader.load();
```
