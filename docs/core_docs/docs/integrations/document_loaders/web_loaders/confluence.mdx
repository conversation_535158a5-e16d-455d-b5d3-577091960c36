---
sidebar_class_name: node-only
---

# Confluence

:::tip Compatibility
Only available on Node.js.
:::

This covers how to load document objects from pages in a Confluence space.

## Credentials

- You'll need to set up an access token and provide it along with your confluence username in order to authenticate the request
- You'll also need the `space key` for the space containing the pages to load as documents. This can be found in the url when navigating to your space e.g. `https://example.atlassian.net/wiki/spaces/{SPACE_KEY}`
- And you'll need to install `html-to-text` to parse the pages into plain text

```bash npm2yarn
npm install @langchain/community @langchain/core html-to-text
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/confluence.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
