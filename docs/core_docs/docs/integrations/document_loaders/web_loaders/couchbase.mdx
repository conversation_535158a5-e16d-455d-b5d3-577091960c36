---
hide_table_of_contents: true
sidebar_class_name: node-only
---

# Couchbase

[Couchbase](http://couchbase.com/) is an award-winning distributed NoSQL cloud database that delivers unmatched versatility, performance, scalability, and financial value for all of your cloud, mobile, AI, and edge computing applications.

This guide shows how to use load documents from couchbase database.

# Installation

```bash npm2yarn
npm install @langchain/community @langchain/core couchbase
```

## Usage

### Querying for Documents from Couchbase

For more details on connecting to a Couchbase cluster, please check the [Node.js SDK documentation](https://docs.couchbase.com/nodejs-sdk/current/howtos/managing-connections.html#connection-strings).

For help with querying for documents using SQL++ (SQL for JSON), please check the [documentation](https://docs.couchbase.com/server/current/n1ql/n1ql-language-reference/index.html).

```typescript
import { CouchbaseDocumentLoader } from "@langchain/community/document_loaders/web/couchbase";
import { Cluster } from "couchbase";

const connectionString = "couchbase://localhost"; // valid couchbase connection string
const dbUsername = "Administrator"; // valid database user with read access to the bucket being queried
const dbPassword = "Password"; // password for the database user

// query is a valid SQL++ query
const query = `
    SELECT h.* FROM \`travel-sample\`.inventory.hotel h 
    WHERE h.country = 'United States'
    LIMIT 1
`;
```

### Connect to Couchbase Cluster

```typescript
const couchbaseClient = await Cluster.connect(connectionString, {
  username: dbUsername,
  password: dbPassword,
  configProfile: "wanDevelopment",
});
```

### Create the Loader

```typescript
const loader = new CouchbaseDocumentLoader(
  couchbaseClient, // The connected couchbase cluster client
  query // A valid SQL++ query which will return the required data
);
```

### Load Documents

You can fetch the documents by calling the `load` method of the loader. It will return a list with all the documents. If you want to avoid this blocking call, you can call `lazy_load` method that returns an Iterator.

```typescript
// using load method
docs = await loader.load();
console.log(docs);
```

```typescript
// using lazy_load
for await (const doc of this.lazyLoad()) {
  console.log(doc);
  break; // break based on required condition
}
```

### Specifying Fields with Content and Metadata

The fields that are part of the Document content can be specified using the `pageContentFields` parameter.
The metadata fields for the Document can be specified using the `metadataFields` parameter.

```typescript
const loaderWithSelectedFields = new CouchbaseDocumentLoader(
  couchbaseClient,
  query,
  // pageContentFields
  [
    "address",
    "name",
    "city",
    "phone",
    "country",
    "geo",
    "description",
    "reviews",
  ],
  ["id"] // metadataFields
);

const filtered_docs = await loaderWithSelectedFields.load();
console.log(filtered_docs);
```
