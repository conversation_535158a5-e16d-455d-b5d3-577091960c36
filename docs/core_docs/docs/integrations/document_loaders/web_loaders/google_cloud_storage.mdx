---
hide_table_of_contents: true
sidebar_class_name: node-only
---

# Google Cloud Storage

:::tip Compatibility
Only available on Node.js.
:::

This covers how to load a Google Cloud Storage File into LangChain documents.

## Setup

To use this loader, you'll need to have Unstructured already set up and ready to use at an available URL endpoint. It can also be configured to run locally.

See the docs [here](/docs/integrations/document_loaders/file_loaders/unstructured) for information on how to do that.

You'll also need to install the official Google Cloud Storage SDK:

```bash npm2yarn
npm install @langchain/community @langchain/core @google-cloud/storage
```

## Usage

Once Unstructured is configured, you can use the Google Cloud Storage loader to load files and then convert them into a Document.

In addition, you can optionally provide a `storageOptions` parameter to specify not only your storage options but also other authentication ways if you don't want Application Default Credentials(ADC) as default manner.

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/google_cloud_storage.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
