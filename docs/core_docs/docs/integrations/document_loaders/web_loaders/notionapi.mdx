---
sidebar_class_name: node-only
hide_table_of_contents: true
---

# Notion API

This guide will take you through the steps required to load documents from Notion pages and databases using the Notion API.

## Overview

Notion is a versatile productivity platform that consolidates note-taking, task management, and data organization tools into one interface.

This document loader is able to take full Notion pages and databases and turn them into a LangChain Documents ready to be integrated into your projects.

## Setup

1. You will first need to install the official Notion client and the [notion-to-md](https://www.npmjs.com/package/notion-to-md) package as peer dependencies:

```bash npm2yarn
npm install @langchain/community @langchain/core @notionhq/client notion-to-md
```

2. Create a [Notion integration](https://www.notion.so/my-integrations) and securely record the Internal Integration Secret (also known as `NOTION_INTEGRATION_TOKEN`).
3. Add a connection to your new integration on your page or database. To do this open your Notion page, go to the settings pips in the top right and scroll down to `Add connections` and select your new integration.
4. Get the `PAGE_ID` or `DATABASE_ID` for the page or database you want to load.

> The 32 char hex in the url path represents the `ID`. For example:

> PAGE_ID: [https://www.notion.so/skarard/LangChain-Notion-API-`b34ca03f219c4420a6046fc4bdfdf7b4`](https://www.notion.so/skarard/LangChain-Notion-API-b34ca03f219c4420a6046fc4bdfdf7b4)

> DATABASE_ID: [https://www.notion.so/skarard/`c393f19c3903440da0d34bf9c6c12ff2`?v=9c70a0f4e174498aa0f9021e0a9d52de](https://www.notion.so/skarard/c393f19c3903440da0d34bf9c6c12ff2?v=9c70a0f4e174498aa0f9021e0a9d52de)

> REGEX: `/(?<!=)[0-9a-f]{32}/`

## Example Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/notionapi.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
