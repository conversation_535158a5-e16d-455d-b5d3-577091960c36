---
hide_table_of_contents: true
sidebar_class_name: node-only
---

# S3 File

:::tip Compatibility
Only available on Node.js.
:::

This covers how to load document objects from an s3 file object.

## Setup

To run this index you'll need to have Unstructured already set up and ready to use at an available URL endpoint. It can also be configured to run locally.

See the docs [here](/docs/integrations/document_loaders/file_loaders/unstructured) for information on how to do that.

You'll also need to install the official AWS SDK:

```bash npm2yarn
npm install @langchain/community @langchain/core @aws-sdk/client-s3
```

## Usage

Once Unstructured is configured, you can use the S3 loader to load files and then convert them into a Document.

You can optionally provide a s3Config parameter to specify your bucket region, access key, and secret access key. If these are not provided, you will need to have them in your environment (e.g., by running `aws configure`).

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/s3.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
