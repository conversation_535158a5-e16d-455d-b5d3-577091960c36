---
hide_table_of_contents: true
---

# Search<PERSON><PERSON> Loader

This guide shows how to use SearchApi with LangChain to load web search results.

## Overview

[SearchApi](https://www.searchapi.io/) is a real-time API that grants developers access to results from a variety of search engines, including engines like [Google Search](https://www.searchapi.io/docs/google),
[Google News](https://www.searchapi.io/docs/google-news), [Google Scholar](https://www.searchapi.io/docs/google-scholar), [YouTube Transcripts](https://www.searchapi.io/docs/youtube-transcripts) or any other engine that could be found in documentation.
This API enables developers and businesses to scrape and extract meaningful data directly from the result pages of all these search engines, providing valuable insights for different use-cases.

This guide shows how to load web search results using the `SearchApiLoader` in LangChain. The `SearchApiLoader` simplifies the process of loading and processing web search results from SearchApi.

## Setup

You'll need to sign up and retrieve your [SearchApi API key](https://www.searchapi.io/).

## Usage

Here's an example of how to use the `SearchApiLoader`:

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/searchapi.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core @langchain/openai
```

<CodeBlock language="typescript">{Example}</CodeBlock>

In this example, the `SearchApiLoader` is used to load web search results, which are then stored in memory using `MemoryVectorStore`. A retrieval chain is then used to retrieve the most relevant documents from the memory and answer the question based on these documents. This demonstrates how the `SearchApiLoader` can streamline the process of loading and processing web search results.
