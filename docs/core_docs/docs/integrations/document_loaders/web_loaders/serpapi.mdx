---
hide_table_of_contents: true
---

# <PERSON><PERSON><PERSON><PERSON> Loader

This guide shows how to use SerpAPI with LangChain to load web search results.

## Overview

[SerpAPI](https://serpapi.com/) is a real-time API that provides access to search results from various search engines. It is commonly used for tasks like competitor analysis and rank tracking. It empowers businesses to scrape, extract, and make sense of data from all search engines' result pages.

This guide shows how to load web search results using the `SerpAPILoader` in LangChain. The `SerpAPILoader` simplifies the process of loading and processing web search results from SerpAPI.

## Setup

You'll need to sign up and retrieve your [SerpAPI API key](https://serpapi.com/dashboard).

## Usage

Here's an example of how to use the `SerpAPILoader`:

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/serpapi.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core @langchain/openai
```

<CodeBlock language="typescript">{Example}</CodeBlock>

In this example, the `SerpAPILoader` is used to load web search results, which are then stored in memory using `MemoryVectorStore`. A retrieval chain is then used to retrieve the most relevant documents from the memory and answer the question based on these documents. This demonstrates how the `SerpAPILoader` can streamline the process of loading and processing web search results.
