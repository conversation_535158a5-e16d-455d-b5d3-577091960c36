# Sitemap Loader

This notebook goes over how to use the [`SitemapLoader`](https://api.js.langchain.com/classes/_langchain_community.document_loaders_web_sitemap.SitemapLoader.html) class to load sitemaps into `Document`s.

## Setup

First, we need to install the `langchain` package:

```bash npm2yarn
npm install @langchain/community @langchain/core
```

The URL passed in must either contain the `.xml` path to the sitemap, or a default `/sitemap.xml` will be appended to the URL.

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/sitemap.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

Or, if you want to only load the sitemap and not the contents of each page from the sitemap, you can use the `parseSitemap` method:

import ParseSitemapExample from "@examples/document_loaders/parse_sitemap.ts";

<CodeBlock language="typescript">{ParseSitemapExample}</CodeBlock>
