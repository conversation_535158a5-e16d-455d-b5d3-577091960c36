---
hide_table_of_contents: true
sidebar_class_name: node-only
---

# Sonix Audio

:::tip Compatibility
Only available on Node.js.
:::

This covers how to load document objects from an audio file using the [Sonix](https://sonix.ai/) API.

## Setup

To run this loader you will need to create an account on the https://sonix.ai/ and obtain an auth key from the https://my.sonix.ai/api page.

You'll also need to install the `sonix-speech-recognition` library:

```bash npm2yarn
npm install @langchain/community @langchain/core sonix-speech-recognition
```

## Usage

Once auth key is configured, you can use the loader to create transcriptions and then convert them into a Document.
In the `request` parameter, you can either specify a local file by setting `audioFilePath` or a remote file using `audioUrl`.
You will also need to specify the audio language. See the list of supported languages [here](https://sonix.ai/docs/api#languages).

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/sonix_audio_transcription.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
