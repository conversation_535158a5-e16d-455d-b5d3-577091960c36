---
hide_table_of_contents: true
---

# Blockchain Data

This example shows how to load blockchain data, including NFT metadata and transactions for a contract address, via the sort.xyz SQL API.

You will need a free Sort API key, visiting sort.xyz to obtain one.

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/sort_xyz_blockchain.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core @langchain/openai
```

<CodeBlock language="typescript">{Example}</CodeBlock>
