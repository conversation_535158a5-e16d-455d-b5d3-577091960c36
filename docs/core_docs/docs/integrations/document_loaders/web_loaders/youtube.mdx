---
hide_table_of_contents: true
---

# YouTube transcripts

This covers how to load YouTube transcripts into LangChain documents.

## Setup

You'll need to install the [youtubei.js](https://www.npmjs.com/package/youtubei.js) to extract metadata:

```bash npm2yarn
npm install @langchain/community @langchain/core youtubei.js
```

## Usage

You need to specify a link to the video in the `url`. You can also specify `language` in [ISO 639-1](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) and `addVideoInfo` flag.

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_loaders/youtube.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
