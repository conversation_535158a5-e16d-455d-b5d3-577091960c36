# OpenAI functions metadata tagger

It can often be useful to tag ingested documents with structured metadata, such as the title, tone, or length of a document, to allow for more targeted similarity search later. However, for large numbers of documents, performing this labelling process manually can be tedious.

The `MetadataTagger` document transformer automates this process by extracting metadata from each provided document according to a provided schema. It uses a configurable OpenAI Functions-powered chain under the hood, so if you pass a custom LLM instance, it must be an OpenAI model with functions support.

**Note:** This document transformer works best with complete documents, so it's best to run it first with whole documents before doing any other splitting or processing!

### Usage

For example, let's say you wanted to index a set of movie reviews. You could initialize the document transformer as follows:

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/document_transformers/metadata_tagger.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{Example}</CodeBlock>

There is an additional `createMetadataTagger` method that accepts a valid JSON Schema object as well.

### Customization

You can pass the underlying tagging chain the standard LLMChain arguments in the second options parameter.
For example, if you wanted to ask the LLM to focus specific details in the input documents, or extract metadata in a certain style, you could pass in a custom prompt:

import CustomExample from "@examples/document_transformers/metadata_tagger_custom_prompt.ts";

<CodeBlock language="typescript">{CustomExample}</CodeBlock>
