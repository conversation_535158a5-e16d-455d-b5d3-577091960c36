# AI21

You can get started with AI21Labs' Jurassic family of models, as well as see a full list of available foundational models, by signing up for an API key [on their website](https://www.ai21.com/).

Here's an example of initializing an instance in LangChain.js:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

import CodeBlock from "@theme/CodeBlock";
import AI21Example from "@examples/models/llm/ai21.ts";

<CodeBlock language="typescript">{AI21Example}</CodeBlock>

## Related

- LLM [conceptual guide](/docs/concepts/text_llms)
- LLM [how-to guides](/docs/how_to/#llms)
