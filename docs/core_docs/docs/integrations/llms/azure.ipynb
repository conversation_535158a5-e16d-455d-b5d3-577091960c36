{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Azure OpenAI\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# AzureOpenAI\n", "\n", "```{=mdx}\n", "\n", ":::caution\n", "You are currently on a page documenting the use of Azure OpenAI [text completion models](/docs/concepts/text_llms). The latest and most popular Azure OpenAI models are [chat completion models](/docs/concepts/chat_models).\n", "\n", "Unless you are specifically using `gpt-3.5-turbo-instruct`, you are probably looking for [this page instead](/docs/integrations/chat/azure/).\n", ":::\n", "\n", ":::info\n", "\n", "Previously, LangChain.js supported integration with Azure OpenAI using the dedicated [Azure OpenAI SDK](https://github.com/Azure/azure-sdk-for-js/tree/main/sdk/openai/openai). This SDK is now deprecated in favor of the new Azure integration in the OpenAI SDK, which allows to access the latest OpenAI models and features the same day they are released, and allows seemless transition between the OpenAI API and Azure OpenAI.\n", "\n", "If you are using Azure OpenAI with the deprecated SDK, see the [migration guide](#migration-from-azure-openai-sdk) to update to the new API.\n", "\n", ":::\n", "\n", "```\n", "\n", "[Azure OpenAI](https://learn.microsoft.com/en-us/azure/ai-services/openai/) is a Microsoft Azure service that provides powerful language models from OpenAI.\n", "\n", "This will help you get started with AzureOpenAI completion models (LLMs) using LangChain. For detailed documentation on `AzureOpenAI` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_openai.AzureOpenAI.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/llms/azure_openai) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [AzureOpenAI](https://api.js.langchain.com/classes/langchain_openai.AzureOpenAI.html) | [@langchain/openai](https://api.js.langchain.com/modules/langchain_openai.html) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/openai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/openai?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access AzureOpenAI models you'll need to create an Azure account, get an API key, and install the `@langchain/openai` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [azure.microsoft.com](https://azure.microsoft.com/) to sign up to AzureOpenAI and generate an API key. \n", "\n", "You'll also need to have an Azure OpenAI instance deployed. You can deploy a version on Azure Portal following [this guide](https://learn.microsoft.com/azure/ai-services/openai/how-to/create-resource?pivots=web-portal).\n", "\n", "Once you have your instance running, make sure you have the name of your instance and key. You can find the key in the Azure Portal, under the \"Keys and Endpoint\" section of your instance.\n", "\n", "If you're using Node.js, you can define the following environment variables to use the service:\n", "\n", "```bash\n", "AZURE_OPENAI_API_INSTANCE_NAME=<YOUR_INSTANCE_NAME>\n", "AZURE_OPENAI_API_DEPLOYMENT_NAME=<YOUR_DEPLOYMENT_NAME>\n", "AZURE_OPENAI_API_KEY=<YOUR_KEY>\n", "AZURE_OPENAI_API_VERSION=\"2024-02-01\"\n", "```\n", "\n", "Alternatively, you can pass the values directly to the `AzureOpenAI` constructor.\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain AzureOpenAI integration lives in the `@langchain/openai` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/openai @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 7, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import { AzureOpenAI } from \"@langchain/openai\"\n", "\n", "const llm = new AzureOpenAI({\n", "  model: \"gpt-3.5-turbo-instruct\",\n", "  azureOpenAIApiKey: \"<your_key>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_KEY\n", "  azureOpenAIApiInstanceName: \"<your_instance_name>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_INSTANCE_NAME\n", "  azureOpenAIApiDeploymentName: \"<your_deployment_name>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME\n", "  azureOpenAIApiVersion: \"<api_version>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_VERSION\n", "  temperature: 0,\n", "  maxTokens: undefined,\n", "  timeout: undefined,\n", "  maxRetries: 2,\n", "  // other params...\n", "})"]}, {"cell_type": "markdown", "id": "0ee90032", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 8, "id": "035dea0f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["provides AI solutions to businesses. They offer a range of services including natural language processing, computer vision, and machine learning. Their solutions are designed to help businesses automate processes, gain insights from data, and improve decision-making. AzureOpenAI also offers consulting services to help businesses identify and implement the best AI solutions for their specific needs. They work with a variety of industries, including healthcare, finance, and retail. With their expertise in AI and their partnership with Microsoft Azure, AzureOpenAI is a trusted provider of AI solutions for businesses looking to stay ahead in the rapidly evolving world of technology.\n"]}], "source": ["const inputText = \"AzureOpenAI is an AI company that \"\n", "\n", "const completion = await llm.invoke(inputText)\n", "completion"]}, {"cell_type": "markdown", "id": "add38532", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our completion model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 9, "id": "078e9db2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Ich liebe Programmieren.\n"]}], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = new PromptTemplate({\n", "  template: \"How to say {input} in {output_language}:\\n\",\n", "  inputVariables: [\"input\", \"output_language\"],\n", "})\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "e99eef30", "metadata": {}, "source": ["## Using Azure Managed Identity\n", "\n", "If you're using Azure Managed Identity, you can configure the credentials like this:"]}, {"cell_type": "code", "execution_count": null, "id": "c21d1eb8", "metadata": {}, "outputs": [], "source": ["import {\n", "  DefaultAzureCredential,\n", "  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "} from \"@azure/identity\";\n", "import { AzureOpenAI } from \"@langchain/openai\";\n", "\n", "const credentials = new DefaultAzureCredential();\n", "const azureADTokenProvider = getBearerTokenProvider(\n", "  credentials,\n", "  \"https://cognitiveservices.azure.com/.default\"\n", ");\n", "\n", "const managedIdentityLLM = new AzureOpenAI({\n", "  azureADTokenProvider,\n", "  azureOpenAIApiInstanceName: \"<your_instance_name>\",\n", "  azureOpenAIApiDeploymentName: \"<your_deployment_name>\",\n", "  azureOpenAIApiVersion: \"<api_version>\",\n", "});\n"]}, {"cell_type": "markdown", "id": "94c2572b", "metadata": {}, "source": ["## Using a different domain\n", "\n", "If your instance is hosted under a domain other than the default `openai.azure.com`, you'll need to use the alternate `AZURE_OPENAI_BASE_PATH` environment variable.\n", "For example, here's how you would connect to the domain `https://westeurope.api.microsoft.com/openai/deployments/{DEPLOYMENT_NAME}`:"]}, {"cell_type": "code", "execution_count": null, "id": "bbf107a2", "metadata": {}, "outputs": [], "source": ["import { AzureOpenAI } from \"@langchain/openai\";\n", "\n", "const differentDomainLLM = new AzureOpenAI({\n", "  azureOpenAIApiKey: \"<your_key>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_KEY\n", "  azureOpenAIApiDeploymentName: \"<your_deployment_name>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_DEPLOYMENT_NAME\n", "  azureOpenAIApiVersion: \"<api_version>\", // In Node.js defaults to process.env.AZURE_OPENAI_API_VERSION\n", "  azureOpenAIBasePath:\n", "    \"https://westeurope.api.microsoft.com/openai/deployments\", // In Node.js defaults to process.env.AZURE_OPENAI_BASE_PATH\n", "});\n"]}, {"cell_type": "markdown", "id": "afcff984", "metadata": {}, "source": ["## Migration from Azure OpenAI SDK\n", "\n", "If you are using the deprecated Azure OpenAI SDK with the `@langchain/azure-openai` package, you can update your code to use the new Azure integration following these steps:\n", "\n", "1. Install the new `@langchain/openai` package and remove the previous `@langchain/azure-openai` package:\n", "   ```bash\n", "   npm install @langchain/openai\n", "   npm uninstall @langchain/azure-openai\n", "   ```\n", "2. Update your imports to use the new `AzureOpenAI` and `AzureChatOpenAI` classes from the `@langchain/openai` package:\n", "   ```typescript\n", "   import { AzureOpenAI } from \"@langchain/openai\";\n", "   ```\n", "3. Update your code to use the new `AzureOpenAI` and `AzureChatOpenAI` classes and pass the required parameters:\n", "\n", "   ```typescript\n", "   const model = new AzureOpenAI({\n", "     azureOpenAIApiKey: \"<your_key>\",\n", "     azureOpenAIApiInstanceName: \"<your_instance_name>\",\n", "     azureOpenAIApiDeploymentName: \"<your_deployment_name>\",\n", "     azureOpenAIApiVersion: \"<api_version>\",\n", "   });\n", "   ```\n", "\n", "   Notice that the constructor now requires the `azureOpenAIApiInstanceName` parameter instead of the `azureOpenAIEndpoint` parameter, and adds the `azureOpenAIApiVersion` parameter to specify the API version.\n", "\n", "   - If you were using Azure Managed Identity, you now need to use the `azureADTokenProvider` parameter to the constructor instead of `credentials`, see the [Azure Managed Identity](#using-azure-managed-identity) section for more details.\n", "\n", "   - If you were using environment variables, you now have to set the `AZURE_OPENAI_API_INSTANCE_NAME` environment variable instead of `AZURE_OPENAI_API_ENDPOINT`, and add the `AZURE_OPENAI_API_VERSION` environment variable to specify the API version.\n"]}, {"cell_type": "markdown", "id": "e9bdfcef", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all AzureOpenAI features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_openai.AzureOpenAI.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}