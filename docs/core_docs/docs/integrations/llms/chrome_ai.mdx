---
sidebar_label: ChromeA<PERSON>
---

import CodeBlock from "@theme/CodeBlock";

# ChromeAI

:::info
This feature is **experimental** and is subject to change.
:::

:::note
The `Built-in AI Early Preview Program` by Google is currently in beta. To apply for access or find more information, please visit [this link](https://developer.chrome.com/docs/ai/built-in).
:::

ChromeAI leverages Gemini Nano to run LLMs directly in the browser or in a [worker](https://developer.mozilla.org/en-US/docs/Web/API/Worker),
without the need for an internet connection. This allows for running faster and private models without ever having data leave the consumers device.

## Getting started

Once you've been granted access to the program, follow Google's provided instructions to download the model.

Once downloaded, you can start using `ChromeAI` in the browser as follows:

```typescript
import { ChromeAI } from "@langchain/community/experimental/llms/chrome_ai";

const model = new ChromeAI({
  temperature: 0.5, // Optional, defaults to 0.5
  topK: 40, // Optional, defaults to 40
});

const response = await model.invoke("Write me a short poem please");

/*
  In the realm where moonlight weaves its hue,
  Where dreams and secrets gently intertwine,
  There's a place of tranquility and grace,
  Where whispers of the night find their place.

  Beneath the canopy of starlit skies,
  Where dreams take flight and worries cease,
  A haven of tranquility, pure and true,
  Where the heart finds solace, finding dew.

  In this realm where dreams find their release,
  Where the soul finds peace, at every peace,
  Let us wander, lost in its embrace,
  Finding solace in this tranquil space.
*/
```

### Streaming

`ChromeAI` also supports streaming outputs:

```typescript
import { ChromeAI } from "@langchain/community/experimental/llms/chrome_ai";

const model = new ChromeAI({
  temperature: 0.5, // Optional, defaults to 0.5
  topK: 40, // Optional, defaults to 40
});

for await (const chunk of await model.stream("How are you?")) {
  console.log(chunk);
}

/*
  As
   an
   AI
   language
   model
  ,
   I
   don
  '
  t
   have
   personal
   experiences
   or
   the
   ability
   to
   experience
   emotions
  .
   Therefore
  ,
   I
   cannot
   directly
   answer
   the
   question
   "
  How
   are
   you
  ?".
  
  
  
  May
   I
   suggest
   answering
   something
   else
  ?
*/
```

## Related

- LLM [conceptual guide](/docs/concepts/text_llms)
- LLM [how-to guides](/docs/how_to/#llms)
