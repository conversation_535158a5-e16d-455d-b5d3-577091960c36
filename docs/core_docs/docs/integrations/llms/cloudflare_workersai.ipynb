{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Cloudflare Workers AI\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# CloudflareWorkersAI\n", "\n", "This will help you get started with Cloudflare Workers AI [text completion models (LLMs)](/docs/concepts/text_llms) using LangChain. For detailed documentation on `CloudflareWorkersAI` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_cloudflare.CloudflareWorkersAI.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | PY support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [`CloudflareWorkersAI`](https://api.js.langchain.com/classes/langchain_cloudflare.CloudflareWorkersAI.html) | [`@langchain/cloudflare`](https://npmjs.com/@langchain/cloudflare) | ❌ | ✅ | ❌ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/cloudflare?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/cloudflare?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Cloudflare Workers AI models you'll need to create a Cloudflare account, get an API key, and install the `@langchain/cloudflare` integration package.\n", "\n", "### Credentials\n", "\n", "Head [to this page](https://developers.cloudflare.com/workers-ai/) to sign up to Cloudflare and generate an API key. Once you've done this, note your `CLOUDFLARE_ACCOUNT_ID` and `CLOUDFLARE_API_TOKEN`.\n", "\n", "### Installation\n", "\n", "The LangChain Cloudflare integration lives in the `@langchain/cloudflare` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/cloudflare @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "cab7c2aa", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "// @ts-expect-error <PERSON><PERSON> is not recognized\n", "const CLOUDFLARE_ACCOUNT_ID = Deno.env.get(\"CLOUDFLARE_ACCOUNT_ID\");\n", "// @ts-expect-error <PERSON><PERSON> is not recognized\n", "const CLOUDFLARE_API_TOKEN = Deno.env.get(\"CLOUDFLARE_API_TOKEN\");"]}, {"cell_type": "code", "execution_count": 2, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import { CloudflareWorkersAI } from \"@langchain/cloudflare\";\n", "\n", "const llm = new CloudflareWorkersAI({\n", "  model: \"@cf/meta/llama-3.1-8b-instruct\", // Default value\n", "  cloudflareAccountId: CLOUDFLARE_ACCOUNT_ID,\n", "  cloudflareApiToken: CLOUDFLARE_API_TOKEN,\n", "  // Pass a custom base URL to use Cloudflare AI Gateway\n", "  // baseUrl: `https://gateway.ai.cloudflare.com/v1/{YOUR_ACCOUNT_ID}/{GATEWAY_NAME}/workers-ai/`,\n", "});"]}, {"cell_type": "markdown", "id": "0ee90032", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 3, "id": "035dea0f", "metadata": {"tags": []}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"Cloudflare is not an AI company, but rather a content delivery network (CDN) and security company. T\"\u001b[39m... 876 more characters"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["const inputText = \"Cloudflare is an AI company that \"\n", "\n", "const completion = await llm.invoke(inputText);\n", "completion"]}, {"cell_type": "markdown", "id": "add38532", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our completion model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 4, "id": "078e9db2", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"That's a simple but sweet statement! \\n\"\u001b[39m +\n", "  \u001b[32m\"\\n\"\u001b[39m +\n", "  \u001b[32m'To say \"I love programming\" in German, you can say: \"ICH LIEB'\u001b[39m... 366 more characters"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = PromptTemplate.fromTemplate(\"How to say {input} in {output_language}:\\n\")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "e9bdfcef", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `CloudflareWorkersAI` features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_cloudflare.CloudflareWorkersAI.html"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}