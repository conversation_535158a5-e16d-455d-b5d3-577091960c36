{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Cohere\n", "lc_docs_skip_validation: true\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# Cohere\n", "\n", "```{=mdx}\n", "\n", ":::warning Legacy\n", "\n", "Cohere has marked their `generate` endpoint for LLMs as deprecated. Follow their [migration guide](https://docs.cohere.com/docs/migrating-from-cogenerate-to-cochat) to start using their Chat API via the [`ChatCohere`](/docs/integrations/chat/cohere) integration.\n", "\n", ":::\n", "\n", ":::caution\n", "You are currently on a page documenting the use of Cohere models as [text completion models](/docs/concepts/text_llms). Many popular models available on Cohere are [chat completion models](/docs/concepts/chat_models).\n", "\n", "You may be looking for [this page instead](/docs/integrations/chat/cohere/).\n", ":::\n", "\n", "```\n", "\n", "This will help you get started with Cohere completion models (LLMs) using LangChain. For detailed documentation on `Cohere` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_cohere.Cohere.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/llms/cohere) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [Cohere](https://api.js.langchain.com/classes/langchain_cohere.Cohere.html) | [@langchain/cohere](https://api.js.langchain.com/modules/langchain_cohere.html) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/cohere?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/cohere?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Cohere models you'll need to create a Cohere account, get an API key, and install the `@langchain/cohere` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [cohere.com](https://cohere.com) to sign up to Cohere and generate an API key. Once you've done this set the `COHERE_API_KEY` environment variable:\n", "\n", "```bash\n", "export COHERE_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain Cohere integration lives in the `@langchain/cohere` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/cohere @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 2, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import { Cohere } from \"@langchain/cohere\"\n", "\n", "const llm = new Cohere({\n", "  model: \"command\",\n", "  temperature: 0,\n", "  maxTokens: undefined,\n", "  maxRetries: 2,\n", "  // other params...\n", "})"]}, {"cell_type": "markdown", "id": "2518004d", "metadata": {}, "source": ["### Custom client for Cohere on Azure, Cohere on AWS Bedrock, and Standalone Cohere Instance.\n", "\n", "We can instantiate a custom `CohereClient` and pass it to the ChatCohere constructor.\n", "\n", "**Note:** If a custom client is provided both `COHERE_API_KEY` environment variable and `apiKey` parameter in the constructor will be ignored."]}, {"cell_type": "code", "execution_count": null, "id": "79da9b26", "metadata": {}, "outputs": [], "source": ["import { Cohere } from \"@langchain/cohere\";\n", "import { CohereClient } from \"cohere-ai\";\n", "\n", "const client = new CohereClient({\n", "  token: \"<your-api-key>\",\n", "  environment: \"<your-cohere-deployment-url>\", //optional\n", "  // other params\n", "});\n", "\n", "const llmWithCustomClient = new Cohere({\n", "  client,\n", "  // other params...\n", "});"]}, {"cell_type": "markdown", "id": "0ee90032", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 3, "id": "035dea0f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cohere is a company that provides natural language processing models that help companies improve human-machine interactions. Cohere was founded in 2019 by <PERSON>, <PERSON>, and <PERSON>. \n"]}], "source": ["const inputText = \"Cohere is an AI company that \"\n", "\n", "const completion = await llm.invoke(inputText)\n", "completion"]}, {"cell_type": "markdown", "id": "add38532", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our completion model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 4, "id": "078e9db2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Ich liebe Programming.\n", "\n", "But for day to day purposes Ich mag Programming. would be enough and perfectly understood.\n", "\n", "I love programming is \"Ich liebe Programming\" and I like programming is \"Ich mag Programming\" respectively.\n", "\n", "There are also other ways to express this feeling, such as \"Ich habe Spaß mit Programming\", which means \"I enjoy programming\". But \"Ich mag\" and \"Ich liebe\" are the most common expressions for this.\n", "\n", "Let me know if I can be of further help with something else! \n"]}], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = new PromptTemplate({\n", "  template: \"How to say {input} in {output_language}:\\n\",\n", "  inputVariables: [\"input\", \"output_language\"],\n", "})\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "e9bdfcef", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all Cohere features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_cohere.Cohere.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}