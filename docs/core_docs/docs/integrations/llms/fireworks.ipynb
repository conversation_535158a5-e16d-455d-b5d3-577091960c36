{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {}, "source": ["---\n", "sidebar_label: Fireworks\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# Fireworks\n", "\n", "\n", "```{=mdx}\n", "\n", ":::caution\n", "You are currently on a page documenting the use of Fireworks models as [text completion models](/docs/concepts/text_llms). Many popular models available on Fireworks are [chat completion models](/docs/concepts/chat_models).\n", "\n", "You may be looking for [this page instead](/docs/integrations/chat/fireworks/).\n", ":::\n", "\n", "```\n", "\n", "[Fireworks AI](https://fireworks.ai/) is an AI inference platform to run and customize models. For a list of all models served by Fireworks see the [Fireworks docs](https://fireworks.ai/models).\n", "\n", "This will help you get started with Fireworks completion models (LLMs) using LangChain. For detailed documentation on `Fireworks` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_community_llms_fireworks.Fireworks.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/llms/fireworks) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [Fireworks](https://api.js.langchain.com/classes/langchain_community_llms_fireworks.Fireworks.html) | [@langchain/community](https://api.js.langchain.com/modules/langchain_community_llms_fireworks.html) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Fireworks models you'll need to create a Fireworks account, get an API key, and install the `@langchain/community` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [fireworks.ai](https://fireworks.ai/) to sign up to Fireworks and generate an API key. Once you've done this set the `FIREWORKS_API_KEY` environment variable:\n", "\n", "```bash\n", "export FIREWORKS_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain Fireworks integration lives in the `@langchain/community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import { Fireworks } from \"@langchain/community/llms/fireworks\"\n", "\n", "const llm = new Fireworks({\n", "  model: \"accounts/fireworks/models/llama-v3-70b-instruct\",\n", "  temperature: 0,\n", "  maxTokens: undefined,\n", "  timeout: undefined,\n", "  maxRetries: 2,\n", "  // other params...\n", "})"]}, {"cell_type": "markdown", "id": "0ee90032", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "id": "035dea0f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" helps businesses automate their customer support using AI-powered chatbots. We believe that AI can help businesses provide better customer support at a lower cost. Our chatbots are designed to be highly customizable and can be integrated with various platforms such as Facebook Messenger, Slack, and more.\n", "\n", "We are looking for a talented and motivated **Machine Learning Engineer** to join our team. As a Machine Learning Engineer at Fireworks, you will be responsible for developing and improving our AI models that power our chatbots. You will work closely with our data scientists, software engineers, and product managers to design, develop, and deploy AI models that can understand and respond to customer inquiries.\n", "\n", "**Responsibilities:**\n", "\n", "* Develop and improve AI models that can understand and respond to customer inquiries\n", "* Work with data scientists to design and develop new AI models\n", "* Collaborate with software engineers to integrate AI models with our chatbot platform\n", "* Work with product managers to understand customer requirements and develop AI models that meet those requirements\n", "* Develop and maintain data pipelines to support AI model development and deployment\n", "* Develop and maintain tools to monitor and evaluate AI model performance\n", "* Stay up-to-date with the latest developments in AI and machine learning and apply this knowledge to improve our AI models\n", "\n", "**Requirements:**\n", "\n", "* Bachelor's\n"]}], "source": ["const inputText = \"Fireworks is an AI company that \"\n", "\n", "const completion = await llm.invoke(inputText)\n", "completion"]}, {"cell_type": "markdown", "id": "add38532", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our completion model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 3, "id": "078e9db2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ich liebe Programmieren.\n", "\n", "How to say I love coding. in German:\n", "<PERSON>ch liebe Coden.\n", "\n", "How to say I love to code. in German:\n", "Ich liebe es zu coden.\n", "\n", "How to say I'm a programmer. in German:\n", "<PERSON>ch bin ein Programmierer.\n", "\n", "How to say I'm a coder. in German:\n", "<PERSON><PERSON> bin ein Coder.\n", "\n", "How to say I'm a developer. in German:\n", "<PERSON>ch bin ein Entwickler.\n", "\n", "How to say I'm a software engineer. in German:\n", "<PERSON>ch bin ein Software-Ingenieur.\n", "\n", "How to say I'm a tech enthusiast. in German:\n", "<PERSON><PERSON> bin ein Technik-Enthusiast.\n", "\n", "How to say I'm passionate about technology. in German:\n", "<PERSON>ch bin leidenschaftlich für Technologie.\n", "\n", "How to say I'm passionate about coding. in German:\n", "<PERSON>ch bin leidenschaftlich für Coden.\n", "\n", "How to say I'm passionate about programming. in German:\n", "Ich bin leidenschaftlich für Programmieren.\n", "\n", "How to say I enjoy coding. in German:\n", "Ich genieße Coden.\n", "\n", "How to say I enjoy programming. in German:\n", "Ich genieße Programmieren.\n", "\n", "How to say I'm good at coding. in German:\n", "<PERSON>ch bin gut im Coden.\n", "\n", "How to say I'm\n"]}], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = PromptTemplate.fromTemplate(\"How to say {input} in {output_language}:\\n\")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "4989353f", "metadata": {}, "source": ["Behind the scenes, Fireworks AI uses the OpenAI SDK and OpenAI compatible API, with some caveats:\n", "\n", "- Certain properties are not supported by the Fireworks API, see [here](https://readme.fireworks.ai/docs/openai-compatibility#api-compatibility).\n", "- Generation using multiple prompts is not supported.\n"]}, {"cell_type": "markdown", "id": "e9bdfcef", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all Fireworks features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_community_llms_fireworks.Fireworks.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}