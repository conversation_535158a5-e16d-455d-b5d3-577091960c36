{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Google Vertex AI\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# Google Vertex AI\n", "\n", "```{=mdx}\n", "\n", ":::caution\n", "You are currently on a page documenting the use of Google Vertex models as [text completion models](/docs/concepts/text_llms). Many popular models available on Google Vertex are [chat completion models](/docs/concepts/chat_models).\n", "\n", "You may be looking for [this page instead](/docs/integrations/chat/google_vertex_ai/).\n", ":::\n", "\n", "```\n", "\n", "[Google Vertex](https://cloud.google.com/vertex-ai) is a service that exposes all foundation models available in Google Cloud, like `gemini-1.5-pro`, `gemini-1.5-flash`, etc.\n", "\n", "This will help you get started with VertexAI completion models (LLMs) using LangChain. For detailed documentation on `VertexAI` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_google_vertexai.VertexAI.html).\n", "\n", "## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/llms/google_vertex_ai_palm) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [VertexAI](https://api.js.langchain.com/classes/langchain_google_vertexai.VertexAI.html) | [`@langchain/google-vertexai`](https://www.npmjs.com/package/@langchain/google-vertexai) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/google-vertexai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/google-vertexai?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "LangChain.js supports two different authentication methods based on whether\n", "you're running in a Node.js environment or a web environment.\n", "\n", "To access VertexAI models you'll need to create a Google Cloud Platform (GCP) account, get an API key, and install the `@langchain/google-vertexai` integration package.\n", "\n", "### Credentials\n", "\n", "#### Node.js\n", "\n", "You should make sure the Vertex AI API is\n", "enabled for the relevant project and that you've authenticated to\n", "Google Cloud using one of these methods:\n", "\n", "- You are logged into an account (using `gcloud auth application-default login`)\n", "  permitted to that project.\n", "- You are running on a machine using a service account that is permitted\n", "  to the project.\n", "- You have downloaded the credentials for a service account that is permitted\n", "  to the project and set the `GOOGLE_APPLICATION_CREDENTIALS` environment\n", "  variable to the path of this file.\n", "  **or**\n", "- You set the `GOOGLE_API_KEY` environment variable to the API key for the project.\n", "\n", "#### Web\n", "\n", "To call Vertex AI models in web environments (like Edge functions), you'll need to install\n", "the `@langchain/google-vertexai-web` package.\n", "\n", "Then, you'll need to add your service account credentials directly as a `GOOGLE_VERTEX_AI_WEB_CREDENTIALS` environment variable:\n", "\n", "```\n", "GOOGLE_VERTEX_AI_WEB_CREDENTIALS={\"type\":\"service_account\",\"project_id\":\"YOUR_PROJECT-12345\",...}\n", "```\n", "\n", "You can also pass your credentials directly in code like this:\n", "\n", "```typescript\n", "import { VertexAI } from \"@langchain/google-vertexai\";\n", "// Or uncomment this line if you're using the web version:\n", "// import { VertexAI } from \"@langchain/google-vertexai-web\";\n", "\n", "const model = new VertexAI({\n", "  authOptions: {\n", "    credentials: {\"type\":\"service_account\",\"project_id\":\"YOUR_PROJECT-12345\",...},\n", "  },\n", "});\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain VertexAI integration lives in the `@langchain/google-vertexai` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/google-vertexai @langchain/core\n", "</Npm2Yarn>\n", "\n", "or for web environments:\n", "\n", "<Npm2Yarn>\n", "  @langchain/google-vertexai-web @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 2, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import { VertexAI } from \"@langchain/google-vertexai-web\"\n", "\n", "const llm = new VertexAI({\n", "  model: \"gemini-pro\",\n", "  temperature: 0,\n", "  maxRetries: 2,\n", "  // other params...\n", "})"]}, {"cell_type": "markdown", "id": "0ee90032", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": null, "id": "035dea0f", "metadata": {"tags": []}, "outputs": [], "source": ["const inputText = \"VertexAI is an AI company that \"\n", "\n", "const completion = await llm.invoke(inputText)\n", "completion"]}, {"cell_type": "markdown", "id": "f580765e", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["```txt\n", "offers a wide range of cloud computing services and artificial intelligence solutions to businesses and developers worldwide.\n", "```"]}, {"cell_type": "markdown", "id": "add38532", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our completion model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": null, "id": "078e9db2", "metadata": {}, "outputs": [], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = PromptTemplate.fromTemplate(\"How to say {input} in {output_language}:\\n\")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "4d106b41", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["```txt\n", "\"Ich liebe Programmieren.\"\n", "Pronunciation guide:\n", "\n", "Ich: [ɪç] (similar to \"ikh\" with a soft \"ch\" sound)\n", "liebe: [ˈliːbə] (LEE-buh)\n", "Programmieren: [pʁoɡʁaˈmiːʁən] (pro-gra-MEE-ren)\n", "\n", "You could also say:\n", "\"Ich liebe es zu programmieren.\"\n", "Which translates more literally to \"I love to program.\" This version is a bit more formal or precise.\n", "Pronunciation:\n", "\n", "es: [ɛs] (like the letter \"S\")\n", "zu: [tsuː] (tsoo)\n", "\n", "Both versions are correct and commonly used.\n", "```"]}, {"cell_type": "markdown", "id": "e9bdfcef", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all VertexAI features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_google_vertexai.VertexAI.html"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}