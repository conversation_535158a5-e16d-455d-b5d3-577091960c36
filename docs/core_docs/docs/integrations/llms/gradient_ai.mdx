---
sidebar_class_name: node-only
---

# Gradient AI

LangChain.js supports integration with Gradient AI. Check out [Gradient AI](https://docs.gradient.ai/docs) for a list of available models.

## Setup

You'll need to install the official Gradient Node SDK as a peer dependency:

```bash npm2yarn
npm i @gradientai/nodejs-sdk
```

You will need to set the following environment variables for using the Gradient AI API.

1. `GRADIENT_ACCESS_TOKEN`
2. `GRADIENT_WORKSPACE_ID`

Alternatively, these can be set during the GradientAI Class instantiation as `gradientAccessKey` and `workspaceId` respectively.
For example:

```typescript
const model = new GradientLLM({
  gradientAccessKey: "My secret Access Token"
  workspaceId: "My secret workspace id"
});
```

## Usage

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

import CodeBlock from "@theme/CodeBlock";
import GradientLLMBaseExample from "@examples/llms/gradient_ai-base.ts";
import GradientLLMAdapterExample from "@examples/llms/gradient_ai-adapter.ts";

### Using Gradient's Base Models

<CodeBlock language="typescript">{GradientLLMBaseExample}</CodeBlock>

### Using your own fine-tuned Adapters

The use your own custom adapter simply set `adapterId` during setup.

<CodeBlock language="typescript">{GradientLLMAdapterExample}</CodeBlock>

## Related

- LLM [conceptual guide](/docs/concepts/text_llms)
- LLM [how-to guides](/docs/how_to/#llms)
