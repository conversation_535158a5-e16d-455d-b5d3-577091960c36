# HuggingFaceInference

Here's an example of calling a HugggingFaceInference model as an LLM:

```bash npm2yarn
npm install @langchain/community @langchain/core @huggingface/inference@2
```

import UnifiedModelParamsTooltip from "@mdx_components/unified_model_params_tooltip.mdx";

<UnifiedModelParamsTooltip></UnifiedModelParamsTooltip>

```typescript
import { HuggingFaceInference } from "@langchain/community/llms/hf";

const model = new HuggingFaceInference({
  model: "gpt2",
  apiKey: "YOUR-API-KEY", // In Node.js defaults to process.env.HUGGINGFACEHUB_API_KEY
});
const res = await model.invoke("1 + 1 =");
console.log({ res });
```

## Related

- LLM [conceptual guide](/docs/concepts/text_llms)
- LLM [how-to guides](/docs/how_to/#llms)
