---
sidebar_position: 0
sidebar_class_name: hidden
---

# LLMs

:::caution
You are currently on a page documenting the use of [text completion models](/docs/concepts/text_llms). Many of the latest and most popular models are [chat completion models](/docs/concepts/chat_models).

Unless you are specifically using more advanced prompting techniques, you are probably looking for [this page instead](/docs/integrations/chat/).
:::

[LLMs](docs/concepts/#llms) are language models that takes a string as input and return a string as output.

:::info
If you'd like to write your own LLM, see [this how-to](/docs/how_to/custom_llm). If you'd like to contribute an integration, see [Contributing integrations](/docs/contributing).
:::

## All LLMs

import { IndexTable } from "@theme/FeatureTables";

<IndexTable />
