---
sidebar_class_name: hidden
---

# NIBittensor

:::warning
This module has been deprecated and is no longer supported. The documentation below will not work in versions 0.2.0 or later.
:::

LangChain.js offers experimental support for Neural Internet's Bittensor LLM models.

Here's an example:

```typescript
import { NIBittensorLLM } from "langchain/experimental/llms/bittensor";

const model = new NIBittensorLLM();

const res = await model.invoke(`What is Bittensor?`);

console.log({ res });

/*
  {
    res: "\nBittensor is opensource protocol..."
  }
 */
```

## Related

- LLM [conceptual guide](/docs/concepts/text_llms)
- LLM [how-to guides](/docs/how_to/#llms)
