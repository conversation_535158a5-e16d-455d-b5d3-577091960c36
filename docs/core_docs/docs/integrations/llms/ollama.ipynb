{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Ollama\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# Ollama\n", "\n", "```{=mdx}\n", "\n", ":::caution\n", "You are currently on a page documenting the use of Ollama models as [text completion models](/docs/concepts/text_llms). Many popular models available on Ollama are [chat completion models](/docs/concepts/chat_models).\n", "\n", "You may be looking for [this page instead](/docs/integrations/chat/ollama/).\n", ":::\n", "\n", "```\n", "\n", "This will help you get started with Ollama [text completion models (LLMs)](/docs/concepts/text_llms) using LangChain. For detailed documentation on `Ollama` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_ollama.Ollama.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "[<PERSON><PERSON><PERSON>](https://ollama.ai/) allows you to run open-source large language models, such as Llama 3, locally.\n", "\n", "Ollama bundles model weights, configuration, and data into a single package, defined by a Modelfile. It optimizes setup and configuration details, including GPU usage.\n", "\n", "This example goes over how to use LangChain to interact with an Ollama-run Llama 2 7b instance.\n", "For a complete list of supported models and model variants, see the [Ollama model library](https://github.com/jmorganca/ollama#model-library).\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/llms/ollama/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [`Ollama`](https://api.js.langchain.com/classes/langchain_ollama.Ollama.html) | [`@langchain/ollama`](https://npmjs.com/@langchain/ollama) | ✅ | ❌ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/ollama?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/ollama?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Ollama embedding models you'll need to follow [these instructions](https://github.com/jmorganca/ollama) to install Ollama, and install the `@langchain/ollama` integration package.\n", "\n", "### Credentials\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain Ollama integration lives in the `@langchain/ollama` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/ollama @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 3, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import { Ollama } from \"@langchain/ollama\"\n", "\n", "const llm = new Ollama({\n", "  model: \"llama3\", // Default value\n", "  temperature: 0,\n", "  maxRetries: 2,\n", "  // other params...\n", "})"]}, {"cell_type": "markdown", "id": "0ee90032", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 4, "id": "035dea0f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I think you meant to say \"<PERSON>\" instead of \"<PERSON>lla<PERSON>\". Olivia is not a well-known AI company, but there are several other AI companies with similar names. Here are a few examples:\n", "\n", "* Oliva AI: A startup that uses artificial intelligence to help businesses optimize their operations and improve customer experiences.\n", "* Olivia Technologies: A company that develops AI-powered solutions for industries such as healthcare, finance, and education.\n", "* Olivia.ai: A platform that uses AI to help businesses automate their workflows and improve productivity.\n", "\n", "If you meant something else by \"<PERSON><PERSON><PERSON>\", please let me know and I'll do my best to help!\n"]}], "source": ["const inputText = \"Ollama is an AI company that \"\n", "\n", "const completion = await llm.invoke(inputText)\n", "completion"]}, {"cell_type": "markdown", "id": "add38532", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our completion model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 5, "id": "078e9db2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["A programmer's passion!\n", "\n", "In German, you can express your love for programming with the following phrases:\n", "\n", "1. <PERSON>ch liebe Programmieren: This is a direct translation of \"I love programming.\"\n", "2. <PERSON><PERSON>ren ist meine Leidenschaft: This means \"Programming is my passion.\"\n", "3. Ich bin total verliebt in Programmieren: This translates to \"I'm totally in love with programming.\"\n", "4. <PERSON><PERSON><PERSON> macht mich glücklich: This phrase means \"Programming makes me happy\" or \"I'm joyful when programming.\"\n", "\n", "If you want to be more casual, you can use:\n", "\n", "1. <PERSON><PERSON> bin ein Programmier-Fan: This is a playful way to say \"I'm a fan of programming.\"\n", "2. <PERSON><PERSON><PERSON> ist mein Ding: This translates to \"Programming is my thing\" or \"I'm all about programming.\"\n", "\n", "Remember that German has different forms for formal and informal speech, so adjust the phrases according to your relationship with the person you're speaking to!\n"]}], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = PromptTemplate.fromTemplate(\"How to say {input} in {output_language}:\\n\")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "e99eef30", "metadata": {}, "source": ["## Multimodal models\n", "\n", "Ollama supports open source multimodal models like [LLaVA](https://ollama.ai/library/llava) in versions 0.1.15 and up.\n", "You can bind base64 encoded image data to multimodal-capable models to use as context like this:"]}, {"cell_type": "code", "execution_count": 7, "id": "1ff218e2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" The image shows a hot dog placed inside what appears to be a bun that has been specially prepared to resemble a hot dog bun. This is an example of a creative or novelty food item, where the bread used for the bun looks similar to a cooked hot dog itself, playing on the name \"hot dog.\" The image also shows the typical garnishes like ketchup and mustard on the side. \n"]}], "source": ["import { Ollama } from \"@langchain/ollama\";\n", "import * as fs from \"node:fs/promises\";\n", "\n", "const imageData = await fs.readFile(\"../../../../../examples/hotdog.jpg\");\n", "\n", "const model = new Ollama({\n", "  model: \"llava\",\n", "}).bind({\n", "  images: [imageData.toString(\"base64\")],\n", "});\n", "\n", "const res = await model.invoke(\"What's in this image?\");\n", "console.log(res);"]}, {"cell_type": "markdown", "id": "cac0a2dd", "metadata": {}, "source": ["## Related\n", "\n", "- LLM [conceptual guide](/docs/concepts/text_llms)\n", "- LLM [how-to guides](/docs/how_to/#llms)"]}, {"cell_type": "markdown", "id": "e9bdfcef", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `Ollama` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_ollama.Ollama.html)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}