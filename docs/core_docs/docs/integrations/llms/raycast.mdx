# RaycastAI

> **Note:** This is a community-built integration and is not officially supported by Raycast.

You can utilize the LangChain's RaycastAI class within the [Raycast Environment](https://developers.raycast.com/api-reference/ai) to enhance your Raycast extension with Langchain's capabilities.

- The RaycastAI class is only available in the Raycast environment and only to [Raycast Pro](https://www.raycast.com/pro) users as of August 2023. You may check how to create an extension for Raycast [here](https://developers.raycast.com/).

- There is a rate limit of approx 10 requests per minute for each Raycast Pro user. If you exceed this limit, you will receive an error. You can set your desired rpm limit by passing `rateLimitPerMinute` to the `RaycastAI` constructor as shown in the example, as this rate limit may change in the future.

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

import CodeBlock from "@theme/CodeBlock";

```ts
import { RaycastAI } from "@langchain/community/llms/raycast";

import { Tool } from "@langchain/core/tools";

const model = new RaycastAI({
  rateLimitPerMinute: 10, // It is 10 by default so you can omit this line
  model: "<model_name>",
  creativity: 0, // `creativity` is a term used by Raycast which is equivalent to `temperature` in some other LLMs
});
```

## Related

- LLM [conceptual guide](/docs/concepts/text_llms)
- LLM [how-to guides](/docs/how_to/#llms)
