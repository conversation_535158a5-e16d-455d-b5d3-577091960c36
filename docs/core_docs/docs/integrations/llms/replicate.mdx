import CodeBlock from "@theme/CodeBlock";

# Replicate

Here's an example of calling a Replicate model as an LLM:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install replicate@1 @langchain/community @langchain/core
```

import ReplicateLlama2 from "@examples/models/llm/replicate_llama2.ts";

<CodeBlock language="typescript">{ReplicateLlama2}</CodeBlock>

You can run other models through Replicate by changing the `model` parameter.

You can find a full list of models on [Replicate's website](https://replicate.com/explore).

## Related

- LLM [conceptual guide](/docs/concepts/text_llms)
- LLM [how-to guides](/docs/how_to/#llms)
