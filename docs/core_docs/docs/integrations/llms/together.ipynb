{"cells": [{"cell_type": "raw", "id": "67db2992", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Together AI\n", "---"]}, {"cell_type": "markdown", "id": "9597802c", "metadata": {}, "source": ["# TogetherAI\n", "\n", ":::caution\n", "You are currently on a page documenting the use of Together AI models as [text completion models](/docs/concepts/text_llms). Many popular models available on Together AI are [chat completion models](/docs/concepts/chat_models).\n", "\n", "You may be looking for [this page instead](/docs/integrations/chat/togetherai/).\n", ":::\n", "\n", "[Together AI](https://www.together.ai/) offers an API to query [50+ leading open-source models](https://docs.together.ai/docs/inference-models) in a couple lines of code.\n", "\n", "This will help you get started with Together AI [text completion models (LLMs)](/docs/concepts/text_llms) using LangChain. For detailed documentation on `TogetherAI` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_community_llms_togetherai.TogetherAI.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Serializable | [PY support](https://python.langchain.com/docs/integrations/llms/together/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: | :---: |\n", "| [`TogetherAI`](https://api.js.langchain.com/classes/langchain_community_llms_togetherai.TogetherAI.html) | [`@langchain/community`](https://npmjs.com/@langchain/community) | ❌ | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access `ChatTogetherAI` models you'll need to create a Together account, get an API key [here](https://api.together.xyz/), and install the `@langchain/community` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [api.together.ai](https://api.together.ai/) to sign up to TogetherAI and generate an API key. Once you've done this set the `TOGETHER_AI_API_KEY` environment variable:\n", "\n", "```bash\n", "export TOGETHER_AI_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain TogetherAI integration lives in the `@langchain/community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "id": "0a760037", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "a0562a13", "metadata": {}, "outputs": [], "source": ["import { TogetherAI } from \"@langchain/community/llms/togetherai\";\n", "\n", "const llm = new TogetherAI({\n", "  model: \"meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo\",\n", "  maxTokens: 256,\n", "});"]}, {"cell_type": "markdown", "id": "0ee90032", "metadata": {}, "source": ["## Invocation"]}, {"cell_type": "code", "execution_count": 2, "id": "035dea0f", "metadata": {"tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" offers a range of AI-powered solutions to help businesses and organizations improve their customer service, sales, and marketing efforts. Their platform uses natural language processing (NLP) and machine learning algorithms to analyze customer interactions and provide insights and recommendations to help businesses improve their customer experience.\n", "Together's solutions include:\n", "1. Customer Service: Together's customer service solution uses AI to analyze customer interactions and provide insights and recommendations to help businesses improve their customer experience. This includes analyzing customer feedback, sentiment analysis, and predictive analytics to identify areas for improvement.\n", "2. Sales: Together's sales solution uses AI to analyze customer interactions and provide insights and recommendations to help businesses improve their sales efforts. This includes analyzing customer behavior, sentiment analysis, and predictive analytics to identify opportunities for upselling and cross-selling.\n", "3. Marketing: Together's marketing solution uses AI to analyze customer interactions and provide insights and recommendations to help businesses improve their marketing efforts. This includes analyzing customer behavior, sentiment analysis, and predictive analytics to identify areas for improvement.\n", "Together's platform is designed to be easy to use and integrates with a range of popular CRM and marketing automation tools. Their solutions are available as a cloud-based subscription service, making it easy for businesses to get started with AI-powered customer service, sales, and marketing.\n", "Overall,\n"]}], "source": ["const inputText = \"Together is an AI company that \"\n", "\n", "const completion = await llm.invoke(inputText)\n", "completion"]}, {"cell_type": "markdown", "id": "add38532", "metadata": {}, "source": ["## Chaining\n", "\n", "We can [chain](/docs/how_to/sequence/) our completion model with a prompt template like so:"]}, {"cell_type": "code", "execution_count": 3, "id": "078e9db2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ich liebe Programmieren.\n", "\n", "How to say I love programming. in French:\n", "J'adore programmer.\n", "\n", "How to say I love programming. in Spanish:\n", "Me encanta programar.\n", "\n", "How to say I love programming. in Italian:\n", "Mi piace programmare.\n", "\n", "How to say I love programming. in Portuguese:\n", "Eu amo programar.\n", "\n", "How to say I love programming. in Russian:\n", "Я люблю программирование.\n", "\n", "How to say I love programming. in Japanese:\n", "私はプログラミングが好きです。\n", "\n", "How to say I love programming. in Chinese:\n", "我喜欢编程。\n", "\n", "How to say I love programming. in Korean:\n", "나는 프로그래밍을 좋아합니다.\n", "\n", "How to say I love programming. in Arabic:\n", "أنا أحب البرمجة.\n", "\n", "How to say I love programming. in Hebrew:\n", "אני אוהבת לתכנת.\n", "\n", "How to say I love programming. in Hindi:\n", "\n", "मुझे प्रोग्रामिंग पसंद है।\n", "\n", "\n", "\n", "I hope this helps you express your love for programming in different languages!\n"]}], "source": ["import { PromptTemplate } from \"@langchain/core/prompts\"\n", "\n", "const prompt = PromptTemplate.fromTemplate(\"How to say {input} in {output_language}:\\n\")\n", "\n", "const chain = prompt.pipe(llm);\n", "await chain.invoke(\n", "  {\n", "    output_language: \"German\",\n", "    input: \"I love programming.\",\n", "  }\n", ")"]}, {"cell_type": "markdown", "id": "e9bdfcef", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `TogetherAi` features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_community_llms_togetherai.TogetherAI.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}, "vscode": {"interpreter": {"hash": "e971737741ff4ec9aff7dc6155a1060a59a8a6d52c757dbbe66bf8ee389494b1"}}}, "nbformat": 4, "nbformat_minor": 5}