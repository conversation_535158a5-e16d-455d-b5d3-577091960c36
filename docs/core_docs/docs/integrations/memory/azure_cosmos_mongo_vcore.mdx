---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Azure Cosmos DB Mongo vCore Chat Message History

The AzureCosmosDBMongoChatMessageHistory uses Azure Cosmos DB Mongo vCore to store chat message history. For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory`.
If you don't have an Azure account, you can [create a free account](https://azure.microsoft.com/free/) to get started.

## Setup

You'll first need to install the [`@langchain/azure-cosmosdb`](https://www.npmjs.com/package/@langchain/azure-cosmosdb) package:

```bash npm2yarn
npm install @langchain/azure-cosmosdb @langchain/core
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

You'll also need to have an Azure Cosmos DB mongo vCore instance running. You can deploy a free version on Azure Portal without any cost, following [this guide](https://learn.microsoft.com/en-us/azure/cosmos-db/mongodb/vcore/quickstart-portal).

Once you have your instance running, make sure you have the connection string.

## Usage

import Example from "@examples/memory/azure_cosmosdb_mongo.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
