---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Cassandra Chat Memory

For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory` for a Cassandra cluster.

## Setup

First, install the Cassandra Node.js driver:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install cassandra-driver @langchain/openai @langchain/community @langchain/core
```

Depending on your database providers, the specifics of how to connect to the database will vary. We will create a document `configConnection` which will be used as part of the vector store configuration.

### Apache Cassandra®

```typescript
const configConnection = {
  contactPoints: ['h1', 'h2'],
  localDataCenter: 'datacenter1',
  credentials: {
    username: <...> as string,
    password: <...> as string,
  },
};
```

### Astra DB

Astra DB is a cloud-native Cassandra-as-a-Service platform.

1. Create an [Astra DB account](https://astra.datastax.com/register).
2. Create a [vector enabled database](https://astra.datastax.com/createDatabase).
3. Create a [token](https://docs.datastax.com/en/astra/docs/manage-application-tokens.html) for your database.

```typescript
const configConnection = {
  serviceProviderArgs: {
    astra: {
      token: <...> as string,
      endpoint: <...> as string,
    },
  },
};
```

Instead of `endpoint:`, you many provide property `datacenterID:` and optionally `regionName:`.

## Usage

import Example from "@examples/memory/cassandra-store.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
