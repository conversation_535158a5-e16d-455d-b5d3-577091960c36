---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# DynamoDB-Backed Chat Memory

For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory` for a DynamoDB instance.

## Setup

First, install the AWS DynamoDB client in your project:

```bash npm2yarn
npm install @aws-sdk/client-dynamodb
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

Next, sign into your AWS account and create a DynamoDB table. Name the table `langchain`, and name your partition key `id`. Make sure your partition key is a string. You can leave sort key and the other settings alone.

You'll also need to retrieve an AWS access key and secret key for a role or user that has access to the table and add them to your environment variables.

## Usage

import Example from "@examples/memory/dynamodb-store.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
