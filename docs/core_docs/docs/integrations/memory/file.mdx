---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# File System Chat Message History

The `FileSystemChatMessageHistory` uses a JSON file to store chat message history. For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory`.

## Setup

You'll first need to install the [`@langchain/community`](https://www.npmjs.com/package/@langchain/community) package:

```bash npm2yarn
npm install @langchain/community @langchain/core
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

## Usage

import Example from "@examples/memory/file.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
