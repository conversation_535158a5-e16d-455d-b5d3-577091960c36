# Google Cloud SQL for PostgreSQL

[Cloud SQL](https://cloud.google.com/sql) is a fully managed relational database service that offers high
performance, seamless integration, and impressive scalability and offers database engines such as PostgreSQL.

This guide provides a quick overview of how to use Cloud SQL for PostgreSQL to store messages and provide
conversation with the PostgresChatMessageHistory class.

## Overview

### Before you begin

In order to use this package, you first need to go through the following steps:

1.  [Select or create a Cloud Platform project.](https://developers.google.com/workspace/guides/create-project)
2.  [Enable billing for your project.](https://cloud.google.com/billing/docs/how-to/modify-project#enable_billing_for_a_project)
3.  [Enable the Cloud SQL Admin API.](https://console.cloud.google.com/flows/enableapi?apiid=sqladmin.googleapis.com)
4.  [Setup Authentication.](https://cloud.google.com/docs/authentication)
5.  [Create a CloudSQL instance](https://cloud.google.com/sql/docs/postgres/connect-instance-auth-proxy#create-instance)
6.  [Create a CloudSQL database](https://cloud.google.com/sql/docs/postgres/create-manage-databases)
7.  [Add a user to the database](https://cloud.google.com/sql/docs/postgres/create-manage-users)

### Authentication

Authenticate locally to your Google Cloud account using the `gcloud auth login` command.

### Set Your Google Cloud Project

Set your Google Cloud project ID to leverage Google Cloud resources locally:

```bash
gcloud config set project YOUR-PROJECT-ID
```

If you don't know your project ID, try the following:

- Run `gcloud config list`.
- Run `gcloud projects list`.
- See the support page: [Locate the project ID](https://support.google.com/googleapi/answer/7014113).

## Setting up a PostgresChatMessageHistory instance

To use the PostgresChatMessageHistory class, you'll need to install the [`@langchain/google-cloud-sql-pg`](https://www.npmjs.com/package/@langchain/google-cloud-sql-pg)
package and then follow the steps bellow.

First, you'll need to log in to your Google Cloud account and set the following environment variables based
on your Google Cloud project; these will be defined based on how you want to configure (fromInstance,
fromEngine, fromEngineArgs) your PostgresEngine instance:

```bash
PROJECT_ID="your-project-id"
REGION="your-project-region" // example: "us-central1"
INSTANCE_NAME="your-instance"
DB_NAME="your-database-name"
DB_USER="your-database-user"
PASSWORD="your-database-password"
```

### Setting up an instance

To instantiate a PostgresChatMessageHistory, you'll first need to create a database connection through the
PostgresEngine, then initialize the chat history table and finally call the `.initialize()` method to instantiate
the chat message history.

```typescript
import {
  PostgresChatMessageHistory,
  PostgresEngine,
  PostgresEngineArgs,
} from "@langchain/google-cloud-sql-pg";
import * as dotenv from "dotenv";

dotenv.config();

const peArgs: PostgresEngineArgs = {
  user: process.env.DB_USER ?? "",
  password: process.env.PASSWORD ?? "",
};

// PostgresEngine instantiation
const engine: PostgresEngine = await PostgresEngine.fromInstance(
  process.env.PROJECT_ID ?? "",
  process.env.REGION ?? "",
  process.env.INSTANCE_NAME ?? "",
  process.env.DB_NAME ?? "",
  peArgs
);

// Chat history table initialization
await engine.initChatHistoryTable("my_chat_history_table");

// PostgresChatMessageHistory instantiation
const historyInstance: PostgresChatMessageHistory =
  await PostgresChatMessageHistory.initialize(
    engine,
    "test",
    "my_chat_history_table"
  );
```

## Manage Chat Message History

### Add Messages to the chat history

You can add a message to the chat history by using the addMessage method or you can use the addMessages method
to pass an array of messages.

```typescript
import { AIMessage, BaseMessage, HumanMessage } from "@langchain/core/messages";

// Add one message
const msg = new HumanMessage("Hi!");
await historyInstance.addMessage(msg);

// Add an array of messages
const msg1: HumanMessage = new HumanMessage("Hi!");
const msg2: AIMessage = new AIMessage("what's up?");
const msg3: HumanMessage = new HumanMessage("How are you?");
const messages: BaseMessage[] = [msg1, msg2, msg3];
await historyInstance.addMessages(messages);
```

### Get Messages saved on the chat history

```typescript
const messagesSaved: BaseMessage[] = await historyInstance.getMessages();
console.log(messagesSaved);
```

### Clear Messages from the chat history

To remove all messages from the chat history, just call the clear method from the PostgresChatMessageHistory class.

```typescript
await historyInstance.clear();
```
