---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# IPFS Datastore Chat Memory

For a storage backend you can use the IPFS Datastore Chat Memory to wrap an IPFS Datastore allowing you to use any IPFS compatible datastore.

## Setup

First, install the integration dependencies:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install cborg interface-datastore it-all @langchain/community @langchain/core
```

Now you can install and use an IPFS Datastore of your choice. Here are some options:

- [datastore-core](https://github.com/ipfs/js-stores/blob/main/packages/datastore-core) Datastore in-memory implementation.
- [datastore-fs](https://github.com/ipfs/js-stores/blob/main/packages/datastore-fs) Datastore implementation with file system backend.
- [datastore-idb](https://github.com/ipfs/js-stores/blob/main/packages/datastore-idb) Datastore implementation with IndexedDB backend.
- [datastore-level](https://github.com/ipfs/js-stores/blob/main/packages/datastore-level) Datastore implementation with level(up|down) backend
- [datastore-s3](https://github.com/ipfs/js-stores/blob/main/packages/datastore-s3) Datastore implementation backed by s3.

## Usage

```typescript
// Replace FsDatastore with the IPFS Datastore of your choice.
import { FsDatastore } from "datastore-fs";
import { IPFSDatastoreChatMessageHistory } from "@langchain/community/stores/message/ipfs_datastore";

const datastore = new FsDatastore("path/to/store");
const sessionId = "my-session";

const history = new IPFSDatastoreChatMessageHistory({ datastore, sessionId });
```
