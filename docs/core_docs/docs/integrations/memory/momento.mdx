---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Momento-Backed Chat Memory

For distributed, serverless persistence across chat sessions, you can swap in a [Momento](https://gomomento.com/)-backed chat message history.
Because a Momento cache is instantly available and requires zero infrastructure maintenance, it's a great way to get started with chat history whether building locally or in production.

## Setup

You will need to install the [Momento Client Library](https://github.com/momentohq/client-sdk-javascript) in your project. Given Momento's compatibility with Node.js, browser, and edge environments, ensure you install the relevant package.

To install for **Node.js**:

```bash npm2yarn
npm install @gomomento/sdk
```

To install for **browser/edge workers**:

```bash npm2yarn
npm install @gomomento/sdk-web
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

You will also need an API key from [Momento](https://gomomento.com/). You can sign up for a free account [here](https://console.gomomento.com/).

## Usage

To distinguish one chat history session from another, we need a unique `sessionId`. You may also provide an optional `sessionTtl` to make sessions expire after a given number of seconds.

import MomentoExample from "@examples/memory/momento.ts";

<CodeBlock language="typescript">{MomentoExample}</CodeBlock>
