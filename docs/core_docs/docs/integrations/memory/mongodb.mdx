---
hide_table_of_contents: true
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# MongoDB Chat Memory

:::tip Compatibility
Only available on Node.js.

You can still create API routes that use MongoDB with Next.js by setting the `runtime` variable to `nodejs` like so:

```typescript
export const runtime = "nodejs";
```

You can read more about Edge runtimes in the Next.js documentation [here](https://nextjs.org/docs/app/building-your-application/rendering/edge-and-nodejs-runtimes).
:::

For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory` for a MongoDB instance.

## Setup

You need to install Node MongoDB SDK in your project:

```bash npm2yarn
npm install -S mongodb
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

You will also need a MongoDB instance to connect to.

## Usage

Each chat history session stored in MongoDB must have a unique session id.

import Example from "@examples/memory/mongodb.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
