---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Motörhead Memory

[<PERSON><PERSON><PERSON><PERSON><PERSON>](https://github.com/getmetal/motorhead) is a memory server implemented in Rust. It automatically handles incremental summarization in the background and allows for stateless applications.

## Setup

See instructions at [<PERSON><PERSON><PERSON><PERSON><PERSON>](https://github.com/getmetal/motorhead) for running the server locally, or https://getmetal.io to get API keys for the hosted version.

## Usage

import Example from "@examples/memory/motorhead.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{Example}</CodeBlock>
