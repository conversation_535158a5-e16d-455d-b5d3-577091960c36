---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# PlanetScale Chat Memory

Because PlanetScale works via a REST API, you can use this with [Vercel Edge](https://vercel.com/docs/concepts/functions/edge-functions/edge-runtime), [Cloudflare Workers](https://developers.cloudflare.com/workers/) and other Serverless environments.

For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory` for an PlanetScale [Database](https://planetscale.com/) instance.

## Setup

You will need to install [@planetscale/database](https://github.com/planetscale/database-js) in your project:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @planetscale/database @langchain/community @langchain/core
```

You will also need an PlanetScale Account and a database to connect to. See instructions on [PlanetScale Docs](https://planetscale.com/docs) on how to create a HTTP client.

## Usage

Each chat history session stored in PlanetScale database must have a unique id.
The `config` parameter is passed directly into the `new Client()` constructor of [@planetscale/database](https://planetscale.com/docs/tutorials/planetscale-serverless-driver), and takes all the same arguments.

import Example from "@examples/memory/planetscale.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Advanced Usage

You can also directly pass in a previously created [@planetscale/database](https://planetscale.com/docs/tutorials/planetscale-serverless-driver) client instance:

import AdvancedExample from "@examples/memory/planetscale_advanced.ts";

<CodeBlock language="typescript">{AdvancedExample}</CodeBlock>
