---
hide_table_of_contents: true
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# Postgres Chat Memory

For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` for a [Postgres](https://www.postgresql.org/) Database.

## Setup

First install the [node-postgres](https://node-postgres.com/) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core pg
```

## Usage

Each chat history session is stored in a Postgres database and requires a session id.

The connection to postgres is handled through a pool. You can either pass an instance of a pool via the `pool` parameter or pass a pool config via the `poolConfig` parameter. See [pg-node docs on pools](https://node-postgres.com/apis/pool)
for more information. A provided pool takes precedence, thus if both a pool instance and a pool config are passed, only the pool will be used.

import Example from "@examples/memory/postgres.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
