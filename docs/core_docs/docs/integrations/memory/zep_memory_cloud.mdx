---
hide_table_of_contents: true
---

# Zep Cloud Memory

> Recall, understand, and extract data from chat histories. Power personalized AI experiences.

> [Zep](https://www.getzep.com) is a long-term memory service for AI Assistant apps.
> With Zep, you can provide AI assistants with the ability to recall past conversations, no matter how distant,
> while also reducing hallucinations, latency, and cost.

## How Zep Cloud works

Zep persists and recalls chat histories, and automatically generates summaries and other artifacts from these chat histories.
It also embeds messages and summaries, enabling you to search Zep for relevant context from past conversations.
Zep does all of this asynchronously, ensuring these operations don't impact your user's chat experience.
Data is persisted to database, allowing you to scale out when growth demands.

Zep also provides a simple, easy to use abstraction for document vector search called Document Collections.
This is designed to complement Zep's core memory features, but is not designed to be a general purpose vector database.

Zep allows you to be more intentional about constructing your prompt:

- automatically adding a few recent messages, with the number customized for your app;
- a summary of recent conversations prior to the messages above;
- and/or contextually relevant summaries or messages surfaced from the entire chat session.
- and/or relevant Business data from Zep Document Collections.

Zep Cloud offers:

- **Fact Extraction**: Automatically build fact tables from conversations, without having to define a data schema upfront.
- **Dialog Classification**: Instantly and accurately classify chat dialog. Understand user intent and emotion, segment users, and more. Route chains based on semantic context, and trigger events.
- **Structured Data Extraction**: Quickly extract business data from chat conversations using a schema you define. Understand what your Assistant should ask for next in order to complete its task.

## Installation

Sign up for [Zep Cloud](https://app.getzep.com/) and create a project.

Follow the [Zep Cloud Typescript SDK Installation Guide](https://help.getzep.com/sdks) to install and get started with Zep.

You'll need your Zep Cloud Project API Key to use the Zep Cloud Memory. See the [Zep Cloud docs](https://help.getzep.com/projects) for more information.

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @getzep/zep-cloud @langchain/openai @langchain/community @langchain/core
```

### ZepCloudChatMessageHistory + RunnableWithMessageHistory usage

import CodeBlock from "@theme/CodeBlock";
import ZepCloudMessageHistoryExample from "@examples/guides/expression_language/zep/zep_cloud_message_history.ts";

<CodeBlock language="typescript">{ZepCloudMessageHistoryExample}</CodeBlock>

### ZepCloudChatMessageHistory + RunnableWithMessageHistory + ZepVectorStore (as retriever) usage

import ZepCloudMessageHistoryWithVectorStoreExample from "@examples/guides/expression_language/zep/zep_cloud_message_history_vector_store.ts";

<CodeBlock language="typescript">
  {ZepCloudMessageHistoryWithVectorStoreExample}
</CodeBlock>

### Memory Usage

import ZepCloudMemoryExample from "@examples/memory/zep_cloud.ts";

<CodeBlock language="typescript">{ZepCloudMemoryExample}</CodeBlock>
