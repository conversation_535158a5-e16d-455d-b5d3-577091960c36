# Anthropic

All functionality related to Anthropic models.

[Anthropic](https://www.anthropic.com/) is an AI safety and research company, and is the creator of <PERSON>.
This page covers all integrations between Anthropic models and LangChain.

## Prompting Best Practices

Anthropic models have several prompting best practices compared to OpenAI models.

**System Messages may only be the first message**

Anthropic models require any system messages to be the first one in your prompts.

## `ChatAnthropic`

`ChatAnthropic` is a subclass of LangChain's `ChatModel`, meaning it works best with `ChatPromptTemplate`.
You can import this wrapper with the following code:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/anthropic @langchain/core
```

```typescript
import { ChatAnthropic } from "@langchain/anthropic";
const model = new ChatAnthropic({});
```

When working with ChatModels, it is preferred that you design your prompts as `ChatPromptTemplate`s.
Here is an example below of doing that:

```typescript
import { ChatPromptTemplate } from "langchain/prompts";

const prompt = ChatPromptTemplate.fromMessages([
  ["system", "You are a helpful chatbot"],
  ["human", "Tell me a joke about {topic}"],
]);
```

You can then use this in a chain as follows:

```typescript
const chain = prompt.pipe(model);
await chain.invoke({ topic: "bears" });
```

See the [chat model integration page](/docs/integrations/chat/anthropic/) for more examples, including multimodal inputs.
