---
keywords: [gemini, gemini-pro, gemma]
---

# Google

Functionality related to [Google Cloud Platform](https://cloud.google.com/)
and [AI Studio](https://aistudio.google.com/)

## Chat models

### Gemini Models

Access Gemini models such as `gemini-1.5-pro` and `gemini-2.0-flex` through the [`ChatGoogleGenerativeAI`](/docs/integrations/chat/google_generativeai),
or if using VertexAI, via the [`ChatVertexAI`](/docs/integrations/chat/google_vertex_ai) class.

import Tabs from "@theme/Tabs";
import TabItem from "@theme/TabItem";
import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<Tabs>
<TabItem value="genai" label="GenAI" default>
<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/google-genai @langchain/core
```

Configure your API key.

```
export GOOGLE_API_KEY=your-api-key
```

```typescript
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";

const model = new ChatGoogleGenerativeAI({
  model: "gemini-pro",
  maxOutputTokens: 2048,
});

// Batch and stream are also supported
const res = await model.invoke([
  [
    "human",
    "What would be a good company name for a company that makes colorful socks?",
  ],
]);
```

More recent Gemini models support image inputs:

```typescript
const visionModel = new ChatGoogleGenerativeAI({
  model: "gemini-2.0-flash",
  maxOutputTokens: 2048,
});
const image = fs.readFileSync("./hotdog.jpg").toString("base64");
const input2 = [
  new HumanMessage({
    content: [
      {
        type: "text",
        text: "Describe the following image.",
      },
      {
        type: "image_url",
        image_url: `data:image/png;base64,${image}`,
      },
    ],
  }),
];

const res = await visionModel.invoke(input2);
```

:::tip
Click [here](/docs/integrations/chat/google_generativeai) for the `@langchain/google-genai` specific integration docs
:::

</TabItem>

<TabItem value="vertexai" label="VertexAI" default>
<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/google-vertexai @langchain/core
```

Then, you'll need to add your service account credentials, either directly as a `GOOGLE_VERTEX_AI_WEB_CREDENTIALS` environment variable:

```
GOOGLE_VERTEX_AI_WEB_CREDENTIALS={"type":"service_account","project_id":"YOUR_PROJECT-12345",...}
```

or as a file path:

```
GOOGLE_VERTEX_AI_WEB_CREDENTIALS_FILE=/path/to/your/credentials.json
```

```typescript
import { ChatVertexAI } from "@langchain/google-vertexai";
// Or, if using the web entrypoint:
// import { ChatVertexAI } from "@langchain/google-vertexai-web";

const model = new ChatVertexAI({
  model: "gemini-1.0-pro",
  maxOutputTokens: 2048,
});

// Batch and stream are also supported
const res = await model.invoke([
  [
    "human",
    "What would be a good company name for a company that makes colorful socks?",
  ],
]);
```

Gemini vision models support image inputs when providing a single human message. For example:

```typescript
const visionModel = new ChatVertexAI({
  model: "gemini-pro-vision",
  maxOutputTokens: 2048,
});
const image = fs.readFileSync("./hotdog.png").toString("base64");
const input2 = [
  new HumanMessage({
    content: [
      {
        type: "text",
        text: "Describe the following image.",
      },
      {
        type: "image_url",
        image_url: `data:image/png;base64,${image}`,
      },
    ],
  }),
];

const res = await visionModel.invoke(input2);
```

:::tip
Click [here](/docs/integrations/chat/google_vertex_ai) for the `@langchain/google-vertexai` specific integration docs
:::

</TabItem>
</Tabs>

The value of `image_url` must be a base64 encoded image (e.g., `data:image/png;base64,abcd124`).

### Gemma

Access the `gemma-3-27b-it` model through AI Studio using the `ChatGoogle` class.
(This class is a superclass of the [`ChatVertexAI`](/docs/integrations/chat/google_vertex_ai)
class that works with both Vertex AI and the AI Studio APIs.)

:::tip
Since Gemma is an open model, it may also be available from other platforms
including [Ollama](/docs/integrations/chat/ollama/).
:::

```bash npm2yarn
npm install @langchain/google-gauth @langchain/core
```

Configure your API key.

```
export GOOGLE_API_KEY=your-api-key
```

```typescript
import { ChatGoogle } from "@langchain/google-gauth";

const model = new ChatGoogle({
  model: "gemma-3-27b-it",
});

const res = await model.invoke([
  {
    role: "user",
    content:
      "What would be a good company name for a company that makes colorful socks?",
  },
]);
```

### Third Party Models

See above for setting up authentication through Vertex AI to use these models.

[Anthropic](/docs/integrations/chat/anthropic) Claude models are also available through
the [Vertex AI](https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude)
platform. See [here](https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude)
for more information about enabling access to the models and the model names to use.

PaLM models are no longer supported.

## Vector Store

### Vertex AI Vector Search

> [Vertex AI Vector Search](https://cloud.google.com/vertex-ai/docs/matching-engine/overview),
> formerly known as Vertex AI Matching Engine, provides the industry's leading high-scale
> low latency vector database. These vector databases are commonly
> referred to as vector similarity-matching or an approximate nearest neighbor (ANN) service.

```typescript
import { MatchingEngine } from "@langchain/community/vectorstores/googlevertexai";
```

### Postgres Vector Store

The [PostgresVectorStore](/docs/integrations/vectorstores/google_cloudsql_pg) module from the
[`@langchain/google-cloud-sql-pg`](https://www.npmjs.com/package/@langchain/google-cloud-sql-pg) package provides a way to use the CloudSQL for PostgresSQL to store
vector embeddings using the class.

```bash
$ yarn add @langchain/google-cloud-sql-pg
```

Set your environment variables:

```bash
PROJECT_ID="your-project-id"
REGION="your-project-region"
INSTANCE_NAME="your-instance"
DB_NAME="your-database-name"
DB_USER="your-database-user"
PASSWORD="your-database-password"
```

Create a DB connection through the PostgresEngine class:

```typescript
const engine: PostgresEngine = await PostgresEngine.fromInstance(
  process.env.PROJECT_ID ?? "",
  process.env.REGION ?? "",
  process.env.INSTANCE_NAME ?? "",
  process.env.DB_NAME ?? "",
  peArgs
);
```

Initialize the vector store table:

```typescript
await engine.initVectorstoreTable(
  "my_vector_store_table",
  768,
  vectorStoreArgs
);
```

Create a vector store instance:

```typescript
const vectorStore = await PostgresVectorStore.initialize(
  engine,
  embeddingService,
  "my_vector_store_table",
  pvectorArgs
);
```

## Tools

### Google Search

- Set up a Custom Search Engine, following [these instructions](https://stackoverflow.com/questions/37083058/programmatically-searching-google-in-python-using-custom-search)
- Get an API Key and Custom Search Engine ID from the previous step, and set them as environment variables `GOOGLE_API_KEY` and `GOOGLE_CSE_ID` respectively

There exists a `GoogleCustomSearch` utility which wraps this API. To import this utility:

```typescript
import { GoogleCustomSearch } from "@langchain/community/tools/google_custom_search";
```

We can easily load this wrapper as a Tool (to use with an Agent). We can do this with:

```typescript
const tools = [new GoogleCustomSearch({})];
// Pass this variable into your agent.
```

## Chat history

### Postgres Chat Message History

The [PostgresChatMessageHistory](/docs/integrations/memory/google_cloudsql_pg) from
the [`@langchain/google-cloud-sql-pg`](https://www.npmjs.com/package/@langchain/google-cloud-sql-pg) package provides a way to use the CloudSQL for PostgresSQL to store messages and provide conversation history.

```bash
$ yarn add @langchain/google-cloud-sql-pg
```

_Note:_ See the [Postgres Vector Store](#Postgres Vector Store) section on this page to learn
how to install the package and initialize a DB connection.

Initialize the chat history table:

```typescript
await engine.initChatHistoryTable("chat_message_table");
```

Create a chat message history instance:

```typescript
const historyInstance = await PostgresChatMessageHistory.initialize(
  engine,
  "test",
  "chat_message_table"
);
```

## Document Loaders

### Postgres Loader

The [PostgresLoader](/docs/integrations/memory/google_cloudsql_pg) from
`@langchain/google-cloud-sql-pg` provides a way to use the CloudSQL for PostgresSQL to load data as LangChain `Document`s.

_Note:_ See the [Postgres Vector Store](#Postgres Vector Store) section on this page to learn
how to install the package and initialize a DB connection.

Create a loader instance:

```typescript
const documentLoaderArgs: PostgresLoaderOptions = {
  tableName: "test_table_custom",
  contentColumns: ["fruit_name", "variety"],
  metadataColumns: [
    "fruit_id",
    "quantity_in_stock",
    "price_per_unit",
    "organic",
  ],
  format: "text",
};

const documentLoaderInstance = await PostgresLoader.initialize(
  PEInstance,
  documentLoaderArgs
);

const documents = await documentLoaderInstance.load();
```
