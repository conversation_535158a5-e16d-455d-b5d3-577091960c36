---
sidebar_position: 0
sidebar_class_name: hidden
---

# Providers

<PERSON><PERSON><PERSON><PERSON> integrates with many providers.

## Partner Packages

These providers have standalone `@langchain/{provider}` packages for improved versioning, dependency management and testing.

For specifics on how to use each package, look for their pages in the appropriate component docs section (e.g. [chat models](/docs/integrations/chat/)).

- [Anthropic](https://www.npmjs.com/package/@langchain/anthropic)
- [Cerebras](https://www.npmjs.com/package/@langchain/cerebras)
- [Cloudflare](https://www.npmjs.com/package/@langchain/cloudflare)
- [Cohere](https://www.npmjs.com/package/@langchain/cohere)
- [Exa](https://www.npmjs.com/package/@langchain/exa)
- [Google GenAI](https://www.npmjs.com/package/@langchain/google-genai)
- [Google VertexAI](https://www.npmjs.com/package/@langchain/google-vertexai)
- [Google VertexAI (Web Environments)](https://www.npmjs.com/package/@langchain/google-vertexai-web)
- [Groq](https://www.npmjs.com/package/@langchain/groq)
- [MistralAI](https://www.npmjs.com/package/@langchain/mistralai)
- [MongoDB](https://www.npmjs.com/package/@langchain/mongodb)
- [Nomic](https://www.npmjs.com/package/@langchain/nomic)
- [OpenAI](https://www.npmjs.com/package/@langchain/openai)
- [Pinecone](https://www.npmjs.com/package/@langchain/pinecone)
- [Qdrant](https://www.npmjs.com/package/@langchain/qdrant)
- [Redis](https://www.npmjs.com/package/@langchain/redis)
- [Tavily](https://www.npmjs.com/package/@langchain/tavily)
- [Weaviate](https://www.npmjs.com/package/@langchain/weaviate)
- [Yandex](https://www.npmjs.com/package/@langchain/yandex)
- [Azure CosmosDB](https://www.npmjs.com/package/@langchain/azure-cosmosdb)
- [xAI](https://www.npmjs.com/package/@langchain/xai)
