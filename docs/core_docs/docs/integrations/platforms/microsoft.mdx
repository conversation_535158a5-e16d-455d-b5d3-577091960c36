---
keywords: [azure]
---

import CodeBlock from "@theme/CodeBlock";

# Microsoft

All functionality related to `Microsoft Azure` and other `Microsoft` products.

## Chat Models

### Azure OpenAI

See a [usage example](/docs/integrations/chat/azure)

import AzureChatOpenAI from "@examples/models/chat/integration_azure_openai.ts";

<UnifiedModelParamsTooltip></UnifiedModelParamsTooltip>

<CodeBlock language="typescript">{AzureChatOpenAI}</CodeBlock>

## LLM

### Azure OpenAI

> [Microsoft Azure](https://en.wikipedia.org/wiki/Microsoft_Azure), often referred to as `Azure` is a cloud computing platform run by `Microsoft`, which offers access, management, and development of applications and services through global data centers. It provides a range of capabilities, including software as a service (SaaS), platform as a service (PaaS), and infrastructure as a service (IaaS). `Microsoft Azure` supports many programming languages, tools, and frameworks, including Microsoft-specific and third-party software and systems.

> [Azure OpenAI](https://azure.microsoft.com/products/ai-services/openai-service/) is a cloud service to help you quickly develop generative AI experiences with a diverse set of prebuilt and curated models from OpenAI, Meta and beyond.

LangChain.js supports integration with [Azure OpenAI](https://azure.microsoft.com/products/ai-services/openai-service/) using the new Azure integration in the [OpenAI SDK](https://github.com/openai/openai-node).

You can learn more about Azure OpenAI and its difference with the OpenAI API on [this page](https://learn.microsoft.com/azure/ai-services/openai/overview). If you don't have an Azure account, you can [create a free account](https://azure.microsoft.com/free/) to get started.

You'll need to have an Azure OpenAI instance deployed. You can deploy a version on Azure Portal following [this guide](https://learn.microsoft.com/azure/ai-services/openai/how-to/create-resource?pivots=web-portal).

Once you have your instance running, make sure you have the name of your instance and key. You can find the key in the Azure Portal, under the "Keys and Endpoint" section of your instance.

If you're using Node.js, you can define the following environment variables to use the service:

```bash
AZURE_OPENAI_API_INSTANCE_NAME=<YOUR_INSTANCE_NAME>
AZURE_OPENAI_API_DEPLOYMENT_NAME=<YOUR_DEPLOYMENT_NAME>
AZURE_OPENAI_API_EMBEDDINGS_DEPLOYMENT_NAME=<YOUR_EMBEDDINGS_DEPLOYMENT_NAME>
AZURE_OPENAI_API_KEY=<YOUR_KEY>
AZURE_OPENAI_API_VERSION="2024-02-01"
```

:::info

You can find the list of supported API versions in the [Azure OpenAI documentation](https://learn.microsoft.com/azure/ai-services/openai/reference).

:::

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

See a [usage example](/docs/integrations/llms/azure).

import AzureOpenAI from "@examples/models/llm/azure_openai.ts";

import UnifiedModelParamsTooltip from "@mdx_components/unified_model_params_tooltip.mdx";

<UnifiedModelParamsTooltip></UnifiedModelParamsTooltip>

<CodeBlock language="typescript">{AzureOpenAI}</CodeBlock>

## Text Embedding Models

### Azure OpenAI

See a [usage example](/docs/integrations/text_embedding/azure_openai)

import AzureOpenAIEmbeddings from "@examples/models/embeddings/azure_openai.ts";

<UnifiedModelParamsTooltip></UnifiedModelParamsTooltip>

<CodeBlock language="typescript">{AzureOpenAIEmbeddings}</CodeBlock>

## Vector stores

### Azure AI Search

> [Azure AI Search](https://azure.microsoft.com/products/ai-services/ai-search) (formerly known as Azure Search and Azure Cognitive Search) is a distributed, RESTful search engine optimized for speed and relevance on production-scale workloads on Azure. It supports also vector search using the [k-nearest neighbor](https://en.wikipedia.org/wiki/Nearest_neighbor_search) (kNN) algorithm and also [semantic search](https://learn.microsoft.com/azure/search/semantic-search-overview).

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install -S @langchain/community @langchain/core @azure/search-documents
```

See a [usage example](/docs/integrations/vectorstores/azure_aisearch).

```typescript
import { AzureAISearchVectorStore } from "@langchain/community/vectorstores/azure_aisearch";
```

### Azure Cosmos DB for NoSQL

> [Azure Cosmos DB for NoSQL](https://learn.microsoft.com/azure/cosmos-db/nosql/) provides support for querying items with flexible schemas and native support for JSON. It now offers vector indexing and search. This feature is designed to handle high-dimensional vectors, enabling efficient and accurate vector search at any scale. You can now store vectors directly in the documents alongside your data. Each document in your database can contain not only traditional schema-free data, but also high-dimensional vectors as other properties of the documents.

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/azure-cosmosdb @langchain/core
```

See a [usage example](/docs/integrations/vectorstores/azure_cosmosdb_nosql).

```typescript
import { AzureCosmosDBNoSQLVectorStore } from "@langchain/azure-cosmosdb";
```

### Azure Cosmos DB for MongoDB vCore

> [Azure Cosmos DB for MongoDB vCore](https://learn.microsoft.com/azure/cosmos-db/mongodb/vcore/) makes it easy to create a database with full native MongoDB support. You can apply your MongoDB experience and continue to use your favorite MongoDB drivers, SDKs, and tools by pointing your application to the API for MongoDB vCore account’s connection string. Use vector search in Azure Cosmos DB for MongoDB vCore to seamlessly integrate your AI-based applications with your data that’s stored in Azure Cosmos DB.

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/azure-cosmosdb @langchain/core
```

See a [usage example](/docs/integrations/vectorstores/azure_cosmosdb_mongodb).

```typescript
import { AzureCosmosDBMongoDBVectorStore } from "@langchain/azure-cosmosdb";
```

## Semantic Cache

### Azure Cosmos DB NoSQL Semantic Cache

> The Semantic Cache feature is supported with Azure Cosmos DB for NoSQL integration, enabling users to retrieve cached responses based on semantic similarity between the user input and previously cached results. It leverages [AzureCosmosDBNoSQLVectorStore](/docs/integrations/vectorstores/azure_cosmosdb_nosql), which stores vector embeddings of cached prompts. These embeddings enable similarity-based searches, allowing the system to retrieve relevant cached results.

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/azure-cosmosdb @langchain/core
```

See a [usage example](/docs/integrations/llm_caching/azure_cosmosdb_nosql).

```typescript
import { AzureCosmosDBNoSQLSemanticCache } from "@langchain/azure-cosmosdb";
```

## Chat Message History

### Azure Cosmos DB NoSQL Chat Message History

> The AzureCosmosDBNoSQLChatMessageHistory uses Cosmos DB to store chat message history. For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory`.

```bash npm2yarn
npm install @langchain/azure-cosmosdb @langchain/core
```

See [usage example](/docs/integrations/memory/azure_cosmosdb_nosql.mdx).

```typescript
import { AzureCosmosDBNoSQLChatMessageHistory } from "@langchain/azure-cosmosdb";
```

### Azure Cosmos DB MongoDB vCore Chat Message History

> The AzureCosmosDBMongoChatMessageHistory uses Cosmos DB Mongo vCore to store chat message history. For longer-term persistence across chat sessions, you can swap out the default in-memory `chatHistory` that backs chat memory classes like `BufferMemory`.

```bash npm2yarn
npm install @langchain/azure-cosmosdb @langchain/core
```

See a [usage example](/docs/integrations/memory/azure_cosmos_mongo_vcore.mdx).

```typescript
import { AzureCosmosDBMongoChatMessageHistory } from "@langchain/azure-cosmosdb";
```

## Document loaders

### Azure Blob Storage

> [Azure Blob Storage](https://learn.microsoft.com/azure/storage/blobs/storage-blobs-introduction) is Microsoft's object storage solution for the cloud. Blob Storage is optimized for storing massive amounts of unstructured data. Unstructured data is data that doesn't adhere to a particular data model or definition, such as text or binary data.

> [Azure Files](https://learn.microsoft.com/azure/storage/files/storage-files-introduction) offers fully managed
> file shares in the cloud that are accessible via the industry standard Server Message Block (`SMB`) protocol,
> Network File System (`NFS`) protocol, and `Azure Files REST API`. `Azure Files` are based on the `Azure Blob Storage`.

`Azure Blob Storage` is designed for:

- Serving images or documents directly to a browser.
- Storing files for distributed access.
- Streaming video and audio.
- Writing to log files.
- Storing data for backup and restore, disaster recovery, and archiving.
- Storing data for analysis by an on-premises or Azure-hosted service.

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core @azure/storage-blob
```

See a [usage example for the Azure Blob Storage](/docs/integrations/document_loaders/web_loaders/azure_blob_storage_container).

```typescript
import { AzureBlobStorageContainerLoader } from "@langchain/community/document_loaders/web/azure_blob_storage_container";
```

See a [usage example for the Azure Files](/docs/integrations/document_loaders/web_loaders/azure_blob_storage_file).

```typescript
import { AzureBlobStorageFileLoader } from "@langchain/community/document_loaders/web/azure_blob_storage_file";
```

## Tools

### Azure Container Apps Dynamic Sessions

> [Azure Container Apps dynamic sessions](https://learn.microsoft.com/azure/container-apps/sessions) provide fast access to secure sandboxed environments that are ideal for running code or applications that require strong isolation from other workloads.

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/azure-dynamic-sessions @langchain/core
```

See a [usage example](/docs/integrations/tools/azure_dynamic_sessions).

```typescript
import { SessionsPythonREPLTool } from "@langchain/azure-dynamic-sessions";
```
