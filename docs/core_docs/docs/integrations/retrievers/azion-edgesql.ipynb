{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Azion EdgeSQL\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# AzionRetriever\n", "\n", "## Overview\n", "\n", "This will help you getting started with the [AzionRetriever](/docs/concepts/#retrievers). For detailed documentation of all AzionRetriever features and configurations head to the [API reference](https://api.js.langchain.com/classes/_langchain_community.retrievers_azion_edgesql.AzionRetriever.html).\n", "\n", "### Integration details\n", "\n", "\n", "| Retriever | Self-host | Cloud offering | Package | [Py support] |\n", "| :--- | :---: | :---: | :---: | :---: |\n", "[AzionRetriever](https://api.js.langchain.com/classes/_langchain_community.retrievers_azion_edgesql.AzionRetriever.html) | ❌ | ❌ | @langchain/community | ❌ |\n", "\n", "\n", "## Setup\n", "\n", "To use the AzionRetriever, you need to set the AZION_TOKEN environment variable.\n", "\n", "```typescript\n", "process.env.AZION_TOKEN = \"your-api-key\"\n", "```\n", "\n", "If you are using OpenAI embeddings for this guide, you'll need to set your OpenAI key as well:\n", "\n", "```typescript\n", "process.env.OPENAI_API_KEY = \"YOUR_API_KEY\";\n", "```\n", "If you want to get automated tracing from individual queries, you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "// process.env.LANGSMITH_API_KEY = \"<YOUR API KEY HERE>\";\n", "// process.env.LANGSMITH_TRACING = \"true\";\n", "```\n", "\n", "### Installation\n", "\n", "This retriever lives in the `@langchain/community/retrievers/azion_edgesql` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  azion @langchain/openai @langchain/community\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our retriever:"]}, {"cell_type": "code", "execution_count": 56, "id": "70cc8e65-2a02-408a-bbc6-8ef649057d82", "metadata": {}, "outputs": [], "source": ["import { AzionRetriever } from \"@langchain/community/retrievers/azion_edgesql\";\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const embeddingModel = new OpenAIEmbeddings({\n", "  model: \"text-embedding-3-small\"\n", "})\n", "\n", "const chatModel = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  apiKey: process.env.OPENAI_API_KEY\n", "})\n", "\n", "const retriever = new AzionRetriever(embeddingModel, \n", "  {dbName:\"langchain\",\n", "   vectorTable:\"documents\", // table where the vector embeddings are stored\n", "   ftsTable:\"documents_fts\", // table where the fts index is stored\n", "   searchType:\"hybrid\", // search type to use for the retriever\n", "   ftsK:2, // number of results to return from the fts index\n", "   similarityK:2, // number of results to return from the vector index\n", "   metadataItems:[\"language\",\"topic\"],\n", "   filters: [{ operator: \"=\", column: \"language\", value: \"en\" }],\n", "   entityExtractor:chatModel\n", "\n", "}) // number of results to return from the vector index"]}, {"cell_type": "markdown", "id": "5c5f2839-4020-424e-9fc9-07777eede442", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "code", "execution_count": 57, "id": "51a60dbe-9f2e-4e04-bb62-23968f17164a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  Document {\n", "    pageContent: 'Australia s indigenous people have inhabited the continent for over 65,000 years',\n", "    metadata: { language: 'en', topic: 'history', searchtype: 'similarity' },\n", "    id: '3'\n", "  },\n", "  Document {\n", "    pageContent: 'Australia is a leader in solar energy adoption and renewable technology',\n", "    metadata: { language: 'en', topic: 'technology', searchtype: 'similarity' },\n", "    id: '5'\n", "  },\n", "  Document {\n", "    pageContent: 'Australia s tech sector is rapidly growing with innovation hubs in major cities',\n", "    metadata: { language: 'en', topic: 'technology', searchtype: 'fts' },\n", "    id: '7'\n", "  }\n", "]\n"]}], "source": ["const query = \"Australia\"\n", "\n", "await retriever.invoke(query);"]}, {"cell_type": "markdown", "id": "dfe8aad4-8626-4330-98a9-7ea1ca5d2e0e", "metadata": {}, "source": ["## Use within a chain\n", "\n", "Like other retrievers, AzionRetriever can be incorporated into LLM applications via [chains](/docs/how_to/sequence/).\n", "\n", "We will need a LLM or chat model:\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 43, "id": "25b647a3-f8f2-4541-a289-7a241e43f9df", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "});"]}, {"cell_type": "code", "execution_count": 44, "id": "23e11cc9-abd6-4855-a7eb-799f45ca01ae", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { RunnablePassthrough, RunnableSequence } from \"@langchain/core/runnables\";\n", "import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "\n", "import type { Document } from \"@langchain/core/documents\";\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(`\n", "Answer the question based only on the context provided.\n", "\n", "Context: {context}\n", "\n", "Question: {question}`);\n", "\n", "const formatDocs = (docs: Document[]) => {\n", "  return docs.map((doc) => doc.pageContent).join(\"\\n\\n\");\n", "}\n", "\n", "// See https://js.langchain.com/docs/tutorials/rag\n", "const ragChain = RunnableSequence.from([\n", "  {\n", "    context: retriever.pipe(formatDocs),\n", "    question: new RunnablePassthrough(),\n", "  },\n", "  prompt,\n", "  llm,\n", "  new StringOutputParser(),\n", "]);"]}, {"cell_type": "code", "execution_count": 45, "id": "d47c37dd-5c11-416c-a3b6-bec413cd70e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The context mentions that the 2024 Olympics are in Paris.\n"]}], "source": ["await rag<PERSON><PERSON><PERSON>.invoke(\"Paris\")"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all AzionRetriever features and configurations head to the [API reference](https://api.js.langchain.com/classes/_langchain_community.retrievers_azion_edgesql.AzionRetriever.html).\n"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}