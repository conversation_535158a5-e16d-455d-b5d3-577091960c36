{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BM25\n", "\n", "BM25, also known as [Okapi BM25](https://en.wikipedia.org/wiki/Okapi_BM25), is a ranking function used in information retrieval systems to estimate the relevance of documents to a given search query.\n", "\n", "You can use it as part of your retrieval pipeline as a to rerank documents as a postprocessing step after retrieving an initial set of documents from another source.\n", "\n", "## Setup\n", "\n", "The `BM25Retriever` is exported from `@langchain/community`. You'll need to install it like this:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "```\n", "\n", "This retriever uses code from [`this implementation`](https://github.com/FurkanToprak/OkapiBM25) of Okapi BM25.\n", "\n", "## Usage\n", "\n", "You can now create a new retriever with previously retrieved documents:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  { pageContent: 'mitochondria is made of lipids', metadata: {} },\n", "  {\n", "    pageContent: 'mitochondria is the powerhouse of the cell',\n", "    metadata: {}\n", "  },\n", "  { pageContent: 'Buildings are made out of brick', metadata: {} },\n", "  { pageContent: 'Buildings are made out of wood', metadata: {} }\n", "]\n"]}], "source": ["import { BM25Retriever } from \"@langchain/community/retrievers/bm25\";\n", "\n", "const retriever = BM25Retriever.fromDocuments([\n", "  { pageContent: \"Buildings are made out of brick\", metadata: {} },\n", "  { pageContent: \"Buildings are made out of wood\", metadata: {} },\n", "  { pageContent: \"Buildings are made out of stone\", metadata: {} },\n", "  { pageContent: \"Cars are made out of metal\", metadata: {} },\n", "  { pageContent: \"Cars are made out of plastic\", metadata: {} },\n", "  { pageContent: \"mitochondria is the powerhouse of the cell\", metadata: {} },\n", "  { pageContent: \"mitochondria is made of lipids\", metadata: {} },\n", "], { k: 4 });\n", "\n", "// Will return the 4 documents reranked by the BM25 algorithm\n", "await retriever.invoke(\"mitochondria\");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}