---
hide_table_of_contents: true
sidebar_class_name: hidden
---

# ChatGPT Plugin Retriever

:::warning
This module has been deprecated and is no longer supported. The documentation below will not work in versions 0.2.0 or later.
:::

This example shows how to use the ChatGPT Retriever Plugin within LangChain.

To set up the ChatGPT Retriever Plugin, please follow instructions [here](https://github.com/openai/chatgpt-retrieval-plugin).

## Usage

```typescript
import { ChatGPTPluginRetriever } from "langchain/retrievers/remote";

const retriever = new ChatGPTPluginRetriever({
  url: "http://0.0.0.0:8000",
  auth: {
    bearer: "super-secret-jwt-token-with-at-least-32-characters-long",
  },
});

const docs = await retriever.invoke("hello world");

console.log(docs);
```

## Related

- Retriever [conceptual guide](/docs/concepts/retrievers)
- Retriever [how-to guides](/docs/how_to/#retrievers)
