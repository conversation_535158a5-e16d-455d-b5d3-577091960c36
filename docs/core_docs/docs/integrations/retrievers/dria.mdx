---
hide_table_of_contents: true
---

# <PERSON>ia Retriever

The [Dria](https://dria.co/profile) retriever allows an agent to perform a text-based search across a comprehensive knowledge hub.

## Setup

To use Dria retriever, first install Dria JS client:

```bash npm2yarn
npm install dria
```

You need to provide two things to the retriever:

- **API Key**: you can get yours at your [profile page](https://dria.co/profile) when you create an account.
- **Contract ID**: accessible at the top of the page when viewing a knowledge or in its URL.
  For example, the Bitcoin whitepaper is uploaded on Dria at https://dria.co/knowledge/2KxNbEb040GKQ1DSDNDsA-Fsj_BlQIEAlzBNuiapBR0, so its contract ID is `2KxNbEb040GKQ1DSDNDsA-Fsj_BlQIEAlzBNuiapBR0`.
  Contract ID can be omitted during instantiation, and later be set via `dria.contractId = "your-contract"`

Dria retriever exposes the underlying [Dria client](https://npmjs.com/package/dria) as well, refer to the [Dria documentation](https://github.com/firstbatchxyz/dria-js-client?tab=readme-ov-file#usage) to learn more about the client.

## Usage

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install dria @langchain/community @langchain/core
```

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/retrievers/dria.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Retriever [conceptual guide](/docs/concepts/retrievers)
- Retriever [how-to guides](/docs/how_to/#retrievers)
