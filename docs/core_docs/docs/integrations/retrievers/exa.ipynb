{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Exa\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# ExaRetriever\n", "\n", "## Overview\n", "\n", "[Exa](https://exa.ai/) is a search engine that retrieves relevant content from the web given some input query.\n", "\n", "This guide will help you getting started with the Exa [retriever](/docs/concepts/retrievers). For detailed documentation of all `ExaRetriever` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_exa.ExaRetriever.html).\n", "\n", "### Integration details\n", "\n", "| Retriever | Source | Package |\n", "| :--- | :--- | :---: |\n", "[ExaRetriever](https://api.js.langchain.com/classes/langchain_exa.ExaRetriever.html) | Information on the web. | [`@langchain/exa`](https://www.npmjs.com/package/@langchain/exa) |\n", "\n", "## Setup\n", "\n", "You'll need to set your API key as an environment variable.\n", "\n", "The `Exa` class defaults to `EXASEARCH_API_KEY` when searching for your API key.\n", "\n", "```typescript\n", "process.env.EXASEARCH_API_KEY=\"<YOUR API KEY>\";\n", "```\n", "\n", "If you want to get automated tracing from individual queries, you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "// process.env.LANGSMITH_API_KEY = \"<YOUR API KEY HERE>\";\n", "// process.env.LANGSMITH_TRACING = \"true\";\n", "```\n", "\n", "### Installation\n", "\n", "This retriever lives in the `@langchain/exa` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/exa @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our retriever:"]}, {"cell_type": "code", "execution_count": 8, "id": "70cc8e65-2a02-408a-bbc6-8ef649057d82", "metadata": {}, "outputs": [], "source": ["import { ExaRetriever } from \"@langchain/exa\";\n", "import Exa from \"exa-js\";\n", "\n", "const retriever = new ExaRetriever({\n", "  // @lc-ts-ignore\n", "  client: new Exa(\n", "    process.env.EXASEARCH_API_KEY // default API key\n", "  ),\n", "  searchArgs: {\n", "    numResults: 2,\n", "  }\n", "});"]}, {"cell_type": "markdown", "id": "5c5f2839-4020-424e-9fc9-07777eede442", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "code", "execution_count": 9, "id": "51a60dbe-9f2e-4e04-bb62-23968f17164a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  Document {\n", "    pageContent: 'President <PERSON><PERSON>’s State of the Union Address\\n' +\n", "      '<PERSON>am Speaker, <PERSON><PERSON> Vice President, and our First Lady and Second Gentleman, members of Congress and the Cabinet, Justices of the Supreme Court, my fellow Americans: Last year, COVID-19 kept us apart. This year, we’re finally together again.\\n' +\n", "      'Tonight — tonight we meet as Democrats, Republicans, and independents, but, most importantly, as Americans with a duty to one another, to America, to the American people, and to the Constitution, and an unwavering resolve that freedom will always triumph over tyranny.\\n' +\n", "      'Six — thank you. Six days ago, Russia’s <PERSON> sought to shake the very foundations of the free world, thinking he could make it bend to his menacing ways. But he badly miscalculated. He thought he could roll into Ukraine and the world would roll over. Instead, he met with a wall of strength he never anticipated or imagined. He met the Ukrainian people.\\n' +\n", "      'From President <PERSON><PERSON><PERSON><PERSON> to every Ukrainian, their fearlessness, their courage, their determination literally inspires the world. Groups of citizens blocking tanks with their bodies. Everyone from students to retirees, to teachers turned soldiers defending their homeland.\\n' +\n", "      'And in this struggle — President <PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament, “Light will win over darkness.”\\n' +\n", "      'The Ukrainian Ambassador to the United States is here tonight sitting with the First Lady. Let each of us, if you’re able to stand, stand and send an unmistakable signal to the world and Ukraine. Thank you. Thank you, thank you, thank you.\\n' +\n", "      'She’s bright, she’s strong, and she’s resolved.\\n' +\n", "      'Yes. We, the United States of America, stand with the Ukrainian people.\\n' +\n", "      'Throughout our history, we’ve learned this lesson: When dictators do not pay a price for their aggression, they cause more chaos; they keep moving; and the costs, the threats to the America — and America, to the world keeps rising.\\n' +\n", "      'That’s why the NATO Alliance was created: to secure peace and stability in Europe after World War Two.\\n' +\n", "      'The United States is a member, along with 29 other nations. It matters. American diplomacy matters. American resolve matters.\\n' +\n", "      '<PERSON>’s latest attack on Ukraine was premeditated and totally unprovoked. He rejected repeated efforts at diplomacy.\\n' +\n", "      'He thought the West and NATO wouldn’t respond. He thought he could divide us at home, in this chamber, in this nation. He thought he could divide us in Europe as well.\\n' +\n", "      'But <PERSON> was wrong. We are ready. We are united. And that’s what we did: We stayed united.\\n' +\n", "      'We prepared extensively and carefully. We spent months building coalitions of other freedom-loving nations in Europe and the Americas to — from America to the Asian and African continents to confront <PERSON>.\\n' +\n", "      'Like many of you, I spent countless hours unifying our European Allies.\\n' +\n", "      'We shared with the world, in advance, what we knew <PERSON> was planning and precisely how he would try to falsely and justify his aggression.\\n' +\n", "      'We countered Russia’s lies with the truth. And now — now that he’s acted, the free world is holding him accountable, along with 27 members of the European Union — including France, Germany, Italy — as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others. Even Switzerland are inflicting pain on Russia and supporting the people of Ukraine.\\n' +\n", "      '<PERSON> is now isolated from the world more than he has ever been.\\n' +\n", "      'Together. Together. Together, along with our Allies, we are right now enforcing powerful economic sanctions. We’re cutting off Russia’s largest banks from the international financial system; preventing Russia’s Central Bank from defending the Russian ruble, making <PERSON>’s $630 billion war fund worthless. We’re choking Russia’s access, we’re choking Russia’s access to technology that will sap its economic strength and weaken its military for years to come.\\n' +\n", "      'Tonight, I say to the Russian oligarchs and the corrupt leaders who’ve bilked billions of dollars off this violent regime: No more.\\n' +\n", "      'The United States — I mean it. The United States Department of Justice is assembling a dedicated task force to go after the crimes of the Russian oligarchs.\\n' +\n", "      'We’re joining with European Allies to find and seize their yachts, their luxury apartments, their private jets. We’re coming for your ill-begotten gains.\\n' +\n", "      'And, tonight, I’m announcing that we will join our Allies in closing off American air space to all Russian flights, further isolating Russia and adding an additional squeeze on their economy.\\n' +\n", "      'He has no idea what’s coming.\\n' +\n", "      'The ruble has already lost 30 percent of its value, the Russian stock market has lost 40 percent of its value, and trading remains suspended.\\n' +\n", "      'The Russian economy is reeling, and <PERSON> alone is the one to blame.\\n' +\n", "      'Together with our Allies, we’re providing support to the Ukrainians in their fight for freedom: military assistance, economic assistance, humanitarian assistance. We’re giving more than a billion dollars in direct assistance to Ukraine. And we’ll continue to aid the Ukrainian people as they defend their country and help ease their suffering.\\n' +\n", "      'But let me be clear: Our forces are not engaged and will not engage in the conflict with Russian forces in Ukraine. Our forces are not going to Europe to fight in Ukraine but to defend our NATO Allies in the event that <PERSON> decides to keep moving west.\\n' +\n", "      'For that purpose, we have mobilized American ground forces, air squadrons, ship deployments to protect NATO countries, including Poland, Romania, Latvia, Lithuania, and Estonia.\\n' +\n", "      'And as I’ve made crystal clear, the United States and our Allies will defend every inch of territory that is NATO territory with the full force of our collective power — every single inch.\\n' +\n", "      'And we’re clear-eyed. The Ukrainians are fighting back with pure courage. But the next few days, weeks, and months will be hard on them.\\n' +\n", "      '<PERSON> has unleashed violence and chaos. But while he may make gains on the battlefield, he will pay a continuing high price over the long run.\\n' +\n", "      'And a pound of Ukrainian people — the proud, proud people — pound for pound, ready to fight with every inch of (inaudible) they have. They’ve known 30 years of independence — have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.\\n' +\n", "      'To all Americans, I’ll be honest with you, as I’ve always promised I would be. A Russian dictator infa- — invading a foreign country has costs around the world. And I’m taking robust action to make sure the pain of our sanctions is targeted at the Russian economy and that we use every tool at our disposal to protect American businesses and consumers.\\n' +\n", "      'Tonight, I can announce the United States has worked with 30 other countries to release 60 million barrels of oil from reserves around the world. America will lead that effort, releasing 30 million barrels of our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, united with our Allies.\\n' +\n", "      'These steps will help blunt gas prices here at home. But I know news about what’s happening can seem alarming to all Americans. But I want you to know: We’re going to be okay. We’re going to be okay.\\n' +\n", "      'When the history of this era is written, <PERSON>’s war on Ukraine will have left Russia weaker and the rest of the world stronger.\\n' +\n", "      'While it shouldn’t and while it shouldn’t have taken something so terrible for people around the world to see what’s at stake, now everyone sees it clearly.\\n' +\n", "      'We see the unity among leaders of nations, a more unified Europe, a more unified West.\\n' +\n", "      'We see unity among the people who are gathering in cities in large crowds around the world, even in Russia, to demonstrate their support for the people of Ukraine.\\n' +\n", "      'In the battle between democracy and autocracies, democracies are rising to the moment and the world is clearly choosing the side of peace and security.\\n' +\n", "      'This is the real test, and it’s going to take time. So, let us continue to draw inspiration from the iron will of the Ukrainian people.\\n' +\n", "      'To our fellow Ukrainian Americans who forged a deep bond that connects our two nations: We stand with you. We stand with you.\\n' +\n", "      '<PERSON> may circle Kyiv with tanks, but he’ll never gain the hearts and souls of Ukrainian people. He’ll never — he’ll never extinguish their love of freedom. And he will never, never weaken the resolve of the free world.\\n' +\n", "      'We meet tonight in an America that has lived through two of the hardest years this nation has ever faced. The pandemic has been punishing. And so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more.\\n' +\n", "      'I understand, like many of you did. My dad had to leave his home in Scranton, Pennsylvania, to find work. So, like many of you, I grew up in a family when the price of food went up, it was felt throughout the family; it had an impact.\\n' +\n", "      'That’s why one of the first things I did as President was fight to pass the American Rescue Plan, because people were hurting. We needed to act and we did.\\n' +\n", "      'American Rescue Plan \\n' +\n", "      'Few pieces of legislation have done more at a critical moment in our history to lift us out of a crisis. It fueled our efforts to vaccinate the nation and combat COVID-19. It delivered immediate economic relief to tens of millions of Americans. It helped put food on the table. Remember those long lines of cars waiting for hours just to get a box of food put in their trunk? It cut the cost of healthcare insurance. And as my dad used to say, it gave the people “just a little bit of breathing room.”\\n' +\n", "      'And unlike the $2 trillion tax cut passed in the previous administration that benefitted the top 1 percent of Americans, the American Rescue Plan helped working people and left no one behind. And, folks — and it worked. It worked.\\n' +\n", "      'It worked and created jobs — lots of jobs. In fact, our economy created over 6.5 million new jobs just last year, more jobs in one year than ever before in the history of the United States of America.\\n' +\n", "      'Economic Progress Report \\n' +\n", "      'The economy grew at a rate of 5.7 last year — the strongest growth'... 35166 more characters,\n", "    metadata: {\n", "      score: 0.16303963959217072,\n", "      title: '2022 State of the Union Address | The White House',\n", "      id: 'https://www.whitehouse.gov/state-of-the-union-2022/',\n", "      url: 'https://www.whitehouse.gov/state-of-the-union-2022/',\n", "      publishedDate: '2022-02-25',\n", "      author: ''\n", "    },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: \"The President. Thank you all very, very much. Thank you, please. Thank you so much. <PERSON>am Speaker, <PERSON><PERSON> Vice President, and our First Lady and Second Gentleman, Members of Congress and the Cabinet, Justices of the Supreme Court, my fellow Americans: Last year, COVID-19 kept us apart. This year, we're finally together again.\\n\" +\n", "      'Tonight we meet as Democrats, Republicans, and Independents, but most importantly, as Americans with a duty to one another, to America, to the American people, and to the Constitution, and an unwavering resolve that freedom will always triumph over tyranny.\\n' +\n", "      \"Six—[applause]—thank you. Six days ago, Russia's <PERSON> sought to shake the very foundations of the free world, thinking he could make it bend to his menacing ways. But he badly miscalculated. He thought he could roll into Ukraine and the world would roll over. Instead, he met with a wall of strength he never anticipated or imagined. He met the Ukrainian people.\\n\" +\n", "      'From President <PERSON><PERSON><PERSON><PERSON><PERSON>, their—to every Ukrainian, their fearlessness, their courage, their determination literally inspires the world. Groups of citizens blocking tanks with their bodies. Everyone from students to retirees, to teachers turned soldiers defending their homeland. And in this struggle—President <PERSON><PERSON><PERSON><PERSON><PERSON> said in his speech to the European Parliament, \"Light will win over darkness.\"\\n' +\n", "      \"The Ukrainian Ambassador to the United States is here tonight sitting with the First Lady. Let each of us, if you're able to stand, stand and send an unmistakable signal to the world and Ukraine. Thank you. Thank you, thank you, thank you. She's bright, she's strong, and she's resolved. Yes. We, the United States of America, stand with the Ukrainian people.\\n\" +\n", "      \"Throughout our history, we've learned this lesson: When dictators do not pay a price for their aggression, they cause more chaos; they keep moving; and the costs, the threats to the America—and America, to the world keeps rising. That's why the NATO alliance was created: to secure peace and stability in Europe after World War II. The United States is a member, along with 29 other nations. It matters. American diplomacy matters. American resolve matters.\\n\" +\n", "      \"<PERSON>'s latest attack on Ukraine was premeditated and totally unprovoked. He rejected repeated—repeated—efforts at diplomacy. He thought the West and NATO wouldn't respond. He thought he could divide us at home, in this Chamber, in this Nation. He thought he could divide us in Europe as well.\\n\" +\n", "      \"But <PERSON> was wrong. We are ready. We are united. And that's what we did: We stayed united. We prepared extensively and carefully. We spent months building coalitions of other freedom-loving nations in Europe and the Americas to—from America to the Asian and African continents to confront <PERSON>.\\n\" +\n", "      \"Like many of you, I spent countless hours unifying our European allies. We shared with the world in advance what we knew <PERSON> was planning and precisely how he would try to falsify and justify his aggression. We countered Russia's lies with the truth. And now—now that he's acted, the free world is holding him accountable, along with 27 members of the European Union—including France, Germany, Italy—as well as countries like the United Kingdom, Canada, Japan, Korea, Australia, New Zealand, and many others—even Switzerland—are inflicting pain on Russia and supporting the people of Ukraine. <PERSON> is now isolated from the world more than he has ever been.\\n\" +\n", "      \"Together, along with our allies, we are right now enforcing powerful economic sanctions. We're cutting off Russia's largest banks from the international financial system; preventing Russia's Central Bank from defending the Russian ruble, making <PERSON>'s $630 billion war fund worthless. We're choking Russia's access to technology that will sap its economic strength and weaken its military for years to come.\\n\" +\n", "      'Tonight I say to the Russian oligarchs and the corrupt leaders who have bilked billions of dollars off this violent regime: No more. The United States—[applause]—I mean it. The United States Department of Justice is assembling a dedicated task force to go after the crimes of the Russian oligarchs.\\n' +\n", "      \"We're joining with European allies to find and seize their yachts, their luxury apartments, their private jets. We're coming for your ill-begotten gains. And tonight I'm announcing that we will join our allies in closing off American air space to all Russian flights, further isolating Russia and adding an additional squeeze on their economy.\\n\" +\n", "      \"He has no idea what's coming. The ruble has already lost 30 percent of its value, the Russian stock market has lost 40 percent of its value, and trading remains suspended. The Russian economy is reeling, and <PERSON> alone is the one to blame.\\n\" +\n", "      \"Together with our allies, we're providing support to the Ukrainians in their fight for freedom: military assistance, economic assistance, humanitarian assistance. We're giving more than a billion dollars in direct assistance to Ukraine. And we'll continue to aid the Ukrainian people as they defend their country and help ease their suffering.\\n\" +\n", "      \"But let me be clear: Our Forces are not engaged and will not engage in the conflict with Russian forces in Ukraine. Our Forces are not going to Europe to fight [in]* Ukraine but to defend our NATO allies in the event that <PERSON> decides to keep moving west. For that purpose, we have mobilized American ground forces, air squadrons, ship deployments to protect NATO countries, including Poland, Romania, Latvia, Lithuania, and Estonia. And as I've made crystal clear, the United States and our allies will defend every inch of territory that is NATO territory with the full force of our collective power—every single inch.\\n\" +\n", "      \"And we're clear eyed. The Ukrainians are fighting back with pure courage. But the next few days, weeks, and months will be hard on them. Putin has unleashed violence and chaos. But while he may make gains on the battlefield, he'll pay a continuing high price over the long run. And a pound of Ukrainian people—the proud, proud people—pound for pound, ready to fight with every inch of energy they have. They've known 30 years of independence—have repeatedly shown that they will not tolerate anyone who tries to take their country backwards.\\n\" +\n", "      \"To all Americans, I'll be honest with you, as I've always promised I would be. A Russian dictator invading a foreign country has costs around the world. And I'm taking robust action to make sure the pain of our sanctions is targeted at Russian economy and that we use every tool at our disposal to protect American businesses and consumers.\\n\" +\n", "      'Tonight I can announce the United States has worked with 30 other countries to release 60 million barrels of oil from reserves around the world. America will lead that effort, releasing 30 million barrels of our own Strategic Petroleum Reserve. And we stand ready to do more if necessary, united with our allies.\\n' +\n", "      \"These steps will help blunt gas prices here at home. But I know news about what's happening can seem alarming to all Americans. But I want you to know: We're going to be okay. We're going to be okay.\\n\" +\n", "      \"When the history of this era is written, <PERSON>'s war on Ukraine will have left Russia weaker and the rest of the world stronger.\\n\" +\n", "      \"While it shouldn't have taken something so terrible for people around the world to see what's at stake, now everyone sees it clearly. We see the unity among leaders of nations, a more unified Europe, a more unified West. We see unity among the people who are gathering in cities in large crowds around the world, even in Russia, to demonstrate their support for the people of Ukraine.\\n\" +\n", "      \"In the battle between democracy and autocracies, democracies are rising to the moment, and the world is clearly choosing the side of peace and security. This is the real test, and it's going to take time. So let us continue to draw inspiration from the iron will of the Ukrainian people.\\n\" +\n", "      \"To our fellow Ukrainian Americans who forged a deep bond that connects our two nations: We stand with you. We stand with you. Putin may circle Kiev with tanks, but he'll never gain the hearts and souls of the Uranian [Ukrainian]* people. He'll never extinguish their love of freedom. And he will never, never weaken the resolve of the free world.\\n\" +\n", "      'We meet tonight in an America that has lived through 2 of the hardest years this Nation has ever faced. The pandemic has been punishing. And so many families are living paycheck to paycheck, struggling to keep up with the rising cost of food, gas, housing, and so much more.\\n' +\n", "      \"I understand, like many of you did. My dad had to leave his home in Scranton, Pennsylvania, to find work. So, like many of you, I grew up in a family when the price of food went up, it was felt throughout the family; it had an impact. That's why one of the first things I did as President was fight to pass the American Rescue Plan, because people were hurting. We needed to act, and we did.\\n\" +\n", "      'Few pieces of legislation have done more at a critical moment in our history to lift us out of a crisis. It fueled our efforts to vaccinate the Nation and combat COVID-19. It delivered immediate economic relief to tens of millions of Americans. It helped put food on the table. Remember those long lines of cars waiting for hours just to get a box of food put in their trunk? It cut the cost of health care insurance. And as my dad used to say, it gave the people \"just a little bit of breathing room.\"\\n' +\n", "      'And unlike the $2 trillion tax cut passed in the previous administration that benefited the top 1 percent of Americans, the American Rescue Plan——\\n' +\n", "      ' Audience members. Boo!\\n' +\n", "      ' The President. ——the American Rescue Plan helped working people and left no one behind. And, folks—and it worked. It worked. It worked and created jobs, lots of jobs. In fact, our economy created over 6.5 million new jobs just last year, more jobs in 1 year than ever before in the history of the United States of America. The economy grew at a rate of 5.7 last year, the strongest growth rate in 40 years and the first step in'... 35254 more characters,\n", "    metadata: {\n", "      score: 0.16301880776882172,\n", "      title: 'Address Before a Joint Session of the Congress on the State of the Union',\n", "      id: 'https://www.presidency.ucsb.edu/documents/address-before-joint-session-the-congress-the-state-the-union-28',\n", "      url: 'https://www.presidency.ucsb.edu/documents/address-before-joint-session-the-congress-the-state-the-union-28',\n", "      publishedDate: '2022-03-01',\n", "      author: ''\n", "    },\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["const query = \"What did the speaker say about <PERSON> in the 2022 State of the Union?\";\n", "\n", "await retriever.invoke(query);"]}, {"cell_type": "markdown", "id": "dfe8aad4-8626-4330-98a9-7ea1ca5d2e0e", "metadata": {}, "source": ["## Use within a chain\n", "\n", "Like other retrievers, ExaRetriever can be incorporated into LLM applications via [chains](/docs/how_to/sequence/).\n", "\n", "We will need a LLM or chat model:\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 10, "id": "25b647a3-f8f2-4541-a289-7a241e43f9df", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "});"]}, {"cell_type": "code", "execution_count": 11, "id": "23e11cc9-abd6-4855-a7eb-799f45ca01ae", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { RunnablePassthrough, RunnableSequence } from \"@langchain/core/runnables\";\n", "import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "\n", "import type { Document } from \"@langchain/core/documents\";\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(`\n", "Answer the question based only on the context provided.\n", "\n", "Context: {context}\n", "\n", "Question: {question}`);\n", "\n", "const formatDocs = (docs: Document[]) => {\n", "  return docs.map((doc) => doc.pageContent).join(\"\\n\\n\");\n", "}\n", "\n", "// See https://js.langchain.com/docs/tutorials/rag\n", "const ragChain = RunnableSequence.from([\n", "  {\n", "    context: retriever.pipe(formatDocs),\n", "    question: new RunnablePassthrough(),\n", "  },\n", "  prompt,\n", "  llm,\n", "  new StringOutputParser(),\n", "]);"]}, {"cell_type": "code", "execution_count": 12, "id": "d47c37dd-5c11-416c-a3b6-bec413cd70e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["In the 2022 State of the Union Address, the speaker, President <PERSON><PERSON>, honored Justice <PERSON>, describing him as someone who has dedicated his life to serve the country. He acknowledged <PERSON> as an Army veteran and a constitutional scholar, and he expressed gratitude for his service. President <PERSON><PERSON> also mentioned that one of the most serious constitutional responsibilities of a President is nominating someone to serve on the United States Supreme Court, and he highlighted his nomination of <PERSON><PERSON><PERSON> to succeed <PERSON>.\n"]}], "source": ["await <PERSON><PERSON><PERSON><PERSON>.invoke(\"What did the speaker say about <PERSON> in the 2022 State of the Union?\");"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ExaRetriever features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_exa.ExaRetriever.html)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}