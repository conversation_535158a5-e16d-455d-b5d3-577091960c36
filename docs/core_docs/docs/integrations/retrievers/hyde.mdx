---
hide_table_of_contents: true
---

# HyDE Retriever

This example shows how to use the HyDE Retriever, which implements Hypothetical Document Embeddings (HyDE) as described in [this paper](https://arxiv.org/abs/2212.10496).

At a high level, HyDE is an embedding technique that takes queries, generates a hypothetical answer, and then embeds that generated document and uses that as the final example.

In order to use HyDE, we therefore need to provide a base embedding model, as well as an LLM that can be used to generate those documents. By default, the HyDE class comes with some default prompts to use (see the paper for more details on them), but we can also create our own, which should have a single input variable `{question}`.

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/retrievers/hyde.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Retriever [conceptual guide](/docs/concepts/retrievers)
- Retriever [how-to guides](/docs/how_to/#retrievers)
