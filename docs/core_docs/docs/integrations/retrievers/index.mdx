---
sidebar_position: 0
sidebar_class_name: hidden
---

import { CategoryTable, IndexTable } from "@theme/FeatureTables";

# Retrievers

A [retriever](/docs/concepts/retrievers) is an interface that returns documents given an unstructured query.
It is more general than a vector store.
A retriever does not need to be able to store documents, only to return (or retrieve) them.

Retrievers accept a string query as input and return a list of Documents.

For specifics on how to use retrievers, see the [relevant how-to guides here](/docs/how_to/#retrievers).

Note that all [vector stores](/docs/concepts/#vectorstores) can be [cast to retrievers](/docs/how_to/vectorstore_retriever/).
Refer to the vector store [integration docs](/docs/integrations/vectorstores/) for available vector store retrievers.

:::info
If you'd like to write your own retriever, see [this how-to](/docs/how_to/custom_retriever/). If you'd like to contribute an integration, see [Contributing integrations](/docs/contributing).
:::

## All retrievers

<IndexTable />
