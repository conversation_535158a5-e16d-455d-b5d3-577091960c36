{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Amazon Kendra Retriever\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON>endraRetriever\n", "\n", "## Overview\n", "\n", "[Amazon Kendra](https://aws.amazon.com/kendra/) is an intelligent search service provided by Amazon Web Services (AWS).\n", "It utilizes advanced natural language processing (NLP) and machine learning algorithms to enable powerful search capabilities across various data sources within an organization.\n", "Kendra is designed to help users find the information they need quickly and accurately, improving productivity and decision-making.\n", "\n", "With Kendra, users can search across a wide range of content types, including documents, FAQs, knowledge bases, manuals, and websites.\n", "It supports multiple languages and can understand complex queries, synonyms, and contextual meanings to provide highly relevant search results.\n", "\n", "This will help you getting started with the Amazon Kendra [`retriever`](/docs/concepts/retrievers). For detailed documentation of all `AWSKendraRetriever` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_aws.AmazonKendraRetriever.html).\n", "\n", "### Integration details\n", "\n", "| Retriever | Source | Package |\n", "| :--- | :--- | :---: |\n", "[AWSKendraRetriever](https://api.js.langchain.com/classes/langchain_aws.AmazonKendraRetriever.html) | Various AWS resources | [`@langchain/aws`](https://www.npmjs.com/package/@langchain/aws) |\n", "\n", "## Setup\n", "\n", "You'll need an AWS account and an Amazon Kendra instance to get started. See this [tutorial](https://docs.aws.amazon.com/kendra/latest/dg/getting-started.html) from AWS for more information.\n", "\n", "If you want to get automated tracing from individual queries, you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "// process.env.LANGSMITH_API_KEY = \"<YOUR API KEY HERE>\";\n", "// process.env.LANGSMITH_TRACING = \"true\";\n", "```\n", "\n", "### Installation\n", "\n", "This retriever lives in the `@langchain/aws` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/aws @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our retriever:"]}, {"cell_type": "code", "execution_count": null, "id": "70cc8e65-2a02-408a-bbc6-8ef649057d82", "metadata": {}, "outputs": [], "source": ["import { AmazonKendraRetriever } from \"@langchain/aws\";\n", "\n", "const retriever = new AmazonKendraRetriever({\n", "  topK: 10,\n", "  indexId: \"YOUR_INDEX_ID\",\n", "  region: \"us-east-2\", // Your region\n", "  clientOptions: {\n", "    credentials: {\n", "      accessKeyId: \"YOUR_ACCESS_KEY_ID\",\n", "      secretAccessKey: \"YOUR_SECRET_ACCESS_KEY\",\n", "    },\n", "  },\n", "});"]}, {"cell_type": "markdown", "id": "5c5f2839-4020-424e-9fc9-07777eede442", "metadata": {}, "source": ["## Usage"]}, {"cell_type": "code", "execution_count": null, "id": "51a60dbe-9f2e-4e04-bb62-23968f17164a", "metadata": {}, "outputs": [], "source": ["const query = \"...\"\n", "\n", "await retriever.invoke(query);"]}, {"cell_type": "markdown", "id": "dfe8aad4-8626-4330-98a9-7ea1ca5d2e0e", "metadata": {}, "source": ["## Use within a chain\n", "\n", "Like other retrievers, the `AWSKendraRetriever` can be incorporated into LLM applications via [chains](/docs/how_to/sequence/).\n", "\n", "We will need a LLM or chat model:\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "25b647a3-f8f2-4541-a289-7a241e43f9df", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "});"]}, {"cell_type": "code", "execution_count": null, "id": "23e11cc9-abd6-4855-a7eb-799f45ca01ae", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { RunnablePassthrough, RunnableSequence } from \"@langchain/core/runnables\";\n", "import { StringOutputParser } from \"@langchain/core/output_parsers\";\n", "\n", "import type { Document } from \"@langchain/core/documents\";\n", "\n", "const prompt = ChatPromptTemplate.fromTemplate(`\n", "Answer the question based only on the context provided.\n", "\n", "Context: {context}\n", "\n", "Question: {question}`);\n", "\n", "const formatDocs = (docs: Document[]) => {\n", "  return docs.map((doc) => doc.pageContent).join(\"\\n\\n\");\n", "}\n", "\n", "// See https://js.langchain.com/docs/tutorials/rag\n", "const ragChain = RunnableSequence.from([\n", "  {\n", "    context: retriever.pipe(formatDocs),\n", "    question: new RunnablePassthrough(),\n", "  },\n", "  prompt,\n", "  llm,\n", "  new StringOutputParser(),\n", "]);"]}, {"cell_type": "code", "execution_count": null, "id": "d47c37dd-5c11-416c-a3b6-bec413cd70e8", "metadata": {}, "outputs": [], "source": ["await rag<PERSON><PERSON><PERSON>.invoke(query);"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `AmazonKendraRetriever` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_aws.AmazonKendraRetriever.html)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "typescript", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 5}