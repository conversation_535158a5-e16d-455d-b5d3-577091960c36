---
hide_table_of_contents: true
---

# Metal Retriever

This example shows how to use the Metal Retriever in a retrieval chain to retrieve documents from a Metal index.

## Setup

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm i @getmetal/metal-sdk @langchain/community @langchain/core
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/retrievers/metal.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Retriever [conceptual guide](/docs/concepts/retrievers)
- Retriever [how-to guides](/docs/how_to/#retrievers)
