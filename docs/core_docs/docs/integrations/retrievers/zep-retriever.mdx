---
hide_table_of_contents: true
---

# Zep Open Source Retriever

> [Zep](https://www.getzep.com) is a long-term memory service for AI Assistant apps.
> With Zep, you can provide AI assistants with the ability to recall past conversations, no matter how distant,
> while also reducing hallucinations, latency, and cost.

> Interested in Zep Cloud? See [Zep Cloud Installation Guide](https://help.getzep.com/sdks)

This example shows how to use the Zep Retriever in a retrieval chain to retrieve documents from Zep Open Source memory store.

## Installation

Follow the [Zep Open Source Quickstart Guide](https://docs.getzep.com/deployment/quickstart/) to install and get started with Zep.

## Setup

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm i @getzep/zep-js @langchain/community @langchain/core
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/retrievers/zep.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Retriever [conceptual guide](/docs/concepts/retrievers)
- Retriever [how-to guides](/docs/how_to/#retrievers)
