---
sidebar_class_name: node-only
---

# <PERSON> KV

This example demonstrates how to setup chat history storage using the `CassandraKVStore` `BaseStore` integration. Note there is a `CassandraChatMessageHistory`
integration which may be easier to use for chat history storage; the `CassandraKVStore` is useful if you want a more general-purpose key-value store with
prefixable keys.

## Setup

```bash npm2yarn
npm install @langchain/community @langchain/core cassandra-driver
```

Depending on your database providers, the specifics of how to connect to the database will vary. We will create a document `configConnection` which will be used as part of the store configuration.

### Apache Cassandra®

Storage Attached Indexes (used by `yieldKeys`) are supported in [Apache Cassandra® 5.0](https://cassandra.apache.org/_/blog/Apache-Cassandra-5.0-Features-Storage-Attached-Indexes.html) and above. You can use a standard connection document, for example:

```typescript
const configConnection = {
  contactPoints: ['h1', 'h2'],
  localDataCenter: 'datacenter1',
  credentials: {
    username: <...> as string,
    password: <...> as string,
  },
};
```

### Astra DB

Astra DB is a cloud-native Cassandra-as-a-Service platform.

1. Create an [Astra DB account](https://astra.datastax.com/register).
2. Create a [vector enabled database](https://astra.datastax.com/createDatabase).
3. Create a [token](https://docs.datastax.com/en/astra/docs/manage-application-tokens.html) for your database.

```typescript
const configConnection = {
  serviceProviderArgs: {
    astra: {
      token: <...> as string,
      endpoint: <...> as string,
    },
  },
};
```

Instead of `endpoint:`, you many provide property `datacenterID:` and optionally `regionName:`.

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/stores/cassandra_storage.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- [Key-value store conceptual guide](/docs/concepts/key_value_stores)
