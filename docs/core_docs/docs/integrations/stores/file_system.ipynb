{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: File System Store\n", "sidebar_class_name: node-only\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# LocalFileStore\n", "\n", "```{=mdx}\n", "\n", ":::tip Compatibility\n", "\n", "Only available on Node.js.\n", "\n", ":::\n", "\n", "```\n", "\n", "This will help you get started with [LocalFileStore](/docs/concepts/key_value_stores). For detailed documentation of all LocalFileStore features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain.storage_file_system.LocalFileStore.html).\n", "\n", "## Overview\n", "\n", "The `LocalFileStore` is a wrapper around the `fs` module for storing data as key-value pairs.\n", "Each key value pair has its own file nested inside the directory passed to the `.fromPath` method.\n", "The file name is the key and inside contains the value of the key.\n", "\n", "```{=mdx}\n", "\n", ":::info\n", "\n", "The path passed to the `.fromPath` must be a directory, not a file.\n", "\n", ":::\n", "\n", ":::warning\n", "\n", "\n", "This file store can alter any text file in the provided directory and any subfolders.\n", "Make sure that the path you specify when initializing the store is free of other files.\n", "\n", ":::\n", "\n", "```\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | [PY support](https://python.langchain.com/docs/integrations/stores/file_system/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [LocalFileStore](https://api.js.langchain.com/classes/langchain.storage_file_system.LocalFileStore.html) | [langchain](https://api.js.langchain.com/modules/langchain.storage_file_system.html) | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/langchain?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/langchain?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "### Installation\n", "\n", "The LangChain `LocalFileStore` integration lives in the `langchain` package:\n", "\n", "```{=mdx}\n", "\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our byte store:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import { LocalFileStore } from \"langchain/storage/file_system\"\n", "\n", "const kvStore = await LocalFileStore.fromPath(\"./messages\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define an encoder and decoder for converting the data to `Uint8Array` and back:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["const encoder = new TextEncoder();\n", "const decoder = new TextDecoder();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage\n", "\n", "You can set data under keys like this using the `mset` method:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 'value1', 'value2' ]\n"]}], "source": ["await kvStore.mset(\n", "  [\n", "    [\"key1\", encoder.encode(\"value1\")],\n", "    [\"key2\", encoder.encode(\"value2\")],\n", "  ]\n", ")\n", "\n", "const results = await kvStore.mget(\n", "  [\n", "    \"key1\",\n", "    \"key2\",\n", "  ]\n", ")\n", "console.log(results.map((v) => decoder.decode(v)));"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And you can delete data using the `mdelete` method:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ undefined, undefined ]\n"]}], "source": ["await kvStore.mdelete(\n", "  [\n", "    \"key1\",\n", "    \"key2\",\n", "  ]\n", ")\n", "\n", "await kvStore.mget(\n", "  [\n", "    \"key1\",\n", "    \"key2\",\n", "  ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Yielding values\n", "\n", "If you want to get back all the keys you can call the `yieldKeys` method. Optionally, you can pass a key prefix to only get back keys which match that prefix."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 'message:id:key1', 'message:id:key2' ]\n"]}], "source": ["import { LocalFileStore } from \"langchain/storage/file_system\"\n", "\n", "const kvStoreForYield = await LocalFileStore.fromPath(\"./messages\");\n", "\n", "const encoderForYield = new TextEncoder();\n", "\n", "// Add some data to the store\n", "await kvStoreForYield.mset(\n", "  [\n", "    [\"message:id:key1\", encoderForYield.encode(\"value1\")],\n", "    [\"message:id:key2\", encoderForYield.encode(\"value2\")],\n", "  ]\n", ")\n", "\n", "const yieldedKeys = [];\n", "for await (const key of kvStoreForYield.yieldKeys(\"message:id:\")) {\n", "  yieldedKeys.push(key);\n", "}\n", "\n", "console.log(yieldedKeys);"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import fs from \"fs\";\n", "\n", "// Cleanup\n", "await fs.promises.rm(\"./messages\", { recursive: true, force: true });"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all LocalFileStore features and configurations, head to the [API reference](https://api.js.langchain.com/classes/langchain_storage_file_system.LocalFileStore.html)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}