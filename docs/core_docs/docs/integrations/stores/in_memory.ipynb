{"cells": [{"cell_type": "raw", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: InMemory Store\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# InMemoryStore\n", "\n", "This will help you get started with [InMemoryStore](/docs/concepts/key_value_stores). For detailed documentation of all InMemoryStore features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_core.stores.InMemoryStore.html).\n", "\n", "The `InMemoryStore` allows for a generic type to be assigned to the values in the store. We'll assign type `BaseMessage` as the type of our values, keeping with the theme of a chat history store.\n", "\n", "## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | Local | [PY support](https://python.langchain.com/docs/integrations/stores/in_memory/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [InMemoryStore](https://api.js.langchain.com/classes/langchain_core.stores.InMemoryStore.html) | [@langchain/core](https://api.js.langchain.com/modules/langchain_core.stores.html) | ✅ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/core?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/core?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "### Installation\n", "\n", "The LangChain InMemoryStore integration lives in the `@langchain/core` package:\n", "\n", "```{=mdx}\n", "\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/core\n", "</Npm2Yarn>\n", "\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our byte store:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import { InMemoryStore } from \"@langchain/core/stores\"\n", "import { BaseMessage } from \"@langchain/core/messages\";\n", "\n", "const kvStore = new InMemoryStore<BaseMessage>();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Usage\n", "\n", "You can set data under keys like this using the `mset` method:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  HumanMessage {\n", "    \"content\": \"value1\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {}\n", "  },\n", "  AIMessage {\n", "    \"content\": \"value2\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {},\n", "    \"tool_calls\": [],\n", "    \"invalid_tool_calls\": []\n", "  }\n", "]\n"]}], "source": ["import { AIMessage, HumanMessage } from \"@langchain/core/messages\";\n", "\n", "await kvStore.mset(\n", "  [\n", "    [\"key1\", new HumanMessage(\"value1\")],\n", "    [\"key2\", new AIMessage(\"value2\")],\n", "  ]\n", ")\n", "\n", "await kvStore.mget(\n", "  [\n", "    \"key1\",\n", "    \"key2\",\n", "  ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And you can delete data using the `mdelete` method:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ undefined, undefined ]\n"]}], "source": ["await kvStore.mdelete(\n", "  [\n", "    \"key1\",\n", "    \"key2\",\n", "  ]\n", ")\n", "\n", "await kvStore.mget(\n", "  [\n", "    \"key1\",\n", "    \"key2\",\n", "  ]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Yielding values\n", "\n", "If you want to get back all the keys you can call the `yieldKeys` method. Optionally, you can pass a key prefix to only get back keys which match that prefix."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 'message:id:key1', 'message:id:key2' ]\n"]}], "source": ["import { InMemoryStore } from \"@langchain/core/stores\"\n", "import { AIMessage, BaseMessage, HumanMessage } from \"@langchain/core/messages\";\n", "\n", "const kvStoreForYield = new InMemoryStore<BaseMessage>();\n", "\n", "// Add some data to the store\n", "await kvStoreForYield.mset(\n", "  [\n", "    [\"message:id:key1\", new HumanMessage(\"value1\")],\n", "    [\"message:id:key2\", new AIMessage(\"value2\")],\n", "  ]\n", ")\n", "\n", "const yieldedKeys = [];\n", "for await (const key of kvStoreForYield.yieldKeys(\"message:id:\")) {\n", "  yieldedKeys.push(key);\n", "}\n", "\n", "console.log(yieldedKeys);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all InMemoryStore features and configurations, head to the [API reference](https://api.js.langchain.com/classes/langchain_core.stores.InMemoryStore.html)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}