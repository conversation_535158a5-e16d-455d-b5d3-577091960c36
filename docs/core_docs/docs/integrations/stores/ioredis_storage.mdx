# IORedis

This example demonstrates how to setup chat history storage using the `RedisByteStore` `BaseStore` integration.

## Setup

```bash npm2yarn
npm install @langchain/community @langchain/core ioredis
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/stores/ioredis_storage.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- [Key-value store conceptual guide](/docs/concepts/key_value_stores)
