# Upstash Redis

This example demonstrates how to setup chat history storage using the `UpstashRedisStore` `BaseStore` integration.

## Setup

```bash npm2yarn
npm install @langchain/community @langchain/core @upstash/redis
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/stores/upstash_redis_storage.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- [Key-value store conceptual guide](/docs/concepts/key_value_stores)
