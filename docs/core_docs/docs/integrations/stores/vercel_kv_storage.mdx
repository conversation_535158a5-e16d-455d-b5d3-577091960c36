# Vercel KV

This example demonstrates how to setup chat history storage using the `VercelKVStore` `BaseStore` integration.

## Setup

```bash npm2yarn
npm install @langchain/community @langchain/core @vercel/kv
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/stores/vercel_kv_storage.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- [Key-value store conceptual guide](/docs/concepts/key_value_stores)
