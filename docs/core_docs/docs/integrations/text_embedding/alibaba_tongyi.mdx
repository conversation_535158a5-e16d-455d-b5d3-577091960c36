---
sidebar_class_name: node-only
---

# <PERSON><PERSON><PERSON>

The `AlibabaTongyiEmbeddings` class uses the Alibaba Tongyi API to generate embeddings for a given text.

## Setup

You'll need to sign up for an Alibaba API key and set it as an environment variable named `ALIBABA_API_KEY`.

Then, you'll need to install the [`@langchain/community`](https://www.npmjs.com/package/@langchain/community) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import AlibabaTongyiExample from "@examples/embeddings/alibaba_tongyi.ts";

<CodeBlock language="typescript">{AlibabaTongyiExample}</CodeBlock>

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
