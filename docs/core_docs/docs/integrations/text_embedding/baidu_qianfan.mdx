# Bai<PERSON> Qianfan

The `BaiduQianfanEmbeddings` class uses the Baidu Qianfan API to generate embeddings for a given text.

## Setup

An API key is required to use this embedding model. You can get one by registering at https://cloud.baidu.com/doc/WENXINWORKSHOP/s/alj562vvu.

Please set the acquired API key as an environment variable named BAIDU_API_KEY, and set your secret key as an environment variable named BAIDU_SECRET_KEY.

Then, you'll need to install the [`@langchain/baidu-qianfan`](https://www.npmjs.com/package/@langchain/baidu-qianfan) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/baidu-qianfan @langchain/core
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import BaiduQianFanExample from "@examples/embeddings/baidu_qianfan.ts";

<CodeBlock language="typescript">{BaiduQianFanExample}</CodeBlock>

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
