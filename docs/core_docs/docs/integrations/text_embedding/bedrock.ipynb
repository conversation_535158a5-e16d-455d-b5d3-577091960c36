{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Amazon Bedrock\n", "---"]}, {"cell_type": "markdown", "id": "9a3d6f34", "metadata": {}, "source": ["# BedrockEmbeddings\n", "\n", "[Amazon Bedrock](https://aws.amazon.com/bedrock/) is a fully managed service that offers a choice of high-performing foundation models (FMs) from leading AI companies like AI21 Labs, Anthropic, Cohere, Meta, Stability AI, and Amazon via a single API, along with a broad set of capabilities you need to build generative AI applications with security, privacy, and responsible AI.\n", "\n", "This will help you get started with Amazon Bedrock [embedding models](/docs/concepts/embedding_models) using LangChain. For detailed documentation on `Bedrock` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_aws.BedrockEmbeddings.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | [Py support](https://python.langchain.com/docs/integrations/text_embedding/bedrock/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [Bedrock](https://api.js.langchain.com/classes/langchain_aws.BedrockEmbeddings.html) | [@langchain/aws](https://api.js.langchain.com/modules/langchain_aws.html) | ❌ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/aws?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/aws?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Bedrock embedding models you'll need to create an AWS account, get an API key, and install the `@langchain/aws` integration package.\n", "\n", "Head to the [AWS docs](https://docs.aws.amazon.com/bedrock/latest/userguide/getting-started.html) to sign up for AWS and setup your credentials. You'll also need to turn on model access for your account, which you can do by [following these instructions](https://docs.aws.amazon.com/bedrock/latest/userguide/model-access.html).\n", "\n", "### Credentials\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain Bedrock integration lives in the `@langchain/aws` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/aws @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "45dd1724", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and embed text.\n", "\n", "There are a few different ways to authenticate with AWS - the below examples rely on an access key, secret access key and region set in your environment variables:"]}, {"cell_type": "code", "execution_count": 1, "id": "9ea7a09b", "metadata": {}, "outputs": [], "source": ["import { BedrockEmbeddings } from \"@langchain/aws\";\n", "\n", "const embeddings = new BedrockEmbeddings({\n", "  region: process.env.BEDROCK_AWS_REGION!,\n", "  credentials: {\n", "    accessKeyId: process.env.BEDROCK_AWS_ACCESS_KEY_ID!,\n", "    secretAccessKey: process.env.BEDROCK_AWS_SECRET_ACCESS_KEY!,\n", "  },\n", "  model: \"amazon.titan-embed-text-v1\",\n", "});"]}, {"cell_type": "markdown", "id": "77d271b6", "metadata": {}, "source": ["## Indexing and Retrieval\n", "\n", "Embedding models are often used in retrieval-augmented generation (RAG) flows, both as part of indexing data as well as later retrieving it. For more detailed instructions, please see our RAG tutorials under the [working with external knowledge tutorials](/docs/tutorials/#working-with-external-knowledge).\n", "\n", "Below, see how to index and retrieve data using the `embeddings` object we initialized above. In this example, we will index and retrieve a sample document using the demo [`MemoryVectorStore`](/docs/integrations/vectorstores/memory)."]}, {"cell_type": "code", "execution_count": 2, "id": "d817716b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LangChain is the framework for building context-aware reasoning applications\n"]}], "source": ["// Create a vector store with a sample text\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const text = \"LangChain is the framework for building context-aware reasoning applications\";\n", "\n", "const vectorstore = await MemoryVectorStore.fromDocuments(\n", "  [{ pageContent: text, metadata: {} }],\n", "  embeddings,\n", ");\n", "\n", "// Use the vector store as a retriever that returns a single document\n", "const retriever = vectorstore.asRetriever(1);\n", "\n", "// Retrieve the most similar text\n", "const retrievedDocuments = await retriever.invoke(\"What is <PERSON><PERSON><PERSON><PERSON>?\");\n", "\n", "retrievedDocuments[0].pageContent;"]}, {"cell_type": "markdown", "id": "e02b9855", "metadata": {}, "source": ["## Direct Usage\n", "\n", "Under the hood, the vectorstore and retriever implementations are calling `embeddings.embedDocument(...)` and `embeddings.embedQuery(...)` to create embeddings for the text(s) used in `fromDocuments` and the retriever's `invoke` operations, respectively.\n", "\n", "You can directly call these methods to get embeddings for your own use cases.\n", "\n", "### Embed single texts\n", "\n", "You can embed queries for search with `embedQuery`. This generates a vector representation specific to the query:"]}, {"cell_type": "code", "execution_count": 3, "id": "0d2befcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "         0.625,  0.111328125,      0.265625,   -0.20019531,  0.40820312,\n", "  -0.010803223,  -0.22460938, -0.0002937317,    0.29882812, -0.14355469,\n", "  -0.068847656,   -0.3984375,          0.75,    -0.1953125,  -0.5546875,\n", "  -0.087402344,       0.5625,      1.390625,    -0.3515625,  0.39257812,\n", "  -0.061767578,      0.65625,   -0.36328125,   -0.06591797,    0.234375,\n", "   -0.36132812,   0.42382812,  -0.115234375,   -0.28710938, -0.29296875,\n", "     -0.765625,  -0.16894531,    0.23046875,     0.6328125, -0.08544922,\n", "    0.13671875, 0.0004272461,        0.3125,    0.12207031,   -0.546875,\n", "    0.14257812, -0.119628906,  -0.111328125,    0.61328125,      0.6875,\n", "     0.3671875,   -0.2578125,   -0.27734375,      0.703125,    0.203125,\n", "    0.17675781,  -0.26757812,   -0.76171875,    0.71484375,  0.77734375,\n", "    -0.1953125, -0.007232666,  -0.044921875,    0.23632812, -0.24121094,\n", "  -0.012207031,    0.5078125,    0.08984375,    0.56640625,  -0.3046875,\n", "     0.6484375,        -0.25,   -0.37890625,    -0.2421875,  0.38476562,\n", "   -0.18164062,  -0.05810547,     0.7578125,    0.04296875,    0.609375,\n", "    0.50390625,  0.023803711,   -0.23046875,   0.099121094,  0.79296875,\n", "     -1.296875,     0.671875,   -0.66796875,    0.43359375, 0.087890625,\n", "    0.14550781,  -0.37304688,  -0.068359375, 0.00012874603, -0.47265625,\n", "     -0.765625,   0.07861328,  -0.029663086,   0.076660156, -0.32617188,\n", "     -0.453125,   -0.5546875,   -0.45703125,     1.1015625, -0.29492188\n", "]\n"]}], "source": ["const singleVector = await embeddings.embedQuery(text);\n", "\n", "console.log(singleVector.slice(0, 100));"]}, {"cell_type": "markdown", "id": "1b5a7d03", "metadata": {}, "source": ["### Embed multiple texts\n", "\n", "You can embed multiple texts for indexing with `embedDocuments`. The internals used for this method may (but do not have to) differ from embedding queries:"]}, {"cell_type": "code", "execution_count": 4, "id": "2f4d6e97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "         0.625,  0.111328125,      0.265625,   -0.20019531,  0.40820312,\n", "  -0.010803223,  -0.22460938, -0.0002937317,    0.29882812, -0.14355469,\n", "  -0.068847656,   -0.3984375,          0.75,    -0.1953125,  -0.5546875,\n", "  -0.087402344,       0.5625,      1.390625,    -0.3515625,  0.39257812,\n", "  -0.061767578,      0.65625,   -0.36328125,   -0.06591797,    0.234375,\n", "   -0.36132812,   0.42382812,  -0.115234375,   -0.28710938, -0.29296875,\n", "     -0.765625,  -0.16894531,    0.23046875,     0.6328125, -0.08544922,\n", "    0.13671875, 0.0004272461,        0.3125,    0.12207031,   -0.546875,\n", "    0.14257812, -0.119628906,  -0.111328125,    0.61328125,      0.6875,\n", "     0.3671875,   -0.2578125,   -0.27734375,      0.703125,    0.203125,\n", "    0.17675781,  -0.26757812,   -0.76171875,    0.71484375,  0.77734375,\n", "    -0.1953125, -0.007232666,  -0.044921875,    0.23632812, -0.24121094,\n", "  -0.012207031,    0.5078125,    0.08984375,    0.56640625,  -0.3046875,\n", "     0.6484375,        -0.25,   -0.37890625,    -0.2421875,  0.38476562,\n", "   -0.18164062,  -0.05810547,     0.7578125,    0.04296875,    0.609375,\n", "    0.50390625,  0.023803711,   -0.23046875,   0.099121094,  0.79296875,\n", "     -1.296875,     0.671875,   -0.66796875,    0.43359375, 0.087890625,\n", "    0.14550781,  -0.37304688,  -0.068359375, 0.00012874603, -0.47265625,\n", "     -0.765625,   0.07861328,  -0.029663086,   0.076660156, -0.32617188,\n", "     -0.453125,   -0.5546875,   -0.45703125,     1.1015625, -0.29492188\n", "]\n", "[\n", "       0.65625,    0.48242188,    0.70703125,   -0.13378906,    0.859375,\n", "     0.2578125,   -0.13378906, -0.0002670288,      -0.34375,  0.25585938,\n", "   -0.33984375,   -0.26367188,      0.828125,   -0.23242188, -0.61328125,\n", "    0.12695312,    0.43359375,     1.3828125,  -0.099121094,   0.3203125,\n", "   -0.34765625,    0.35351562,   -0.28710938,   0.009521484, 0.083496094,\n", "   0.040283203,   -0.25390625,    0.17871094,   0.044189453, -0.19628906,\n", "    0.45898438,    0.21191406,    0.67578125,     0.8359375, -0.29101562,\n", "   0.021118164,    0.13671875,   0.083984375,    0.34570312,  0.30859375,\n", "  -0.001625061,    0.31835938,   -0.18164062, -0.0058288574,  0.22460938,\n", "    0.26757812,   -0.09082031,    0.17480469,     1.4921875, -0.24316406,\n", "    0.36523438,    0.14550781,     -0.609375,    0.33007812,  0.10595703,\n", "     0.3671875,    0.18359375,   -0.62109375,    0.51171875, 0.024047852,\n", "   0.092285156,   -0.44335938,     0.4921875,      0.609375, -0.48242188,\n", "      0.796875,   -0.47851562,      -0.53125,   -0.66796875,  0.68359375,\n", "   -0.16796875,   0.110839844,    0.84765625,      0.703125,   0.8671875,\n", "    0.37695312, -0.0022888184,   -0.30664062,     0.3671875,  0.16503906,\n", "   -0.59765625,     0.3203125,      -0.34375,    0.08251953,    0.890625,\n", "    0.38476562,   -0.24707031,        -0.125, 0.00013160706, -0.69921875,\n", "      -0.53125,   0.052490234,    0.27734375,    0.42773438, -0.38867188,\n", "    -0.2578125,         -0.25,      -0.46875,      0.828125, -0.94140625\n", "]\n"]}], "source": ["const text2 = \"LangGraph is a library for building stateful, multi-actor applications with LLMs\";\n", "\n", "const vectors = await embeddings.embedDocuments([text, text2]);\n", "\n", "console.log(vectors[0].slice(0, 100));\n", "console.log(vectors[1].slice(0, 100));"]}, {"cell_type": "markdown", "id": "1d337b4e", "metadata": {}, "source": ["## Configuring the Bedrock Runtime Client\n", "\n", "You can pass in your own instance of the `BedrockRuntimeClient` if you want to customize options like\n", "`credentials`, `region`, `retryPolicy`, etc."]}, {"cell_type": "code", "execution_count": null, "id": "c3eb2444", "metadata": {}, "outputs": [], "source": ["import { BedrockRuntimeClient } from \"@aws-sdk/client-bedrock-runtime\";\n", "import { BedrockEmbeddings } from \"@langchain/aws\";\n", "\n", "const getCredentials = () => {\n", "  // do something to get credentials\n", "}\n", "\n", "// @lc-ts-ignore\n", "const client = new BedrockRuntimeClient({\n", "  region: \"us-east-1\",\n", "  credentials: getCredentials(),\n", "});\n", "\n", "const embeddingsWithCustomClient = new BedrockEmbeddings({\n", "  client,\n", "});"]}, {"cell_type": "markdown", "id": "8938e581", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all Bedrock features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_aws.BedrockEmbeddings.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}