{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: ByteDance Doubao\n", "---"]}, {"cell_type": "markdown", "id": "9a3d6f34", "metadata": {}, "source": ["# ByteDanceDoubaoEmbeddings\n", "\n", "This will help you get started with ByteDanceDoubao [embedding models](/docs/concepts/embedding_models) using LangChain.  For detailed documentation on `ByteDanceDoubaoEmbeddings` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/_langchain_community.embeddings_bytedance_doubao.ByteDanceDoubaoEmbeddings.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Py support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [ByteDanceDoubaoEmbeddings](https://api.js.langchain.com/classes/_langchain_community.embeddings_bytedance_doubao.ByteDanceDoubaoEmbeddings.html) | [@langchain/community](https://api.js.langchain.com/modules/_langchain_community.html) | ❌ | ❌ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "You'll need to sign up for an [ARK API key](https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey) and set it as an environment variable named `ARK_API_KEY`. Then you should [create a entrypoint](https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint) for embedding models, and use the entrypoint's name as `model`.\n", "\n", "Then, you'll need to install the [`@langchain/community`](https://www.npmjs.com/package/@langchain/community) package\n", "\n", "### Credentials\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGCHAIN_TRACING_V2=\"true\"\n", "# export LANGCHAIN_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain ByteDanceDoubaoEmbeddings integration lives in the `@langchain/community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "45dd1724", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and embed text:"]}, {"cell_type": "code", "execution_count": 1, "id": "9ea7a09b", "metadata": {}, "outputs": [], "source": ["import { ByteDanceDoubaoEmbeddings } from \"@langchain/community/embeddings/bytedance_doubao\";\n", "\n", "const embeddings = new ByteDanceDoubaoEmbeddings({\n", "  model: 'ep-xxx-xxx' // your entrypoint's name\n", "});"]}, {"cell_type": "markdown", "id": "77d271b6", "metadata": {}, "source": ["## Indexing and Retrieval\n", "\n", "Embedding models are often used in retrieval-augmented generation (RAG) flows, both as part of indexing data as well as later retrieving it. For more detailed instructions, please see our RAG tutorials under the [working with external knowledge tutorials](/docs/tutorials/#working-with-external-knowledge).\n", "\n", "Below, see how to index and retrieve data using the `embeddings` object we initialized above. In this example, we will index and retrieve a sample document using the demo [`MemoryVectorStore`](/docs/integrations/vectorstores/memory)."]}, {"cell_type": "code", "execution_count": 2, "id": "d817716b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LangChain is the framework for building context-aware reasoning applications\n"]}], "source": ["// Create a vector store with a sample text\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const text = \"LangChain is the framework for building context-aware reasoning applications\";\n", "\n", "const vectorstore = await MemoryVectorStore.fromDocuments(\n", "  [{ pageContent: text, metadata: {} }],\n", "  embeddings,\n", ");\n", "\n", "// Use the vector store as a retriever that returns a single document\n", "const retriever = vectorstore.asRetriever(1);\n", "\n", "// Retrieve the most similar text\n", "const retrievedDocuments = await retriever.invoke(\"What is <PERSON><PERSON><PERSON><PERSON>?\");\n", "\n", "retrievedDocuments[0].pageContent;"]}, {"cell_type": "markdown", "id": "e02b9855", "metadata": {}, "source": ["## Direct Usage\n", "\n", "Under the hood, the vectorstore and retriever implementations are calling `embeddings.embedDocument(...)` and `embeddings.embedQuery(...)` to create embeddings for the text(s) used in `fromDocuments` and the retriever's `invoke` operations, respectively.\n", "\n", "You can directly call these methods to get embeddings for your own use cases.\n", "\n", "### Embed single texts\n", "\n", "You can embed queries for search with `embedQuery`. This generates a vector representation specific to the query:"]}, {"cell_type": "code", "execution_count": 3, "id": "0d2befcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "   0.026051683,   0.029081265,  -0.040726297,  -0.015116953, -0.010691089,\n", "   0.030181013, -0.0065084146,   -0.02079503,   0.013575795,   0.03452527,\n", "   0.009578291,   0.007026421,  -0.030110886,   0.013489622,  -0.04294787,\n", "   0.011141899,  -0.043768786,   -0.00362867, -0.0081198225,  -0.03426076,\n", "   0.010075142,   0.027787417,   -0.09052663,   -0.06039698, -0.009462592,\n", "    0.06232288,   0.051121354,   0.011977532,   0.089046724,  0.059000008,\n", "   0.031860664,  -0.034242127,   0.020339863,   0.011483523,  -0.05429335,\n", "   -0.04963588,    0.03263794,   -0.05581542,   0.013908403, -0.012356067,\n", "  -0.007802118,  -0.010027855,    0.00281217,  -0.101886116, -0.079341754,\n", "   0.011269771,  0.0035983133,  -0.027667878,   0.032092705, -0.052843474,\n", "  -0.045283325,     0.0382421,     0.0193055,   0.011050924,  0.021132186,\n", "  -0.037696265,  0.0006107435,  0.0043520257,  -0.028798066,  0.049155913,\n", "    0.03590549, -0.0040995986,   0.019772101,  -0.076119535, 0.0031298609,\n", "    0.03368174,   0.039398745,  -0.011813277,  -0.019313531, -0.013108803,\n", "  -0.044905286,  -0.022326004,   -0.01656178,   -0.06658457,  0.016789088,\n", "   0.049952697,   0.006615693,   -0.01694402,  -0.018105473, 0.0049101883,\n", "  -0.004966945,   0.049762275,   -0.03556957,  -0.015986584,  -0.03190983,\n", "   -0.05336687, -0.0020468342, -0.0016106658,  -0.035291273, -0.029783724,\n", "  -0.010153295,   0.052100364,    0.05528949,    0.01379487, -0.024542747,\n", "   0.028773975,   0.010087022,   0.030448131,  -0.042391222,  0.016596776\n", "]\n"]}], "source": ["const singleVector = await embeddings.embedQuery(text);\n", "\n", "console.log(singleVector.slice(0, 100));"]}, {"cell_type": "markdown", "id": "1b5a7d03", "metadata": {}, "source": ["### Embed multiple texts\n", "\n", "You can embed multiple texts for indexing with `embedDocuments`. The internals used for this method may (but do not have to) differ from embedding queries:"]}, {"cell_type": "code", "execution_count": 4, "id": "2f4d6e97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "   0.026051683,   0.029081265,  -0.040726297,  -0.015116953, -0.010691089,\n", "   0.030181013, -0.0065084146,   -0.02079503,   0.013575795,   0.03452527,\n", "   0.009578291,   0.007026421,  -0.030110886,   0.013489622,  -0.04294787,\n", "   0.011141899,  -0.043768786,   -0.00362867, -0.0081198225,  -0.03426076,\n", "   0.010075142,   0.027787417,   -0.09052663,   -0.06039698, -0.009462592,\n", "    0.06232288,   0.051121354,   0.011977532,   0.089046724,  0.059000008,\n", "   0.031860664,  -0.034242127,   0.020339863,   0.011483523,  -0.05429335,\n", "   -0.04963588,    0.03263794,   -0.05581542,   0.013908403, -0.012356067,\n", "  -0.007802118,  -0.010027855,    0.00281217,  -0.101886116, -0.079341754,\n", "   0.011269771,  0.0035983133,  -0.027667878,   0.032092705, -0.052843474,\n", "  -0.045283325,     0.0382421,     0.0193055,   0.011050924,  0.021132186,\n", "  -0.037696265,  0.0006107435,  0.0043520257,  -0.028798066,  0.049155913,\n", "    0.03590549, -0.0040995986,   0.019772101,  -0.076119535, 0.0031298609,\n", "    0.03368174,   0.039398745,  -0.011813277,  -0.019313531, -0.013108803,\n", "  -0.044905286,  -0.022326004,   -0.01656178,   -0.06658457,  0.016789088,\n", "   0.049952697,   0.006615693,   -0.01694402,  -0.018105473, 0.0049101883,\n", "  -0.004966945,   0.049762275,   -0.03556957,  -0.015986584,  -0.03190983,\n", "   -0.05336687, -0.0020468342, -0.0016106658,  -0.035291273, -0.029783724,\n", "  -0.010153295,   0.052100364,    0.05528949,    0.01379487, -0.024542747,\n", "   0.028773975,   0.010087022,   0.030448131,  -0.042391222,  0.016596776\n", "]\n", "[\n", "      0.0558515,   0.028698817,  -0.037476595,  0.0048659276,  -0.019229038,\n", "    -0.04713716,  -0.020947812,  -0.017550547,    0.01205507,   0.027693441,\n", "   -0.011791304,   0.009862203,   0.019662278,  -0.037511427,  -0.022662448,\n", "    0.036224432,  -0.051760387,  -0.030165697,  -0.008899774,  -0.024518963,\n", "    0.010077767,   0.032209765,    -0.0854303,  -0.038666975,  -0.036021013,\n", "    0.060899545,   0.045867186,   0.003365381,    0.09387081,   0.038216405,\n", "    0.011449426,  -0.016495887,   0.020602569,   -0.02368503,  -0.014733645,\n", "   -0.065408126, -0.0065152845,  -0.027103946, 0.00038956117,   -0.08648814,\n", "    0.029316466,  -0.054449145,   0.034129277,  -0.055225655,  -0.043182302,\n", "   0.0011148591,   0.044116337,  -0.046552557,   0.032423045,   -0.03269365,\n", "    -0.05062933,   0.021473562,  -0.011019348,  -0.019621233, -0.0003149565,\n", "  -0.0046085776,  0.0052610254, -0.0029293327,  -0.035793293,   0.034469575,\n", "    0.037724957,   0.009572597,   0.014198464,    -0.0878237,  0.0056973165,\n", "    0.023563445,   0.030928325,   0.025520306,    0.01836824,  -0.016456697,\n", "   -0.061934732,   0.009764942,  -0.035812028,   -0.04429064,   0.031323086,\n", "    0.056027107, -0.0019782048,  -0.015204176,  -0.008684945, -0.0010460864,\n", "    0.054642987,   0.044149086,  -0.032964867,  -0.012044753,  -0.019075096,\n", "   -0.027932597,   0.018542245,   -0.02602878,   -0.04645578,  -0.020976603,\n", "    0.018999187,   0.050663687,   0.016725155,  0.0076955976,   0.011448177,\n", "    0.053931057,   -0.03234989,   0.024429373,  -0.023123834,    0.02197912\n", "]\n"]}], "source": ["const text2 = \"LangGraph is a library for building stateful, multi-actor applications with LLMs\";\n", "\n", "const vectors = await embeddings.embedDocuments([text, text2]);\n", "\n", "console.log(vectors[0].slice(0, 100));\n", "console.log(vectors[1].slice(0, 100));"]}, {"cell_type": "markdown", "id": "b48d41bb", "metadata": {}, "source": ["## Related\n", "\n", "- Embedding model [conceptual guide](/docs/concepts/embedding_models)\n", "- Embedding model [how-to guides](/docs/how_to/#embedding-models)"]}, {"cell_type": "markdown", "id": "eacd89fe", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all ByteDanceDoubaoEmbeddings features and configurations head to the API reference: https://api.js.langchain.com/classes/_langchain_community.embeddings_bytedance_doubao.ByteDanceDoubaoEmbeddings.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}