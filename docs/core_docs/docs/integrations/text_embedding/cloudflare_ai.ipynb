{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Cloudflare Workers AI\n", "lc_docs_skip_validation: true\n", "---"]}, {"cell_type": "markdown", "id": "9a3d6f34", "metadata": {}, "source": ["# CloudflareWorkersAIEmbeddings\n", "\n", "This will help you get started with Cloudflare Workers AI [embedding models](/docs/concepts/embedding_models) using LangChain. For detailed documentation on `CloudflareWorkersAIEmbeddings` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_cloudflare.CloudflareWorkersAIEmbeddings.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | Py support | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [`CloudflareWorkersAIEmbeddings`](https://api.js.langchain.com/classes/langchain_cloudflare.CloudflareWorkersAIEmbeddings.html) | [`@langchain/cloudflare`](https://npmjs.com/@langchain/cloudflare) | ❌ | ❌ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/cloudflare?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/cloudflare?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Cloudflare embedding models you'll need to create a Cloudflare account and install the `@langchain/cloudflare` integration package. This integration is made to run in a Cloudflare worker and accept a binding.\n", "\n", "Follow [the official docs](https://developers.cloudflare.com/workers-ai/get-started/workers-wrangler/) to set up your worker.\n", "\n", "Your `wrangler.toml` file should look similar to this:\n", "\n", "```toml\n", "name = \"langchain-test\"\n", "main = \"worker.js\"\n", "compatibility_date = \"2024-01-10\"\n", "\n", "[[vectorize]]\n", "binding = \"VECTORIZE_INDEX\"\n", "index_name = \"langchain-test\"\n", "\n", "[ai]\n", "binding = \"AI\"\n", "```\n", "\n", "### Credentials\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain CloudflareWorkersAIEmbeddings integration lives in the `@langchain/cloudflare` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/cloudflare @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "45dd1724", "metadata": {}, "source": ["## Usage\n", "\n", "Below is an example worker that uses Workers AI embeddings with a [Cloudflare Vectorize vectorstore](/docs/integrations/vectorstores/cloudflare_vectorize/)."]}, {"cell_type": "code", "execution_count": null, "id": "9ea7a09b", "metadata": {}, "outputs": [], "source": ["// @ts-nocheck\n", "\n", "import type {\n", "  VectorizeIndex,\n", "  <PERSON><PERSON><PERSON>,\n", "  Request,\n", "} from \"@cloudflare/workers-types\";\n", "\n", "import {\n", "  CloudflareVectorizeStore,\n", "  CloudflareWorkersAIEmbeddings,\n", "} from \"@langchain/cloudflare\";\n", "\n", "export interface Env {\n", "  VECTORIZE_INDEX: VectorizeIndex;\n", "  AI: <PERSON><PERSON><PERSON>;\n", "}\n", "\n", "export default {\n", "  async fetch(request: Request, env: Env) {\n", "    const { pathname } = new URL(request.url);\n", "    const embeddings = new CloudflareWorkersAIEmbeddings({\n", "      binding: env.<PERSON>,\n", "      model: \"@cf/baai/bge-small-en-v1.5\",\n", "    });\n", "    const store = new CloudflareVectorizeStore(embeddings, {\n", "      index: env.VECTORIZE_INDEX,\n", "    });\n", "    if (pathname === \"/\") {\n", "      const results = await store.similaritySearch(\"hello\", 5);\n", "      return Response.json(results);\n", "    } else if (pathname === \"/load\") {\n", "      // Upsertion by id is supported\n", "      await store.addDocuments(\n", "        [\n", "          {\n", "            pageContent: \"hello\",\n", "            metadata: {},\n", "          },\n", "          {\n", "            pageContent: \"world\",\n", "            metadata: {},\n", "          },\n", "          {\n", "            pageContent: \"hi\",\n", "            metadata: {},\n", "          },\n", "        ],\n", "        { ids: [\"id1\", \"id2\", \"id3\"] }\n", "      );\n", "\n", "      return Response.json({ success: true });\n", "    } else if (pathname === \"/clear\") {\n", "      await store.delete({ ids: [\"id1\", \"id2\", \"id3\"] });\n", "      return Response.json({ success: true });\n", "    }\n", "\n", "    return Response.json({ error: \"Not Found\" }, { status: 404 });\n", "  },\n", "};"]}, {"cell_type": "markdown", "id": "8938e581", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `CloudflareWorkersAIEmbeddings` features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_cloudflare.CloudflareWorkersAIEmbeddings.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "typescript", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}