{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Fireworks\n", "---"]}, {"cell_type": "markdown", "id": "9a3d6f34", "metadata": {}, "source": ["# FireworksEmbeddings\n", "\n", "This will help you get started with FireworksEmbeddings [embedding models](/docs/concepts/embedding_models) using LangChain. For detailed documentation on `FireworksEmbeddings` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_community_embeddings_fireworks.FireworksEmbeddings.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | [Py support](https://python.langchain.com/docs/integrations/text_embedding/fireworks/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [FireworksEmbeddings](https://api.js.langchain.com/classes/langchain_community_embeddings_fireworks.FireworksEmbeddings.html) | [@langchain/community](https://api.js.langchain.com/modules/langchain_community_embeddings_fireworks.html) | ❌ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/community?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Fireworks embedding models you'll need to create a Fireworks account, get an API key, and install the `@langchain/community` integration package.\n", "\n", "### Credentials\n", "\n", "Head to [fireworks.ai](https://fireworks.ai/) to sign up to `Fireworks` and generate an API key. Once you've done this set the `FIREWORKS_API_KEY` environment variable:\n", "\n", "```bash\n", "export FIREWORKS_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain `FireworksEmbeddings` integration lives in the `@langchain/community` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "45dd1724", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "9ea7a09b", "metadata": {}, "outputs": [], "source": ["import { FireworksEmbeddings } from \"@langchain/community/embeddings/fireworks\";\n", "\n", "const embeddings = new FireworksEmbeddings({\n", "  modelName: \"nomic-ai/nomic-embed-text-v1.5\",\n", "});"]}, {"cell_type": "markdown", "id": "77d271b6", "metadata": {}, "source": ["## Indexing and Retrieval\n", "\n", "Embedding models are often used in retrieval-augmented generation (RAG) flows, both as part of indexing data as well as later retrieving it. For more detailed instructions, please see our RAG tutorials under the [working with external knowledge tutorials](/docs/tutorials/#working-with-external-knowledge).\n", "\n", "Below, see how to index and retrieve data using the `embeddings` object we initialized above. In this example, we will index and retrieve a sample document using the demo [`MemoryVectorStore`](/docs/integrations/vectorstores/memory)."]}, {"cell_type": "code", "execution_count": 2, "id": "d817716b", "metadata": {}, "outputs": [{"data": {"text/plain": ["\u001b[32m\"LangChain is the framework for building context-aware reasoning applications\"\u001b[39m"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["// Create a vector store with a sample text\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const text = \"LangChain is the framework for building context-aware reasoning applications\";\n", "\n", "const vectorstore = await MemoryVectorStore.fromDocuments(\n", "  [{ pageContent: text, metadata: {} }],\n", "  embeddings,\n", ");\n", "\n", "// Use the vector store as a retriever that returns a single document\n", "const retriever = vectorstore.asRetriever(1);\n", "\n", "// Retrieve the most similar text\n", "const retrievedDocuments = await retriever.invoke(\"What is <PERSON><PERSON><PERSON><PERSON>?\");\n", "\n", "retrievedDocuments[0].pageContent;"]}, {"cell_type": "markdown", "id": "e02b9855", "metadata": {}, "source": ["## Direct Usage\n", "\n", "Under the hood, the vectorstore and retriever implementations are calling `embeddings.embedDocument(...)` and `embeddings.embedQuery(...)` to create embeddings for the text(s) used in `fromDocuments` and the retriever's `invoke` operations, respectively.\n", "\n", "You can directly call these methods to get embeddings for your own use cases.\n", "\n", "### Embed single texts\n", "\n", "You can embed queries for search with `embedQuery`. This generates a vector representation specific to the query:"]}, {"cell_type": "code", "execution_count": 3, "id": "0d2befcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "        0.01666259765625,      0.011688232421875,          -0.1181640625,\n", "          -0.10205078125,       0.05438232421875,      -0.08905029296875,\n", "      -0.018096923828125,    0.00952911376953125,         -0.08056640625,\n", "     -0.0283050537109375,   -0.01512908935546875,     0.0312042236328125,\n", "        0.08197021484375,      0.022552490234375,  0.0012683868408203125,\n", "         0.0133056640625,      -0.04327392578125,  -0.004322052001953125,\n", "       -0.02410888671875, -0.0012350082397460938,      -0.04632568359375,\n", "        0.02996826171875,    -0.0134124755859375,     -0.037811279296875,\n", "        0.07672119140625,      0.021759033203125,     0.0179290771484375,\n", "  -0.0002741813659667969,       -0.0582275390625,    -0.0224456787109375,\n", "   0.0027675628662109375,     -0.017425537109375,   -0.01520538330078125,\n", "    -0.01146697998046875,     -0.055267333984375,           -0.083984375,\n", "       0.056793212890625,  -0.003383636474609375,     -0.034271240234375,\n", "        0.05108642578125,   -0.01018524169921875,        0.0462646484375,\n", "   0.0012178421020507812,   0.005779266357421875,        0.0684814453125,\n", "     0.00797271728515625,    -0.0176544189453125,    0.00257110595703125,\n", "       0.059539794921875,      -0.06573486328125,        -0.075439453125,\n", "     -0.0247344970703125,    -0.0276947021484375,   0.003940582275390625,\n", "        0.02630615234375,        0.0660400390625,        0.0157470703125,\n", "       0.033050537109375,          -0.0478515625,      -0.03338623046875,\n", "       0.050384521484375,       0.07757568359375,        -0.045166015625,\n", "        0.07586669921875,  0.0021915435791015625,     0.0237579345703125,\n", "      -0.052703857421875,       0.05023193359375,    -0.0274810791015625,\n", "  -0.0025081634521484375,         0.019287109375,      -0.03802490234375,\n", "      0.0216217041015625,      0.025360107421875,         -0.04443359375,\n", "      -0.029205322265625,  -0.002414703369140625,      0.027130126953125,\n", "       0.028961181640625,         0.078857421875, -0.0009660720825195312,\n", "       0.017608642578125,       0.05755615234375,    -0.0285797119140625,\n", "      0.0039215087890625,  -0.006908416748046875,      -0.05364990234375,\n", "    -0.01342010498046875,       -0.0247802734375,       0.08331298828125,\n", "       0.032928466796875,    0.00543975830078125,    -0.0168304443359375,\n", "      -0.050018310546875,         -0.05908203125,      0.031951904296875,\n", "     -0.0200347900390625,      0.019134521484375,     -0.018035888671875,\n", "    -0.01178741455078125\n", "]\n"]}], "source": ["const singleVector = await embeddings.embedQuery(text);\n", "\n", "console.log(singleVector.slice(0, 100));"]}, {"cell_type": "markdown", "id": "1b5a7d03", "metadata": {}, "source": ["### Embed multiple texts\n", "\n", "You can embed multiple texts for indexing with `embedDocuments`. The internals used for this method may (but do not have to) differ from embedding queries:"]}, {"cell_type": "code", "execution_count": 4, "id": "2f4d6e97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "       0.016632080078125,    0.01165008544921875,          -0.1181640625,\n", "       -0.10186767578125,       0.05438232421875,      -0.08905029296875,\n", "     -0.0180511474609375,    0.00957489013671875,         -0.08056640625,\n", "           -0.0283203125,    -0.0151214599609375,        0.0311279296875,\n", "        0.08184814453125,     0.0225982666015625,  0.0012750625610351562,\n", "        0.01336669921875,     -0.043365478515625,  -0.004322052001953125,\n", "       -0.02410888671875, -0.0012559890747070312,     -0.046356201171875,\n", "      0.0298919677734375,     -0.013458251953125,      -0.03765869140625,\n", "        0.07672119140625,     0.0217132568359375,     0.0179290771484375,\n", "  -0.0002269744873046875,       -0.0582275390625,          -0.0224609375,\n", "    0.002834320068359375,    -0.0174407958984375,   -0.01512908935546875,\n", "    -0.01146697998046875,     -0.055206298828125,      -0.08404541015625,\n", "         0.0567626953125, -0.0033092498779296875,     -0.034271240234375,\n", "        0.05108642578125,     -0.010101318359375,      0.046173095703125,\n", "   0.0011806488037109375,      0.005706787109375,       0.06854248046875,\n", "      0.0079193115234375,    -0.0176239013671875,   0.002552032470703125,\n", "       0.059539794921875,      -0.06573486328125,      -0.07537841796875,\n", "       -0.02484130859375,     -0.027740478515625,   0.003925323486328125,\n", "            0.0263671875,        0.0660400390625,     0.0156402587890625,\n", "       0.033050537109375,     -0.047821044921875,       -0.0333251953125,\n", "       0.050445556640625,       0.07757568359375,     -0.045257568359375,\n", "        0.07586669921875,  0.0021724700927734375,     0.0237274169921875,\n", "      -0.052703857421875,      0.050323486328125,       -0.0274658203125,\n", "  -0.0024662017822265625,     0.0194244384765625,      -0.03802490234375,\n", "        0.02166748046875,      0.025360107421875,     -0.044464111328125,\n", "     -0.0292816162109375, -0.0025119781494140625,     0.0271148681640625,\n", "       0.028961181640625,         0.078857421875, -0.0008907318115234375,\n", "       0.017669677734375,           0.0576171875,    -0.0285797119140625,\n", "      0.0039825439453125,   -0.00687408447265625,       -0.0535888671875,\n", "     -0.0134735107421875,    -0.0247650146484375,        0.0831298828125,\n", "       0.032989501953125,   0.005443572998046875,    -0.0167999267578125,\n", "      -0.050018310546875,     -0.059051513671875,        0.0318603515625,\n", "     -0.0200958251953125,     0.0191192626953125,    -0.0180206298828125,\n", "    -0.01175689697265625\n", "]\n", "[\n", "       -0.02667236328125,      0.036651611328125,         -0.1630859375,\n", "        -0.0904541015625,     -0.022430419921875,       -0.095458984375,\n", "      -0.037628173828125,    0.00473785400390625,    -0.046051025390625,\n", "      0.0109710693359375,        0.0113525390625,    0.0254364013671875,\n", "        0.09368896484375,     0.0144195556640625, -0.007564544677734375,\n", "  -0.0014705657958984375, -0.0007691383361816406,    -0.015716552734375,\n", "     -0.0242156982421875,     -0.024871826171875,      0.00885009765625,\n", "   0.0012922286987304688,      0.023712158203125,    -0.054595947265625,\n", "        0.06329345703125,        0.0289306640625,    0.0233612060546875,\n", "        -0.0374755859375,       -0.0489501953125,    -0.029510498046875,\n", "      0.0173492431640625,     0.0171356201171875,     -0.01629638671875,\n", "        -0.0352783203125,     -0.039398193359375,     -0.11138916015625,\n", "      0.0296783447265625,   -0.01467132568359375, 0.0009188652038574219,\n", "       0.048187255859375,     -0.010650634765625,               0.03125,\n", "    0.005214691162109375,        -0.015869140625,      0.06939697265625,\n", "        -0.0428466796875,     0.0266571044921875,        0.044189453125,\n", "       0.049957275390625,     -0.054290771484375,    0.0107574462890625,\n", "       -0.03265380859375,    -0.0109100341796875,   -0.0144805908203125,\n", "        0.03936767578125,       0.07989501953125,    -0.056976318359375,\n", "      0.0308380126953125,     -0.035125732421875,    -0.038848876953125,\n", "        0.10748291015625,       0.01129150390625,      -0.0665283203125,\n", "        0.09710693359375,       0.03143310546875,   -0.0104522705078125,\n", "      -0.062469482421875,      0.021148681640625,     -0.00970458984375,\n", "       -0.06756591796875,       0.01019287109375,      0.00433349609375,\n", "       0.032928466796875,      0.020233154296875,     -0.01336669921875,\n", "      -0.015472412109375,    -0.0175933837890625,   -0.0142364501953125,\n", "   -0.007450103759765625,          0.03759765625,  0.003551483154296875,\n", "         0.0069580078125,      0.042266845703125, -0.007488250732421875,\n", "        0.01922607421875,         0.007080078125,   -0.0255889892578125,\n", "   -0.007686614990234375,       -0.0848388671875,     0.058563232421875,\n", "       0.021148681640625,      0.034393310546875,   0.01087188720703125,\n", "     -0.0196380615234375,      -0.09515380859375,       0.0054931640625,\n", "      -0.012481689453125,   0.003322601318359375,    -0.019683837890625,\n", "     -0.0307159423828125\n", "]\n"]}], "source": ["const text2 = \"LangGraph is a library for building stateful, multi-actor applications with LLMs\";\n", "\n", "const vectors = await embeddings.embedDocuments([text, text2]);\n", "\n", "console.log(vectors[0].slice(0, 100));\n", "console.log(vectors[1].slice(0, 100));"]}, {"cell_type": "markdown", "id": "8938e581", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all FireworksEmbeddings features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_community_embeddings_fireworks.FireworksEmbeddings.html"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON>", "language": "typescript", "name": "deno"}, "language_info": {"file_extension": ".ts", "mimetype": "text/x.typescript", "name": "typescript", "nb_converter": "script", "pygments_lexer": "typescript", "version": "5.3.3"}}, "nbformat": 4, "nbformat_minor": 5}