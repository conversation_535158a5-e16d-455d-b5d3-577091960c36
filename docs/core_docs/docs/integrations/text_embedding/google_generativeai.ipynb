{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Google Generative AI\n", "---"]}, {"cell_type": "markdown", "id": "9a3d6f34", "metadata": {}, "source": ["# GoogleGenerativeAIEmbeddings\n", "\n", "This will help you get started with Google Generative AI [embedding models](/docs/concepts/embedding_models) using LangChain. For detailed documentation on `GoogleGenerativeAIEmbeddings` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_google_genai.GoogleGenerativeAIEmbeddings.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | [Py support](https://python.langchain.com/docs/integrations/text_embedding/google_generative_ai/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [`GoogleGenerativeAIEmbeddings`](https://api.js.langchain.com/classes/langchain_google_genai.GoogleGenerativeAIEmbeddings.html) | [`@langchain/google-genai`](https://npmjs.com/@langchain/google-genai) | ❌ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/google-genai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/google-genai?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Google Generative AI embedding models you'll need to sign up for a Google AI account, get an API key, and install the `@langchain/google-genai` integration package.\n", "\n", "### Credentials\n", "\n", "Get an API key here: https://ai.google.dev/tutorials/setup.\n", "\n", "Next, set your key as an environment variable named `GOOGLE_API_KEY`:\n", "\n", "```bash\n", "export GOOGLE_API_KEY=\"your-api-key\"\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain `GoogleGenerativeAIEmbeddings` integration lives in the `@langchain/google-genai` package. You may also wish to install the official SDK:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/google-genai @langchain/core @google/generative-ai\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "45dd1724", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and embed text:"]}, {"cell_type": "code", "execution_count": 1, "id": "9ea7a09b", "metadata": {}, "outputs": [], "source": ["import { GoogleGenerativeAIEmbeddings } from \"@langchain/google-genai\";\n", "import { TaskType } from \"@google/generative-ai\";\n", "\n", "const embeddings = new GoogleGenerativeAIEmbeddings({\n", "  model: \"text-embedding-004\", // 768 dimensions\n", "  taskType: TaskType.RETRIEVAL_DOCUMENT,\n", "  title: \"Document title\",\n", "});"]}, {"cell_type": "markdown", "id": "77d271b6", "metadata": {}, "source": ["## Indexing and Retrieval\n", "\n", "Embedding models are often used in retrieval-augmented generation (RAG) flows, both as part of indexing data as well as later retrieving it. For more detailed instructions, please see our RAG tutorials under the [working with external knowledge tutorials](/docs/tutorials/#working-with-external-knowledge).\n", "\n", "Below, see how to index and retrieve data using the `embeddings` object we initialized above. In this example, we will index and retrieve a sample document using the demo [`MemoryVectorStore`](/docs/integrations/vectorstores/memory)."]}, {"cell_type": "code", "execution_count": 2, "id": "d817716b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LangChain is the framework for building context-aware reasoning applications\n"]}], "source": ["// Create a vector store with a sample text\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const text = \"LangChain is the framework for building context-aware reasoning applications\";\n", "\n", "const vectorstore = await MemoryVectorStore.fromDocuments(\n", "  [{ pageContent: text, metadata: {} }],\n", "  embeddings,\n", ");\n", "\n", "// Use the vector store as a retriever that returns a single document\n", "const retriever = vectorstore.asRetriever(1);\n", "\n", "// Retrieve the most similar text\n", "const retrievedDocuments = await retriever.invoke(\"What is <PERSON><PERSON><PERSON><PERSON>?\");\n", "\n", "retrievedDocuments[0].pageContent;"]}, {"cell_type": "markdown", "id": "e02b9855", "metadata": {}, "source": ["## Direct Usage\n", "\n", "Under the hood, the vectorstore and retriever implementations are calling `embeddings.embedDocument(...)` and `embeddings.embedQuery(...)` to create embeddings for the text(s) used in `fromDocuments` and the retriever's `invoke` operations, respectively.\n", "\n", "You can directly call these methods to get embeddings for your own use cases.\n", "\n", "### Embed single texts\n", "\n", "You can embed queries for search with `embedQuery`. This generates a vector representation specific to the query:"]}, {"cell_type": "code", "execution_count": 3, "id": "0d2befcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  -0.018286658,   0.020051053,  -0.057487167,   0.0059406986, -0.0036901247,\n", "  -0.010400916,    0.03396853,  -0.010867519,    0.015650319,   0.026443942,\n", "   0.012251757,   -0.01581729,    0.02031182, -0.00062176475,  0.0065521155,\n", "   -0.07107355,   0.033614952,    0.07109807,   -0.021078493,   0.048039366,\n", "   0.022973344,    -0.0361746,   -0.04550704,   -0.048807852,    0.03414146,\n", "   0.042450827,    0.02930612,   0.027274853,   -0.027707053,   -0.04167595,\n", "    0.01708843,   0.028532283, -0.0018593844,      -0.096786,  -0.034648854,\n", "  0.0013152987,   0.024425535,    0.04937838,    0.036890924,  -0.074619934,\n", "  -0.028723065,   0.029158255,  -0.023993572,     0.03163398,   -0.02036324,\n", "   -0.02333609,  -0.017407075, -0.0059643993,    -0.05564625,   0.051022638,\n", "    0.03264913,  -0.008254581,  -0.030552095,    0.072952054,   -0.05448913,\n", "   0.012030814,   -0.07978849,  -0.030417662,   0.0038343794,    0.03237516,\n", "  -0.054259773,    -0.0524064,   -0.02145499,    0.006439614,    0.04988943,\n", "   -0.03232189,    0.00990776,   -0.03863326,    -0.04979561,   0.009874035,\n", "   -0.02617946,    0.02135152,  -0.070599854,     0.08655627,   -0.02080979,\n", "  -0.014944934,  0.0034440767,  -0.035236854,    0.027093545,   0.032249685,\n", "   -0.03559674,   0.046849757,    0.06965356,    0.028780492,    0.02865287,\n", "   -0.07999455, -0.0058599655,  -0.050316703,   -0.018346578,  -0.038311094,\n", "    0.08026719,   0.049136136,   -0.05372233,  -0.0062247813,    0.01791339,\n", "   -0.03635157,  -0.031860247,  -0.031322744,    0.044055287,   0.034934316\n", "]\n"]}], "source": ["const singleVector = await embeddings.embedQuery(text);\n", "\n", "console.log(singleVector.slice(0, 100));"]}, {"cell_type": "markdown", "id": "1b5a7d03", "metadata": {}, "source": ["### Embed multiple texts\n", "\n", "You can embed multiple texts for indexing with `embedDocuments`. The internals used for this method may (but do not have to) differ from embedding queries:"]}, {"cell_type": "code", "execution_count": 4, "id": "2f4d6e97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  -0.018286658,   0.020051053,  -0.057487167,   0.0059406986, -0.0036901247,\n", "  -0.010400916,    0.03396853,  -0.010867519,    0.015650319,   0.026443942,\n", "   0.012251757,   -0.01581729,    0.02031182, -0.00062176475,  0.0065521155,\n", "   -0.07107355,   0.033614952,    0.07109807,   -0.021078493,   0.048039366,\n", "   0.022973344,    -0.0361746,   -0.04550704,   -0.048807852,    0.03414146,\n", "   0.042450827,    0.02930612,   0.027274853,   -0.027707053,   -0.04167595,\n", "    0.01708843,   0.028532283, -0.0018593844,      -0.096786,  -0.034648854,\n", "  0.0013152987,   0.024425535,    0.04937838,    0.036890924,  -0.074619934,\n", "  -0.028723065,   0.029158255,  -0.023993572,     0.03163398,   -0.02036324,\n", "   -0.02333609,  -0.017407075, -0.0059643993,    -0.05564625,   0.051022638,\n", "    0.03264913,  -0.008254581,  -0.030552095,    0.072952054,   -0.05448913,\n", "   0.012030814,   -0.07978849,  -0.030417662,   0.0038343794,    0.03237516,\n", "  -0.054259773,    -0.0524064,   -0.02145499,    0.006439614,    0.04988943,\n", "   -0.03232189,    0.00990776,   -0.03863326,    -0.04979561,   0.009874035,\n", "   -0.02617946,    0.02135152,  -0.070599854,     0.08655627,   -0.02080979,\n", "  -0.014944934,  0.0034440767,  -0.035236854,    0.027093545,   0.032249685,\n", "   -0.03559674,   0.046849757,    0.06965356,    0.028780492,    0.02865287,\n", "   -0.07999455, -0.0058599655,  -0.050316703,   -0.018346578,  -0.038311094,\n", "    0.08026719,   0.049136136,   -0.05372233,  -0.0062247813,    0.01791339,\n", "   -0.03635157,  -0.031860247,  -0.031322744,    0.044055287,   0.034934316\n", "]\n", "[\n", "    0.011669316,    0.02170385,   -0.07519182,     0.003981285,\n", "   0.0053525288,   0.008397044,   0.036672726,     0.016549919,\n", "    0.061946314,    0.06280753,  -0.009199135,     0.014644887,\n", "    0.046459496,  0.0122919325,  -0.013300706,    -0.051746193,\n", "     -0.0490098,   0.045586824,   -0.05053146,     0.044294067,\n", "   -0.012607168, -0.0071777054,  -0.048455723,    -0.075109236,\n", "    0.013327612,  -0.025612017,   0.050875787,     0.030091539,\n", "   -0.027163379,   -0.05760821,   0.014368641,    0.0044602253,\n", "    0.035219245,  -0.033304706,  -0.045474708,    -0.038022216,\n", "    0.012366698,   0.028978042,   0.038591366,     -0.10646444,\n", "   -0.036803752,   0.018911313,   0.005681761,     0.025365992,\n", "   -0.017165288, -0.0048005017,  -0.011460135,    0.0027811683,\n", "    -0.04971402, -0.0019232291,    0.02141983,   -0.0013272346,\n", "    -0.03337951,   0.030568397,   -0.05704511,     -0.01187748,\n", "   -0.025354648,   0.016188234,  -0.022018699,    0.0096449675,\n", "   -0.027020318,  -0.038059015,  -0.024455398,     0.021858294,\n", "    0.010713859,   -0.07203855,   -0.05562406, 0.0000034690818,\n", "   -0.054289237, -0.0027928432, -0.0010051605,     0.008493095,\n", "   -0.064746305,   0.024419345,  -0.016629996,     -0.02686531,\n", "    -0.02300653,   -0.03263113,   0.019998727,     0.029680967,\n", "    -0.04365641,   0.013594972,   0.056486532,     0.025913332,\n", "    0.025457978,  -0.048536208,   0.020046104,     -0.05857287,\n", "   -0.032664414,  -0.032940287,    0.10053288,    -0.021389635,\n", "  -0.0044220444,   0.037026003,    0.03142132,    -0.048912503,\n", "    -0.07961264,  -0.051056523,   0.048032805,      0.04831778\n", "]\n"]}], "source": ["const text2 = \"LangGraph is a library for building stateful, multi-actor applications with LLMs\";\n", "\n", "const vectors = await embeddings.embedDocuments([text, text2]);\n", "\n", "console.log(vectors[0].slice(0, 100));\n", "console.log(vectors[1].slice(0, 100));"]}, {"cell_type": "markdown", "id": "8938e581", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `GoogleGenerativeAIEmbeddings` features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_google_genai.GoogleGenerativeAIEmbeddings.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}