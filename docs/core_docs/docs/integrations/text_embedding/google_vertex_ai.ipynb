{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Google Vertex AI\n", "---"]}, {"cell_type": "markdown", "id": "9a3d6f34", "metadata": {}, "source": ["# VertexAIEmbeddings\n", "\n", "[Google Vertex](https://cloud.google.com/vertex-ai) is a service that exposes all foundation models available in Google Cloud.\n", "\n", "This will help you get started with Google Vertex AI [embedding models](/docs/concepts/embedding_models) using LangChain. For detailed documentation on `VertexAIEmbeddings` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/langchain_google_vertexai.GoogleVertexAIEmbeddings.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | [Py support](https://python.langchain.com/docs/integrations/text_embedding/google_vertex_ai_palm/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [`VertexAIEmbeddings`](https://api.js.langchain.com/classes/langchain_google_vertexai.GoogleVertexAIEmbeddings.html) | [`@langchain/google-vertexai`](https://npmjs.com/@langchain/google-vertexai) | ❌ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/google-vertexai?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/google-vertexai?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "LangChain.js supports two different authentication methods based on whether\n", "you're running in a Node.js environment or a web environment.\n", "\n", "To access `ChatVertexAI` models you'll need to setup Google VertexAI in your Google Cloud Platform (GCP) account, save the credentials file, and install the `@langchain/google-vertexai` integration package.\n", "\n", "### Credentials\n", "\n", "Head to your [GCP account](https://console.cloud.google.com/) and generate a credentials file. Once you've done this set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable:\n", "\n", "```bash\n", "export GOOGLE_APPLICATION_CREDENTIALS=\"path/to/your/credentials.json\"\n", "```\n", "\n", "If running in a web environment, you should set the `GOOGLE_VERTEX_AI_WEB_CREDENTIALS` environment variable as a JSON stringified object, and install the `@langchain/google-vertexai-web` package:\n", "\n", "```bash\n", "GOOGLE_VERTEX_AI_WEB_CREDENTIALS={\"type\":\"service_account\",\"project_id\":\"YOUR_PROJECT-12345\",...}\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain `VertexAIEmbeddings` integration lives in the `@langchain/google-vertexai` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/google-vertexai @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "45dd1724", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and embed text:"]}, {"cell_type": "code", "execution_count": 1, "id": "9ea7a09b", "metadata": {}, "outputs": [], "source": ["import { VertexAIEmbeddings } from \"@langchain/google-vertexai\";\n", "// Uncomment the following line if you're running in a web environment:\n", "// import { VertexAIEmbeddings } from \"@langchain/google-vertexai-web\"\n", "\n", "const embeddings = new VertexAIEmbeddings({\n", "  model: \"text-embedding-004\",\n", "  // ...\n", "});"]}, {"cell_type": "markdown", "id": "77d271b6", "metadata": {}, "source": ["## Indexing and Retrieval\n", "\n", "Embedding models are often used in retrieval-augmented generation (RAG) flows, both as part of indexing data as well as later retrieving it. For more detailed instructions, please see our RAG tutorials under the [working with external knowledge tutorials](/docs/tutorials/#working-with-external-knowledge).\n", "\n", "Below, see how to index and retrieve data using the `embeddings` object we initialized above. In this example, we will index and retrieve a sample document using the demo [`MemoryVectorStore`](/docs/integrations/vectorstores/memory)."]}, {"cell_type": "code", "execution_count": 2, "id": "d817716b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LangChain is the framework for building context-aware reasoning applications\n"]}], "source": ["// Create a vector store with a sample text\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const text = \"LangChain is the framework for building context-aware reasoning applications\";\n", "\n", "const vectorstore = await MemoryVectorStore.fromDocuments(\n", "  [{ pageContent: text, metadata: {} }],\n", "  embeddings,\n", ");\n", "\n", "// Use the vector store as a retriever that returns a single document\n", "const retriever = vectorstore.asRetriever(1);\n", "\n", "// Retrieve the most similar text\n", "const retrievedDocuments = await retriever.invoke(\"What is <PERSON><PERSON><PERSON><PERSON>?\");\n", "\n", "retrievedDocuments[0].pageContent;"]}, {"cell_type": "markdown", "id": "e02b9855", "metadata": {}, "source": ["## Direct Usage\n", "\n", "Under the hood, the vectorstore and retriever implementations are calling `embeddings.embedDocument(...)` and `embeddings.embedQuery(...)` to create embeddings for the text(s) used in `fromDocuments` and the retriever's `invoke` operations, respectively.\n", "\n", "You can directly call these methods to get embeddings for your own use cases.\n", "\n", "### Embed single texts\n", "\n", "You can embed queries for search with `embedQuery`. This generates a vector representation specific to the query:"]}, {"cell_type": "code", "execution_count": 3, "id": "0d2befcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    -0.02831101417541504,   0.022063178941607475,  -0.07454229146242142,\n", "    0.006448323838412762,   0.001955120824277401, -0.017617391422390938,\n", "       0.018649872392416,   -0.05262855067849159, 0.0006953597767278552,\n", "  -0.0018249079585075378,   0.022437218576669693, 0.0036489504855126143,\n", "   0.0018086736090481281,   0.016940006986260414, -0.007894322276115417,\n", "    -0.04187627509236336,   0.039501357823610306,   0.06918870657682419,\n", "   -0.006931832991540432,   0.049655742943286896,  0.021211417391896248,\n", "   -0.029322246089577675,   -0.04546992480754852,  -0.01769082061946392,\n", "    0.046703994274139404,    0.03127637133002281,  0.006355373188853264,\n", "    0.014901148155331612,  -0.006893016863614321,  -0.05992589890956879,\n", "   -0.009733330458402634,   0.015709295868873596, -0.017982766032218933,\n", "     -0.0852997675538063,  -0.032453566789627075, 0.0014507169835269451,\n", "     0.03345133736729622,   0.048862338066101074,  0.006664620712399483,\n", "    -0.06287197023630142,   -0.02109423652291298,  0.018176473677158356,\n", "   -0.022175665944814682,    0.03340170532464981, -0.008905526250600815,\n", "    -0.03492079675197601,   -0.03819998353719711,  -0.05230168625712395,\n", "    -0.05247239023447037,   0.048254698514938354,  0.046494755893945694,\n", "   -0.029708227142691612,  -0.002180763054639101,  0.051957979798316956,\n", "    -0.05483679473400116,    0.00700812041759491,  -0.08181990683078766,\n", "    -0.02295914851129055,   0.026530204340815544,   0.04028692841529846,\n", "    -0.05230272561311722,  -0.057705819606781006, -0.015022763051092625,\n", "       0.002143724123016,    0.06361843645572662, -0.027828887104988098,\n", "    0.006870461627840996,  -0.016140831634402275, -0.034440942108631134,\n", "   -0.004059414379298687,  -0.042537953704595566,  -0.00984653178602457,\n", "    -0.07701274752616882,    0.09815558046102524, -0.025801729410886765,\n", "   -0.008693721145391464, -0.0010926402173936367, -0.027235493063926697,\n", "     0.06945550441741943,   0.023456251248717308,  -0.02160717360675335,\n", "     0.03252667561173439,    0.05874639376997948, -0.001329384627752006,\n", "     0.03664775192737579,   -0.07353461533784866, -0.028453022241592407,\n", "    -0.05666429176926613,  -0.012955721467733383, -0.041723109781742096,\n", "     0.07209191471338272,     0.0326194241642952,   -0.0496046207845211,\n", "   -0.025037819519639015,   0.004625750705599785,  -0.03622527793049812,\n", "   -0.022546149790287018,  0.0053468807600438595,   0.03879072889685631,\n", "     0.03238753229379654\n", "]\n"]}], "source": ["const singleVector = await embeddings.embedQuery(text);\n", "\n", "console.log(singleVector.slice(0, 100));"]}, {"cell_type": "markdown", "id": "1b5a7d03", "metadata": {}, "source": ["### Embed multiple texts\n", "\n", "You can embed multiple texts for indexing with `embedDocuments`. The internals used for this method may (but do not have to) differ from embedding queries:"]}, {"cell_type": "code", "execution_count": 4, "id": "2f4d6e97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "    -0.02831101417541504,   0.022063178941607475,  -0.07454229146242142,\n", "    0.006448323838412762,   0.001955120824277401, -0.017617391422390938,\n", "       0.018649872392416,   -0.05262855067849159, 0.0006953597767278552,\n", "  -0.0018249079585075378,   0.022437218576669693, 0.0036489504855126143,\n", "   0.0018086736090481281,   0.016940006986260414, -0.007894322276115417,\n", "    -0.04187627509236336,   0.039501357823610306,   0.06918870657682419,\n", "   -0.006931832991540432,   0.049655742943286896,  0.021211417391896248,\n", "   -0.029322246089577675,   -0.04546992480754852,  -0.01769082061946392,\n", "    0.046703994274139404,    0.03127637133002281,  0.006355373188853264,\n", "    0.014901148155331612,  -0.006893016863614321,  -0.05992589890956879,\n", "   -0.009733330458402634,   0.015709295868873596, -0.017982766032218933,\n", "     -0.0852997675538063,  -0.032453566789627075, 0.0014507169835269451,\n", "     0.03345133736729622,   0.048862338066101074,  0.006664620712399483,\n", "    -0.06287197023630142,   -0.02109423652291298,  0.018176473677158356,\n", "   -0.022175665944814682,    0.03340170532464981, -0.008905526250600815,\n", "    -0.03492079675197601,   -0.03819998353719711,  -0.05230168625712395,\n", "    -0.05247239023447037,   0.048254698514938354,  0.046494755893945694,\n", "   -0.029708227142691612,  -0.002180763054639101,  0.051957979798316956,\n", "    -0.05483679473400116,    0.00700812041759491,  -0.08181990683078766,\n", "    -0.02295914851129055,   0.026530204340815544,   0.04028692841529846,\n", "    -0.05230272561311722,  -0.057705819606781006, -0.015022763051092625,\n", "       0.002143724123016,    0.06361843645572662, -0.027828887104988098,\n", "    0.006870461627840996,  -0.016140831634402275, -0.034440942108631134,\n", "   -0.004059414379298687,  -0.042537953704595566,  -0.00984653178602457,\n", "    -0.07701274752616882,    0.09815558046102524, -0.025801729410886765,\n", "   -0.008693721145391464, -0.0010926402173936367, -0.027235493063926697,\n", "     0.06945550441741943,   0.023456251248717308,  -0.02160717360675335,\n", "     0.03252667561173439,    0.05874639376997948, -0.001329384627752006,\n", "     0.03664775192737579,   -0.07353461533784866, -0.028453022241592407,\n", "    -0.05666429176926613,  -0.012955721467733383, -0.041723109781742096,\n", "     0.07209191471338272,     0.0326194241642952,   -0.0496046207845211,\n", "   -0.025037819519639015,   0.004625750705599785,  -0.03622527793049812,\n", "   -0.022546149790287018,  0.0053468807600438595,   0.03879072889685631,\n", "     0.03238753229379654\n", "]\n", "[\n", "  -0.00007261172140715644,    0.03209814056754112,  -0.10099327564239502,\n", "   -0.0017932605696842074, -0.0016863049240782857,  0.009428824298083782,\n", "     0.023065969347953796,  -0.018305035308003426,   0.03765229508280754,\n", "      0.03357342258095741,  0.0018431750359013677,   0.03230319544672966,\n", "     0.024983661249279976,    0.02752346731722355, -0.027390114963054657,\n", "     -0.01945030689239502,   -0.05770668387413025,  0.046621184796094894,\n", "     -0.03308689966797829,    0.03985097259283066, -0.021250328049063683,\n", "    -0.001940526650287211,   -0.06034174561500549,  -0.05026412755250931,\n", "      0.02385033667087555,   -0.03279203176498413,   0.02966252714395523,\n", "      0.01294293999671936,  -0.009747475385665894,  -0.07896383106708527,\n", "    -0.013269499875605106,  -0.011228476651012897,  0.022224457934498787,\n", "    -0.018957728520035744,   -0.05092151463031769, -0.043285638093948364,\n", "     0.016826728358864784,   0.010665969923138618,  0.021219193935394287,\n", "     -0.08588971197605133,  -0.038367897272109985,  0.012244532816112041,\n", "     0.009497410617768764,   0.017629485577344894, 0.0013116559712216258,\n", "    -0.016468070447444916,   -0.04423798993229866,  -0.04043079912662506,\n", "     -0.05485917255282402,  -0.007577189709991217,  0.028067218139767647,\n", "    -0.022974666208028793,  0.0006999042234383523,  0.009812192991375923,\n", "     -0.05387532711029053,  -0.016531387344002724, -0.015153753571212292,\n", "      0.03397523611783981, -0.0018232968868687749,   0.01200891938060522,\n", "    -0.013123664073646069,  -0.043459296226501465,  -0.01856262981891632,\n", "     0.018269911408424377,   0.016155652701854706,  -0.05597233399748802,\n", "     -0.05852395296096802,   0.020076945424079895, -0.033808667212724686,\n", "    -0.008225022815167904,  -0.014589417725801468,  -0.01408824510872364,\n", "     -0.06293410807847977,   0.026668129488825798,  -0.01397104375064373,\n", "    -0.017627086490392685,   -0.03409220278263092, -0.018559949472546577,\n", "      0.07163946330547333,   0.015611495822668076, -0.034166790544986725,\n", "    -0.005098687019199133,    0.04163505882024765, -0.010681619867682457,\n", "     0.027817489579319954,  -0.031076539307832718, -0.006825212389230728,\n", "     -0.06810358166694641,   -0.03793689236044884,  -0.03981738165020943,\n", "      0.09524374455213547,   -0.03607913851737976,  0.003638653317466378,\n", "      0.02828306518495083,   0.018808560445904732, -0.047244682908058167,\n", "     -0.06114668399095535,   -0.02395530976355076,  0.036157332360744476,\n", "       0.0422002375125885\n", "]\n"]}], "source": ["const text2 = \"LangGraph is a library for building stateful, multi-actor applications with LLMs\";\n", "\n", "const vectors = await embeddings.embedDocuments([text, text2]);\n", "\n", "console.log(vectors[0].slice(0, 100));\n", "console.log(vectors[1].slice(0, 100));"]}, {"cell_type": "markdown", "id": "8938e581", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `VertexAIEmbeddings` features and configurations head to the API reference: https://api.js.langchain.com/classes/langchain_google_vertexai.GoogleVertexAIEmbeddings.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}