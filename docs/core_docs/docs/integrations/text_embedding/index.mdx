---
sidebar_position: 0
sidebar_class_name: hidden
---

# Embeddings

[Embedding models](/docs/concepts/embedding_models) create a vector representation of a piece of text.

This page documents integrations with various model providers that allow you to use embeddings in LangChain.

import EmbeddingTabs from "@theme/EmbeddingTabs";

<EmbeddingTabs />

```javascript
await embeddings.embedQuery("Hello, world!");
```

import { CategoryTable, IndexTable } from "@theme/FeatureTables";

<IndexTable />
