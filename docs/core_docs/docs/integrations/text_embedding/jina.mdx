---
sidebar_label: Jina
---

# Jina Embeddings

The `JinaEmbeddings` class utilizes the Jina API to generate embeddings for given text inputs. This guide will walk you through the setup and usage of the `JinaEmbeddings` class, helping you integrate it into your project seamlessly.

## Installation

Install the `@langchain/community` package as shown below:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm i @langchain/community @langchain/core
```

## Initialization

With this integration, you can use the Jina embeddings model to get embeddings for your text data. Here is the [link](https://jina.ai/embeddings) to the embeddings models.

First, you need to sign up on the Jina website and get the API token from [here](https://jina.ai/embeddings). You can copy model names from the dropdown in the api playground.

To use the `JinaEmbeddings` class, you need an API token from Jina. You can pass this token directly to the constructor or set it as an environment variable (`JINA_API_KEY`).

### Basic Usage

Here’s how to create an instance of `JinaEmbeddings`:

```typescript
import { JinaEmbeddings } from "@langchain/community/embeddings/jina";

const embeddings = new JinaEmbeddings({
  apiKey: "YOUR_API_TOKEN",
  model: "jina-clip-v2", // Optional, defaults to "jina-clip-v2"
});
```

If the `apiKey` is not provided, it will be read from the `JINA_API_KEY` environment variable.

## Generating Embeddings

### Embedding a Single Query

To generate embeddings for a single text query, use the `embedQuery` method:

```typescript
const embedding = await embeddings.embedQuery(
  "What would be a good company name for a company that makes colorful socks?"
);
console.log(embedding);
```

### Embedding Multiple Documents

To generate embeddings for multiple documents, use the `embedDocuments` method.

```typescript
import { localImageToBase64 } from "@langchain/community/utils/local_image_to_base64";
const documents = [
  "hello",
  {
    text: "hello",
  },
  {
    image: "https://i.ibb.co/nQNGqL0/beach1.jpg",
  },
  {
    image: await localImageToBase64("beach1.jpg"),
  },
];

const embeddingsArray = await embeddings.embedDocuments(documents);
console.log(embeddingsArray);
```

## Error Handling

If the API token is not provided and cannot be found in the environment variables, an error will be thrown:

```typescript
try {
  const embeddings = new JinaEmbeddings();
} catch (error) {
  console.error("Jina API token not found");
}
```

## Example

Here’s a complete example of how to set up and use the `JinaEmbeddings` class:

```typescript
import { JinaEmbeddings } from "@langchain/community/embeddings/jina";
import { localImageToBase64 } from "@langchain/community/embeddings/jina/util";

const embeddings = new JinaEmbeddings({
  apiKey: "YOUR_API_TOKEN",
  model: "jina-embeddings-v2-base-en",
});

async function runExample() {
  const queryEmbedding = await embeddings.embedQuery("Example query text.");
  console.log("Query Embedding:", queryEmbedding);

  const documents = [
    "hello",
    {
      text: "hello",
    },
    {
      image: "https://i.ibb.co/nQNGqL0/beach1.jpg",
    },
    {
      image: await localImageToBase64("beach1.jpg"),
    },
  ];
  const documentEmbeddings = await embeddings.embedDocuments(documents);
  console.log("Document Embeddings:", documentEmbeddings);
}

runExample();
```

## Feedback and Support

For feedback or questions, please contact [<EMAIL>](mailto:<EMAIL>).

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
