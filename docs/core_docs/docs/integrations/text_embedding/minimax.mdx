# Minimax

The `MinimaxEmbeddings` class uses the Minimax API to generate embeddings for a given text.

# Setup

To use Minimax model, you'll need a [Minimax account](https://api.minimax.chat), an [API key](https://api.minimax.chat/user-center/basic-information/interface-key), and a [Group ID](https://api.minimax.chat/user-center/basic-information)

# Usage

```typescript
import { MinimaxEmbeddings } from "langchain/embeddings/minimax";

export const run = async () => {
  /* Embed queries */
  const embeddings = new MinimaxEmbeddings();
  const res = await embeddings.embedQuery("Hello world");
  console.log(res);
  /* Embed documents */
  const documentRes = await embeddings.embedDocuments([
    "Hello world",
    "Bye bye",
  ]);
  console.log({ documentRes });
};
```

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
