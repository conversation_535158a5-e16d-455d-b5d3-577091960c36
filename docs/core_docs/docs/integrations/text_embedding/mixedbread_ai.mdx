# Mixedbread AI

The `MixedbreadAIEmbeddings` class uses the [Mixedbread AI](https://mixedbread.ai/) API to generate text embeddings. This guide will walk you through setting up and using the `MixedbreadAIEmbeddings` class, helping you integrate it into your project effectively.

## Installation

To install the `@langchain/mixedbread-ai` package, use the following command:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/mixedbread-ai @langchain/core
```

## Initialization

First, sign up on the Mixedbread AI website and get your API key from [here](https://mixedbread.ai/). You can then use this key to initialize the `MixedbreadAIEmbeddings` class.

You can pass the API key directly to the constructor or set it as an environment variable (`MXBAI_API_KEY`).

### Basic Usage

Here’s how to create an instance of `MixedbreadAIEmbeddings`:

```typescript
import { MixedbreadAIEmbeddings } from "@langchain/mixedbread-ai";

const embeddings = new MixedbreadAIEmbeddings({
  apiKey: "YOUR_API_KEY",
  // Optionally specify model
  // model: "mixedbread-ai/mxbai-embed-large-v1",
});
```

If the `apiKey` is not provided, it will be read from the `MXBAI_API_KEY` environment variable.

## Generating Embeddings

### Embedding a Single Query

To generate embeddings for a single text query, use the `embedQuery` method:

```typescript
const embedding = await embeddings.embedQuery(
  "Represent this sentence for searching relevant passages: Is baking fun?"
);
console.log(embedding);
```

### Embedding Multiple Documents

To generate embeddings for multiple documents, use the `embedDocuments` method. This method handles batching automatically based on the `batchSize` parameter:

```typescript
const documents = ["Baking bread is fun", "I love baking"];

const embeddingsArray = await embeddings.embedDocuments(documents);
console.log(embeddingsArray);
```

## Customizing Requests

You can customize the SDK by passing additional parameters.

```typescript
const customEmbeddings = new MixedbreadAIEmbeddings({
  apiKey: "YOUR_API_KEY",
  baseUrl: "...",
  maxRetries: 6,
});
```

## Error Handling

If the API key is not provided and cannot be found in the environment variables, an error will be thrown:

```typescript
try {
  const embeddings = new MixedbreadAIEmbeddings();
} catch (error) {
  console.error(error);
}
```

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
