---
sidebar_label: Nomic
---

# Nomic

The `NomicEmbeddings` class uses the Nomic AI API to generate embeddings for a given text.

## Setup

In order to use the Nomic API you'll need an API key.
You can sign up for a Nomic account and create an API key [here](https://atlas.nomic.ai/).

You'll first need to install the [`@langchain/nomic`](https://www.npmjs.com/package/@langchain/nomic) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/nomic @langchain/core
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import NomicExample from "@examples/models/embeddings/nomic.ts";

<CodeBlock language="typescript">{NomicExample}</CodeBlock>

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
