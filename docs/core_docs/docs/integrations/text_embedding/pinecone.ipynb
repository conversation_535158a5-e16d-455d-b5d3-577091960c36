{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Pinecone\n", "---"]}, {"cell_type": "markdown", "id": "9a3d6f34", "metadata": {}, "source": ["# PineconeEmbeddings\n", "\n", "This will help you get started with PineconeEmbeddings [embedding models](/docs/concepts/embedding_models) using LangChain. For detailed documentation on `PineconeEmbeddings` features and configuration options, please refer to the [API reference](https://api.js.langchain.com/classes/_langchain_pinecone.PineconeEmbeddings.html).\n", "\n", "## Overview\n", "### Integration details\n", "\n", "| Class | Package | Local | [Py support](https://python.langchain.com/docs/integrations/text_embedding/pinecone/) | Package downloads | Package latest |\n", "| :--- | :--- | :---: | :---: |  :---: | :---: |\n", "| [PineconeEmbeddings](https://api.js.langchain.com/classes/_langchain_pinecone.PineconeEmbeddings.html) | [@langchain/pinecone](https://api.js.langchain.com/classes/_langchain_pinecone.PineconeEmbeddings.html) | ❌ | ✅ | ![NPM - Downloads](https://img.shields.io/npm/dm/@langchain/pinecone?style=flat-square&label=%20&) | ![NPM - Version](https://img.shields.io/npm/v/@langchain/pinecone?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "To access Pinecone embedding models you'll need to create a Pinecone account, get an API key, and install the `@langchain/pinecone` integration package.\n", "\n", "### Credentials\n", "\n", "Sign up for a [Pinecone](https://www.pinecone.io/) account, retrieve your API key, and set it as an environment variable named `PINECONE_API_KEY`:\n", "\n", "```typescript\n", "process.env.PINECONE_API_KEY = \"your-pinecone-api-key\";\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```bash\n", "# export LANGSMITH_TRACING=\"true\"\n", "# export LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "The LangChain PineconeEmbeddings integration lives in the `@langchain/pinecone` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/pinecone @langchain/core @pinecone-database/pinecone@5\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "45dd1724", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our model object and generate chat completions:"]}, {"cell_type": "code", "execution_count": 1, "id": "9ea7a09b", "metadata": {}, "outputs": [], "source": ["import { PineconeEmbeddings } from \"@langchain/pinecone\";\n", "\n", "const embeddings = new PineconeEmbeddings({\n", "  model: \"multilingual-e5-large\",\n", "});"]}, {"cell_type": "markdown", "id": "77d271b6", "metadata": {}, "source": ["## Indexing and Retrieval\n", "\n", "Embedding models are often used in retrieval-augmented generation (RAG) flows, both as part of indexing data as well as later retrieving it. For more detailed instructions, please see our RAG tutorials under the [working with external knowledge tutorials](/docs/tutorials/#working-with-external-knowledge).\n", "\n", "Below, see how to index and retrieve data using the `embeddings` object we initialized above. In this example, we will index and retrieve a sample document using the demo [`MemoryVectorStore`](/docs/integrations/vectorstores/memory)."]}, {"cell_type": "code", "execution_count": 2, "id": "d817716b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LangChain is the framework for building context-aware reasoning applications\n"]}], "source": ["// Create a vector store with a sample text\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const text = \"LangChain is the framework for building context-aware reasoning applications\";\n", "\n", "const vectorstore = await MemoryVectorStore.fromDocuments(\n", "  [{ pageContent: text, metadata: {} }],\n", "  embeddings,\n", ");\n", "\n", "// Use the vector store as a retriever that returns a single document\n", "const retriever = vectorstore.asRetriever(1);\n", "\n", "// Retrieve the most similar text\n", "const retrievedDocuments = await retriever.invoke(\"What is <PERSON><PERSON><PERSON><PERSON>?\");\n", "\n", "retrievedDocuments[0].pageContent;"]}, {"cell_type": "markdown", "id": "e02b9855", "metadata": {}, "source": ["## Direct Usage\n", "\n", "Under the hood, the vectorstore and retriever implementations are calling `embeddings.embedDocument(...)` and `embeddings.embedQuery(...)` to create embeddings for the text(s) used in `fromDocuments` and the retriever's `invoke` operations, respectively.\n", "\n", "You can directly call these methods to get embeddings for your own use cases.\n", "\n", "### Embed single texts\n", "\n", "You can embed queries for search with `embedQuery`. This generates a vector representation specific to the query:"]}, {"cell_type": "code", "execution_count": 3, "id": "0d2befcd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "         0.0191650390625,  0.004924774169921875,     -0.015838623046875,\n", "          -0.04248046875,     0.040191650390625,      -0.02679443359375,\n", "     -0.0240936279296875,     0.058624267578125,      0.027069091796875,\n", "        -0.0435791015625,      0.01934814453125,      0.040191650390625,\n", "     -0.0194244384765625,   0.01386260986328125,    -0.0216827392578125,\n", "    -0.01073455810546875,   -0.0166168212890625,    0.01073455810546875,\n", "        -0.0228271484375,       0.0062255859375,      0.035064697265625,\n", "     -0.0114593505859375,   -0.0257110595703125,    -0.0285797119140625,\n", "        0.01190185546875,    -0.022186279296875,   -0.01500701904296875,\n", "       -0.03240966796875, 0.0019063949584960938,     -0.039337158203125,\n", "     -0.0047454833984375,              -0.03125,       -0.0123291015625,\n", "    -0.00899505615234375,        -0.02880859375,      0.014678955078125,\n", "         0.0452880859375,      0.05035400390625,     -0.053436279296875,\n", "      0.0265960693359375,   -0.0206756591796875,       0.06658935546875,\n", "      -0.032989501953125,  -0.00724029541015625,  0.0024967193603515625,\n", "      0.0282135009765625,     0.047088623046875,       -0.0255126953125,\n", "      -0.008453369140625,   -0.0039215087890625,     0.0282135009765625,\n", "      0.0270843505859375,      -0.0133056640625,    -0.0296173095703125,\n", "        -0.0455322265625,    0.0225982666015625,      -0.04803466796875,\n", "    -0.00891876220703125,     -0.04669189453125,      0.022064208984375,\n", "     -0.0266571044921875,  -0.01480865478515625,     0.0295257568359375,\n", "    -0.01561737060546875,      -0.0411376953125,    0.01345062255859375,\n", "      0.0219879150390625,    -0.012786865234375,     -0.051727294921875,\n", "  -0.0002830028533935547,   0.00690460205078125,   -0.01303863525390625,\n", "        -0.0457763671875,    -0.026763916015625,    -0.0181121826171875,\n", "     0.00946807861328125,       0.0250244140625,      -0.01458740234375,\n", "         0.0394287109375,   -0.0162200927734375,       0.05169677734375,\n", "     0.01126861572265625,   0.01265716552734375,     -0.009307861328125,\n", "          0.052490234375,    0.0135345458984375,    0.01332855224609375,\n", "       0.040130615234375,       0.0638427734375,     0.0181121826171875,\n", "    0.004207611083984375,          0.0771484375,      0.024078369140625,\n", "       0.012420654296875,       -0.030517578125, -0.0019245147705078125,\n", "      0.0243682861328125,    0.0254974365234375,  0.0036334991455078125,\n", "   -0.004550933837890625\n", "]\n"]}], "source": ["const singleVector = await embeddings.embedQuery(text);\n", "\n", "console.log(singleVector.slice(0, 100));"]}, {"cell_type": "markdown", "id": "1b5a7d03", "metadata": {}, "source": ["### Embed multiple texts\n", "\n", "You can embed multiple texts for indexing with `embedDocuments`. The internals used for this method may (but do not have to) differ from embedding queries:"]}, {"cell_type": "code", "execution_count": 4, "id": "2f4d6e97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "      0.0190887451171875,  0.00482940673828125,   -0.0158233642578125,\n", "       -0.04254150390625,    0.040130615234375,   -0.0268096923828125,\n", "          -0.02392578125,    0.058624267578125,    0.0269927978515625,\n", "          -0.04345703125,   0.0193328857421875,     0.040374755859375,\n", "     -0.0196075439453125,  0.01384735107421875,    -0.021881103515625,\n", "    -0.01068878173828125,   -0.016510009765625,   0.01079559326171875,\n", "     -0.0227813720703125,        0.00634765625,     0.035064697265625,\n", "     -0.0113983154296875,  -0.0257720947265625,   -0.0285491943359375,\n", "       0.011749267578125,  -0.0222625732421875,   -0.0148468017578125,\n", "        -0.0325927734375,  0.00203704833984375,      -0.0394287109375,\n", "   -0.004878997802734375,  -0.0311126708984375,  -0.01248931884765625,\n", "    -0.00897979736328125,  -0.0286407470703125,    0.0146331787109375,\n", "        0.04522705078125,    0.050201416015625,    -0.053314208984375,\n", "      0.0265960693359375,  -0.0207366943359375,      0.06658935546875,\n", "       -0.03302001953125,  -0.0073699951171875,    0.0024261474609375,\n", "       0.028228759765625,     0.04705810546875,   -0.0255279541015625,\n", "     -0.0084075927734375,   -0.003814697265625,    0.0281524658203125,\n", "      0.0272064208984375, -0.01322174072265625,   -0.0295257568359375,\n", "      -0.045623779296875,    0.022735595703125,         -0.0478515625,\n", "    -0.00885772705078125,   -0.046844482421875,     0.022003173828125,\n", "      -0.026458740234375,  -0.0148468017578125,    0.0295562744140625,\n", "    -0.01555633544921875,   -0.041229248046875,      0.01336669921875,\n", "       0.022125244140625, -0.01276397705078125,    -0.051666259765625,\n", "  -0.0002474784851074219, 0.006740570068359375,  -0.01306915283203125,\n", "       -0.04583740234375,      -0.026611328125,   -0.0182342529296875,\n", "        0.00946044921875,   0.0250701904296875,   -0.0146942138671875,\n", "       0.039459228515625,   -0.016265869140625,     0.051788330078125,\n", "     0.01110076904296875,         0.0126953125,  -0.00925445556640625,\n", "       0.052581787109375,  0.01363372802734375,   0.01332855224609375,\n", "        0.04010009765625,      0.0638427734375,     0.018157958984375,\n", "      0.0040740966796875,     0.07720947265625,    0.0240325927734375,\n", "      0.0123443603515625,  -0.0302886962890625, -0.001865386962890625,\n", "       0.024383544921875,    0.025604248046875,   0.00353240966796875,\n", "   -0.004474639892578125\n", "]\n", "[\n", "     0.0053253173828125,    0.01305389404296875,    -0.0253448486328125,\n", "      -0.04241943359375,      0.034942626953125,     -0.017425537109375,\n", "         -0.02783203125,         0.064208984375,     0.0244903564453125,\n", "       -0.0467529296875,      0.021209716796875,       0.02191162109375,\n", "      -0.03131103515625,     -0.019073486328125,   -0.01413726806640625,\n", "     -0.008636474609375,     -0.011627197265625,     0.0229339599609375,\n", "      -0.00762939453125,    0.00594329833984375,     0.0201263427734375,\n", "       -0.0247802734375,      -0.05047607421875,      -0.03765869140625,\n", "     0.0034332275390625,     -0.014617919921875,     -0.043548583984375,\n", "      -0.03594970703125,  0.0002884864807128906,      -0.03656005859375,\n", "    -0.0102691650390625,     0.0121307373046875,    -0.0284271240234375,\n", "       -0.0113525390625,   -0.01195526123046875,    0.01143646240234375,\n", "      0.051727294921875,        0.0230712890625,     -0.046417236328125,\n", "     0.0198211669921875,      -0.02337646484375,      0.040985107421875,\n", "      -0.03314208984375,     -0.025909423828125,   -0.00809478759765625,\n", "     0.0291595458984375,             0.04296875,     -0.016143798828125,\n", "      0.005706787109375,       0.00860595703125, -0.0035343170166015625,\n", "     0.0118560791015625,    -0.0135650634765625,    -0.0294036865234375,\n", "     -0.029876708984375,             0.03515625,       -0.0545654296875,\n", "   0.006862640380859375,     -0.041839599609375,      0.021148681640625,\n", "    -0.0279998779296875,   -0.00949859619140625,       0.03314208984375,\n", "  -0.002727508544921875,          -0.0400390625,    0.01311492919921875,\n", "    0.01177215576171875, -0.0010013580322265625,        -0.052001953125,\n", "    0.00112152099609375,   -0.00815582275390625,        0.0321044921875,\n", "       -0.0496826171875,    -0.0151519775390625,    -0.0262908935546875,\n", "  -0.005207061767578125,     0.0207977294921875,        -0.022705078125,\n", "      0.009735107421875,   0.000682830810546875,       0.05792236328125,\n", "       -0.0145263671875,       0.03643798828125,  0.0018339157104492188,\n", "      0.047210693359375,  0.0017986297607421875,     0.0300140380859375,\n", "      0.027923583984375,      0.044708251953125,      0.027618408203125,\n", "    0.00104522705078125,       0.05987548828125,       0.06304931640625,\n", "     -0.039703369140625,       -0.0386962890625,    0.00797271728515625,\n", "     0.0254974365234375,     0.0245819091796875,      0.010467529296875,\n", "    -0.0080413818359375\n", "]\n"]}], "source": ["const text2 = \"LangGraph is a library for building stateful, multi-actor applications with LLMs\";\n", "\n", "const vectors = await embeddings.embedDocuments([text, text2]);\n", "\n", "console.log(vectors[0].slice(0, 100));\n", "console.log(vectors[1].slice(0, 100));"]}, {"cell_type": "markdown", "id": "8938e581", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all PineconeEmbeddings features and configurations head to the API reference: https://api.js.langchain.com/classes/_langchain_pinecone.PineconeEmbeddings.html"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}