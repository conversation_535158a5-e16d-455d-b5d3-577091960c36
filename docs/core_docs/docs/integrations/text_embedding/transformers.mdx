import CodeBlock from "@theme/CodeBlock";

# HuggingFace Transformers

The `TransformerEmbeddings` class uses the [Transformers.js](https://huggingface.co/docs/transformers.js/index) package to generate embeddings for a given text.

It runs locally and even works directly in the browser, allowing you to create web apps with built-in embeddings.

## Setup

You'll need to install the [@huggingface/transformers](https://www.npmjs.com/package/@huggingface/transformers) package as a peer dependency:

:::tip Compatibility
If you are using a version of community older than 0.3.21, install the older `@xenova/transformers` package and
import the embeddings from `"@langchain/community/embeddings/hf_transformers"` below.
:::

```bash npm2yarn
npm install @huggingface/transformers
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

## Example

Note that if you're using in a browser context, you'll likely want to put all inference-related code in a web worker to avoid
blocking the main thread.

See [this guide](https://huggingface.co/docs/transformers.js/tutorials/next) and the other resources in the Transformers.js docs for an idea of how to
set up your project.

import HFTransformersExample from "@examples/models/embeddings/hf_transformers.ts";

<CodeBlock language="typescript">{HFTransformersExample}</CodeBlock>

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
