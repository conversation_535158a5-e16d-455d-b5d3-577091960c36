# Voyage AI

The `VoyageEmbeddings` class uses the Voyage AI REST API to generate embeddings for a given text.

The `inputType` parameter allows you to specify the type of input text for better embedding results. You can set it to `query`, `document`, or leave it undefined (which is equivalent to `None`).

- `query`: Use this for search or retrieval queries. Voyage AI will prepend a prompt to optimize the embeddings for query use cases.
- `document`: Use this for documents or content that you want to be retrievable. Voyage AI will prepend a prompt to optimize the embeddings for document use cases.
- `None` (default): The input text will be directly encoded without any additional prompt.

Additionally, the class supports new parameters for further customization of the embedding process:

- **truncation**: Whether to truncate the input texts to the maximum length allowed by the model.
- **outputDimension**: The desired dimension of the output embeddings.
- **outputDtype**: The data type of the output embeddings. Can be `"float"` or `"int8"`.
- **encodingFormat**: The format of the output embeddings. Can be `"float"`, `"base64"`, or `"ubinary"`.

```typescript
import { VoyageEmbeddings } from "@langchain/community/embeddings/voyage";

const embeddings = new VoyageEmbeddings({
  apiKey: "YOUR-API-KEY", // In Node.js defaults to process.env.VOYAGEAI_API_KEY
  inputType: "document", // Optional: specify input type as 'query', 'document', or omit for None / Undefined / Null
  truncation: true, // Optional: enable truncation of input texts
  outputDimension: 768, // Optional: set desired output embedding dimension
  outputDtype: "float", // Optional: set output data type ("float" or "int8")
  encodingFormat: "float", // Optional: set output encoding format ("float", "base64", or "ubinary")
});
```

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
