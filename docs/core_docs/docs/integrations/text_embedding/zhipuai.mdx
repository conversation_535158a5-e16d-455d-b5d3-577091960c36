---
sidebar_class_name: node-only
---

# <PERSON>hipuAI

The `ZhipuAIEmbeddings` class uses the ZhipuAI API to generate embeddings for a given text.

## Setup

You'll need to sign up for an ZhipuAI API key and set it as an environment variable named `ZHIPUAI_API_KEY`.

https://open.bigmodel.cn

Then, you'll need to install the [`@langchain/community`](https://www.npmjs.com/package/@langchain/community) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core jsonwebtoken
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import ZhipuAIExample from "@examples/embeddings/zhipuai.ts";

<CodeBlock language="typescript">{ZhipuAIExample}</CodeBlock>

## Related

- Embedding model [conceptual guide](/docs/concepts/embedding_models)
- Embedding model [how-to guides](/docs/how_to/#embedding-models)
