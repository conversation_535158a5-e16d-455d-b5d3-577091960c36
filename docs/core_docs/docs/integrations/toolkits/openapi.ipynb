{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: OpenApi <PERSON>\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# OpenApiToolkit\n", "\n", "```{=mdx}\n", "\n", ":::caution Disclaimer ⚠️\n", "\n", "This agent can make requests to external APIs. Use with caution, especially when granting access to users.\n", "\n", "Be aware that this agent could theoretically send requests with provided credentials or other sensitive data to unverified or potentially malicious URLs --although it should never in theory.\n", "\n", "Consider adding limitations to what actions can be performed via the agent, what APIs it can access, what headers can be passed, and more.\n", "\n", "In addition, consider implementing measures to validate URLs before sending requests, and to securely handle and protect sensitive data such as credentials.\n", "\n", ":::\n", "\n", "```\n", "\n", "This will help you getting started with the [OpenApiToolkit](/docs/concepts/tools/#toolkits). For detailed documentation of all OpenApiToolkit features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain.agents.OpenApiToolkit.html).\n", "\n", "The `OpenAPIToolkit` has access to the following tools:\n", "\n", "| Name            | Description |\n", "|-----------------|-------------|\n", "| `requests_get`  | A portal to the internet. Use this when you need to get specific content from a website. Input should be a url string (i.e. \"https://www.google.com\"). The output will be the text response of the GET request. |\n", "| `requests_post` | Use this when you want to POST to a website. Input should be a json string with two keys: \"url\" and \"data\". The value of \"url\" should be a string, and the value of \"data\" should be a dictionary of key-value pairs you want to POST to the url as a JSON body. Be careful to always use double quotes for strings in the json string. The output will be the text response of the POST request. |\n", "| `json_explorer` | Can be used to answer questions about the openapi spec for the API. Always use this tool before trying to make a request. Example inputs to this tool: 'What are the required query parameters for a GET request to the /bar endpoint?' 'What are the required parameters in the request body for a POST request to the /foo endpoint?' Always give this tool a specific question. |\n", "\n", "## Setup\n", "\n", "This toolkit requires an OpenAPI spec file. The LangChain.js repository has a [sample OpenAPI spec file in the `examples` directory](https://github.com/langchain-ai/langchainjs/blob/cc21aa29102571204f4443a40b53d28581a12e30/examples/openai_openapi.yaml). You can use this file to test the toolkit.\n", "\n", "If you want to get automated tracing from runs of individual tools, you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "process.env.LANGSMITH_TRACING=\"true\"\n", "process.env.LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "This toolkit lives in the `langchain` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our toolkit. First, we need to define the LLM we would like to use in the toolkit.\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 3, "id": "63aec3e6", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "code", "execution_count": 4, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { OpenApiToolkit } from \"langchain/agents/toolkits\"\n", "import * as fs from \"fs\";\n", "import * as yaml from \"js-yaml\";\n", "import { JsonSpec, JsonObject } from \"langchain/tools\";\n", "\n", "// Load & convert the OpenAPI spec from YAML to JSON.\n", "const yamlFile = fs.readFileSync(\"../../../../../examples/openai_openapi.yaml\", \"utf8\");\n", "const data = yaml.load(yamlFile) as JsonObject;\n", "if (!data) {\n", "  throw new Error(\"Failed to load OpenAPI spec\");\n", "}\n", "\n", "// Define headers for the API requests.\n", "const headers = {\n", "  \"Content-Type\": \"application/json\",\n", "  Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,\n", "};\n", "\n", "const toolkit = new OpenApiToolkit(new JsonSpec(data), llm, headers);"]}, {"cell_type": "markdown", "id": "5c5f2839-4020-424e-9fc9-07777eede442", "metadata": {}, "source": ["## Tools\n", "\n", "View available tools:"]}, {"cell_type": "code", "execution_count": 6, "id": "51a60dbe-9f2e-4e04-bb62-23968f17164a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'requests_get',\n", "    description: 'A portal to the internet. Use this when you need to get specific content from a website.\\n' +\n", "      '  Input should be a url string (i.e. \"https://www.google.com\"). The output will be the text response of the GET request.'\n", "  },\n", "  {\n", "    name: 'requests_post',\n", "    description: 'Use this when you want to POST to a website.\\n' +\n", "      '  Input should be a json string with two keys: \"url\" and \"data\".\\n' +\n", "      '  The value of \"url\" should be a string, and the value of \"data\" should be a dictionary of\\n' +\n", "      '  key-value pairs you want to POST to the url as a JSON body.\\n' +\n", "      '  Be careful to always use double quotes for strings in the json string\\n' +\n", "      '  The output will be the text response of the POST request.'\n", "  },\n", "  {\n", "    name: 'j<PERSON>_explorer',\n", "    description: '\\n' +\n", "      'Can be used to answer questions about the openapi spec for the API. Always use this tool before trying to make a request. \\n' +\n", "      'Example inputs to this tool: \\n' +\n", "      \"    'What are the required query parameters for a GET request to the /bar endpoint?'\\n\" +\n", "      \"    'What are the required parameters in the request body for a POST request to the /foo endpoint?'\\n\" +\n", "      'Always give this tool a specific question.'\n", "  }\n", "]\n"]}], "source": ["const tools = toolkit.getTools();\n", "\n", "console.log(tools.map((tool) => ({\n", "  name: tool.name,\n", "  description: tool.description,\n", "})))"]}, {"cell_type": "markdown", "id": "dfe8aad4-8626-4330-98a9-7ea1ca5d2e0e", "metadata": {}, "source": ["## Use within an agent\n", "\n", "First, ensure you have LangGraph installed:\n", "\n", "```{=mdx}\n", "<Npm2Yarn>\n", "  @langchain/langgraph\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "code", "execution_count": 5, "id": "310bf18e-6c9a-4072-b86e-47bc1fcca29d", "metadata": {}, "outputs": [], "source": ["import { createReactAgent } from \"@langchain/langgraph/prebuilt\"\n", "\n", "const agentExecutor = createReactAgent({ llm, tools });"]}, {"cell_type": "code", "execution_count": 6, "id": "23e11cc9-abd6-4855-a7eb-799f45ca01ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'requests_post',\n", "    args: {\n", "      input: '{\"url\":\"https://api.openai.com/v1/chat/completions\",\"data\":{\"model\":\"gpt-4o-mini\",\"messages\":[{\"role\":\"user\",\"content\":\"tell me a joke.\"}]}}'\n", "    },\n", "    type: 'tool_call',\n", "    id: 'call_1HqyZrbYgKFwQRfAtsZA2uL5'\n", "  }\n", "]\n", "{\n", "  \"id\": \"chatcmpl-9t36IIuRCs0WGMEy69HUqPcKvOc1w\",\n", "  \"object\": \"chat.completion\",\n", "  \"created\": 1722906986,\n", "  \"model\": \"gpt-4o-mini-2024-07-18\",\n", "  \"choices\": [\n", "    {\n", "      \"index\": 0,\n", "      \"message\": {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"Why don't skeletons fight each other? \\n\\nThey don't have the guts!\"\n", "      },\n", "      \"logprobs\": null,\n", "      \"finish_reason\": \"stop\"\n", "    }\n", "  ],\n", "  \"usage\": {\n", "    \"prompt_tokens\": 12,\n", "    \"completion_tokens\": 15,\n", "    \"total_tokens\": 27\n", "  },\n", "  \"system_fingerprint\": \"fp_48196bc67a\"\n", "}\n", "\n", "Here's a joke for you:\n", "\n", "**Why don't skeletons fight each other?**  \n", "They don't have the guts!\n"]}], "source": ["const exampleQuery = \"Make a POST request to openai /chat/completions. The prompt should be 'tell me a joke.'. Ensure you use the model 'gpt-4o-mini'.\"\n", "\n", "const events = await agentExecutor.stream(\n", "  { messages: [[\"user\", exampleQuery]]},\n", "  { streamMode: \"values\", }\n", ")\n", "\n", "for await (const event of events) {\n", "  const lastMsg = event.messages[event.messages.length - 1];\n", "  if (lastMsg.tool_calls?.length) {\n", "    console.dir(lastMsg.tool_calls, { depth: null });\n", "  } else if (lastMsg.content) {\n", "    console.log(lastMsg.content);\n", "  }\n", "}"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all OpenApiToolkit features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain.agents.OpenApiToolkit.html)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}