---
sidebar_label: AWS Step Functions Toolkit
hide_table_of_contents: true
---

# AWS Step Functions Toolkit

**AWS Step Functions** are a visual workflow service that helps developers use AWS services to build distributed applications, automate processes, orchestrate microservices, and create data and machine learning (ML) pipelines.

By including a `AWSSfn` tool in the list of tools provided to an Agent, you can grant your Agent the ability to invoke async workflows running in your AWS Cloud.

When an Agent uses the `AWSSfn` tool, it will provide an argument of type `string` which will in turn be passed into one of the supported actions this tool supports. The supported actions are: `StartExecution`, `DescribeExecution`, and `SendTaskSuccess`.

## Setup

You'll need to install the Node AWS Step Functions SDK:

```bash npm2yarn
npm install @aws-sdk/client-sfn
```

## Usage

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

### Note about credentials:

- If you have not run [`aws configure`](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-configure.html) via the AWS CLI, the `region`, `accessKeyId`, and `secretAccessKey` must be provided to the AWSSfn constructor.
- The IAM role corresponding to those credentials must have permission to invoke the Step Function.

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/agents/aws_sfn.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>
