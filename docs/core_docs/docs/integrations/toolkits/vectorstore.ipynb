{"cells": [{"cell_type": "raw", "id": "afaf8039", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: VectorStore Toolkit\n", "---"]}, {"cell_type": "markdown", "id": "e49f1e0d", "metadata": {}, "source": ["# VectorStoreToolkit\n", "\n", "This will help you getting started with the [VectorStoreToolkit](/docs/concepts/tools/#toolkits). For detailed documentation of all VectorStoreToolkit features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain.agents.VectorStoreToolkit.html).\n", "\n", "The `VectorStoreToolkit` is a toolkit which takes in a vector store, and converts it to a tool which can then be invoked, passed to LLMs, agents and more.\n", "\n", "## Setup\n", "\n", "If you want to get automated tracing from runs of individual tools, you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "process.env.LANGSMITH_TRACING=\"true\"\n", "process.env.LANGSMITH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "### Installation\n", "\n", "This toolkit lives in the `langchain` package:\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "markdown", "id": "a38cde65-254d-4219-a441-068766c0d4b5", "metadata": {}, "source": ["## Instantiation\n", "\n", "Now we can instantiate our toolkit. First, we need to define the LLM we'll use in the toolkit.\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "79d116f5", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "code", "execution_count": 3, "id": "cb09c344-1836-4e0c-acf8-11d13ac1dbae", "metadata": {}, "outputs": [], "source": ["import { VectorStoreToolkit, VectorStoreInfo } from \"langchain/agents/toolkits\"\n", "import { OpenAIEmbeddings } from \"@langchain/openai\"\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\"\n", "import { RecursiveCharacterTextSplitter } from \"@langchain/textsplitters\";\n", "import fs from \"fs\";\n", "\n", "// Load a text file to use as our data source.\n", "const text = fs.readFileSync(\"../../../../../examples/state_of_the_union.txt\", \"utf8\");\n", "\n", "// Split the text into chunks before inserting to our store\n", "const textSplitter = new RecursiveCharacterTextSplitter({ chunkSize: 1000 });\n", "const docs = await textSplitter.createDocuments([text]);\n", "\n", "const vectorStore = await MemoryVectorStore.fromDocuments(docs, new OpenAIEmbeddings());\n", "\n", "const vectorStoreInfo: VectorStoreInfo = {\n", "  name: \"state_of_union_address\",\n", "  description: \"the most recent state of the Union address\",\n", "  vectorStore,\n", "};\n", "\n", "const toolkit = new VectorStoreToolkit(vectorStoreInfo, llm);"]}, {"cell_type": "markdown", "id": "5c5f2839-4020-424e-9fc9-07777eede442", "metadata": {}, "source": ["## Tools\n", "\n", "Here, we can see it converts our vector store into a tool:"]}, {"cell_type": "code", "execution_count": 4, "id": "51a60dbe-9f2e-4e04-bb62-23968f17164a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'state_of_union_address',\n", "    description: 'Useful for when you need to answer questions about state_of_union_address. Whenever you need information about the most recent state of the Union address you should ALWAYS use this. Input should be a fully formed question.'\n", "  }\n", "]\n"]}], "source": ["const tools = toolkit.getTools();\n", "\n", "console.log(tools.map((tool) => ({\n", "  name: tool.name,\n", "  description: tool.description,\n", "})))"]}, {"cell_type": "markdown", "id": "dfe8aad4-8626-4330-98a9-7ea1ca5d2e0e", "metadata": {}, "source": ["## Use within an agent\n", "\n", "First, ensure you have LangGraph installed:\n", "\n", "```{=mdx}\n", "<Npm2Yarn>\n", "  @langchain/langgraph\n", "</Npm2Yarn>\n", "```\n", "\n", "Then, instantiate the agent:"]}, {"cell_type": "code", "execution_count": 6, "id": "310bf18e-6c9a-4072-b86e-47bc1fcca29d", "metadata": {}, "outputs": [], "source": ["import { createReactAgent } from \"@langchain/langgraph/prebuilt\"\n", "\n", "const agentExecutor = createReactAgent({ llm, tools });"]}, {"cell_type": "code", "execution_count": 8, "id": "23e11cc9-abd6-4855-a7eb-799f45ca01ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'state_of_union_address',\n", "    args: {\n", "      input: 'What did <PERSON><PERSON> say about <PERSON><PERSON><PERSON> in the State of the Union address?'\n", "    },\n", "    type: 'tool_call',\n", "    id: 'call_glJSWLNrftKHa92A6j8x4jhd'\n", "  }\n", "]\n", "In the State of the Union address, <PERSON><PERSON> mentioned that he nominated Circuit Court of Appeals Judge <PERSON><PERSON><PERSON>, describing her as one of the nation’s top legal minds who will continue <PERSON>’s legacy of excellence. He highlighted her background as a former top litigator in private practice, a former federal public defender, and noted that she comes from a family of public school educators and police officers. He also pointed out that she has received a broad range of support since her nomination.\n", "In the State of the Union address, President <PERSON><PERSON> spoke about <PERSON><PERSON><PERSON>, stating that he nominated her as one of the nation’s top legal minds who will continue <PERSON>’s legacy of excellence. He highlighted her experience as a former top litigator in private practice and a federal public defender, as well as her background coming from a family of public school educators and police officers. <PERSON><PERSON> also noted that she has received a broad range of support since her nomination.\n"]}], "source": ["const example<PERSON><PERSON>y = \"What did biden say about <PERSON><PERSON><PERSON> is the state of the union address?\"\n", "\n", "const events = await agentExecutor.stream(\n", "  { messages: [[\"user\", exampleQuery]]},\n", "  { streamMode: \"values\", }\n", ")\n", "\n", "for await (const event of events) {\n", "  const lastMsg = event.messages[event.messages.length - 1];\n", "  if (lastMsg.tool_calls?.length) {\n", "    console.dir(lastMsg.tool_calls, { depth: null });\n", "  } else if (lastMsg.content) {\n", "    console.log(lastMsg.content);\n", "  }\n", "}"]}, {"cell_type": "markdown", "id": "3a5bb5ca-c3ae-4a58-be67-2cd18574b9a3", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all VectorStoreToolkit features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain.agents.VectorStoreToolkit.html)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}