import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/tools/connery.ts";
import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

# Connery Action Tool

Using this tool, you can integrate individual Connery Action into your LangChain agent.

:::note
If you want to use more than one Connery Action in your agent,
check out the [Connery Toolkit](/docs/integrations/toolkits/connery) documentation.
:::

## What is Connery?

Connery is an open-source plugin infrastructure for AI.

With Connery, you can easily create a custom plugin with a set of actions and seamlessly integrate them into your LangChain agent.
Connery will take care of critical aspects such as runtime, authorization, secret management, access management, audit logs, and other vital features.

Furthermore, <PERSON><PERSON>, supported by our community, provides a diverse collection of ready-to-use open-source plugins for added convenience.

Learn more about Connery:

- GitHub: https://github.com/connery-io/connery
- Documentation: https://docs.connery.io

## Prerequisites

To use Connery Actions in your LangChain agent, you need to do some preparation:

1. Set up the Connery runner using the [Quickstart](https://docs.connery.io/docs/runner/quick-start/) guide.
2. Install all the plugins with the actions you want to use in your agent.
3. Set environment variables `CONNERY_RUNNER_URL` and `CONNERY_RUNNER_API_KEY` so the toolkit can communicate with the Connery Runner.

## Example of using Connery Action Tool

### Setup

To use the Connery Action Tool you need to install the following official peer dependency:

```bash npm2yarn
npm install @langchain/community @langchain/core
```

<IntegrationInstallTooltip></IntegrationInstallTooltip>

### Usage

In the example below, we fetch action by its ID from the Connery Runner and then call it with the specified parameters.

Here, we use the ID of the **Send email** action from the [Gmail](https://github.com/connery-io/gmail) plugin.

:::info
You can see a LangSmith trace of this example [here](https://smith.langchain.com/public/c4b6723d-f91c-440c-8682-16ec8297a602/r).
:::

<CodeBlock language="typescript">{Example}</CodeBlock>

:::note
Connery Action is a structured tool, so you can only use it in the agents supporting structured tools.
:::

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
