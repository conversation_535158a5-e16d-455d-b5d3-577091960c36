---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";
import ToolExample from "@examples/tools/dalle_image_generation.ts";

# Dall-E Tool

The Dall-E tool allows your agent to create images using OpenAI's Dall-E image generation tool.

## Setup

You will need an OpenAI API Key which you can get from the [OpenAI web site](https://openai.com)
and then set the OPENAI_API_KEY environment variable to the key you just created.

To use the Dall-E Tool you need to install the LangChain OpenAI integration package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
