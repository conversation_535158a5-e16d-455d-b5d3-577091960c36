---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Discord Tool

The Discord Tool gives your agent the ability to search, read, and write messages to discord channels.
It is useful for when you need to interact with a discord channel.

## Setup

To use the Discord Tool you need to install the following official peer depencency:

```bash npm2yarn
npm install discord.js
```

## Usage, standalone

import <PERSON><PERSON><PERSON><PERSON>mple from "@examples/tools/discord.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Usage, in an Agent

import AgentExample from "@examples/agents/discord.ts";

<CodeBlock language="typescript">{AgentExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
