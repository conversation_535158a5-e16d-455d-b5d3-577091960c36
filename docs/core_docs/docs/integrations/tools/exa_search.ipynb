{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ExaSearchResults\n", "\n", "Exa (formerly Metaphor Search) is a search engine fully designed for use by LLMs. Search for documents on the internet using natural language queries, then retrieve cleaned HTML content from desired documents.\n", "\n", "Unlike keyword-based search (Google), Exa's neural search capabilities allow it to semantically understand queries and return relevant documents. For example, we could search `\"fascinating article about cats\"` and compare the search results from Google and Exa. Google gives us SEO-optimized listicles based on the keyword “fascinating”. Exa just works.\n", "\n", "This page goes over how to use `ExaSearchResults` with Lang<PERSON><PERSON><PERSON>.\n", "\n", "## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | Serializable | [PY support](https://python.langchain.com/docs/integrations/tools/exa_search/) |  Package latest |\n", "| :--- | :--- | :---: | :---: | :---: |\n", "| [ExaSearchResults](https://api.js.langchain.com/classes/langchain_exa.ExaSearchResults.html) | [@langchain/exa](https://npmjs.com/package/@langchain/exa) | ❌ | ✅ |  ![NPM - Version](https://img.shields.io/npm/v/@langchain/exa?style=flat-square&label=%20&) |\n", "\n", "## Setup\n", "\n", "The integration lives in the `@langchain/exa` package.\n", "\n", "```{=mdx}\n", "\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/exa @langchain/core\n", "</Npm2Yarn>\n", "\n", "```\n", "\n", "### Credentials\n", "\n", "First, get an Exa API key and add it as an environment variable. Get 1000 free searches/month by signing up [here](https://dashboard.exa.ai/login).\n", "\n", "```typescript\n", "process.env.EXASEARCH_API_KEY=\"your-api-key\"\n", "```\n", "\n", "It's also helpful (but not needed) to set up LangSmith for best-in-class observability:\n", "\n", "```typescript\n", "process.env.LANGSMITH_TRACING=\"true\"\n", "process.env.LANGSMITH_API_KEY=\"your-api-key\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Instantiation\n", "\n", "Here we show how to insatiate an instance of the `ExaSearchResults` tool:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import { ExaSearchResults } from \"@langchain/exa\"\n", "import Exa from \"exa-js\";\n", "\n", "// @lc-ts-ignore\n", "const client = new Exa(process.env.EXASEARCH_API_KEY)\n", "\n", "const tool = new ExaSearchResults({\n", "  // @lc-ts-ignore\n", "  client,\n", "  searchArgs: {\n", "    numResults: 2,\n", "  }\n", "})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Invocation\n", "\n", "### [Invoke directly with args](/docs/concepts/tools)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"results\":[{\"score\":0.*****************,\"title\":\"Hawaii Weather Forecast\",\"id\":\"https://www.willyweather.com/hi/hawaii.html\",\"url\":\"https://www.willyweather.com/hi/hawaii.html\",\"publishedDate\":\"2023-01-01\",\"author\":\"\",\"text\":\"Get an account to remove ads    View More Real-Time Extremes   Nation State County      Hottest 78.8 °FFaleolo Intl / Apia, Samoa, HI    Coldest 51.6 °FBradshaw Army Air Field / Hawaii, HI    Windiest 12.7mphBradshaw Army Air Field / Hawaii, HI    Most Humid 100%Hilo, Hilo International Airport, HI    Least Humid 73.32%Kailua / Kona, Keahole Airport, HI    Highest Pressure 1030.5 hPaBradshaw Army Air Field / Hawaii, HI    Lowest Pressure 1008 hPaFaleolo Intl / Apia, Samoa, HI\"},{\"score\":0.****************,\"title\":\"The Hawaii Climate To Prepare For Your Maui Wedding\",\"id\":\"https://mymauiwedding.weebly.com/blog6/the-hawaii-climate-to-prepare-for-your-maui-wedding\",\"url\":\"https://mymauiwedding.weebly.com/blog6/the-hawaii-climate-to-prepare-for-your-maui-wedding\",\"publishedDate\":\"2012-04-26\",\"author\":\"\",\"text\":\"Since the The hawaiian islands environment is very constant throughout the season with only slight heat range changes, you can travel there any season. While the moisture is very high, the continuous exotic sea breezes keep the circumstances very relaxed throughout the season. During the day you will be relaxed in a T-shirt or an Aloha clothing and a couple of shoes. Once the sun places you will probably want to wear a light coat since the circumstances can fall around ten levels. The protecting impact of the hills and the variations in climate at various levels make a variety of environment areas. The unique micro-climates are specific for the internal valleys, hill hills and seashores in The hawaiian islands. Located at the side of the exotic location and due to year-round heated sea exterior circumstances, which keep the overlying environment heated, The hawaiian islands has only two circumstances, both of them heated and one with a little bit more rain. Hawaii Climate During Summer  Between the several weeks of Apr and Nov the environment is more dry and hotter with the conditions including 75-88. In the summer time the northern eastern business gusts of wind carry most of the rain to the destinations leeward part, which delivers a welcome comfort from the hot and dry climate.The conditions you will encounter will be proportional to where you are on the destinations. If you are on the edges that are protected from the gusts of wind, the the southeast part of and European factors, you will encounters hot and dry circumstances. If you are on the windward factors, northern or eastern, you will obtain the complete power of the gusts of wind and encounter moister and shade circumstances. Go windward for exotic circumstances and leeward for an dry environment. Hawaii Climate During Winter  From Dec to Apr it is just a little bit chilly, with conditions between 68-80 F. Winter season is regarded rain. The biggest down pours come between Oct and Apr (the hoo'ilo season). Though stormy weather may be common, they usually complete through the destinations quickly and without event. There are more dark times to mess up your laying in the sun, but it hardly ever down pours more than 3 times in a row in one identify. Winter is search period, so if you're a search participant, come to the Northern Coast in Explore to get the ideal trend. Also, whale viewing period is at the end of winter, during Jan to Apr, so make sure you are here if you want to see these spectacular creatures! Hawaii Climate is Greatly Influenced by the Mountains  The hills around the destinations are accountable for the large variety of circumstances. As an example, Kauai's Mt. Waialele is one of the rainiest destinations on the world. Mt. Waialele gets over 420 inches large of rainfall each season, but just a few kilometers down the line, Waimea Canyn is absolutely dry and has been nicknamed the \\\"Grand Canyn of the Pacific\\\". On Big Isle The hawaiian destinations, Hilo is one of the rainiest places in the nation, with 180 inches large of rainfall a season. But Puako, only 60 kilometers away, gets less than 6 inches large of rainfall. If you choose to discover the organic charm discovered at greater levels such as Mauna Kea, use long jeans and several levels of awesome climate outfits. The heat variety in the greater destinations falls 3.5 levels for every 1,000 toes above sea level.Watching the dawn from Mt Haleakala's peak is a incredible concept, but be sure to package up with neckties and work gloves that will keep you comfortable. The circumstances at the peak can fall to 30 F!. Also know that there is less security from the sun at greater levels so be sure to utilize the sun display liberally and use eyewear and a hat. The environment can modify greatly in just a few time when you are in the hills. The exclusive The hawaiian destinations environment makes it possible to sun shower on the Kona Shore and ski on Mauna Kea in the same day.\"}],\"requestId\":\"2145d8de65373c70250400c2c9e8eb13\"}\n"]}], "source": ["await tool.invoke(\"what is the weather in wailea?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### [Invoke with ToolCall](/docs/concepts/tools)\n", "\n", "We can also invoke the tool with a model-generated `ToolCall`, in which case a `ToolMessage` will be returned:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ToolMessage {\n", "  \"content\": \"{\\\"results\\\":[{\\\"score\\\":0.12955062091350555,\\\"title\\\":\\\"Urban Dictionary: Waianae\\\",\\\"id\\\":\\\"https://www.urbandictionary.com/define.php?term=Waianae\\\",\\\"url\\\":\\\"https://www.urbandictionary.com/define.php?term=Waianae\\\",\\\"publishedDate\\\":\\\"2006-04-19\\\",\\\"author\\\":\\\"\\\",\\\"text\\\":\\\"Hot but good time for go beach ,in this part of Hawaii you HAVE to have respect ,with people and their stuff, but Some people like act dumb and stupid so that’s the only thing that make Waianae look bad , but foreal kine in this part of Hawaii we have respect and if we don’t get that respect you gon expect no respect back .   Get the Waianae mug.    Advertise here for $5/day    Located on the west end of Oahu. Waianae gets a bad reputation for being poor, dirty, scary, etc. Its hot and dry out west and the beaches are super nice. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and Pray for Sex are some great beaches to name a few. Mostly locals and the majority of the homeless live out here. Even though its a little rough, the people have alot of aloha who live out here. Most important thing here is to have respect for other people and their stuff.   Get the WAIANAE mug.    Advertise here for $5/day    When going too the island of Honolulu if you go to an amazing part for the island called Waianae, say ho sole u know where can find 1 top banggahh like get 1 Waianae special. Then say shoots boto  by   August 1, 2021   Get the Waianae special mug.\\\"},{\\\"score\\\":0.12563708424568176,\\\"title\\\":\\\"Mount Waialeale: One of the Wettest Spots on Earth | Hawaii.com\\\",\\\"id\\\":\\\"https://www.hawaii.com/trip-ideas/mount-waialeale-one-of-the-wettest-spots-on-earth/\\\",\\\"url\\\":\\\"https://www.hawaii.com/trip-ideas/mount-waialeale-one-of-the-wettest-spots-on-earth/\\\",\\\"publishedDate\\\":\\\"2022-01-18\\\",\\\"author\\\":\\\"Matthew Jones\\\",\\\"text\\\":\\\"Wai’ale’ale, Kauai without much cloud cover. Photo:  WiseTim .   \\\\nMount Wai‘ale‘ale on the gorgeous island of Kaua‘i is often referred to as the wettest spot on earth. While the more than 5,000-foot tall mountain that’s often enshrouded in clouds does receive a tremendous amount of rainfall each year, it’s more accurately “one of” the wettest spots on earth. The average annual rainfall is around 500 inches but some spots on the planet, such as “Big Bog” on Maui, typically acquire even more moisture.\\\\nLegend Has It\\\\n    Road to Waialeale Basin, Kauai. Photo:  Bryce Edwards .  \\\\nMany legends surround this mystical peak that includes native inhabitants climbing to the top to make offerings to the Hawaiian god, Kane. Remains of a heiau (place of worship constructed from rocks) at the summit confirm that some kind of ancient activity took place here, even though getting to the water-logged location seems nearly impossible.\\\\nWai‘ale‘ale, which is actually a dormant shield volcano, means “rippling or overflowing water” in Hawaiian. Consider yourself lucky if you capture a glimpse of the top of the sky-high summit during your vacation. The best opportunity is during crisp, early mornings before clouds form. But you also need to be in the proper location – Līhu‘e, Kapa‘a, and Wailua offer some of the best vantage points for Wai‘ale‘ale.\\\\nAs Seen From Kuilau Ridge\\\\n    Views of Mount Waialeale from Kuilau Ridge, Kauai. Photo:  Martin Bravenboer .  \\\\nTo get even closer to the second-highest peak on the island you can traverse the Kuilau Ridge Trail in Wailua, located near the end of Kuamo‘o Road. About midway through the easy 2-mile roundtrip hike is a great spot for viewing the mountain.\\\\nWeeping Wall\\\\n    Mount Waialeale “Wall of Tears” from the air. Photo:  FH .  \\\\nFurther down the road and well beyond the paved portion is another hike that takes daring souls to the basin of Wai‘ale‘ale called the “Weeping Wall” where numerous ribbons of waterfalls cascade from the summit. But don’t even consider this adventure unless you’re accompanied by an experienced local guide, as you can easily get lost since there is no maintained trail and there is always a high risk for flash flooding that creates dangerous encounters with rushing water.\\\\nViews from the Alakai Swamp Trail\\\\n    Kilohana Overlook of Hanalei Bay. Photo:  Hawaii Savvy .  \\\\nThat said, there is another safer way to get close to this magical mountain – via the Alaka‘i Swamp Trail located in Koke‘e State Park. The difficult hike is about 8 miles roundtrip and you must start out extremely early to get to the midway point in time to see the vista before fog settles in. But those who see Wai‘ale‘ale uncovered at this prime vantage point, along with Hanalei Bay below, are in for a tremendous treat.\\\"}],\\\"requestId\\\":\\\"37fb09f547148c664026aa61f19c27ed\\\"}\",\n", "  \"name\": \"exa_search_results_json\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {},\n", "  \"tool_call_id\": \"1\"\n", "}\n"]}], "source": ["// This is usually generated by a model, but we'll create a tool call directly for demo purposes.\n", "const modelGeneratedToolCall = {\n", "  args: {\n", "    input: \"what is the weather in wailea\"\n", "  },\n", "  id: \"1\",\n", "  name: tool.name,\n", "  type: \"tool_call\",\n", "}\n", "await tool.invoke(modelGeneratedToolCall)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Chaining\n", "\n", "We can use our tool in a chain by first binding it to a [tool-calling model](/docs/how_to/tool_calling) and then calling it:\n", "\n", "```{=mdx}\n", "\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "\n", "```"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "\n", "import { ChatOpenAI } from \"@langchain/openai\"\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "})"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\"\n", "import { RunnableConfig } from \"@langchain/core/runnables\"\n", "import { AIMessage } from \"@langchain/core/messages\"\n", "\n", "const prompt = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\"system\", \"You are a helpful assistant.\"],\n", "    [\"human\", \"{user_input}\"],\n", "    [\"placeholder\", \"{messages}\"],\n", "  ]\n", ")\n", "\n", "// specifying tool_choice will force the model to call this tool.\n", "const llmWithTools = llm.bindTools([tool], {\n", "  tool_choice: tool.name\n", "})\n", "\n", "const llmChain = prompt.pipe(llmWithTools);\n", "\n", "const toolChain = async (userInput: string, config?: RunnableConfig): Promise<AIMessage> => {\n", "  const input_ = { user_input: userInput };\n", "  const aiMsg = await llmChain.invoke(input_, config);\n", "  const toolMsgs = await tool.batch(aiMsg.tool_calls, config);\n", "  return llmChain.invoke({ ...input_, messages: [aiMsg, ...toolMsgs] }, config);\n", "};\n", "\n", "const toolChainResult = await toolChain(\"What is Anthropic's estimated revenue for 2024?\");"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"tool_calls\": [\n", "    {\n", "      \"name\": \"exa_search_results_json\",\n", "      \"args\": {\n", "        \"input\": \"Anthropic revenue 2024 projections\"\n", "      },\n", "      \"type\": \"tool_call\",\n", "      \"id\": \"call_cgC1G9vjXIjHub0TkVfxiDcr\"\n", "    }\n", "  ],\n", "  \"content\": \"\"\n", "}\n"]}], "source": ["const { tool_calls, content } = toolChainResult;\n", "\n", "console.log(\"AIMessage\", JSON.stringify({\n", "  tool_calls,\n", "  content\n", "}, null, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## With an Agent\n", "\n", "We can create LangChain tools which use the `ExaRetriever` and the `createRetrieverTool` Using these tools we can construct a simple search agent that can answer questions about any topic.\n", "\n", "We'll use LangGraph to create the agent. Make sure you have `@langchain/langgraph` installed:\n", "\n", "```{=mdx}\n", "<Npm2Yarn>\n", "  @langchain/langgraph\n", "</Npm2Yarn>\n", "\n", "Then, define the LLM to use with the agent\n", "\n", "<ChatModelTabs customVarName=\"llmForAgent\" />\n", "```"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from \"@langchain/openai\";\n", "\n", "const llmForAgent = new ChatOpenAI({\n", "  model: \"gpt-4o\",\n", "  temperature: 0\n", "})"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import Exa from \"exa-js\";\n", "import { createRetrieverTool } from \"langchain/tools/retriever\";\n", "import { ExaRetriever } from \"@langchain/exa\";\n", "import { createReactAgent } from \"@langchain/langgraph/prebuilt\";\n", "\n", "// @lc-ts-ignore\n", "const agentClient = new Exa(process.env.EXASEARCH_API_KEY);\n", "\n", "const exaRetrieverForAgent = new ExaRetriever({\n", "  // @lc-ts-ignore\n", "  client: agent<PERSON><PERSON>,\n", "  searchArgs: {\n", "    numResults: 2,\n", "  },\n", "});\n", "\n", "// Convert the ExaRetriever into a tool\n", "const searchToolForAgent = createRetrieverTool(exaRetrieverForAgent, {\n", "  name: \"search\",\n", "  description: \"Get the contents of a webpage given a string search query.\",\n", "});\n", "\n", "const toolsForAgent = [searchToolForAgent];\n", "\n", "const agentExecutor = createReactAgent({\n", "  llm: llmForAgent,\n", "  tools: toolsForAgent,\n", "})"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    name: 'search',\n", "    args: { query: 'fascinating article about cats' },\n", "    type: 'tool_call',\n", "    id: 'call_EcA0tmWsyNktO7HAsGQnqLVt'\n", "  }\n", "]\n", "No one seems to think brushing kitty's teeth is worth the hassle.      <PERSON> / The Atlantic; <PERSON><PERSON>      On the list of perfect pet parents, <PERSON><PERSON>, a professional feline-behavior consultant, probably ranks high. The Ph.D. expert in animal cognition spends half an hour each evening playing with her three torbie cats, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. She’s trained them to take pills in gelatin capsules, just in case they eventually need meds. She even commissioned a screened-in backyard catio so that the girls can safely venture outside. <PERSON><PERSON> would do anything for her cats—well, almost anything. “Guilty as charged,<PERSON> <PERSON><PERSON> told me. “I do not brush my cats’ teeth.” To be fair, most cat owners don’t—probably because they’re well aware that it’s weird, if not downright terrifying, to stick one’s fingers inside an ornery cat’s mouth. Reliable stats are scarce, but informal surveys suggest that less than 5 percent of owners give their cats the dental scrub-a-dub-dub—an estimate that the vets I spoke with endorse. “I’m always very shocked if someone says they brush their cat’s teeth,” says <PERSON><PERSON>, a veterinary dentist in California. When <PERSON>, a vet in North Carolina, suggests the practice to his clients, many of them “look at me like I’ve totally lost it,” he told me. (This is where I out myself as one of the loons: My cats, <PERSON> and <PERSON><PERSON><PERSON>, get their teeth brushed thrice weekly.) There certainly is an element of absurdity to all of this. <PERSON>, after all, aren’t skulking the savannas for Oral-Bs. But our pets don’t share the diets and lifestyles of their wild counterparts, and their teeth are quite susceptible to the buildup of bacteria that can eventually invade the gums to trigger prolonged, painful disease. Studies suggest that most domestic cats older than four end up developing some sort of gum affliction; several experts told me that the rates of periodontal disease in household felines can exceed 80 percent. Left untreated, these ailments can cost a cat one or more teeth, or even spread their effects throughout the body, potentially compromising organs such as the kidneys, liver, and heart. To stave off kitty gum disease, veterinary guidelines and professionals generally recommend that owners clean their cat’s chompers daily, ideally for at least a minute, hitting every tooth. “That’s the gold standard,” says Santiago Peralta, a veterinary dentist at Cornell University. Even a gap of two or three days can leave enough time for tartar to cement, Jeanne Perrone, a veterinary-dentistry trainer in Florida, told me. But brushing feline teeth is also really, really,  really  hard. Most cats aren’t keen on having things shoved into their mouth, especially not bristly, sludge-covered sticks. (Dogs don’t always love cleanings either, but they’re at least used to engaging their owners with their mouths.) My old cat, Luna, was once so desperate to escape a brushing that she shrieked in my face, then peed all over the floor.  Read: Why we think cats are psychopaths  A niche industry has sprouted to ease the ordeal for hygiene-conscious humans: poultry-flavored toothpastes, cat-size toothbrushes, silicone scrubbers that fit on fingers. Sometimes the gear helps; when Chin-Sun Lee, a New Orleans–based writer, purchased malt-flavored toothpaste for her cat, Tuesday, he went bonkers for the stuff. Every morning, he comes trotting over just so he can lick the brush. Krissy Lyon, a neuroscientist at the Salk Institute, told me that one of her cats, Cocchi, is so crazy for his toothpaste that she and her partner have to “restrain him or lock him in a different room” while they’re brushing the teeth of their other cat, Noma.   Tuesday (left) and Calvin (right) getting their teeth brushed. (Courtesy of Chin-Sun Lee and Katherine J. Wu)   But tasty toothpaste isn’t a sufficient lure for all. Valeika, who extols the virtues of feline oral health, admitted that even his own cat, Boocat, doesn’t reap the benefits of his brushing expertise. He “tried hard-core for a couple weeks” when he adopted her seven years ago. But Boocat was too feisty to stand for such a thing. “She can be a real terror,” Valeika told me. “We once saw her chase a bear out of our yard.” Maybe Boocat is picking up on how odd the whole toothbrushing ritual can be. Even most American people weren’t regularly scrubbing their dentition until around the time of World War II. Vet dentistry, which borrowed principles from its human analogue, “is a relatively new discipline,” Peralta told me. “Thirty years ago, nobody was even thinking about dog or cat teeth.” Nor was it all that long ago that people across the country routinely let their pets sleep outside, eat only table scraps, and run hog wild through the streets. Now pets have become overly pampered, their accessories Gooped. Experts told me that they’ve seen all kinds of snake-oil hacks that purport to functionally replace feline toothbrushing—sprays, gels, toys, water additives, even calls to rub cat teeth with coconut oil. A lot of these products end up just cosmetically whitening teeth, temporarily freshening breath, or accomplishing nothing at all. If a super-simple, once-a-month magic bullet for dental hygiene existed, Tsugawa told me, “we’d be doing it for our own teeth.” There are probably a lot of un-toothbrushed cats out there who could be s-l-o-w-l-y taught to accept the process and maybe even enjoy it. Mary Berg, the president of Beyond the Crown Veterinary Education, told me that one of her colleagues trained her pet to relish the process so much that “she could just say ‘Brusha brusha brusha’ and the cat would come running.” But getting to that point can require weeks or months of conditioning. Berg recommends taking it day by day, introducing cats first to the toothpaste, then to getting one or two teeth touched, and on and on until they’re comfy with the whole set—always “with lots of praise and reward afterward,” she said. And that’s all before “you introduce that scary plastic thing.”  Read: An offbeat approach to bonding with cats  That’s a big ask for many owners, especially those who went the cat route because of the creatures’ rep for being low-maintenance. The consequences of skipping toothbrushing are also subtle because they don’t directly affect humans, Delgado told me. Miss a nail trimming, and the couch might pay the price. But cat teeth aren’t often glimpsed.   Boocat, defender of the realm (Courtesy of Steve Valeika)   The potential downsides of brushing, meanwhile, can be screamingly clear. On cat forums and Twitter, the cat-toothbrushing-phobic joke about losing their fingers. But what a lot of people are really afraid of sacrificing is their cat’s love. Broken trust can mar the relationship between owner and pet, Perrone said; people simply can’t communicate to skittish animals that this act of apparent torture is for their own good. Some cats never learn to deal. Even among veterinary experts, toothbrushing rituals are rare. Peralta and his wife just try to clear the bar of “at least once a week” with their own cat, Kit Kat; Berg and Perrone don’t brush their felines’ teeth at all. (Tsugawa does not currently own a cat, but he wasn’t a brusher when he did.) I’m no pro, but I feel a bit torn too. I never took the time to teach Calvin and Hobbes to see toothbrushing as a treat, and they can get pretty grumpy during the ritual itself. Valeika, the North Carolina vet, told me that seeing Boocat’s horrified reactions was the main thing that prompted him to quit the brush. “She would hate it if we were always doing that to her,” he said. “She really would just not be our pet anymore.”   Feline-health experts know they’re losing this fight. “A lot of us are not even talking about toothbrushing anymore, because nobody’s doing it,” Berg said. Luckily, a few well-vetted alternatives to toothbrushing do exist. Berg and Delgado use specialty kibble that can cut down on plaque, and Perrone’s cat, Noriko, is into Greenies dental treats—both options that many pets may be more receptive to. Scientifically, nothing beats bona fide brushing. But realistically, this young art may already be obsolete. The best interventions, Delgado told me, will be the ones that people actually use. “If someone in my profession doesn’t brush their pet’s teeth,” Berg said, “I can’t blame anybody else.”\n", "\n", "Last night I watched the Netflix documentary,  Inside the Mind of a Cat . It was a good show that demonstrated cats are thoughtful creatures and amazing predators and that they may have intellectual capacities on par with dogs.\n", "In addition to learning about the research, I watched the show from my perspective as a mental behaviorist. A mental behavioral position is one that bridges and resolves the old divide in psychology between the behaviorists (who say that the mind is not a scientific construct and thus they just study behavior) and the mentalists (who say they study overt behaviors and then infer mental processes that are presumed to cause the behaviors).\n", "The mental behavioral view says that animals like cats are “minded” creatures, and that they exhibit mental behaviors. To see this, imagine three cats in a tree; one is dead, one is anesthetized, and one is alive and well. Now drop the cats. The mental behaviorist says all three cats behave, but they exhibit different kinds of behaviors as they fall. The first cat falls through the air and lands on the ground. Its behavior is “physical” in that it is caused by the laws and forces of the material world as mapped by the physical sciences. The second cat also falls much like the first. However, if we were to peer inside the cat, we would see that its physiology is very active in maintaining its complex organization. The behaviors of the cat’s cells and organ systems that keep it alive are living behaviors studied by the biological sciences.\n", "The third cat rotates, lands on its feet, and takes off. This is a different kind of behavior that cannot be well-described as either physical or biological. Rather, the proper description is mental. Mental behavior is a particular kind of functional awareness and responsivity that animals exhibit. Such behaviors are actions mediated by the brain and nervous system and the complex active body of the cat. More specifically, mental behaviors are a pattern of activity that emerges as function of a complex adaptive, sensory-motor looping system. \n", "Just as we consider entities like cells that exhibit living behaviors to be alive, we should consider creatures like cats that exhibit mental behaviors to be “minded.” The mental behaviorist argues that mindedness is one of the most important concepts that both science and most modern people are blind to. I say “modern people” because, historically, most cultures have seen clearly that animals behave very differently when compared to plants or bacteria, and most cultures have had some kind of category for specifying this difference. For example, Aristotle divided the \"soul\" into the vegetative, animal, and human layers. In addition, the Great Chain of Being differentiated animals from the rest of the living world. However, our modern scientific system does not have a word for the way animals are in the world that makes them so different. We just call it \"animal behavior.\" And this gap is a major blind spot in our grammar for understanding the world around us.\n", "Returning to the documentary, if we did not really look inside \"the mind\" of cats, what did the documentary actually show? It showed the mental behavioral investment patterns of cats. That is, it showed how cats demonstrate functional awareness and responsivity to various kinds of situations and stimuli. For example, it showed they clearly recognize and respond to their names, it showed they prefer their owners to strangers, and it showed they really do have a unique skill set in their capacity to land on their feet. In other words, it showed cats are minded creatures that exhibit complex adaptive patterns of mental behavior.\n", "Although it becomes obvious when you know how to see the world this way (i.e., when I go for a walk in the woods, the mindedness of the squirrels, birds, and bees is as blatantly apparent to me as the living behaviors of the trees and mushrooms), it nevertheless takes practice to learn how to see mindedness in the world. However, we need to make the effort because failing to see mindedness in the world results in much blindness.\n", "I found a fascinating article from The Atlantic that delves into the often-overlooked aspect of feline care: brushing cats' teeth. Despite being a crucial part of maintaining a cat's health, very few cat owners actually brush their pets' teeth. The article highlights the challenges and absurdities of this task, noting that less than 5% of cat owners engage in this practice. \n", "\n", "Veterinary experts emphasize the importance of dental hygiene for cats, as most domestic cats over the age of four develop some form of gum disease, which can lead to severe health issues if left untreated. The article discusses various tools and techniques, such as poultry-flavored toothpaste and cat-sized toothbrushes, that can make the process easier. However, it also acknowledges the difficulties and resistance many cats show towards toothbrushing.\n", "\n", "Interestingly, the article also touches on the broader context of pet care, noting how the expectations and practices around pet maintenance have evolved over time. It suggests that while brushing a cat's teeth is ideal, alternative methods like dental treats and specialized kibble can also be effective.\n", "\n", "For more details, you can read the full article [here](https://www.theatlantic.com/health/archive/2023/10/cat-dental-care-toothbrushing/675123/).\n"]}], "source": ["const example<PERSON><PERSON>y = \"Summarize for me a fascinating article about cats.\"\n", "\n", "const events = await agentExecutor.stream(\n", "  { messages: [\n", "    [\n", "      \"system\",\n", "      `You are a web researcher who answers user questions by looking up information on the internet and retrieving contents of helpful documents. Cite your sources.`,\n", "    ],\n", "    [\"human\", example<PERSON><PERSON>y],\n", "  ] },\n", "  { streamMode: \"values\", }\n", ")\n", "\n", "for await (const event of events) {\n", "  const lastMsg = event.messages[event.messages.length - 1];\n", "  if (lastMsg.tool_calls?.length) {\n", "    console.dir(lastMsg.tool_calls, { depth: null });\n", "  } else if (lastMsg.content) {\n", "    console.log(lastMsg.content);\n", "  }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Related\n", "\n", "- Tool [conceptual guide](/docs/concepts/tools)\n", "- Tool [how-to guides](/docs/how_to/#tools)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `ExaSearchResults` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_exa.ExaSearchResults.html)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 2}