---
hide_table_of_contents: true
---

# GOAT

[GOAT](https://github.com/goat-sdk/goat) is the finance toolkit for AI agents.

:::warning
This tool exists outside of the main LangChain repository [here](https://github.com/goat-sdk/goat/).
Please use caution when linking wallets to external providers and make sure they are trusted.
:::

## Overview

Create agents that can:

- Send and receive payments
- Purchase physical and digital goods and services
- Engage in various investment strategies:
  - Earn yield
  - Bet on prediction markets
- Purchase crypto assets
- Tokenize any asset
- Get financial insights

### How it works

GOAT leverages blockchains, cryptocurrencies (such as stablecoins), and wallets as the infrastructure to enable agents to become economic actors:

1. Give your agent a [wallet](https://github.com/goat-sdk/goat/tree/main#chains-and-wallets)
2. Allow it to transact [anywhere](https://github.com/goat-sdk/goat/tree/main#chains-and-wallets)
3. Use more than [+200 tools](https://github.com/goat-sdk/goat/tree/main#tools)

See everything GOAT supports [here](https://github.com/goat-sdk/goat/tree/main#chains-and-wallets).

**Lightweight and extendable**
Different from other toolkits, GOAT is designed to be lightweight and extendable by keeping its core minimal and allowing you to install only the tools you need.

If you don't find what you need on our more than 200 integrations you can easily:

- Create your own plugin
- Integrate a new chain
- Integrate a new wallet
- Integrate a new agent framework

See how to do it [here](https://github.com/goat-sdk/goat/tree/main#-contributing).

## Setup

1. Install the core package and langchain adapter:

```bash
npm i @goat-sdk/core @goat-sdk/adapter-langchain
```

2. Install the type of wallet you want to use (e.g solana):

```bash
npm i @goat-sdk/wallet-evm @goat-sdk/wallet-viem
```

3. Install the plugins you want to use in that chain:

```bash
npm i @goat-sdk/plugin-erc20
```

## Instantiation

Now we can instantiate our toolkit:

```typescript
import { http } from "viem";
import { createWalletClient } from "viem";
import { privateKeyToAccount } from "viem/accounts";
import { baseSepolia } from "viem/chains";

import { getOnChainTools } from "@goat-sdk/adapter-langchain";
import { PEPE, USDC, erc20 } from "@goat-sdk/plugin-erc20";

import { sendETH } from "@goat-sdk/wallet-evm";
import { viem } from "@goat-sdk/wallet-viem";

import { ChatOpenAI } from "@langchain/openai";
import { createReactAgent } from "@langchain/langgraph/prebuilt";

// 1. Create a wallet client
const account = privateKeyToAccount(
  process.env.WALLET_PRIVATE_KEY as `0x${string}`
);

const walletClient = createWalletClient({
  account: account,
  transport: http(process.env.RPC_PROVIDER_URL),
  chain: baseSepolia,
});

// 2. Set up the tools
const tools = await getOnChainTools({
  wallet: viem(walletClient),
  plugins: [sendETH(), erc20({ tokens: [USDC, PEPE] })],
});

// 3. Create the agent
const model = new ChatOpenAI({
  model: "gpt-4o",
});

const agent = createReactAgent({ llm: model, tools: tools });
```

## Related

- Tool [conceptual guide](/docs/concepts/#tools)
- Tool [how-to guides](/docs/how_to/#tools)
