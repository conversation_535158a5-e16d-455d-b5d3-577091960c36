---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Google Calendar Tool

The Google Calendar Tools allow your agent to create and view Google Calendar events from a linked calendar.

## Setup

To use the Google Calendar Tools you need to install the following official peer dependency:

```bash npm2yarn
npm install googleapis
```

## Usage

import Too<PERSON><PERSON><PERSON>mple from "@examples/tools/google_calendar.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core @langchain/community @langchain/langgraph
```

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
