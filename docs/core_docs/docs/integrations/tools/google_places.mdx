---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Google Places Tool

The Google Places Tool allows your agent to utilize the Google Places API in order to find addresses,
phone numbers, website, etc. from text about a location listed on Google Places.

## Setup

You will need to get an API key from [Google here](https://developers.google.com/maps/documentation/places/web-service/overview)
and [enable the new Places API](https://console.cloud.google.com/apis/library/places.googleapis.com). Then, set your API key
as `process.env.GOOGLE_PLACES_API_KEY` or pass it in as an `apiKey` constructor argument.

## Usage

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

import ToolExample from "@examples/tools/google_places.ts";

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
