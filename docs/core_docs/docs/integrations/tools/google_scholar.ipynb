{"cells": [{"cell_type": "raw", "id": "10238e62-3465-4973-9279-606cbb7ccf16", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Google Scholar\n", "---"]}, {"cell_type": "markdown", "id": "a6f91f20", "metadata": {}, "source": ["# Google Scholar Tool\n", "\n", "This notebook provides a quick overview for getting started with [`SERPGoogleScholarTool`](https://api.js.langchain.com/classes/_langchain_community.tools_google_scholar.SERPGoogleScholarAPITool.html). For detailed documentation of all `SERPGoogleScholarAPITool` features and configurations, head to the [API reference](https://api.js.langchain.com/classes/_langchain_community.tools_google_scholar.SERPGoogleScholarAPITool.html).\n", "\n", "## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | [PY support](https://python.langchain.com/docs/integrations/tools/google_scholar/) | Package latest |\n", "| :--- | :--- | :---: | :---: |\n", "| [GoogleScholarTool](https://api.js.langchain.com/classes/_langchain_community.tools_google_scholar.SERPGoogleScholarAPITool.html) | [@langchain/community](https://www.npmjs.com/package/@langchain/community) |  ✅  |  ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |\n", "\n", "### Tool features\n", "\n", "- Retrieve academic publications by topic, author, or query.\n", "- Fetch metadata such as title, author, and publication year.\n", "- Advanced search filters, including citation count and journal name.\n", "\n", "## Setup\n", "\n", "The integration lives in the `@langchain/community` package.\n", "\n", "```bash\n", "npm install @langchain/community\n", "```\n", "\n", "### Credentials\n", "\n", "Ensure you have the appropriate API key to access Google Scholar. Set it in your environment variables:\n", "\n", "```typescript\n", "process.env.GOOGLE_SCHOLAR_API_KEY=\"your-serp-api-key\"\n", "```\n", "\n", "It's also helpful to set up [LangSmith](https://smith.langchain.com/) for best-in-class observability:\n", "\n", "```typescript\n", "process.env.LANGSMITH_TRACING=\"true\"\n", "process.env.LANGSMITH_API_KEY=\"your-langchain-api-key\"\n", "```"]}, {"cell_type": "markdown", "id": "1c97218f-f366-479d-8bf7-fe9f2f6df73f", "metadata": {}, "source": ["## Instantiation\n", "\n", "You can import and instantiate an instance of the `SERPGoogleScholarAPITool` tool like this:"]}, {"cell_type": "code", "execution_count": 4, "id": "8b3ddfe9-ca79-494c-a7ab-1f56d9407a64", "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["import { SERPGoogleScholarAPITool } from \"@langchain/community/tools/google_scholar\";\n", "\n", "const tool = new SERPGoogleScholarAPITool({\n", "  apiKey: process.env.SERPAPI_API_KEY,\n", "});"]}, {"cell_type": "markdown", "id": "74147a1a", "metadata": {}, "source": ["## Invocation\n", "\n", "### Invoke directly with args\n", "\n", "You can invoke the tool directly with query arguments:"]}, {"cell_type": "code", "execution_count": null, "id": "65310a8b-eb0c-4d9e-a618-4f4abe2414fc", "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["const results = await tool.invoke({\n", "  query: \"neural networks\",\n", "  maxResults: 5,\n", "});\n", "\n", "console.log(results);"]}, {"cell_type": "markdown", "id": "d6e73897", "metadata": {}, "source": ["### Invoke with ToolCall\n", "\n", "We can also invoke the tool with a model-generated `ToolCall`:"]}, {"cell_type": "code", "execution_count": null, "id": "f90e33a7", "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["const modelGeneratedToolCall = {\n", "  args: { query: \"machine learning\" },\n", "  id: \"1\",\n", "  name: tool.name,\n", "  type: \"tool_call\",\n", "};\n", "await tool.invoke(modelGeneratedToolCall);"]}, {"cell_type": "markdown", "id": "93848b02", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `SERPGoogleScholarAPITool` features and configurations, head to the [API reference](https://api.js.langchain.com/classes/_langchain_community.tools_google_scholar.SERPGoogleScholarAPITool.html)."]}], "metadata": {"kernelspec": {"display_name": "poetry-venv-311", "language": "python", "name": "poetry-venv-311"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}