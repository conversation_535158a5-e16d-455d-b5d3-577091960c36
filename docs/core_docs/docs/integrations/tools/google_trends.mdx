---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Google Trends Tool

The Google Trends Tool allows your agent to utilize the Google Trends API from SerpApi to retrieve and analyze search interest data.
This can be useful for understanding trending topics, regional search interest, and historical popularity of search terms.

For API details see [here](https://serpapi.com/google-trends-api)

SerpApi caches queries, so the first query will be slower while subsequent identical queries will be fast.
Occasionally, related queries will not work while interest over time will be fine. You can check your query [here](https://serpapi.com/playground?engine=google_trends&q=monster&data_type=RELATED_QUERIES).

## Setup

To use this tool, you will need to configure access to the Google Trends API from SerpApi.

Get an API key from [SerpApi](https://serpapi.com/users/sign_in)

Then, set your API key as `process.env.SERPAPI_API_KEY` or pass it in as an `apiKey` constructor argument.

## Usage

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

import ToolExample from "@examples/tools/google_trends.ts";

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
