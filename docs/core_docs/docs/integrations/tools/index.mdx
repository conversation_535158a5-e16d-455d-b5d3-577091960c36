---
sidebar_position: 0
sidebar_class_name: hidden
---

# Tools and Toolkits

import { CategoryTable, IndexTable } from "@theme/FeatureTables";

[Tools](/docs/concepts/tools) are utilities designed to be called by a model: their inputs are designed to be generated by models, and their outputs are designed to be passed back to models.

A [toolkit](/docs/concepts/tools/#toolkits) is a collection of tools meant to be used together. For a list of toolkit integrations, see [this page](/docs/integrations/toolkits/).

:::info
If you'd like to write your own tool, see [this how-to](/docs/how_to/custom_tools/). If you'd like to contribute an integration, see [Contributing integrations](/docs/contributing).
:::

## All Tools

<IndexTable />
