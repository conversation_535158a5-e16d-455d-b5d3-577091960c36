---
sidebar_label: Agent with AWS Lambda
hide_table_of_contents: true
---

# Agent with AWS Lambda Integration

Full docs here: https://docs.aws.amazon.com/lambda/index.html

**AWS Lambda** is a serverless computing service provided by Amazon Web Services (AWS), designed to allow developers to build and run applications and services without the need for provisioning or managing servers. This serverless architecture enables you to focus on writing and deploying code, while AWS automatically takes care of scaling, patching, and managing the infrastructure required to run your applications.

By including a AWSLambda in the list of tools provided to an Agent, you can grant your Agent the ability to invoke code running in your AWS Cloud for whatever purposes you need.

When an Agent uses the AWSLambda tool, it will provide an argument of type `string` which will in turn be passed into the Lambda function via the `event` parameter.

This quick start will demonstrate how an Agent could use a Lambda function to send an email via [Amazon Simple Email Service](https://aws.amazon.com/ses/). The lambda code which sends the email is not provided, but if you'd like to learn how this could be done, see [here](https://repost.aws/knowledge-center/lambda-send-email-ses). Keep in mind this is an intentionally simple example; Lambda can used to execute code for a near infinite number of other purposes (including executing more Langchains)!

### Note about credentials:

- If you have not run [`aws configure`](https://docs.aws.amazon.com/cli/latest/userguide/cli-chap-configure.html) via the AWS CLI, the `region`, `accessKeyId`, and `secretAccessKey` must be provided to the AWSLambda constructor.
- The IAM role corresponding to those credentials must have permission to invoke the lambda function.

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

```typescript
import { OpenAI } from "@langchain/openai";
import { SerpAPI } from "langchain/tools";
import { AWSLambda } from "langchain/tools/aws_lambda";
import { initializeAgentExecutorWithOptions } from "langchain/agents";

const model = new OpenAI({ temperature: 0 });
const emailSenderTool = new AWSLambda({
  name: "email-sender",
  // tell the Agent precisely what the tool does
  description:
    "Sends an email with the specified <NAME_EMAIL>",
  region: "us-east-1", // optional: AWS region in which the function is deployed
  accessKeyId: "abc123", // optional: access key id for a IAM user with invoke permissions
  secretAccessKey: "xyz456", // optional: secret access key for that IAM user
  functionName: "SendEmailViaSES", // the function name as seen in AWS Console
});
const tools = [emailSenderTool, new SerpAPI("api_key_goes_here")];
const executor = await initializeAgentExecutorWithOptions(tools, model, {
  agentType: "zero-shot-react-description",
});

const input = `Find out the capital of Croatia. Once you have it, email the <NAME_EMAIL>.`;
const result = await executor.invoke({ input });
console.log(result);
```

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
