---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Python interpreter tool

:::warning
This tool executes code and can potentially perform destructive actions. Be careful that you trust any code passed to it!
:::

LangChain offers an experimental tool for executing arbitrary Python code.
This can be useful in combination with an LLM that can generate code to perform more powerful computations.

## Usage

import Too<PERSON><PERSON>xample from "@examples/tools/pyinterpreter.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
