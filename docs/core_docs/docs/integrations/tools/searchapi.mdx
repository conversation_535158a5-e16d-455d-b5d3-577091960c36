---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# SearchApi tool

The `SearchApi` tool connects your agents and chains to the internet.

A wrapper around the Search API. This tool is handy when you need to answer questions about current events.

## Usage

Input should be a search query.

import ToolExample from "@examples/tools/searchapi_google_news.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
