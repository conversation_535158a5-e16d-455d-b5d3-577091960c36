---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Searxng Search tool

The `SearxngSearch` tool connects your agents and chains to the internet.

A wrapper around the SearxNG API, this tool is useful for performing meta-search engine queries using the SearxNG API. It is particularly helpful in answering questions about current events.

## Usage

import Too<PERSON><PERSON>xample from "@examples/tools/searxng_search.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
