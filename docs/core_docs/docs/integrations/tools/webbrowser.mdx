---
hide_table_of_contents: true
---

import CodeBlock from "@theme/CodeBlock";

# Web Browser Tool

The Webbrowser Tool gives your agent the ability to visit a website and extract information. It is described to the agent as

```
useful for when you need to find something on or summarize a webpage. input should be a comma separated list of "valid URL including protocol","what you want to find on the page or empty string for a summary".
```

It exposes two modes of operation:

- when called by the Agent with only a URL it produces a summary of the website contents
- when called by the Agent with a URL and a description of what to find it will instead use an in-memory Vector Store to find the most relevant snippets and summarise those

## Setup

To use the Webbrowser Tool you need to install the dependencies:

```bash npm2yarn
npm install cheerio axios
```

## Usage, standalone

import Tool<PERSON>xample from "@examples/tools/webbrowser.ts";

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

<CodeBlock language="typescript">{ToolExample}</CodeBlock>

## Usage, in an Agent

import AgentExample from "@examples/agents/mrkl_browser.ts";

<CodeBlock language="typescript">{AgentExample}</CodeBlock>

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
