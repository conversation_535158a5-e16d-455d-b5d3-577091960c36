---
sidebar_class_name: hidden
---

# Agent with Zapier NLA Integration

:::warning
This module has been deprecated and is no longer supported. The documentation below will not work in versions 0.2.0 or later.
:::

Full docs here: https://nla.zapier.com/start/

**Zapier Natural Language Actions** gives you access to the 5k+ apps and 20k+ actions on Zapier's platform through a natural language API interface.

NLA supports apps like Gmail, Salesforce, Trello, Slack, Asana, HubSpot, Google Sheets, Microsoft Teams, and thousands more apps: https://zapier.com/apps

Zapier NLA handles ALL the underlying API auth and translation from natural language --> underlying API call --> return simplified output for LLMs. The key idea is you, or your users, expose a set of actions via an oauth-like setup window, which you can then query and execute via a REST API.

NLA offers both API Key and OAuth for signing NLA API requests.

Server-side (API Key): for quickly getting started, testing, and production scenarios where <PERSON><PERSON><PERSON><PERSON> will only use actions exposed in the developer's Zapier account (and will use the developer's connected accounts on Zapier.com)

User-facing (Oauth): for production scenarios where you are deploying an end-user facing application and <PERSON><PERSON><PERSON><PERSON> needs access to end-user's exposed actions and connected accounts on Zapier.com

Attach NLA credentials via either an environment variable (`ZAPIER_NLA_OAUTH_ACCESS_TOKEN` or `ZAPIER_NLA_API_KEY`) or refer to the params argument in the API reference for `ZapierNLAWrapper`.

Review [auth docs](https://nla.zapier.com/docs/authentication/) for more details.

The example below demonstrates how to use the Zapier integration as an Agent:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core
```

```typescript
import { OpenAI } from "@langchain/openai";
import { ZapierNLAWrapper } from "langchain/tools";
import {
  initializeAgentExecutorWithOptions,
  ZapierToolKit,
} from "langchain/agents";

const model = new OpenAI({ temperature: 0 });
const zapier = new ZapierNLAWrapper();
const toolkit = await ZapierToolKit.fromZapierNLAWrapper(zapier);

const executor = await initializeAgentExecutorWithOptions(
  toolkit.tools,
  model,
  {
    agentType: "zero-shot-react-description",
    verbose: true,
  }
);
console.log("Loaded agent.");

const input = `Summarize the last email I received regarding Silicon Valley Bank. Send the summary to the #test-zapier Slack channel.`;

console.log(`Executing with input "${input}"...`);

const result = await executor.invoke({ input });

console.log(`Got output ${result.output}`);
```

## Related

- Tool [conceptual guide](/docs/concepts/tools)
- Tool [how-to guides](/docs/how_to/#tools)
