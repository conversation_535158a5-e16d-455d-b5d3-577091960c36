---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# AnalyticDB

[AnalyticDB for PostgreSQL](https://www.alibabacloud.com/help/en/analyticdb-for-postgresql/latest/product-introduction-overview) is a massively parallel processing (MPP) data warehousing service that is designed to analyze large volumes of data online.

`AnalyticDB for PostgreSQL` is developed based on the open source `Greenplum Database` project and is enhanced with in-depth extensions by `Alibaba Cloud`. AnalyticDB for PostgreSQL is compatible with the ANSI SQL 2003 syntax and the PostgreSQL and Oracle database ecosystems. AnalyticDB for PostgreSQL also supports row store and column store. AnalyticDB for PostgreSQL processes petabytes of data offline at a high performance level and supports highly concurrent online queries.

This notebook shows how to use functionality related to the `AnalyticDB` vector database.

To run, you should have an [AnalyticDB](https://www.alibabacloud.com/help/en/analyticdb-for-postgresql/latest/product-introduction-overview) instance up and running:

- Using [AnalyticDB Cloud Vector Database](https://www.alibabacloud.com/product/hybriddb-postgresql).

:::tip Compatibility
Only available on Node.js.
:::

## Setup

LangChain.js accepts [node-postgres](https://node-postgres.com/) as the connections pool for AnalyticDB vectorstore.

```bash npm2yarn
npm install -S pg
```

And we need [pg-copy-streams](https://github.com/brianc/node-pg-copy-streams) to add batch vectors quickly.

```bash npm2yarn
npm install -S pg-copy-streams
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

## Usage

::::danger Security
User-generated data such as usernames should not be used as input for the collection name.  
**This may lead to SQL Injection!**
::::

import UsageExample from "@examples/indexes/vector_stores/analyticdb.ts";

<CodeBlock language="typescript">{UsageExample}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
