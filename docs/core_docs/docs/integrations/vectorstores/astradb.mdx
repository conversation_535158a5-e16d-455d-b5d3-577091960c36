---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# Astra DB

:::tip Compatibility
Only available on Node.js.
:::

DataStax [Astra DB](https://astra.datastax.com/register) is a serverless vector-capable database built on [Apache Cassandra](https://cassandra.apache.org/_/index.html) and made conveniently available through an easy-to-use JSON API.

## Setup

1. Create an [Astra DB account](https://astra.datastax.com/register).
2. Create a [vector enabled database](https://astra.datastax.com/createDatabase).
3. Grab your `API Endpoint` and `Token` from the Database Details.
4. Set up the following env vars:

```bash
export ASTRA_DB_APPLICATION_TOKEN=YOUR_ASTRA_DB_APPLICATION_TOKEN_HERE
export ASTRA_DB_ENDPOINT=YOUR_ASTRA_DB_ENDPOINT_HERE
export ASTRA_DB_COLLECTION=YOUR_ASTRA_DB_COLLECTION_HERE
export OPENAI_API_KEY=YOUR_OPENAI_API_KEY_HERE
```

Where `ASTRA_DB_COLLECTION` is the desired name of your collection

6. Install the Astra TS Client & the LangChain community package

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @datastax/astra-db-ts @langchain/community @langchain/core
```

## Indexing docs

import Example from "@examples/indexes/vector_stores/astra.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Vector Types

Astra DB supports `cosine` (the default), `dot_product`, and `euclidean` similarity search; this is defined when the
vector store is first created as part of the `CreateCollectionOptions`:

```typescript
  vector: {
      dimension: number;
      metric?: "cosine" | "euclidean" | "dot_product";
  };
```

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
