# Azure Cosmos DB for NoSQL

> [Azure Cosmos DB for NoSQL](https://learn.microsoft.com/azure/cosmos-db/nosql/) provides support for querying items with flexible schemas and native support for JSON. It now offers vector indexing and search. This feature is designed to handle high-dimensional vectors, enabling efficient and accurate vector search at any scale. You can now store vectors directly in the documents alongside your data. Each document in your database can contain not only traditional schema-free data, but also high-dimensional vectors as other properties of the documents.

Learn how to leverage the vector search capabilities of Azure Cosmos DB for NoSQL from [this page](https://learn.microsoft.com/azure/cosmos-db/nosql/vector-search). If you don't have an Azure account, you can [create a free account](https://azure.microsoft.com/free/) to get started.

## Setup

You'll first need to install the [`@langchain/azure-cosmosdb`](https://www.npmjs.com/package/@langchain/azure-cosmosdb) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/azure-cosmosdb @langchain/core
```

You'll also need to have an Azure Cosmos DB for NoSQL instance running. You can deploy a free version on Azure Portal without any cost, following [this guide](https://learn.microsoft.com/azure/cosmos-db/nosql/quickstart-portal).

Once you have your instance running, make sure you have the connection string. You can find them in the Azure Portal, under the "Settings / Keys" section of your instance. Then you need to set the following environment variables:

import CodeBlock from "@theme/CodeBlock";
import EnvVars from "@examples/indexes/vector_stores/azure_cosmosdb_nosql/.env.example";

<CodeBlock language="text">{EnvVars}</CodeBlock>

### Using Azure Managed Identity

If you're using Azure Managed Identity, you can configure the credentials like this:

import ManagedIdentityExample from "@examples/indexes/vector_stores/azure_cosmosdb_nosql/azure_cosmosdb_nosql-managed_identity.ts";

<CodeBlock language="typescript">{ManagedIdentityExample}</CodeBlock>

:::info

When using Azure Managed Identity and role-based access control, you must ensure that the database and container have been created beforehand. RBAC does not provide permissions to create databases and containers. You can get more information about the permission model in the [Azure Cosmos DB documentation](https://learn.microsoft.com/azure/cosmos-db/how-to-setup-rbac#permission-model).

:::

## Usage example

Below is an example that indexes documents from a file in Azure Cosmos DB for NoSQL, runs a vector search query, and finally uses a chain to answer a question in natural language
based on the retrieved documents.

import Example from "@examples/indexes/vector_stores/azure_cosmosdb_nosql/azure_cosmosdb_nosql.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
