{"cells": [{"cell_type": "raw", "id": "1957f5cb", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: Faiss\n", "sidebar_class_name: node-only\n", "---"]}, {"cell_type": "markdown", "id": "ef1f0986", "metadata": {}, "source": ["# FaissStore\n", "\n", "```{=mdx}\n", "\n", ":::tip Compatibility\n", "Only available on Node.js.\n", ":::\n", "\n", "```\n", "\n", "[Faiss](https://github.com/facebookresearch/faiss) is a library for efficient similarity search and clustering of dense vectors.\n", "\n", "LangChain.js supports using Faiss as a locally-running vectorstore that can be saved to a file. It also provides the ability to read the saved file from the [LangChain Python implementation](https://python.langchain.com/docs/integrations/vectorstores/faiss#saving-and-loading).\n", "\n", "This guide provides a quick overview for getting started with Faiss [vector stores](/docs/concepts/#vectorstores). For detailed documentation of all `FaissStore` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_vectorstores_faiss.FaissStore.html)."]}, {"cell_type": "markdown", "id": "c824838d", "metadata": {}, "source": ["## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | [PY support](https://python.langchain.com/docs/integrations/vectorstores/faiss) |  Package latest |\n", "| :--- | :--- | :---: | :---: |\n", "| [`FaissStore`](https://api.js.langchain.com/classes/langchain_community_vectorstores_faiss.FaissStore.html) | [`@langchain/community`](https://npmjs.com/@langchain/community) | ✅ |  ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |"]}, {"cell_type": "markdown", "id": "36fdc060", "metadata": {}, "source": ["## Setup\n", "\n", "To use Faiss vector stores, you'll need to install the `@langchain/community` integration package and the [`faiss-node`](https://github.com/ewfian/faiss-node) package as a peer dependency.\n", "\n", "This guide will also use [OpenAI embeddings](/docs/integrations/text_embedding/openai), which require you to install the `@langchain/openai` integration package. You can also use [other supported embeddings models](/docs/integrations/text_embedding) if you wish.\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community faiss-node @langchain/openai @langchain/core\n", "</Npm2Yarn>\n", "```\n", "\n", "### Credentials\n", "\n", "Because Faiss runs locally, you do not need any credentials to use it.\n", "\n", "If you are using OpenAI embeddings for this guide, you'll need to set your OpenAI key as well:\n", "\n", "```typescript\n", "process.env.OPENAI_API_KEY = \"YOUR_API_KEY\";\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "// process.env.LANGSMITH_TRACING=\"true\"\n", "// process.env.LANGSMITH_API_KEY=\"your-api-key\"\n", "```"]}, {"cell_type": "markdown", "id": "93df377e", "metadata": {}, "source": ["## Instantiation"]}, {"cell_type": "code", "execution_count": 2, "id": "dc37144c-208d-4ab3-9f3a-0407a69fe052", "metadata": {"tags": []}, "outputs": [], "source": ["import { FaissStore } from \"@langchain/community/vectorstores/faiss\";\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "const embeddings = new OpenAIEmbeddings({\n", "  model: \"text-embedding-3-small\",\n", "});\n", "\n", "const vectorStore = new FaissStore(embeddings, {});"]}, {"cell_type": "markdown", "id": "ac6071d4", "metadata": {}, "source": ["## Manage vector store\n", "\n", "### Add items to vector store"]}, {"cell_type": "code", "execution_count": 3, "id": "17f5efc0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ '1', '2', '3', '4' ]\n"]}], "source": ["import type { Document } from \"@langchain/core/documents\";\n", "\n", "const document1: Document = {\n", "  pageContent: \"The powerhouse of the cell is the mitochondria\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document2: Document = {\n", "  pageContent: \"Buildings are made out of brick\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document3: Document = {\n", "  pageContent: \"Mitochondria are made out of lipids\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document4: Document = {\n", "  pageContent: \"The 2024 Olympics are in Paris\",\n", "  metadata: { source: \"https://example.com\" }\n", "}\n", "\n", "const documents = [document1, document2, document3, document4];\n", "\n", "await vectorStore.addDocuments(documents, { ids: [\"1\", \"2\", \"3\", \"4\"] });"]}, {"cell_type": "markdown", "id": "dcf1b905", "metadata": {}, "source": ["### Delete items from vector store"]}, {"cell_type": "code", "execution_count": 4, "id": "ef61e188", "metadata": {}, "outputs": [], "source": ["await vectorStore.delete({ ids: [\"4\"] });"]}, {"cell_type": "markdown", "id": "c3620501", "metadata": {}, "source": ["## Query vector store\n", "\n", "Once your vector store has been created and the relevant documents have been added you will most likely wish to query it during the running of your chain or agent. \n", "\n", "### Query directly\n", "\n", "Performing a simple similarity search can be done as follows:"]}, {"cell_type": "code", "execution_count": 5, "id": "aa0a16fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* The powerhouse of the cell is the mitochondria [{\"source\":\"https://example.com\"}]\n", "* Mitochondria are made out of lipids [{\"source\":\"https://example.com\"}]\n"]}], "source": ["const similaritySearchResults = await vectorStore.similaritySearch(\"biology\", 2);\n", "\n", "for (const doc of similaritySearchResults) {\n", "  console.log(`* ${doc.pageContent} [${JSON.stringify(doc.metadata, null)}]`);\n", "}"]}, {"cell_type": "markdown", "id": "3ed9d733", "metadata": {}, "source": ["Filtering by metadata is currently not supported.\n", "\n", "If you want to execute a similarity search and receive the corresponding scores you can run:"]}, {"cell_type": "code", "execution_count": 6, "id": "5efd2eaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* [SIM=1.671] The powerhouse of the cell is the mitochondria [{\"source\":\"https://example.com\"}]\n", "* [SIM=1.705] Mitochondria are made out of lipids [{\"source\":\"https://example.com\"}]\n"]}], "source": ["const similaritySearchWithScoreResults = await vectorStore.similaritySearchWithScore(\"biology\", 2);\n", "\n", "for (const [doc, score] of similaritySearchWithScoreResults) {\n", "  console.log(`* [SIM=${score.toFixed(3)}] ${doc.pageContent} [${JSON.stringify(doc.metadata)}]`);\n", "}"]}, {"cell_type": "markdown", "id": "0c235cdc", "metadata": {}, "source": ["### Query by turning into retriever\n", "\n", "You can also transform the vector store into a [retriever](/docs/concepts/retrievers) for easier usage in your chains. "]}, {"cell_type": "code", "execution_count": 7, "id": "f3460093", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    pageContent: 'The powerhouse of the cell is the mitochondria',\n", "    metadata: { source: 'https://example.com' }\n", "  },\n", "  {\n", "    pageContent: 'Mitochondria are made out of lipids',\n", "    metadata: { source: 'https://example.com' }\n", "  }\n", "]\n"]}], "source": ["const retriever = vectorStore.asRetriever({\n", "  k: 2,\n", "});\n", "await retriever.invoke(\"biology\");"]}, {"cell_type": "markdown", "id": "e2e0a211", "metadata": {}, "source": ["### Usage for retrieval-augmented generation\n", "\n", "For guides on how to use this vector store for retrieval-augmented generation (RAG), see the following sections:\n", "\n", "- [Tutorials: working with external knowledge](/docs/tutorials/#working-with-external-knowledge).\n", "- [How-to: Question and answer with RAG](/docs/how_to/#qa-with-rag)\n", "- [Retrieval conceptual docs](/docs/concepts/retrieval)"]}, {"cell_type": "markdown", "id": "58a88011", "metadata": {}, "source": ["## Merging indexes\n", "\n", "Faiss also supports merging existing indexes:"]}, {"cell_type": "code", "execution_count": null, "id": "79a65a68", "metadata": {}, "outputs": [], "source": ["// Create an initial vector store\n", "const initialStore = await FaissStore.fromTexts(\n", "  [\"Hello world\", \"Bye bye\", \"hello nice world\"],\n", "  [{ id: 2 }, { id: 1 }, { id: 3 }],\n", "  new OpenAIEmbeddings()\n", ");\n", "\n", "// Create another vector store from texts\n", "const newStore = await FaissStore.fromTexts(\n", "  [\"Some text\"],\n", "  [{ id: 1 }],\n", "  new OpenAIEmbeddings()\n", ");\n", "\n", "// merge the first vector store into vectorStore2\n", "await newStore.mergeFrom(initialStore);\n", "\n", "// You can also create a new vector store from another FaissStore index\n", "const newStore2 = await FaissStore.fromIndex(\n", "  newStore,\n", "  new OpenAIEmbeddings()\n", ");\n", "\n", "await newStore2.similaritySearch(\"Bye bye\", 1);"]}, {"cell_type": "markdown", "id": "b92a2301", "metadata": {}, "source": ["## Save an index to file and load it again\n", "\n", "To persist an index on disk, use the `.save` and static `.load` methods:"]}, {"cell_type": "code", "execution_count": null, "id": "9e4aecb9", "metadata": {}, "outputs": [], "source": ["// Create a vector store through any method, here from texts as an example\n", "const persistentStore = await FaissStore.fromTexts(\n", "  [\"Hello world\", \"Bye bye\", \"hello nice world\"],\n", "  [{ id: 2 }, { id: 1 }, { id: 3 }],\n", "  new OpenAIEmbeddings()\n", ");\n", "\n", "// Save the vector store to a directory\n", "const directory = \"your/directory/here\";\n", "\n", "await persistentStore.save(directory);\n", "\n", "// Load the vector store from the same directory\n", "const loadedVectorStore = await FaissStore.load(\n", "  directory,\n", "  new OpenAIEmbeddings()\n", ");\n", "\n", "// vectorStore and loadedVectorStore are identical\n", "const result = await loadedVectorStore.similaritySearch(\"hello world\", 1);\n", "console.log(result);"]}, {"cell_type": "markdown", "id": "069f1b5f", "metadata": {}, "source": ["## Reading saved files from Python\n", "\n", "To enable the ability to read the saved file from [LangChain Python's implementation](https://python.langchain.com/docs/integrations/vectorstores/faiss#saving-and-loading), you'll need to install the [`pickleparser`](https://github.com/ewfian/pickleparser) package.\n", "\n", "```{=mdx}\n", "<Npm2Yarn>\n", "  pickleparser\n", "</Npm2Yarn>\n", "```\n", "\n", "Then you can use the `.loadFromPython` static method:"]}, {"cell_type": "code", "execution_count": null, "id": "d959f997", "metadata": {}, "outputs": [], "source": ["// The directory of data saved from Python\n", "const directoryWithSavedPythonStore = \"your/directory/here\";\n", "\n", "// Load the vector store from the directory\n", "const pythonLoadedStore = await FaissStore.loadFromPython(\n", "  directoryWithSavedPythonStore,\n", "  new OpenAIEmbeddings()\n", ");\n", "\n", "// Search for the most similar document\n", "await pythonLoadedStore.similaritySearch(\"test\", 2);"]}, {"cell_type": "markdown", "id": "8a27244f", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `FaissStore` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_vectorstores_faiss.FaissStore.html)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}