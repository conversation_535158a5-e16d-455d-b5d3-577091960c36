{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Google Cloud SQL for PostgreSQL\n", "\n", "[Cloud SQL](https://cloud.google.com/sql) is a fully managed relational database service that offers high performance, seamless integration, and impressive scalability and offers database engines such as PostgreSQL.\n", "\n", "This guide provides a quick overview of how to use Cloud SQL for PostgreSQL to store vector embeddings with the `PostgresVectorStore` class.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview\n", "\n", "### Integration details\n", "\n", "| Class               | Package                                    | [PY support](https://python.langchain.com/docs/integrations/vectorstores/google_cloud_sql_pg/) | Package latest |\n", "| :------------------ | :----------------------------------------- | :--------------------------------------------------------------------------------------------: | :------------: |\n", "| PostgresVectorStore | [`@langchain/google-cloud-sql-pg`](https://www.npmjs.com/package/@langchain/google-cloud-sql-pg) |                                               ✅                                               |     0.0.1      |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Before you begin\n", "\n", "In order to use this package, you first need to go throught the following steps:\n", "\n", "1.  [Select or create a Cloud Platform project.](https://developers.google.com/workspace/guides/create-project)\n", "2.  [Enable billing for your project.](https://cloud.google.com/billing/docs/how-to/modify-project#enable_billing_for_a_project)\n", "3.  [Enable the Cloud SQL Admin API.](https://console.cloud.google.com/flows/enableapi?apiid=sqladmin.googleapis.com)\n", "4.  [Setup Authentication.](https://cloud.google.com/docs/authentication)\n", "5.  [Create a CloudSQL instance](https://cloud.google.com/sql/docs/postgres/connect-instance-auth-proxy#create-instance)\n", "6.  [Create a CloudSQL database](https://cloud.google.com/sql/docs/postgres/create-manage-databases)\n", "7.  [Add a user to the database](https://cloud.google.com/sql/docs/postgres/create-manage-users)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Authentication\n", "\n", "Authenticate locally to your Google Cloud account using the ```gcloud auth login``` command.\n", "\n", "### Set Your Google Cloud Project\n", "\n", "Set your Google Cloud project ID to leverage Google Cloud resources locally:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "shellscript"}}, "outputs": [], "source": ["gcloud config set project YOUR-PROJECT-ID"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you don't know your project ID, try the following:\n", "*   Run `gcloud config list`.\n", "*   Run `gcloud projects list`.\n", "*   See the support page: [Locate the project ID](https://support.google.com/googleapi/answer/7014113)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting up a PostgresVectorStore instance\n", "\n", "To use the PostgresVectorStore library, you'll need to install the `@langchain/google-cloud-sql-pg` package and then follow the steps bellow.\n", "\n", "First, you'll need to log in to your Google Cloud account and set the following environment variables based on your Google Cloud project; these will be defined based on how you want to configure (fromInstance, fromEngine, fromEngineArgs) your PostgresEngine instance :\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "bat"}}, "outputs": [], "source": ["PROJECT_ID=\"your-project-id\"\n", "REGION=\"your-project-region\" // example: \"us-central1\"\n", "INSTANCE_NAME=\"your-instance\"\n", "DB_NAME=\"your-database-name\"\n", "DB_USER=\"your-database-user\"\n", "PASSWORD=\"your-database-password\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting up an instance\n", "\n", "To instantiate a PostgresVectorStore, you'll first need to create a database connection through the PostgresEngine, then initialize the vector store table and finally call the `.initialize()` method to instantiate the vector store.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["import {\n", "  Column,\n", "  PostgresEng<PERSON>,\n", "  PostgresE<PERSON>ine<PERSON><PERSON><PERSON>,\n", "  PostgresVectorStore,\n", "  PostgresVectorStoreArgs,\n", "  VectorStoreTableArgs,\n", "} from \"@langchain/google-cloud-sql-pg\";\n", "import { SyntheticEmbeddings } from \"@langchain/core/utils/testing\"; // This is used as an Embedding service\n", "import * as dotenv from \"dotenv\";\n", "\n", "dotenv.config();\n", "\n", "const peArgs: PostgresEngineArgs = {\n", "  user: process.env.DB_USER ?? \"\",\n", "  password: process.env.PASSWORD ?? \"\",\n", "};\n", "\n", "// PostgresEngine instantiation\n", "const engine: PostgresEngine = await PostgresEngine.fromInstance(\n", "  process.env.PROJECT_ID ?? \"\",\n", "  process.env.REGION ?? \"\",\n", "  process.env.INSTANCE_NAME ?? \"\",\n", "  process.env.DB_NAME ?? \"\",\n", "  peArgs\n", ");\n", "\n", "const vectorStoreArgs: VectorStoreTableArgs = {\n", "  metadataColumns: [new Column(\"page\", \"TEXT\"), new Column(\"source\", \"TEXT\")],\n", "};\n", "\n", "// Vector store table initilization\n", "await engine.initVectorstoreTable(\"my_vector_store_table\", 768, vectorStoreArgs);\n", "const embeddingService = new SyntheticEmbeddings({ vectorSize: 768 });\n", "\n", "const pvectorArgs: PostgresVectorStoreArgs = {\n", "  metadataColumns: [\"page\", \"source\"],\n", "};\n", "\n", "// PostgresVectorStore instantiation\n", "const vectorStore = await PostgresVectorStore.initialize(\n", "  engine,\n", "  embeddingService,\n", "  \"my_vector_store_table\",\n", "  pvectorArgs\n", ");\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Manage Vector Store\n", "\n", "### Add Documents to vector store\n", "\n", "To add Documents to the vector store, you would be able to it by passing or not the ids\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["import { v4 as uuidv4 } from \"uuid\";\n", "import type { Document } from \"@langchain/core/documents\";\n", "\n", "const document1: Document = {\n", "  pageContent: \"The powerhouse of the cell is the mitochondria\",\n", "  metadata: { page: 0, source: \"https://example.com\" },\n", "};\n", "\n", "const document2: Document = {\n", "  pageContent: \"Buildings are made out of brick\",\n", "  metadata: { page: 1, source: \"https://example.com\" },\n", "};\n", "\n", "const document3: Document = {\n", "  pageContent: \"Mitochondria are made out of lipids\",\n", "  metadata: { page: 2, source: \"https://example.com\" },\n", "};\n", "\n", "const document4: Document = {\n", "  pageContent: \"The 2024 Olympics are in Paris\",\n", "  metadata: { page: 3, source: \"https://example.com\" },\n", "};\n", "\n", "const documents = [document1, document2, document3, document4];\n", "\n", "const ids = [uuidv4(), uuidv4(), uuidv4(), uuidv4()];\n", "\n", "await vectorStore.addDocuments(documents, { ids: ids });\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Delete Documents from vector store\n", "\n", "You can delete one or more Documents from the vector store by passing the arrays of ids to be deleted:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["// deleting a document\n", "const id1 = ids[0];\n", "await vectorStore.delete({ ids: [id1] });\n", "\n", "// deleting more than one document\n", "await vectorStore.delete({ ids: ids });\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Search for documents\n", "\n", "Once your vector store has been created and the relevant documents have been added you will most likely wish to query it during the running of your chain or agent.\n", "\n", "### Query directly\n", "\n", "Performing a simple similarity search can be done as follows:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["const filter = `\"source\" = \"https://example.com\"`;\n", "\n", "const results = await vectorStore.similaritySearch(\"biology\", 2, filter);\n", "\n", "for (const doc of results) {\n", "  console.log(`* ${doc.pageContent} [${JSON.stringify(doc.metadata, null)}]`);\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you want to execute a similarity search and receive the corresponding scores you can run:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["const filter = `\"source\" = \"https://example.com\"`;\n", "const resultsWithScores = await vectorStore.similaritySearchWithScore(\n", "  \"biology\",\n", "  2,\n", "  filter\n", ");\n", "\n", "for (const [doc, score] of resultsWithScores) {\n", "  console.log(\n", "    `* [SIM=${score.toFixed(3)}] ${doc.pageContent} [${JSON.stringify(doc.metadata)}]`\n", "  );\n", "}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Query by using the max marginal relevance search\n", "\n", "The Maximal marginal relevance optimizes for similarity to the query and diversity among selected documents.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "typescript"}}, "outputs": [], "source": ["const options = {\n", "  k: 4,\n", "  filter: `\"source\" = 'https://example.com'`,\n", "};\n", "\n", "const results = await vectorStoreInstance.maxMarginalRelevanceSearch(\"biology\", options);\n", "\n", "for (const doc of results) {\n", "  console.log(`* ${doc.pageContent} [${JSON.stringify(doc.metadata, null)}]`);\n", "}\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}