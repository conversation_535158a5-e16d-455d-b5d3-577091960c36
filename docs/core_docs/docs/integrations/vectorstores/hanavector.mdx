# SAP HANA Cloud Vector Engine

[SAP HANA Cloud Vector Engine](https://www.sap.com/events/teched/news-guide/ai.html#article8) is a vector store fully integrated into the `SAP HANA Cloud database`.

## Setup

You'll first need to install either the [`@sap/hana-client`](https://www.npmjs.com/package/@sap/hana-client) or the [`hdb`](https://www.npmjs.com/package/hdb) package, and the [`@langchain/community`](https://www.npmjs.com/package/@langchain/community) package:

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install -S @langchain/community @langchain/core @sap/hana-client
# or
npm install -S @langchain/community @langchain/core hdb
```

You'll also need to have database connection to a HANA Cloud instance.

import CodeBlock from "@theme/CodeBlock";
import EnvVars from "@examples/indexes/vector_stores/hana_vector/.env.example";

<CodeBlock language="text">{EnvVars}</CodeBlock>

## Create a new index from texts

import ExampleTexts from "@examples/indexes/vector_stores/hana_vector/fromTexts.ts";

<CodeBlock language="typescript">{ExampleTexts}</CodeBlock>

## Create a new index from a loader and perform similarity searches

import ExampleLoader from "@examples/indexes/vector_stores/hana_vector/fromDocs.ts";

<CodeBlock language="typescript">{ExampleLoader}</CodeBlock>

## Creating an HNSW Vector Index

A vector index can significantly speed up top-k nearest neighbor queries for vectors. Users can create a Hierarchical Navigable Small World (HNSW) vector index using the `create_hnsw_index` function.

For more information about creating an index at the database level, such as parameters requirement, please refer to the [official documentation](https://help.sap.com/docs/hana-cloud-database/sap-hana-cloud-sap-hana-database-vector-engine-guide/create-vector-index-statement-data-definition).

import ExampleIndex from "@examples/indexes/vector_stores/hana_vector/createHnswIndex.ts";

<CodeBlock language="typescript">{ExampleIndex}</CodeBlock>

## Basic Vectorstore Operations

import ExampleBasic from "@examples/indexes/vector_stores/hana_vector/basics.ts";

<CodeBlock language="typescript">{ExampleBasic}</CodeBlock>

## Advanced filtering

import { Table, Tr, Th, Td } from "@mdx-js/react";

In addition to the basic value-based filtering capabilities, it is possible to use more advanced filtering. The table below shows the available filter operators.

| Operator   | Semantic                                                                   |
| ---------- | -------------------------------------------------------------------------- |
| `$eq`      | Equality (==)                                                              |
| `$ne`      | Inequality (!=)                                                            |
| `$lt`      | Less than (<)                                                              |
| `$lte`     | Less than or equal (<=)                                                    |
| `$gt`      | Greater than (>)                                                           |
| `$gte`     | Greater than or equal (>=)                                                 |
| `$in`      | Contained in a set of given values (in)                                    |
| `$nin`     | Not contained in a set of given values (not in)                            |
| `$between` | Between the range of two boundary values                                   |
| `$like`    | Text equality based on the "LIKE" semantics in SQL (using "%" as wildcard) |
| `$and`     | Logical "and", supporting 2 or more operands                               |
| `$or`      | Logical "or", supporting 2 or more operands                                |

import ExampleAdvancedFilter from "@examples/indexes/vector_stores/hana_vector/advancedFiltering.ts";

<CodeBlock language="typescript">{ExampleAdvancedFilter}</CodeBlock>

## Using a VectorStore as a retriever in chains for retrieval augmented generation (RAG)

import ExampleChain from "@examples/indexes/vector_stores/hana_vector/chains.ts";

<CodeBlock language="typescript">{ExampleChain}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
