{"cells": [{"cell_type": "raw", "id": "1957f5cb", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: HNSWLib\n", "sidebar_class_name: node-only\n", "---"]}, {"cell_type": "markdown", "id": "ef1f0986", "metadata": {}, "source": ["# HNSWLib\n", "\n", "```{=mdx}\n", ":::tip Compatibility\n", "Only available on Node.js.\n", ":::\n", "```\n", "\n", "HNSWLib is an in-memory vector store that can be saved to a file. It uses the [HNSWLib library](https://github.com/nmslib/hnswlib).\n", "\n", "This guide provides a quick overview for getting started with HNSWLib [vector stores](/docs/concepts/#vectorstores). For detailed documentation of all `HNSWLib` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_vectorstores_hnswlib.HNSWLib.html)."]}, {"cell_type": "markdown", "id": "c824838d", "metadata": {}, "source": ["## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | PY support |  Package latest |\n", "| :--- | :--- | :---: | :---: |\n", "| [`HNSWLib`](https://api.js.langchain.com/classes/langchain_community_vectorstores_hnswlib.HNSWLib.html) | [`@langchain/community`](https://npmjs.com/@langchain/community) | ❌ |  ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |"]}, {"cell_type": "markdown", "id": "36fdc060", "metadata": {}, "source": ["## Setup\n", "\n", "To use HNSWLib vector stores, you'll need to install the `@langchain/community` integration package with the [`hnswlib-node`](https://www.npmjs.com/package/hnswlib-node) package as a peer dependency.\n", "\n", "This guide will also use [OpenAI embeddings](/docs/integrations/text_embedding/openai), which require you to install the `@langchain/openai` integration package. You can also use [other supported embeddings models](/docs/integrations/text_embedding) if you wish.\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community hnswlib-node @langchain/openai @langchain/core\n", "</Npm2Yarn>\n", "```\n", "\n", "```{=mdx}\n", ":::caution\n", "\n", "**On Windows**, you might need to install [Visual Studio](https://visualstudio.microsoft.com/downloads/) first in order to properly build the `hnswlib-node` package.\n", "\n", ":::\n", "```\n", "\n", "### Credentials\n", "\n", "Because HNSWLib runs locally, you do not need any credentials to use it.\n", "\n", "If you are using OpenAI embeddings for this guide, you'll need to set your OpenAI key as well:\n", "\n", "```typescript\n", "process.env.OPENAI_API_KEY = \"YOUR_API_KEY\";\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "// process.env.LANGSMITH_TRACING=\"true\"\n", "// process.env.LANGSMITH_API_KEY=\"your-api-key\"\n", "```"]}, {"cell_type": "markdown", "id": "93df377e", "metadata": {}, "source": ["## Instantiation"]}, {"cell_type": "code", "execution_count": 1, "id": "dc37144c-208d-4ab3-9f3a-0407a69fe052", "metadata": {"tags": []}, "outputs": [], "source": ["import { HNSWLib } from \"@langchain/community/vectorstores/hnswlib\";\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "const embeddings = new OpenAIEmbeddings({\n", "  model: \"text-embedding-3-small\",\n", "});\n", "\n", "const vectorStore = await HNSWLib.fromDocuments([], embeddings);"]}, {"cell_type": "markdown", "id": "ac6071d4", "metadata": {}, "source": ["## Manage vector store\n", "\n", "### Add items to vector store"]}, {"cell_type": "code", "execution_count": 2, "id": "17f5efc0", "metadata": {}, "outputs": [], "source": ["import type { Document } from \"@langchain/core/documents\";\n", "\n", "const document1: Document = {\n", "  pageContent: \"The powerhouse of the cell is the mitochondria\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document2: Document = {\n", "  pageContent: \"Buildings are made out of brick\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document3: Document = {\n", "  pageContent: \"Mitochondria are made out of lipids\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document4: Document = {\n", "  pageContent: \"The 2024 Olympics are in Paris\",\n", "  metadata: { source: \"https://example.com\" }\n", "}\n", "\n", "const documents = [document1, document2, document3, document4];\n", "\n", "await vectorStore.addDocuments(documents);"]}, {"cell_type": "markdown", "id": "c3620501", "metadata": {}, "source": ["Deletion and ids for individual documents are not currently supported.\n", "\n", "## Query vector store\n", "\n", "Once your vector store has been created and the relevant documents have been added you will most likely wish to query it during the running of your chain or agent. \n", "\n", "### Query directly\n", "\n", "Performing a simple similarity search can be done as follows:"]}, {"cell_type": "code", "execution_count": 4, "id": "aa0a16fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* The powerhouse of the cell is the mitochondria [{\"source\":\"https://example.com\"}]\n", "* Mitochondria are made out of lipids [{\"source\":\"https://example.com\"}]\n"]}], "source": ["const filter = (doc) => doc.metadata.source === \"https://example.com\";\n", "\n", "const similaritySearchResults = await vectorStore.similaritySearch(\"biology\", 2, filter);\n", "\n", "for (const doc of similaritySearchResults) {\n", "  console.log(`* ${doc.pageContent} [${JSON.stringify(doc.metadata, null)}]`);\n", "}"]}, {"cell_type": "markdown", "id": "3ed9d733", "metadata": {}, "source": ["The filter is optional, and must be a predicate function that takes a document as input, and returns `true` or `false` depending on whether the document should be returned.\n", "\n", "If you want to execute a similarity search and receive the corresponding scores you can run:"]}, {"cell_type": "code", "execution_count": 5, "id": "5efd2eaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* [SIM=0.835] The powerhouse of the cell is the mitochondria [{\"source\":\"https://example.com\"}]\n", "* [SIM=0.852] Mitochondria are made out of lipids [{\"source\":\"https://example.com\"}]\n"]}], "source": ["const similaritySearchWithScoreResults = await vectorStore.similaritySearchWithScore(\"biology\", 2, filter)\n", "\n", "for (const [doc, score] of similaritySearchWithScoreResults) {\n", "  console.log(`* [SIM=${score.toFixed(3)}] ${doc.pageContent} [${JSON.stringify(doc.metadata)}]`);\n", "}"]}, {"cell_type": "markdown", "id": "0c235cdc", "metadata": {}, "source": ["### Query by turning into retriever\n", "\n", "You can also transform the vector store into a [retriever](/docs/concepts/retrievers) for easier usage in your chains."]}, {"cell_type": "code", "execution_count": 6, "id": "f3460093", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  {\n", "    pageContent: 'The powerhouse of the cell is the mitochondria',\n", "    metadata: { source: 'https://example.com' }\n", "  },\n", "  {\n", "    pageContent: 'Mitochondria are made out of lipids',\n", "    metadata: { source: 'https://example.com' }\n", "  }\n", "]\n"]}], "source": ["const retriever = vectorStore.asRetriever({\n", "  // Optional filter\n", "  filter: filter,\n", "  k: 2,\n", "});\n", "await retriever.invoke(\"biology\");"]}, {"cell_type": "markdown", "id": "e2e0a211", "metadata": {}, "source": ["### Usage for retrieval-augmented generation\n", "\n", "For guides on how to use this vector store for retrieval-augmented generation (RAG), see the following sections:\n", "\n", "- [Tutorials: working with external knowledge](/docs/tutorials/#working-with-external-knowledge).\n", "- [How-to: Question and answer with RAG](/docs/how_to/#qa-with-rag)\n", "- [Retrieval conceptual docs](/docs/concepts/retrieval)"]}, {"cell_type": "markdown", "id": "069f1b5f", "metadata": {}, "source": ["## Save to/load from file\n", "\n", "HNSWLib supports saving your index to a file, then reloading it at a later date:"]}, {"cell_type": "code", "execution_count": null, "id": "f71ce986", "metadata": {}, "outputs": [], "source": ["// Save the vector store to a directory\n", "const directory = \"your/directory/here\";\n", "await vectorStore.save(directory);\n", "\n", "// Load the vector store from the same directory\n", "const loadedVectorStore = await HNSWLib.load(directory, new OpenAIEmbeddings());\n", "\n", "// vectorStore and loadedVectorStore are identical\n", "await loadedVectorStore.similaritySearch(\"hello world\", 1);"]}, {"cell_type": "markdown", "id": "22f0d74f", "metadata": {}, "source": ["### Delete a saved index\n", "\n", "You can use the `.delete` method to clear an index saved to a given directory:"]}, {"cell_type": "code", "execution_count": null, "id": "daabbffd", "metadata": {}, "outputs": [], "source": ["// Load the vector store from the same directory\n", "const savedVectorStore = await HNSWLib.load(directory, new OpenAIEmbeddings());\n", "\n", "await savedVectorStore.delete({ directory });"]}, {"cell_type": "markdown", "id": "8a27244f", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `HNSWLib` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_vectorstores_hnswlib.HNSWLib.html)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}