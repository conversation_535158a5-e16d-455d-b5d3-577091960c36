---
sidebar_position: 0
sidebar_class_name: hidden
---

# Vector stores

A [vector store](/docs/concepts/#vectorstores) stores [embedded](/docs/concepts/embedding_models) data and performs similarity search.

import EmbeddingTabs from "@theme/EmbeddingTabs";

<EmbeddingTabs />

import VectorStoreTabs from "@theme/VectorStoreTabs";

<VectorStoreTabs />

LangChain.js integrates with a variety of vector stores. You can check out a full list below:

import { CategoryTable, IndexTable } from "@theme/FeatureTables";

<IndexTable />
