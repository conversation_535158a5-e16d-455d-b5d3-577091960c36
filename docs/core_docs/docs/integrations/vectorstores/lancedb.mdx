---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# LanceDB

LanceDB is an embedded vector database for AI applications. It is open source and distributed with an Apache-2.0 license.

LanceDB datasets are persisted to disk and can be shared between Node.js and Python.

## Setup

Install the [LanceDB](https://github.com/lancedb/lancedb) [Node.js bindings](https://www.npmjs.com/package/@lancedb/lancedb):

```bash npm2yarn
npm install -S @lancedb/lancedb
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

## Usage

### Create a new index from texts

import ExampleTexts from "@examples/indexes/vector_stores/lancedb/fromTexts.ts";

<CodeBlock language="typescript">{ExampleTexts}</CodeBlock>

### Create a new index from a loader

import ExampleLoader from "@examples/indexes/vector_stores/lancedb/fromDocs.ts";

<CodeBlock language="typescript">{ExampleLoader}</CodeBlock>

### Open an existing dataset

import ExampleLoad from "@examples/indexes/vector_stores/lancedb/load.ts";

<CodeBlock language="typescript">{ExampleLoad}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
