{"cells": [{"cell_type": "raw", "id": "1957f5cb", "metadata": {"vscode": {"languageId": "raw"}}, "source": ["---\n", "sidebar_label: MariaDB\n", "sidebar_class_name: node-only\n", "---"]}, {"cell_type": "markdown", "id": "ef1f0986", "metadata": {}, "source": ["# MariaDB\n", "\n", "```{=mdx}\n", ":::tip Compatibility\n", "Only available on Node.js.\n", ":::\n", "```\n", "\n", "This requires MariaDB 11.7 or later version\n", "\n", "This guide provides a quick overview for getting started with mariadb [vector stores](/docs/concepts/#vectorstores). For detailed documentation of all `MariaDB store` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_vectorstores_mariadb.MariaDBStore.html)."]}, {"cell_type": "markdown", "id": "c824838d", "metadata": {}, "source": ["## Overview\n", "\n", "### Integration details\n", "\n", "| Class | Package | [PY support](https://python.langchain.com/docs/integrations/vectorstores/mariadb/) | Package latest |\n", "| :--- | :--- | :---: | :---: |\n", "| [`MariaDBStore`](https://api.js.langchain.com/classes/langchain_community_vectorstores_mariadb.MariaDBStore.html) | [`@langchain/community`](https://npmjs.com/@langchain/community) | ✅ | ![NPM - Version](https://img.shields.io/npm/v/@langchain/community?style=flat-square&label=%20&) |"]}, {"cell_type": "markdown", "id": "36fdc060", "metadata": {}, "source": ["## Setup\n", "\n", "To use MariaDBVector vector stores, you'll need to set up a MariaDB 11.7 version or later with the [`mariadb`](https://www.npmjs.com/package/mariadb) connector as a peer dependency.\n", "\n", "This guide will also use [OpenAI embeddings](/docs/integrations/text_embedding/openai), which require you to install the `@langchain/openai` integration package. You can also use [other supported embeddings models](/docs/integrations/text_embedding) if you wish.\n", "\n", "We'll also use the [`uuid`](https://www.npmjs.com/package/uuid) package to generate ids in the required format.\n", "\n", "```{=mdx}\n", "import IntegrationInstallTooltip from \"@mdx_components/integration_install_tooltip.mdx\";\n", "import Npm2Yarn from \"@theme/Npm2Yarn\";\n", "\n", "<IntegrationInstallTooltip></IntegrationInstallTooltip>\n", "\n", "<Npm2Yarn>\n", "  @langchain/community @langchain/openai @langchain/core mariadb uuid\n", "</Npm2Yarn>\n", "```\n", "\n", "### Setting up an instance\n", "\n", "Create a file with the below content named docker-compose.yml:\n", "\n", "```yaml\n", "# Run this command to start the database:\n", "# docker-compose up --build\n", "version: \"3\"\n", "services:\n", "  db:\n", "    hostname: 127.0.0.1\n", "    image: mariadb/mariadb:11.7-rc\n", "    ports:\n", "      - 3306:3306\n", "    restart: always\n", "    environment:\n", "      - MARIADB_DATABASE=api\n", "      - MARIADB_USER=myuser\n", "      - MARIADB_PASSWORD=ChangeMe\n", "      - MARIADB_ROOT_PASSWORD=ChangeMe\n", "    volumes:\n", "      - ./init.sql:/docker-entrypoint-initdb.d/init.sql\n", "```\n", "\n", "And then in the same directory, run docker compose up to start the container.\n", "\n", "### Credentials\n", "\n", "To connect to you MariaDB instance, you'll need corresponding credentials. For a full list of supported options, see the [`mariadb` docs](https://github.com/mariadb-corporation/mariadb-connector-nodejs/blob/master/documentation/promise-api.md#connection-options).\n", "\n", "If you are using OpenAI embeddings for this guide, you'll need to set your OpenAI key as well:\n", "\n", "```typescript\n", "process.env.OPENAI_API_KEY = \"YOUR_API_KEY\";\n", "```\n", "\n", "If you want to get automated tracing of your model calls you can also set your [LangSmith](https://docs.smith.langchain.com/) API key by uncommenting below:\n", "\n", "```typescript\n", "// process.env.LANGCHAIN_TRACING_V2=\"true\"\n", "// process.env.LANGCHAIN_API_KEY=\"your-api-key\"\n", "```"]}, {"cell_type": "markdown", "id": "93df377e", "metadata": {}, "source": ["## Instantiation\n", "\n", "To instantiate the vector store, call the `.initialize()` static method. This will automatically check for the presence of a table, given by `tableName` in the passed `config`. If it is not there, it will create it with the required columns.\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "id": "dc37144c-208d-4ab3-9f3a-0407a69fe052", "metadata": {"tags": []}, "outputs": [], "source": ["import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "import {\n", "   DistanceStrategy,\n", "   MariaDBStore,\n", "} from \"@langchain/community/vectorstores/mariadb\";\n", "import { PoolConfig } from \"mariadb\";\n", "\n", "const config = {\n", "  connectionOptions: {\n", "    type: \"mariadb\",\n", "    host: \"127.0.0.1\",\n", "    port: 3306,\n", "    user: \"my<PERSON>\",\n", "    password: \"<PERSON><PERSON>e\",\n", "    database: \"api\",\n", "  } as PoolConfig,\n", "  distanceStrategy: 'EUCLIDEAN' as DistanceStrategy,\n", "};\n", "const vectorStore = await MariaDBStore.initialize(\n", "  new OpenAIEmbeddings(),\n", "   config\n", ");"]}, {"cell_type": "markdown", "id": "ac6071d4", "metadata": {}, "source": ["## Manage vector store\n", "\n", "### Add items to vector store"]}, {"cell_type": "code", "execution_count": 2, "id": "17f5efc0", "metadata": {}, "outputs": [], "source": ["import { v4 as uuidv4 } from \"uuid\";\n", "import type { Document } from \"@langchain/core/documents\";\n", "\n", "const document1: Document = {\n", "  pageContent: \"The powerhouse of the cell is the mitochondria\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document2: Document = {\n", "  pageContent: \"Buildings are made out of brick\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document3: Document = {\n", "  pageContent: \"Mitochondria are made out of lipids\",\n", "  metadata: { source: \"https://example.com\" }\n", "};\n", "\n", "const document4: Document = {\n", "  pageContent: \"The 2024 Olympics are in Paris\",\n", "  metadata: { source: \"https://example.com\" }\n", "}\n", "\n", "const documents = [document1, document2, document3, document4];\n", "\n", "const ids = [uuidv4(), uuidv4(), uuidv4(), uuidv4()]\n", "\n", "// ids are not mandatory, but that's for the example\n", "await vectorStore.addDocuments(documents, { ids: ids });"]}, {"cell_type": "markdown", "id": "dcf1b905", "metadata": {}, "source": ["### Delete items from vector store"]}, {"cell_type": "code", "execution_count": 4, "id": "ef61e188", "metadata": {}, "outputs": [], "source": ["const id4 = ids[ids.length - 1];\n", "\n", "await vectorStore.delete({ ids: [id4] });"]}, {"cell_type": "markdown", "id": "c3620501", "metadata": {}, "source": ["## Query vector store\n", "\n", "Once your vector store has been created and the relevant documents have been added you will most likely wish to query it during the running of your chain or agent. \n", "\n", "### Query directly\n", "\n", "Performing a simple similarity search can be done as follows:"]}, {"cell_type": "code", "execution_count": 5, "id": "aa0a16fa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* The powerhouse of the cell is the mitochondria [{\"year\": 2021}]\n", "* Mitochondria are made out of lipids [{\"year\": 2022}]\n"]}], "source": ["const similaritySearchResults = await vectorStore.similaritySearch(\"biology\", 2, { \"year\": 2021 });\n", "for (const doc of similaritySearchResults) {\n", "  console.log(`* ${doc.pageContent} [${JSON.stringify(doc.metadata, null)}]`);\n", "}"]}, {"cell_type": "markdown", "id": "3ed9d733", "metadata": {}, "source": ["The above filter syntax use be more complex:\n", "\n", "```json\n", "# name = 'martin' OR firstname = 'john'\n", "let res = await vectorStore.similaritySearch(\"biology\", 2, {\"$or\": [{\"name\":\"martin\"}, {\"firstname\", \"john\"}] });\n", "```\n", "\n", "If you want to execute a similarity search and receive the corresponding scores you can run:"]}, {"cell_type": "code", "execution_count": 6, "id": "5efd2eaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* [SIM=0.835] The powerhouse of the cell is the mitochondria [{\"source\":\"https://example.com\"}]\n", "* [SIM=0.852] Mitochondria are made out of lipids [{\"source\":\"https://example.com\"}]\n"]}], "source": ["const similaritySearchWithScoreResults = await vectorStore.similaritySearchWithScore(\"biology\", 2)\n", "\n", "for (const [doc, score] of similaritySearchWithScoreResults) {\n", "  console.log(`* [SIM=${score.toFixed(3)}] ${doc.pageContent} [${JSON.stringify(doc.metadata)}]`);\n", "}"]}, {"cell_type": "markdown", "id": "0c235cdc", "metadata": {}, "source": ["### Query by turning into retriever\n", "\n", "You can also transform the vector store into a [retriever](/docs/concepts/retrievers) for easier usage in your chains. "]}, {"cell_type": "code", "execution_count": 7, "id": "f3460093", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  Document {\n", "    pageContent: 'The powerhouse of the cell is the mitochondria',\n", "    metadata: { source: 'https://example.com' },\n", "    id: undefined\n", "  },\n", "  Document {\n", "    pageContent: 'Mitochondria are made out of lipids',\n", "    metadata: { source: 'https://example.com' },\n", "    id: undefined\n", "  }\n", "]\n"]}], "source": ["const retriever = vectorStore.asRetriever({\n", "  // Optional filter\n", "  // filter: filter,\n", "  k: 2,\n", "});\n", "await retriever.invoke(\"biology\");"]}, {"cell_type": "markdown", "id": "e2e0a211", "metadata": {}, "source": ["### Usage for retrieval-augmented generation\n", "\n", "For guides on how to use this vector store for retrieval-augmented generation (RAG), see the following sections:\n", "\n", "- [Tutorials: working with external knowledge](/docs/tutorials/#working-with-external-knowledge).\n", "- [How-to: Question and answer with RAG](/docs/how_to/#qa-with-rag)\n", "- [Retrieval conceptual docs](/docs/concepts/retrieval)"]}, {"cell_type": "markdown", "id": "371727a8", "metadata": {}, "source": ["## Advanced: reusing connections\n", "\n", "You can reuse connections by creating a pool, then creating new `MariaDBStore` instances directly via the constructor.\n", "\n", "Note that you should call `.initialize()` to set up your database at least once to set up your tables properly before using the constructor."]}, {"cell_type": "code", "execution_count": null, "id": "09efeac4", "metadata": {}, "outputs": [], "source": ["import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "import { MariaDBStore } from \"@langchain/community/vectorstores/mariadb\";\n", "import mariadb from \"mariadb\";\n", "\n", "// First, follow set-up instructions at\n", "// https://js.langchain.com/docs/modules/indexes/vector_stores/integrations/mariadb\n", "\n", "const reusablePool = mariadb.createPool({\n", "  host: \"127.0.0.1\",\n", "  port: 3306,\n", "  user: \"my<PERSON>\",\n", "  password: \"<PERSON><PERSON>e\",\n", "  database: \"api\",\n", "});\n", "\n", "const originalConfig = {\n", "  pool: reusablePool,\n", "  tableName: \"testlangchainjs\",\n", "  collectionName: \"sample\",\n", "  collectionTableName: \"collections\",\n", "  columns: {\n", "    idColumnName: \"id\",\n", "    vectorColumnName: \"vect\",\n", "    contentColumnName: \"content\",\n", "    metadataColumnName: \"metadata\",\n", "  },\n", "};\n", "\n", "// Set up the DB.\n", "// Can skip this step if you've already initialized the DB.\n", "// await MariaDBStore.initialize(new OpenAIEmbeddings(), originalConfig);\n", "const mariadbStore = new MariaDBStore(new OpenAIEmbeddings(), originalConfig);\n", "\n", "await mariadbStore.addDocuments([\n", "  { pageContent: \"what's this\", metadata: { a: 2 } },\n", "  { pageContent: \"<PERSON> drinks milk\", metadata: { a: 1 } },\n", "]);\n", "\n", "const results = await mariadbStore.similaritySearch(\"water\", 1);\n", "\n", "console.log(results);\n", "\n", "/*\n", "  [ Document { pageContent: 'Cat drinks milk', metadata: { a: 1 } } ]\n", "*/\n", "\n", "const mariadbStore2 = new MariaDBStore(new OpenAIEmbeddings(), {\n", "  pool: reusablePool,\n", "  tableName: \"testlangchainjs\",\n", "  collectionTableName: \"collections\",\n", "  collectionName: \"some_other_collection\",\n", "  columns: {\n", "    idColumnName: \"id\",\n", "    vectorColumnName: \"vector\",\n", "    contentColumnName: \"content\",\n", "    metadataColumnName: \"metadata\",\n", "  },\n", "});\n", "\n", "const results2 = await mariadbStore2.similaritySearch(\"water\", 1);\n", "\n", "console.log(results2);\n", "\n", "/*\n", "  []\n", "*/\n", "\n", "await reusablePool.end();"]}, {"cell_type": "markdown", "id": "069f1b5f", "metadata": {}, "source": ["## Closing connections\n", "\n", "Make sure you close the connection when you are finished to avoid excessive resource consumption:"]}, {"cell_type": "code", "execution_count": null, "id": "f71ce986", "metadata": {}, "outputs": [], "source": ["await vectorStore.end();"]}, {"cell_type": "markdown", "id": "8a27244f", "metadata": {}, "source": ["## API reference\n", "\n", "For detailed documentation of all `MariaDBStore` features and configurations head to the [API reference](https://api.js.langchain.com/classes/langchain_community_vectorstores_mariadb.MariaDBStore.html)."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}