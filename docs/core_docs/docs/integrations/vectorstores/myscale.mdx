---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# MyScale

:::tip Compatibility
Only available on Node.js.
:::

[MyScale](https://myscale.com/) is an emerging AI database that harmonizes the power of vector search and SQL analytics, providing a managed, efficient, and responsive experience.

## Setup

1. Launch a cluster through [MyScale's Web Console](https://console.myscale.com/). See [MyScale's official documentation](https://docs.myscale.com/en/quickstart/) for more information.
2. After launching a cluster, view your `Connection Details` from your cluster's `Actions` menu. You will need the host, port, username, and password.
3. Install the required Node.js peer dependency in your workspace.

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install -S @langchain/openai @clickhouse/client @langchain/community @langchain/core
```

## Index and Query Docs

import InsertExample from "@examples/indexes/vector_stores/myscale_fromTexts.ts";

<CodeBlock language="typescript">{InsertExample}</CodeBlock>

## Query Docs From an Existing Collection

import SearchExample from "@examples/indexes/vector_stores/myscale_search.ts";

<CodeBlock language="typescript">{SearchExample}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
