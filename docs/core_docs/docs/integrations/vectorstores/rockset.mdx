---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# Rockset

[Rockset](https://rockset.com) is a real-time analyitics SQL database that runs in the cloud.
Rockset provides vector search capabilities, in the form of [SQL functions](https://rockset.com/docs/vector-functions/#vector-distance-functions), to support AI applications that rely on text similarity.

## Setup

Install the rockset client.

```bash
yarn add @rockset/client
```

### Usage

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/core @langchain/community
```

import UsageExample from "@examples/indexes/vector_stores/rockset.ts";

Below is an example showcasing how to use OpenAI and Rockset to answer questions about a text file:

<CodeBlock language="typescript">{UsageExample}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
