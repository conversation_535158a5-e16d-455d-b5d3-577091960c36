---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# Tigris

Tigris makes it easy to build AI applications with vector embeddings.
It is a fully managed cloud-native database that allows you store and
index documents and vector embeddings for fast and scalable vector search.

:::tip Compatibility
Only available on Node.js.
:::

## Setup

### 1. Install the Tigris SDK

Install the SDK as follows

```bash npm2yarn
npm install -S @tigrisdata/vector
```

### 2. Fetch Tigris API credentials

You can sign up for a free Tigris account [here](https://www.tigrisdata.com/).

Once you have signed up for the Tigris account, create a new project called `vectordemo`.
Next, make a note of the `clientId` and `clientSecret`, which you can get from the
Application Keys section of the project.

## Index docs

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install -S @langchain/openai
```

```typescript
import { VectorDocumentStore } from "@tigrisdata/vector";
import { Document } from "langchain/document";
import { OpenAIEmbeddings } from "@langchain/openai";
import { TigrisVectorStore } from "langchain/vectorstores/tigris";

const index = new VectorDocumentStore({
  connection: {
    serverUrl: "api.preview.tigrisdata.cloud",
    projectName: process.env.TIGRIS_PROJECT,
    clientId: process.env.TIGRIS_CLIENT_ID,
    clientSecret: process.env.TIGRIS_CLIENT_SECRET,
  },
  indexName: "examples_index",
  numDimensions: 1536, // match the OpenAI embedding size
});

const docs = [
  new Document({
    metadata: { foo: "bar" },
    pageContent: "tigris is a cloud-native vector db",
  }),
  new Document({
    metadata: { foo: "bar" },
    pageContent: "the quick brown fox jumped over the lazy dog",
  }),
  new Document({
    metadata: { baz: "qux" },
    pageContent: "lorem ipsum dolor sit amet",
  }),
  new Document({
    metadata: { baz: "qux" },
    pageContent: "tigris is a river",
  }),
];

await TigrisVectorStore.fromDocuments(docs, new OpenAIEmbeddings(), { index });
```

## Query docs

import Search from "@examples/indexes/vector_stores/tigris/search.ts";

```typescript
import { VectorDocumentStore } from "@tigrisdata/vector";
import { OpenAIEmbeddings } from "@langchain/openai";
import { TigrisVectorStore } from "langchain/vectorstores/tigris";

const index = new VectorDocumentStore({
  connection: {
    serverUrl: "api.preview.tigrisdata.cloud",
    projectName: process.env.TIGRIS_PROJECT,
    clientId: process.env.TIGRIS_CLIENT_ID,
    clientSecret: process.env.TIGRIS_CLIENT_SECRET,
  },
  indexName: "examples_index",
  numDimensions: 1536, // match the OpenAI embedding size
});

const vectorStore = await TigrisVectorStore.fromExistingIndex(
  new OpenAIEmbeddings(),
  { index }
);

/* Search the vector DB independently with metadata filters */
const results = await vectorStore.similaritySearch("tigris", 1, {
  "metadata.foo": "bar",
});
console.log(JSON.stringify(results, null, 2));
/*
[
  Document {
    pageContent: 'tigris is a cloud-native vector db',
    metadata: { foo: 'bar' }
  }
]
*/
```

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
