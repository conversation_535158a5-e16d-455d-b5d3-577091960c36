# Turbopuffer

## Setup

First you must sign up for a Turbopuffer account [here](https://turbopuffer.com/join).
Then, once you have an account you can create an API key.

Set your API key as an environment variable:

```bash
export TURBOPUFFER_API_KEY=<YOUR_API_KEY>
```

## Usage

import CodeBlock from "@theme/CodeBlock";
import SimilaritySearchExample from "@examples/indexes/vector_stores/turbopuffer.ts";

Here are some examples of how to use the class. You can filter your queries by previous specified metadata, but
keep in mind that currently only string values are supported.

See [here for more information](https://turbopuffer.com/docs/reference/query#filter-parameters) on acceptable filter formats.

<CodeBlock language="typescript">{SimilaritySearchExample}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
