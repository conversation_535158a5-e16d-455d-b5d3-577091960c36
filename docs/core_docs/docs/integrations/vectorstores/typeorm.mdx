# TypeORM

To enable vector search in a generic PostgreSQL database, LangChain.js supports using [TypeORM](https://typeorm.io/) with the [`pgvector`](https://github.com/pgvector/pgvector) Postgres extension.

## Setup

To work with TypeORM, you need to install the `typeorm` and `pg` packages:

```bash npm2yarn
npm install typeorm
```

```bash npm2yarn
npm install pg
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

### Setup a `pgvector` self hosted instance with `docker-compose`

`pgvector` provides a prebuilt Docker image that can be used to quickly setup a self-hosted Postgres instance.
Create a file below named `docker-compose.yml`:

import CodeBlock from "@theme/CodeBlock";
import DockerExample from "@examples/indexes/vector_stores/typeorm_vectorstore/docker-compose.example.yml";

<CodeBlock language="yml" name="docker-compose.yml">
  {DockerExample}
</CodeBlock>

And then in the same directory, run `docker compose up` to start the container.

You can find more information on how to setup `pgvector` in the [official repository](https://github.com/pgvector/pgvector).

## Usage

import Example from "@examples/indexes/vector_stores/typeorm_vectorstore/typeorm.ts";

One complete example of using `TypeORMVectorStore` is the following:

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
