---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# USearch

:::tip Compatibility
Only available on Node.js.
:::

[USearch](https://github.com/unum-cloud/usearch) is a library for efficient similarity search and clustering of dense vectors.

## Setup

Install the [usearch](https://github.com/unum-cloud/usearch/tree/main/javascript) package, which is a Node.js binding for [USearch](https://github.com/unum-cloud/usearch).

```bash npm2yarn
npm install -S usearch
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

## Usage

### Create a new index from texts

import ExampleTexts from "@examples/indexes/vector_stores/usearch.ts";

<CodeBlock language="typescript">{ExampleTexts}</CodeBlock>

### Create a new index from a loader

import ExampleLoader from "@examples/indexes/vector_stores/usearch_fromdocs.ts";

<CodeBlock language="typescript">{ExampleLoader}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
