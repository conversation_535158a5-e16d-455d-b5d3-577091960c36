---
sidebar_class_name: node-only
---

import CodeBlock from "@theme/CodeBlock";

# Vectara

Vectara is a platform for building GenAI applications. It provides an easy-to-use API for document indexing and querying that is managed by Vectara and is optimized for performance and accuracy.

You can use Vectara as a vector store with LangChain.js.

## 👉 Embeddings Included

Vectara uses its own embeddings under the hood, so you don't have to provide any yourself or call another service to obtain embeddings.

This also means that if you provide your own embeddings, they'll be a no-op.

```typescript
const store = await VectaraStore.fromTexts(
  ["hello world", "hi there"],
  [{ foo: "bar" }, { foo: "baz" }],
  // This won't have an effect. Provide a FakeEmbeddings instance instead for clarity.
  new OpenAIEmbeddings(),
  args
);
```

## Setup

You'll need to:

- Create a [free Vectara account](https://vectara.com/integrations/langchain).
- Create a [corpus](https://docs.vectara.com/docs/console-ui/creating-a-corpus) to store your data
- Create an [API key](https://docs.vectara.com/docs/common-use-cases/app-authn-authz/api-keys) with QueryService and IndexService access so you can access this corpus

Configure your `.env` file or provide args to connect LangChain to your Vectara corpus:

```
VECTARA_CUSTOMER_ID=your_customer_id
VECTARA_CORPUS_ID=your_corpus_id
VECTARA_API_KEY=your-vectara-api-key
```

Note that you can provide multiple corpus IDs separated by commas for querying multiple corpora at once. For example: `VECTARA_CORPUS_ID=3,8,9,43`.
For indexing multiple corpora, you'll need to create a separate VectaraStore instance for each corpus.

## Usage

import Example from "@examples/indexes/vector_stores/vectara.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

Note that `lambda` is a parameter related to Vectara's hybrid search capbility, providing a tradeoff between neural search and boolean/exact match as described [here](https://docs.vectara.com/docs/api-reference/search-apis/lexical-matching). We recommend the value of 0.025 as a default, while providing a way for advanced users to customize this value if needed.

## APIs

Vectara's LangChain vector store consumes Vectara's core APIs:

- [Indexing API](https://docs.vectara.com/docs/indexing-apis/indexing) for storing documents in a Vectara corpus.
- [Search API](https://docs.vectara.com/docs/search-apis/search) for querying this data. This API supports hybrid search.

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
