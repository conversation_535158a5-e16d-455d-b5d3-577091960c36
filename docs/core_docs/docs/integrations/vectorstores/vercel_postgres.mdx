# Vercel Postgres

LangChain.js supports using the [`@vercel/postgres`](https://www.npmjs.com/package/@vercel/postgres) package to use generic Postgres databases
as vector stores, provided they support the [`pgvector`](https://github.com/pgvector/pgvector) Postgres extension.

This integration is particularly useful from web environments like Edge functions.

## Setup

To work with Vercel Postgres, you need to install the `@vercel/postgres` package:

```bash npm2yarn
npm install @vercel/postgres
```

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/community @langchain/core
```

This integration automatically connects using the connection string set under `process.env.POSTGRES_URL`.
You can also pass a connection string manually like this:

```typescript
const vectorstore = await VercelPostgres.initialize(new OpenAIEmbeddings(), {
  postgresConnectionOptions: {
    connectionString:
      "postgres://<username>:<password>@<hostname>:<port>/<dbname>",
  },
});
```

### Connecting to Vercel Postgres

A simple way to get started is to create a serverless [Vercel Postgres instance](https://vercel.com/docs/storage/vercel-postgres/quickstart).
If you're deploying to a Vercel project with an associated Vercel Postgres instance, the required `POSTGRES_URL` environment variable
will already be populated in hosted environments.

### Connecting to other databases

If you prefer to host your own Postgres instance, you can use a similar flow to LangChain's [PGVector](/docs/integrations/vectorstores/pgvector)
vectorstore integration and set the connection string either as an environment variable or as shown above.

## Usage

import CodeBlock from "@theme/CodeBlock";
import Example from "@examples/indexes/vector_stores/vercel_postgres/example.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
