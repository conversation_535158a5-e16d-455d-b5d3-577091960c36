import CodeBlock from "@theme/CodeBlock";

# Voy

[Voy](https://github.com/tantaraio/voy) is a WASM vector similarity search engine written in Rust.
It's supported in non-Node environments like browsers. You can use Voy as a vector store with LangChain.js.

### Install Voy

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai voy-search @langchain/community @langchain/core
```

## Usage

import Example from "@examples/indexes/vector_stores/voy.ts";

<CodeBlock language="typescript">{Example}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
