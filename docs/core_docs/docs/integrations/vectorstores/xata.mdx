# Xata

[Xata](https://xata.io) is a serverless data platform, based on PostgreSQL. It provides a type-safe TypeScript/JavaScript SDK for interacting with your database, and a UI for managing your data.

Xata has a native vector type, which can be added to any table, and supports similarity search. LangChain inserts vectors directly to Xata, and queries it for the nearest neighbors of a given vector, so that you can use all the LangChain Embeddings integrations with Xata.

## Setup

### Install the Xata CLI

```bash
npm install @xata.io/cli -g
```

### Create a database to be used as a vector store

In the [Xata UI](https://app.xata.io) create a new database. You can name it whatever you want, but for this example we'll use `langchain`.
Create a table, again you can name it anything, but we will use `vectors`. Add the following columns via the UI:

- `content` of type "Text". This is used to store the `Document.pageContent` values.
- `embedding` of type "Vector". Use the dimension used by the model you plan to use (1536 for OpenAI).
- any other columns you want to use as metadata. They are populated from the `Document.metadata` object. For example, if in the `Document.metadata` object you have a `title` property, you can create a `title` column in the table and it will be populated.

### Initialize the project

In your project, run:

```bash
xata init
```

and then choose the database you created above. This will also generate a `xata.ts` or `xata.js` file that defines the client you can use to interact with the database. See the [Xata getting started docs](https://xata.io/docs/getting-started/installation) for more details on using the Xata JavaScript/TypeScript SDK.

## Usage

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

import CodeBlock from "@theme/CodeBlock";

### Example: Q&A chatbot using OpenAI and Xata as vector store

This example uses the `VectorDBQAChain` to search the documents stored in Xata and then pass them as context to the OpenAI model, in order to answer the question asked by the user.

import FromDocs from "@examples/indexes/vector_stores/xata.ts";

<CodeBlock language="typescript">{FromDocs}</CodeBlock>

### Example: Similarity search with a metadata filter

This example shows how to implement semantic search using LangChain.js and Xata. Before running it, make sure to add an `author` column of type String to the `vectors` table in Xata.

import SimSearch from "@examples/indexes/vector_stores/xata_metadata.ts";

<CodeBlock language="typescript">{SimSearch}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
