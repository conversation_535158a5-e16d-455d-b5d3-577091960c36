# Zep Open Source

> [Zep](https://www.getzep.com) is a long-term memory service for AI Assistant apps.
> With Zep, you can provide AI assistants with the ability to recall past conversations, no matter how distant,
> while also reducing hallucinations, latency, and cost.

> Interested in Zep Cloud? See [Zep Cloud Installation Guide](https://help.getzep.com/sdks)

import CodeBlock from "@theme/CodeBlock";

**Note:** The `ZepVectorStore` works with `Documents` and is intended to be used as a `Retriever`.
It offers separate functionality to Zep's `ZepMemory` class, which is designed for persisting, enriching
and searching your user's chat history.

## Why Zep's VectorStore? 🤖🚀

Zep automatically embeds documents added to the Zep Vector Store using low-latency models local to the Zep server.
The Zep TS/JS client can be used in non-Node edge environments. These two together with Zep's chat memory functionality
make Zep ideal for building conversational LLM apps where latency and performance are important.

### Supported Search Types

Zep supports both similarity search and Maximal Marginal Relevance (MMR) search. MMR search is particularly useful
for Retrieval Augmented Generation applications as it re-ranks results to ensure diversity in the returned documents.

## Installation

Follow the [Zep Open Source Quickstart Guide](https://docs.getzep.com/deployment/quickstart/) to install and get started with Zep.

## Usage

You'll need your Zep API URL and optionally an API key to use the Zep VectorStore. See the [Zep docs](https://docs.getzep.com) for more information.

In the examples below, we're using Zep's auto-embedding feature which automatically embed documents on the Zep server using
low-latency embedding models. Since LangChain requires passing in a `Embeddings` instance, we pass in `FakeEmbeddings`.

**Note:** If you pass in an `Embeddings` instance other than `FakeEmbeddings`, this class will be used to embed documents.
You must also set your document collection to `isAutoEmbedded === false`. See the `OpenAIEmbeddings` example below.

### Example: Creating a ZepVectorStore from Documents & Querying

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

```bash npm2yarn
npm install @langchain/openai @langchain/community @langchain/core
```

import ExampleDocs from "@examples/indexes/vector_stores/zep/zep_from_docs.ts";

<CodeBlock language="typescript">{ExampleDocs}</CodeBlock>

### Example: Querying a ZepVectorStore using a metadata filter

import ExampleMetadata from "@examples/indexes/vector_stores/zep/zep_with_metadata.ts";

<CodeBlock language="typescript">{ExampleMetadata}</CodeBlock>

### Example: Using a LangChain Embedding Class such as `OpenAIEmbeddings`

import ExampleOpenAI from "@examples/indexes/vector_stores/zep/zep_with_openai_embeddings.ts";

<CodeBlock language="typescript">{ExampleOpenAI}</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
