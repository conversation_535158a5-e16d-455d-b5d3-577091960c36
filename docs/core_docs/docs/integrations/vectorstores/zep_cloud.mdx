# Zep Cloud

> [Zep](https://www.getzep.com) is a long-term memory service for AI Assistant apps.
> With Zep, you can provide AI assistants with the ability to recall past conversations, no matter how distant,
> while also reducing hallucinations, latency, and cost.

import CodeBlock from "@theme/CodeBlock";

**Note:** The `ZepCloudVectorStore` works with `Documents` and is intended to be used as a `Retriever`.
It offers separate functionality to Zep's `ZepCloudMemory` class, which is designed for persisting, enriching
and searching your user's chat history.

## Why Zep's VectorStore? 🤖🚀

Zep automatically embeds documents added to the Zep Vector Store using low-latency models local to the Zep server.
The Zep TS/JS client can be used in non-Node edge environments. These two together with Zep's chat memory functionality
make Zep ideal for building conversational LLM apps where latency and performance are important.

### Supported Search Types

Zep supports both similarity search and Maximal Marginal Relevance (MMR) search. MMR search is particularly useful
for Retrieval Augmented Generation applications as it re-ranks results to ensure diversity in the returned documents.

## Installation

Sign up for [Z<PERSON> Cloud](https://app.getzep.com/) and create a project.

Follow the [Zep Cloud Typescript SDK Installation Guide](https://help.getzep.com/sdks) to install and get started with Zep.

## Usage

You'll need your Zep Cloud Project API Key to use the Zep VectorStore. See the [Zep Cloud docs](https://help.getzep.com/projects) for more information.

Zep auto embeds all documents by default, and it's not expecting to receive any embeddings from the user.
Since LangChain requires passing in a `Embeddings` instance, we pass in `FakeEmbeddings`.

import IntegrationInstallTooltip from "@mdx_components/integration_install_tooltip.mdx";

<IntegrationInstallTooltip></IntegrationInstallTooltip>

### Example: Creating a ZepVectorStore from Documents & Querying

```bash npm2yarn
npm install @getzep/zep-cloud @langchain/openai @langchain/community @langchain/core
```

import ZepCloudVectorStoreExample from "@examples/indexes/vector_stores/zep/zep_cloud.ts";

<CodeBlock language="typescript">{ZepCloudVectorStoreExample}</CodeBlock>

### Example: Using ZepCloudVectorStore with Expression Language

import ZepCloudVectorStoreExpressionLanguageExample from "@examples/guides/expression_language/zep/zep_cloud_vector_store.ts";

<CodeBlock language="typescript">
  {ZepCloudVectorStoreExpressionLanguageExample}
</CodeBlock>

## Related

- Vector store [conceptual guide](/docs/concepts/#vectorstores)
- Vector store [how-to guides](/docs/how_to/#vectorstores)
