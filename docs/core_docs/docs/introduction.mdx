---
sidebar_position: 0
---

# Introduction

**<PERSON><PERSON>hain** is a framework for developing applications powered by large language models (LLMs).

<PERSON><PERSON>hain simplifies every stage of the LLM application lifecycle:

- **Development**: Build your applications using LangChain's open-source [building blocks](/docs/concepts/lcel), [components](/docs/concepts), and [third-party integrations](/docs/integrations/platforms/).
  Use [LangGraph.js](/docs/concepts/architecture#langgraph) to build stateful agents with first-class streaming and human-in-the-loop support.
- **Productionization**: Use [LangSmith](https://docs.smith.langchain.com/) to inspect, monitor and evaluate your chains, so that you can continuously optimize and deploy with confidence.
- **Deployment**: Turn your LangGraph applications into production-ready APIs and Assistants with [LangGraph Cloud](https://langchain-ai.github.io/langgraph/cloud/).

import ThemedImage from "@theme/ThemedImage";
import useBaseUrl from "@docusaurus/useBaseUrl";

<ThemedImage
  alt="Diagram outlining the hierarchical organization of the LangChain framework, displaying the interconnected parts across multiple layers."
  sources={{
    light: useBaseUrl("/svg/langchain_stack_062024.svg"),
    dark: useBaseUrl("/svg/langchain_stack_062024_dark.svg"),
  }}
  title="LangChain Framework Overview"
  style={{ width: "100%" }}
/>

Concretely, the framework consists of the following open-source libraries:

- **`@langchain/core`**: Base abstractions and LangChain Expression Language.
- **`@langchain/community`**: Third party integrations.
  - Partner packages (e.g. **`@langchain/openai`**, **`@langchain/anthropic`**, etc.): Some integrations have been further split into their own lightweight packages that only depend on **`@langchain/core`**.
- **`langchain`**: Chains, agents, and retrieval strategies that make up an application's cognitive architecture.
- **[LangGraph.js](https://langchain-ai.github.io/langgraphjs/)**: Build robust and stateful multi-actor applications with LLMs by modeling steps as edges and nodes in a graph.
- **[LangSmith](https://docs.smith.langchain.com)**: A developer platform that lets you debug, test, evaluate, and monitor LLM applications.

:::note

These docs focus on the JavaScript LangChain library. [Head here](https://python.langchain.com) for docs on the Python LangChain library.

:::

## [Tutorials](/docs/tutorials)

If you're looking to build something specific or are more of a hands-on learner, check out our [tutorials](/docs/tutorials).
This is the best place to get started.

These are the best ones to get started with:

- [Build a Simple LLM Application](/docs/tutorials/llm_chain)
- [Build a Chatbot](/docs/tutorials/chatbot)
- [Build an Agent](https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/)
- [LangGraph.js quickstart](https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/)

Explore the full list of LangChain tutorials [here](/docs/tutorials), and check out other [LangGraph tutorials here](https://langchain-ai.github.io/langgraphjs/tutorials/).

## [How-To Guides](/docs/how_to/)

[Here](/docs/how_to/) you'll find short answers to “How do I….?” types of questions.
These how-to guides don't cover topics in depth - you'll find that material in the [Tutorials](/docs/tutorials) and the [API Reference](https://api.js.langchain.com).
However, these guides will help you quickly accomplish common tasks.

Check out [LangGraph-specific how-tos here](https://langchain-ai.github.io/langgraphjs/how-tos/).

## [Conceptual Guide](/docs/concepts)

Introductions to all the key parts of LangChain you'll need to know! [Here](/docs/concepts) you'll find high level explanations of all LangChain concepts.

For a deeper dive into LangGraph concepts, check out [this page](https://langchain-ai.github.io/langgraph/concepts/).

## [API reference](https://api.js.langchain.com)

Head to the reference section for full documentation of all classes and methods in the LangChain JavaScript packages.

## Ecosystem

### [🦜🛠️ LangSmith](https://docs.smith.langchain.com)

Trace and evaluate your language model applications and intelligent agents to help you move from prototype to production.

### [🦜🕸️ LangGraph](https://langchain-ai.github.io/langgraphjs/)

Build stateful, multi-actor applications with LLMs. Integrates smoothly with LangChain, but can be used without it. LangGraph powers production-grade agents, trusted by Linkedin, Uber, Klarna, GitLab, and many more.

## Additional resources

### [Security](/docs/security)

Read up on our [Security](/docs/security) best practices to make sure you're developing safely with LangChain.

### [Integrations](/docs/integrations/platforms/)

LangChain is part of a rich ecosystem of tools that integrate with our framework and build on top of it. Check out our growing list of [integrations](/docs/integrations/platforms/).

### [Contributing](/docs/contributing)

Check out the developer's guide for guidelines on contributing and help getting your dev environment set up.
