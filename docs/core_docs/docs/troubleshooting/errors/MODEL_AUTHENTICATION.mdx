# MODEL_AUTHENTICATION

Your model provider is denying you access to their service.

## Troubleshooting

The following may help resolve this error:

- Confirm that your API key or other credentials are correct.
- If you are relying on an environment variable to authenticate, confirm that the variable name is correct and that it has a value set.
  - Note that some environments, like Cloudflare Workers, do not support environment variables.
  - For some models, you can try explicitly passing an `apiKey` parameter to rule out any environment variable issues like this:

```ts
const model = new ChatOpenAI({
  apiKey: "YOUR_KEY_HERE",
});
```

- If you are using a proxy or other custom endpoint, make sure that your custom provider does not expect an alternative authentication scheme.
