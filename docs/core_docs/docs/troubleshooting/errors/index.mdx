# Error reference

This page contains guides around resolving common errors you may find while building with <PERSON><PERSON><PERSON><PERSON>.
Errors referenced below will have an `lc_error_code` property corresponding to one of the below codes when they are thrown in code.

- [INVALID_PROMPT_INPUT](/docs/troubleshooting/errors/INVALID_PROMPT_INPUT)
- [INVALID_TOOL_RESULTS](/docs/troubleshooting/errors/INVALID_TOOL_RESULTS)
- [MESSAGE_COERCION_FAILURE](/docs/troubleshooting/errors/MESSAGE_COERCION_FAILURE)
- [MODEL_AUTHENTICATION](/docs/troubleshooting/errors/MODEL_AUTHENTICATION)
- [MODEL_NOT_FOUND](/docs/troubleshooting/errors/MODEL_NOT_FOUND)
- [MODEL_RATE_LIMIT](/docs/troubleshooting/errors/MODEL_RATE_LIMIT)
- [OUTPUT_PARSING_FAILURE](/docs/troubleshooting/errors/OUTPUT_PARSING_FAILURE)
