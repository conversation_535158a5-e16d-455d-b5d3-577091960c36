{"cells": [{"cell_type": "raw", "id": "cb6f552e-775f-4d84-bc7c-dca94c06a33c", "metadata": {}, "source": ["---\n", "title: Tagging\n", "sidebar_class_name: hidden\n", "---"]}, {"cell_type": "markdown", "id": "a0507a4b", "metadata": {}, "source": ["# Classify Text into Labels\n", "\n", "Tagging means labeling a document with classes such as:\n", "\n", "- sentiment\n", "- language\n", "- style (formal, informal etc.)\n", "- covered topics\n", "- political tendency\n", "\n", "![Image description](../../static/img/tagging.png)\n", "\n", "## Overview\n", "\n", "Tagging has a few components:\n", "\n", "* `function`: Like [extraction](/docs/tutorials/extraction), tagging uses [functions](https://openai.com/blog/function-calling-and-other-api-updates) to specify how the model should tag a document\n", "* `schema`: defines how we want to tag the document\n", "\n", "## Quickstart\n", "\n", "Let's see a very straightforward example of how we can use tool calling for tagging in LangChain. We'll use the `.withStructuredOutput()`, supported on [selected chat models](/docs/integrations/chat/).\n", "\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "6a4371c8-b1cd-4a61-a474-819204f2a1b1", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from '@langchain/openai';\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "markdown", "id": "b8ca3f93", "metadata": {}, "source": ["Let's specify a [Zod](https://zod.dev) schema with a few properties and their expected type in our schema."]}, {"cell_type": "code", "execution_count": 2, "id": "39f3ce3e", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { z } from \"zod\";\n", "\n", "const taggingPrompt = ChatPromptTemplate.fromTemplate(\n", "    `Extract the desired information from the following passage.\n", "\n", "Only extract the properties mentioned in the 'Classification' function.\n", "\n", "Passage:\n", "{input}\n", "`\n", ");\n", "\n", "const classificationSchema = z.object({\n", "    sentiment: z.string().describe(\"The sentiment of the text\"),\n", "    aggressiveness: z.number().int().describe(\n", "        \"How aggressive the text is on a scale from 1 to 10\"\n", "    ),\n", "    language: z.string().describe(\"The language the text is written in\"),\n", "});\n", "\n", "// Name is optional, but gives the models more clues as to what your schema represents\n", "const llmWihStructuredOutput = llm.withStructuredOutput(classificationSchema, { name: \"extractor\" })"]}, {"cell_type": "code", "execution_count": 3, "id": "5509b6a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ sentiment: 'positive', aggressiveness: 1, language: 'Spanish' }\n"]}], "source": ["const prompt1 = await taggingPrompt.invoke({\n", "  input: \"Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!\"\n", "})\n", "await llmWihStructuredOutput.invoke(prompt1)"]}, {"cell_type": "markdown", "id": "d921bb53", "metadata": {}, "source": ["As we can see in the example, it correctly interprets what we want.\n", "\n", "The results vary so that we may get, for example, sentiments in different languages ('positive', 'enojado' etc.).\n", "\n", "We will see how to control these results in the next section."]}, {"cell_type": "markdown", "id": "bebb2f83", "metadata": {}, "source": ["## Finer control\n", "\n", "Careful schema definition gives us more control over the model's output. \n", "\n", "Specifically, we can define:\n", "\n", "- possible values for each property\n", "- description to make sure that the model understands the property\n", "- required properties to be returned"]}, {"cell_type": "markdown", "id": "69ef0b9a", "metadata": {}, "source": ["Let's redeclare our Zod schema to control for each of the previously mentioned aspects using enums:"]}, {"cell_type": "code", "execution_count": 4, "id": "6a5f7961", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "\n", "const classificationSchema2 = z.object({\n", "    sentiment: z.enum([\"happy\", \"neutral\", \"sad\"]).describe(\"The sentiment of the text\"),\n", "    aggressiveness: z.number().int().describe(\n", "        \"describes how aggressive the statement is on a scale from 1 to 5. The higher the number the more aggressive\"\n", "    ),\n", "    language: z.enum([\"spanish\", \"english\", \"french\", \"german\", \"italian\"]).describe(\"The language the text is written in\"),\n", "});"]}, {"cell_type": "code", "execution_count": 5, "id": "e5a5881f", "metadata": {}, "outputs": [], "source": ["const taggingPrompt2 = ChatPromptTemplate.fromTemplate(\n", "`Extract the desired information from the following passage.\n", "\n", "Only extract the properties mentioned in the 'Classification' function.\n", "\n", "Passage:\n", "{input}\n", "`\n", ")\n", "\n", "const llmWithStructuredOutput2 = llm.withStructuredOutput(classificationSchema2, { name: \"extractor\" })"]}, {"cell_type": "markdown", "id": "5ded2332", "metadata": {}, "source": ["Now the answers will be restricted in a way we expect!"]}, {"cell_type": "code", "execution_count": 6, "id": "d9b9d53d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ sentiment: 'happy', aggressiveness: 1, language: 'spanish' }\n"]}], "source": ["const prompt2 = await taggingPrompt2.invoke({\n", "  input: \"Estoy increiblemente contento de haberte conocido! Creo que seremos muy buenos amigos!\"\n", "})\n", "await llmWithStructuredOutput2.invoke(prompt2)"]}, {"cell_type": "code", "execution_count": 7, "id": "1c12fa00", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ sentiment: 'sad', aggressiveness: 5, language: 'spanish' }\n"]}], "source": ["const prompt3 = await taggingPrompt2.invoke({\n", "  input: \"Estoy muy enojado con vos! Te voy a dar tu merecido!\"\n", "})\n", "await llmWithStructuredOutput2.invoke(prompt3)"]}, {"cell_type": "code", "execution_count": 8, "id": "0bdfcb05", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ sentiment: 'neutral', aggressiveness: 1, language: 'english' }\n"]}], "source": ["const prompt4 = await taggingPrompt2.invoke({\n", "  input: \"Weather is ok here, I can go outside without much more than a coat\"\n", "})\n", "await llmWithStructuredOutput2.invoke(prompt4)"]}, {"cell_type": "markdown", "id": "cf6b7389", "metadata": {}, "source": ["The [Lang<PERSON><PERSON> trace](https://smith.langchain.com/public/455f5404-8784-49ce-8851-0619b99e936f/r) lets us peek under the hood:\n", "\n", "![](../../static/img/classification_ls_trace.png)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}