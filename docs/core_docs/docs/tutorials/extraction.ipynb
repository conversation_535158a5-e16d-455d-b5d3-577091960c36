{"cells": [{"cell_type": "raw", "id": "df29b30a-fd27-4e08-8269-870df5631f9e", "metadata": {}, "source": ["---\n", "sidebar_position: 4\n", "---"]}, {"cell_type": "markdown", "id": "d28530a6-ddfd-49c0-85dc-b723551f6614", "metadata": {}, "source": ["# Build an Extraction Chain\n", "\n", ":::info Prerequisites\n", "\n", "This guide assumes familiarity with the following concepts:\n", "\n", "- [Chat Models](/docs/concepts/chat_models)\n", "- [Tools](/docs/concepts/tools)\n", "- [Tool calling](/docs/concepts/tool_calling)\n", "\n", ":::\n", "\n", "In this tutorial, we will build a chain to extract structured information from unstructured text. \n", "\n", ":::{.callout-important}\n", "This tutorial will only work with models that support **function/tool calling**\n", ":::"]}, {"cell_type": "markdown", "id": "4412def2-38e3-4bd0-bbf0-fb09ff9e5985", "metadata": {}, "source": ["## Setup\n", "\n", "### Installation\n", "\n", "To install <PERSON><PERSON><PERSON><PERSON> run:\n", "\n", "```{=mdx}\n", "import Npm2Yarn from '@theme/Npm2Yarn';\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core\n", "</Npm2Yarn>\n", "```\n", "\n", "For more details, see our [Installation guide](/docs/how_to/installation/).\n", "\n", "### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls.\n", "As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent.\n", "The best way to do this is with [Lang<PERSON><PERSON>](https://smith.langchain.com).\n", "\n", "After you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "```shell\n", "export LANGSMITH_TRACING=\"true\"\n", "export LANGSMITH_API_KEY=\"...\"\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# export LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "markdown", "id": "54d6b970-2ea3-4192-951e-21237212b359", "metadata": {}, "source": ["## The Schema\n", "\n", "First, we need to describe what information we want to extract from the text.\n", "\n", "We'll use [Zod](https://zod.dev) to define an example schema that extracts personal information.\n", "\n", "```{=mdx}\n", "<Npm2Yarn>\n", "  zod @langchain/core\n", "</Npm2Yarn>\n", "```"]}, {"cell_type": "code", "execution_count": 1, "id": "c141084c-fb94-4093-8d6a-81175d688e40", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "\n", "const personSchema = z.object({\n", "  name: z.optional(z.string()).describe(\"The name of the person\"),\n", "  hair_color: z.optional(z.string()).describe(\"The color of the person's hair if known\"),\n", "  height_in_meters: z.optional(z.string()).describe('Height measured in meters'),\n", "});"]}, {"cell_type": "markdown", "id": "f248dd54-e36d-435a-b154-394ab4ed6792", "metadata": {}, "source": ["There are two best practices when defining schema:\n", "\n", "1. Document the **attributes** and the **schema** itself: This information is sent to the LLM and is used to improve the quality of information extraction.\n", "2. Do not force the LLM to make up information! Above we used `.nullish()` for the attributes allowing the LLM to output `null` or `undefined` if it doesn't know the answer.\n", "\n", ":::{.callout-important}\n", "For best performance, document the schema well and make sure the model isn't force to return results if there's no information to be extracted in the text.\n", ":::\n", "\n", "## The Extractor\n", "\n", "Let's create an information extractor using the schema we defined above."]}, {"cell_type": "code", "execution_count": 2, "id": "a5e490f6-35ad-455e-8ae4-2bae021583ff", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "// Define a custom prompt to provide instructions and any additional context.\n", "// 1) You can add examples into the prompt template to improve extraction quality\n", "// 2) Introduce additional parameters to take context into account (e.g., include metadata\n", "//    about the document from which the text was extracted.)\n", "const promptTemplate = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\n", "      \"system\",\n", "      `You are an expert extraction algorithm.\n", "Only extract relevant information from the text.\n", "If you do not know the value of an attribute asked to extract,\n", "return null for the attribute's value.`,\n", "    ],\n", "    // Please see the how-to about improving performance with\n", "    // reference examples.\n", "    // [\"placeholder\", \"{examples}\"],\n", "    [\"human\", \"{text}\"],\n", "  ],\n", ");"]}, {"cell_type": "markdown", "id": "832bf6a1-8e0c-4b6a-aa37-12fe9c42a6d9", "metadata": {}, "source": ["We need to use a model that supports function/tool calling.\n", "\n", "Please review [the documentation](/docs/integrations/chat) for list of some models that can be used with this API.\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 4, "id": "70a13071-d49a-41a7-aaf5-f1ccda15e78c", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatAnthropic } from \"@langchain/anthropic\";\n", "\n", "const llm = new ChatAnthropic({\n", "  model: \"claude-3-sonnet-20240229\",\n", "  temperature: 0\n", "})"]}, {"cell_type": "markdown", "id": "86af8767-7fe4-4cb1-b218-591848070088", "metadata": {}, "source": ["We enable structured output by creating a new object with the `.withStructuredOutput` method:"]}, {"cell_type": "code", "execution_count": 5, "id": "97f70111-627b-4a08-a3e0-5bac3fae7c53", "metadata": {}, "outputs": [], "source": ["const structured_llm = llm.withStructuredOutput(personSchema)"]}, {"cell_type": "markdown", "id": "62819b5f-b6f5-4fa5-a73e-13b506e60e70", "metadata": {}, "source": ["We can then invoke it normally:"]}, {"cell_type": "code", "execution_count": 6, "id": "04d846a6-d5cb-4009-ac19-61e3aac0177e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ name: \u001b[32m'<PERSON>'\u001b[39m, hair_color: \u001b[32m'blond'\u001b[39m, height_in_meters: \u001b[32m'1.83'\u001b[39m }\n"]}], "source": ["const prompt = await promptTemplate.invoke({\n", "  text: \"<PERSON> is 6 feet tall and has blond hair.\"\n", "})\n", "await structured_llm.invoke(prompt)"]}, {"cell_type": "markdown", "id": "bd1c493d-f9dc-4236-8da9-50f6919f5710", "metadata": {}, "source": [":::{.callout-important} \n", "\n", "Extraction is Generative 🤯\n", "\n", "LLMs are generative models, so they can do some pretty cool things like correctly extract the height of the person in meters\n", "even though it was provided in feet!\n", ":::\n", "\n", "We can see the <PERSON><PERSON><PERSON> trace [here](https://smith.langchain.com/public/3d44b7e8-e7ca-4e02-951d-3290ccc89d64/r).\n", "\n", "Even though we defined our schema with the variable name `personSchema`, <PERSON><PERSON> is unable to infer this name and therefore does not pass it along to the model. To help give the LLM more clues as to what your provided schema represents, you can also give the schema you pass to `withStructuredOutput()` a name:"]}, {"cell_type": "code", "execution_count": 7, "id": "02f44203", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ name: \u001b[32m'<PERSON>'\u001b[39m, hair_color: \u001b[32m'blond'\u001b[39m, height_in_meters: \u001b[32m'1.83'\u001b[39m }\n"]}], "source": ["const structured_llm2 = llm.withStructuredOutput(personSchema, { name: \"person\" })\n", "\n", "const prompt2 = await promptTemplate.invoke({\n", "  text: \"<PERSON> is 6 feet tall and has blond hair.\"\n", "})\n", "await structured_llm2.invoke(prompt2)"]}, {"cell_type": "markdown", "id": "bfe7d31e", "metadata": {}, "source": ["This can improve performance in many cases."]}, {"cell_type": "markdown", "id": "28c5ef0c-b8d1-4e12-bd0e-e2528de87fcc", "metadata": {}, "source": ["## Multiple Entities\n", "\n", "In **most cases**, you should be extracting a list of entities rather than a single entity.\n", "\n", "This can be easily achieved using Zod by nesting models inside one another."]}, {"cell_type": "code", "execution_count": 8, "id": "591a0c16-7a17-4883-91ee-0d6d2fdb265c", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "\n", "const person = z.object({\n", "  name: z.optional(z.string()).describe('The name of the person'),\n", "  hair_color: z.optional(z.string()).describe(\"The color of the person's hair if known\"),\n", "  height_in_meters: z.number().nullish().describe('Height measured in meters'),\n", "});\n", "  \n", "const dataSchema = z.object({\n", "  people: z.array(person).describe('Extracted data about people'),\n", "});"]}, {"cell_type": "markdown", "id": "5f5cda33-fd7b-481e-956a-703f45e40e1d", "metadata": {}, "source": [":::{.callout-important}\n", "Extraction might not be perfect here. Please continue to see how to use **Reference Examples** to improve the quality of extraction, and see the **guidelines** section!\n", ":::"]}, {"cell_type": "code", "execution_count": 9, "id": "cf7062cc-1d1d-4a37-9122-509d1b87f0a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  people: [\n", "    { name: \u001b[32m'<PERSON>'\u001b[39m, hair_color: \u001b[32m'black'\u001b[39m, height_in_meters: \u001b[33m1.83\u001b[39m },\n", "    { name: \u001b[32m'<PERSON>'\u001b[39m, hair_color: \u001b[32m'black'\u001b[39m, height_in_meters: \u001b[1mnull\u001b[22m }\n", "  ]\n", "}\n"]}], "source": ["const structured_llm3 = llm.withStructuredOutput(dataSchema)\n", "const prompt3 = await promptTemplate.invoke({\n", "  text: \"My name is <PERSON>, my hair is black and i am 6 feet tall. <PERSON> has the same color hair as me.\"\n", "})\n", "await structured_llm3.invoke(prompt3)"]}, {"cell_type": "markdown", "id": "fba1d770-bf4d-4de4-9e4f-7384872ef0dc", "metadata": {}, "source": [":::{.callout-tip}\n", "When the schema accommodates the extraction of **multiple entities**, it also allows the model to extract **no entities** if no relevant information\n", "is in the text by providing an empty list. \n", "\n", "This is usually a **good** thing! It allows specifying **required** attributes on an entity without necessarily forcing the model to detect this entity.\n", ":::\n", "\n", "We can see the <PERSON><PERSON><PERSON> trace [here](https://smith.langchain.com/public/272096ab-9ac5-43f9-aa00-3b8443477d17/r)"]}, {"cell_type": "markdown", "id": "f07a7455-7de6-4a6f-9772-0477ef65e3dc", "metadata": {}, "source": ["## Next steps\n", "\n", "Now that you understand the basics of extraction with <PERSON><PERSON><PERSON><PERSON>, you're ready to proceed to the rest of the how-to guides:\n", "\n", "- [Add Examples](/docs/how_to/extraction_examples): Learn how to use **reference examples** to improve performance.\n", "- [Handle Long Text](/docs/how_to/extraction_long_text): What should you do if the text does not fit into the context window of the LLM?\n", "- [Use a Parsing Approach](/docs/how_to/extraction_parse): Use a prompt based approach to extract with models that do not support **tool/function calling**."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}