{"cells": [{"cell_type": "raw", "id": "63ee3f93", "metadata": {}, "source": ["---\n", "sidebar_position: 0\n", "---"]}, {"cell_type": "markdown", "id": "9316da0d", "metadata": {}, "source": ["# Build a simple LLM application with chat models and prompt templates\n", "\n", "In this quickstart we'll show you how to build a simple LLM application with LangChain. This application will translate text from English into another language. This is a relatively simple LLM application - it's just a single LLM call plus some prompting. Still, this is a great way to get started with LangChain - a lot of features can be built with just some prompting and an LLM call!\n", "\n", "After reading this tutorial, you'll have a high level overview of:\n", "\n", "- Using [language models](/docs/concepts/chat_models)\n", "\n", "- Using [prompt templates](/docs/concepts/prompt_templates)\n", "\n", "- Debugging and tracing your application using [LangSmith](https://docs.smith.langchain.com/)\n", "\n", "Let's dive in!\n", "\n", "## Setup\n", "\n", "### Installation\n", "\n", "To install <PERSON><PERSON><PERSON><PERSON> run:\n", "\n", "```{=mdx}\n", "import Npm2Yarn from '@theme/Npm2Yarn';\n", "import TabItem from '@theme/TabItem';\n", "import CodeBlock from \"@theme/CodeBlock\";\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core\n", "</Npm2Yarn>\n", "```\n", "\n", "\n", "For more details, see our [Installation guide](/docs/how_to/installation/).\n", "\n", "### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls.\n", "As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent.\n", "The best way to do this is with [Lang<PERSON><PERSON>](https://smith.langchain.com).\n", "\n", "After you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "```shell\n", "export LANGSMITH_TRACING=\"true\"\n", "export LANGSMITH_API_KEY=\"...\"\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# export LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "markdown", "id": "e5558ca9", "metadata": {}, "source": ["## Using Language Models\n", "\n", "First up, let's learn how to use a language model by itself. LangChain supports many different language models that you can use interchangeably. For details on getting started with a specific model, refer to [supported integrations](/docs/integrations/chat/).\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs openaiParams={`{ model: \"gpt-4\" }`} />\n", "```"]}, {"cell_type": "code", "execution_count": 2, "id": "337d298c-ac1f-4470-a7db-4931b9bb0f8d", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from '@langchain/openai';\n", "\n", "const model = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "markdown", "id": "ca5642ff", "metadata": {}, "source": ["Let's first use the model directly. [ChatModels](/docs/concepts/chat_models) are instances of Lang<PERSON><PERSON><PERSON> [Runnables](/docs/concepts/runnables/), which means they expose a standard interface for interacting with them. To simply call the model, we can pass in a list of [messages](/docs/concepts/messages/) to the `.invoke` method."]}, {"cell_type": "code", "execution_count": 3, "id": "f0ba07c2-fac8-4f44-b70b-67a8598ac862", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AIMessage {\n", "  \"id\": \"chatcmpl-AekSfJkg3QIOsk42BH6Qom4Gt159j\",\n", "  \"content\": \"Ciao!\",\n", "  \"additional_kwargs\": {},\n", "  \"response_metadata\": {\n", "    \"tokenUsage\": {\n", "      \"promptTokens\": 20,\n", "      \"completionTokens\": 3,\n", "      \"totalTokens\": 23\n", "    },\n", "    \"finish_reason\": \"stop\",\n", "    \"usage\": {\n", "      \"prompt_tokens\": 20,\n", "      \"completion_tokens\": 3,\n", "      \"total_tokens\": 23,\n", "      \"prompt_tokens_details\": {\n", "        \"cached_tokens\": 0,\n", "        \"audio_tokens\": 0\n", "      },\n", "      \"completion_tokens_details\": {\n", "        \"reasoning_tokens\": 0,\n", "        \"audio_tokens\": 0,\n", "        \"accepted_prediction_tokens\": 0,\n", "        \"rejected_prediction_tokens\": 0\n", "      }\n", "    },\n", "    \"system_fingerprint\": \"fp_6fc10e10eb\"\n", "  },\n", "  \"tool_calls\": [],\n", "  \"invalid_tool_calls\": [],\n", "  \"usage_metadata\": {\n", "    \"output_tokens\": 3,\n", "    \"input_tokens\": 20,\n", "    \"total_tokens\": 23,\n", "    \"input_token_details\": {\n", "      \"audio\": 0,\n", "      \"cache_read\": 0\n", "    },\n", "    \"output_token_details\": {\n", "      \"audio\": 0,\n", "      \"reasoning\": 0\n", "    }\n", "  }\n", "}\n"]}], "source": ["import { HumanMessage, SystemMessage } from \"@langchain/core/messages\"\n", "\n", "const messages = [\n", "  new SystemMessage(\"Translate the following from English into Italian\"),\n", "  new HumanMessage(\"hi!\"),\n", "];\n", "\n", "await model.invoke(messages)"]}, {"cell_type": "markdown", "id": "f83373db", "metadata": {}, "source": ["```{=mdx}\n", ":::tip\n", "\n", "If we've enabled <PERSON><PERSON><PERSON>, we can see that this run is logged to LangSmith, and can see the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/45f1a650-38fb-41e1-9b61-becc0684f2ce/r). The LangSmith trace reports [token](/docs/concepts/tokens/) usage information, latency, [standard model parameters](/docs/concepts/chat_models/#standard-parameters) (such as temperature), and other information.\n", "\n", ":::\n", "```\n", "\n", "Note that ChatModels receive [message](/docs/concepts/messages/) objects as input and generate message objects as output. In addition to text content, message objects convey conversational [roles](/docs/concepts/messages/#role) and hold important data, such as [tool calls](/docs/concepts/tool_calling/) and token usage counts.\n", "\n", "LangChain also supports chat model inputs via strings or [OpenAI format](/docs/concepts/messages/#openai-format). The following are equivalent:\n", "\n", "```javascript\n", "await model.invoke(\"Hello\")\n", "\n", "await model.invoke([ {role: \"user\", content: \"Hello\" }])\n", "\n", "await model.invoke([new HumanMessage(\"hi!\")])\n", "```\n", "\n", "### Streaming\n", "\n", "Because chat models are [Runnables](/docs/concepts/runnables/), they expose a standard interface that includes async and streaming modes of invocation. This allows us to stream individual tokens from a chat model:"]}, {"cell_type": "code", "execution_count": 5, "id": "0caa31af-cdf1-4fc1-9a07-adb7a54dd6a4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|\n", "C|\n", "iao|\n", "!|\n", "|\n", "|\n"]}], "source": ["const stream = await model.stream(messages);\n", "\n", "const chunks = [];\n", "for await (const chunk of stream) {\n", "  chunks.push(chunk);\n", "  console.log(`${chunk.content}|`);\n", "}"]}, {"cell_type": "markdown", "id": "1ab8da31", "metadata": {}, "source": ["## Prompt Templates\n", "\n", "Right now we are passing a list of messages directly into the language model. Where does this list of messages come from? Usually, it is constructed from a combination of user input and application logic. This application logic usually takes the raw user input and transforms it into a list of messages ready to pass to the language model. Common transformations include adding a system message or formatting a template with the user input.\n", "\n", "[Prompt templates](/docs/concepts/prompt_templates/) are a concept in LangChain designed to assist with this transformation. They take in raw user input and return data (a prompt) that is ready to pass into a language model. \n", "\n", "Let's create a prompt template here. It will take in two user variables:\n", "\n", "- `language`: The language to translate text into\n", "- `text`: The text to translate"]}, {"cell_type": "code", "execution_count": 6, "id": "3e73cc20", "metadata": {}, "outputs": [], "source": ["import { ChatPromptTemplate } from \"@langchain/core/prompts\""]}, {"cell_type": "markdown", "id": "7e876c2a", "metadata": {}, "source": ["First, let's create a string that we will format to be the system message:"]}, {"cell_type": "code", "execution_count": 7, "id": "fd75ecde", "metadata": {}, "outputs": [], "source": ["const systemTemplate = \"Translate the following from English into {language}\""]}, {"cell_type": "markdown", "id": "fedf6f13", "metadata": {}, "source": ["Next, we can create the PromptTemplate. This will be a combination of the `systemTemplate` as well as a simpler template for where to put the text"]}, {"cell_type": "code", "execution_count": 8, "id": "88e566f3", "metadata": {}, "outputs": [], "source": ["const promptTemplate = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\"system\", systemTemplate],\n", "    [\"user\", \"{text}\"]\n", "  ]\n", ")"]}, {"cell_type": "markdown", "id": "d9711ba6", "metadata": {}, "source": ["Note that `ChatPromptTemplate` supports multiple [message roles](/docs/concepts/messages/#role) in a single template. We format the `language` parameter into the system message, and the user `text` into a user message.\n", "\n", "The input to this prompt template is a dictionary. We can play around with this prompt template by itself to see what it does by itself"]}, {"cell_type": "code", "execution_count": 9, "id": "f781b3cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatPromptValue {\n", "  lc_serializable: \u001b[33mtrue\u001b[39m,\n", "  lc_kwargs: {\n", "    messages: [\n", "      SystemMessage {\n", "        \"content\": \"Translate the following from English into italian\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {}\n", "      },\n", "      HumanMessage {\n", "        \"content\": \"hi!\",\n", "        \"additional_kwargs\": {},\n", "        \"response_metadata\": {}\n", "      }\n", "    ]\n", "  },\n", "  lc_namespace: [ \u001b[32m'langchain_core'\u001b[39m, \u001b[32m'prompt_values'\u001b[39m ],\n", "  messages: [\n", "    SystemMessage {\n", "      \"content\": \"Translate the following from English into italian\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    },\n", "    HumanMessage {\n", "      \"content\": \"hi!\",\n", "      \"additional_kwargs\": {},\n", "      \"response_metadata\": {}\n", "    }\n", "  ]\n", "}\n"]}], "source": ["const promptValue = await promptTemplate.invoke({ language: \"italian\", text: \"hi!\" })\n", "\n", "promptValue"]}, {"cell_type": "markdown", "id": "1a49ba9e", "metadata": {}, "source": ["We can see that it returns a `ChatPromptValue` that consists of two messages. If we want to access the messages directly we do:"]}, {"cell_type": "code", "execution_count": 10, "id": "2159b619", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  SystemMessage {\n", "    \"content\": \"Translate the following from English into italian\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {}\n", "  },\n", "  HumanMessage {\n", "    \"content\": \"hi!\",\n", "    \"additional_kwargs\": {},\n", "    \"response_metadata\": {}\n", "  }\n", "]\n"]}], "source": ["promptValue.toChatMessages()"]}, {"cell_type": "markdown", "id": "5a4267a8", "metadata": {}, "source": ["Finally, we can invoke the chat model on the formatted prompt:"]}, {"cell_type": "code", "execution_count": 11, "id": "5f75bd30-c52c-45dc-ac4c-dcb4d81c99bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ciao!\n"]}], "source": ["const response = await model.invoke(promptValue)\n", "console.log(`${response.content}`)"]}, {"cell_type": "markdown", "id": "0b19cecb", "metadata": {}, "source": ["If we take a look at the <PERSON><PERSON><PERSON> trace, we can see all three components show up in the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/6529d912-8564-4686-8df8-999c427621a7/r)."]}, {"cell_type": "markdown", "id": "befdb168", "metadata": {}, "source": ["## Conclusion\n", "\n", "That's it! In this tutorial you've learned how to create your first simple LLM application. You've learned how to work with language models, how to create a prompt template, and how to get great observability into applications you create with LangSmith.\n", "\n", "This just scratches the surface of what you will want to learn to become a proficient AI Engineer. Luckily - we've got a lot of other resources!\n", "\n", "For further reading on the core concepts of <PERSON><PERSON><PERSON><PERSON>, we've got detailed [Conceptual Guides](/docs/concepts).\n", "\n", "If you have more specific questions on these concepts, check out the following sections of the how-to guides:\n", "\n", "- [Chat models](/docs/how_to/#chat-models)\n", "- [Prompt templates](/docs/how_to/#prompt-templates)\n", "\n", "And the Lang<PERSON>mith docs:\n", "\n", "- [<PERSON><PERSON><PERSON>](https://docs.smith.langchain.com)"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}