{"cells": [{"cell_type": "raw", "id": "023635f2-71cf-43f2-a2e2-a7b4ced30a74", "metadata": {}, "source": ["---\n", "sidebar_position: 2\n", "---"]}, {"cell_type": "markdown", "id": "86fc5bb2-017f-434e-8cd6-53ab214a5604", "metadata": {}, "source": ["# Build a Retrieval Augmented Generation (RAG) App: Part 2\n", "\n", "In many Q&A applications we want to allow the user to have a back-and-forth conversation, meaning the application needs some sort of \"memory\" of past questions and answers, and some logic for incorporating those into its current thinking.\n", "\n", "This is a the second part of a multi-part tutorial:\n", "\n", "- [Part 1](/docs/tutorials/rag) introduces RAG and walks through a minimal implementation.\n", "- [Part 2](/docs/tutorials/qa_chat_history) (this guide) extends the implementation to accommodate conversation-style interactions and multi-step retrieval processes.\n", "\n", "Here we focus on **adding logic for incorporating historical messages.** This involves the management of a [chat history](/docs/concepts/chat_history).\n", "\n", "We will cover two approaches:\n", "\n", "1. [Chains](/docs/tutorials/qa_chat_history/#chains), in which we execute at most one retrieval step;\n", "2. [Agents](/docs/tutorials/qa_chat_history/#agents), in which we give an LLM discretion to execute multiple retrieval steps.\n", "\n", "```{=mdx}\n", ":::note\n", "\n", "The methods presented here leverage [tool-calling](/docs/concepts/tool_calling/) capabilities in modern [chat models](/docs/concepts/chat_models). See [this page](/docs/integrations/chat/) for a table of models supporting tool calling features.\n", "\n", ":::\n", "```\n", "\n", "For the external knowledge source, we will use the same [LLM Powered Autonomous Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post by <PERSON><PERSON> from the [Part 1](/docs/tutorials/rag) of the RAG tutorial."]}, {"cell_type": "markdown", "id": "487d8d79-5ee9-4aa4-9fdf-cd5f4303e099", "metadata": {}, "source": ["## Setup\n", "\n", "### Components\n", "\n", "We will need to select three components from Lang<PERSON>hai<PERSON>'s suite of integrations.\n", "\n", "A [chat model](/docs/integrations/chat/):\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 3, "id": "fab0dd56-7437-4aeb-af20-7f420d47ca94", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from '@langchain/openai';\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "markdown", "id": "da14773e-ac98-4a97-944b-4c6ec028d195", "metadata": {}, "source": ["An [embedding model](/docs/integrations/text_embedding/):\n", "\n", "```{=mdx}\n", "import EmbeddingTabs from \"@theme/EmbeddingTabs\";\n", "\n", "<EmbeddingTabs/>\n", "```"]}, {"cell_type": "code", "execution_count": 4, "id": "4691bd31-d8f4-4ba1-aec5-44935400f33c", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "const embeddings = new OpenAIEmbeddings({model: \"text-embedding-3-large\"});"]}, {"cell_type": "markdown", "id": "22fdc314-b91d-4820-b0a8-873b5b6e76f5", "metadata": {}, "source": ["And a [vector store](/docs/integrations/vectorstores/):\n", "\n", "```{=mdx}\n", "import VectorStoreTabs from \"@theme/VectorStoreTabs\";\n", "\n", "<VectorStoreTabs/>\n", "```"]}, {"cell_type": "code", "execution_count": 5, "id": "137d3848-7265-4673-9779-4c5f604da469", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const vectorStore = new MemoryVectorStore(embeddings);"]}, {"cell_type": "markdown", "id": "94bc335b-dc8a-4c40-aece-3aa9057cc6bd", "metadata": {}, "source": ["### Dependencies\n", "\n", "In addition, we'll use the following packages:\n", "\n", "```{=mdx}\n", "import Npm2Yarn from '@theme/Npm2Yarn';\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/community @langchain/langgraph cheerio\n", "</Npm2Yarn>\n", "```"]}, {"attachments": {}, "cell_type": "markdown", "id": "e207ac1d-4a8e-4172-a9ee-3294519a9a40", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls. As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent. The best way to do this is with [LangSmith](https://docs.smith.langchain.com).\n", "\n", "Note that Lang<PERSON>mith is not needed, but it is helpful. If you do want to use LangSmith, after you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "\n", "```bash\n", "export LANGSMITH_TRACING=true\n", "export LANGSMITH_API_KEY=YOUR_KEY\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# export LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "markdown", "id": "fa6ba684-26cf-4860-904e-a4d51380c134", "metadata": {}, "source": ["## Chains\n", "\n", "Let's first revisit the vector store we built in [Part 1](/docs/tutorials/rag), which indexes an [LLM Powered Autonomous Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post by <PERSON><PERSON>."]}, {"cell_type": "code", "execution_count": 6, "id": "ffe06d69-33c9-4ca3-98fb-8c70cde9dba2", "metadata": {}, "outputs": [], "source": ["import \"cheerio\";\n", "import { RecursiveCharacterTextSplitter } from \"@langchain/textsplitters\";\n", "import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "\n", "// Load and chunk contents of the blog\n", "const pTagSelector = \"p\";\n", "const cheerioLoader = new CheerioWebBaseLoader(\n", "  \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "  {\n", "    selector: pTagSelector\n", "  }\n", ");\n", "\n", "const docs = await cheerioLoader.load();\n", "\n", "const splitter = new RecursiveCharacterTextSplitter({\n", "  chunkSize: 1000, chunkOverlap: 200\n", "});\n", "const allSplits = await splitter.splitDocuments(docs);"]}, {"cell_type": "code", "execution_count": 7, "id": "b4369949-39f1-4cdc-b652-179e0b891b51", "metadata": {}, "outputs": [], "source": ["// Index chunks\n", "await vectorStore.addDocuments(allSplits)"]}, {"cell_type": "markdown", "id": "42c26d5f-1493-4ad6-9210-ea2723695149", "metadata": {}, "source": ["In the [Part 1](/docs/tutorials/rag) of the RAG tutorial, we represented the user input, retrieved context, and generated answer as separate keys in the state. Conversational experiences can be naturally represented using a sequence of [messages](/docs/concepts/messages/). In addition to messages from the user and assistant, retrieved documents and other artifacts can be incorporated into a message sequence via [tool messages](/docs/concepts/messages/#toolmessage). This motivates us to represent the state of our RAG application using a sequence of messages. Specifically, we will have\n", "\n", "1. User input as a `HumanMessage`;\n", "2. Vector store query as an `AIMessage` with tool calls;\n", "3. Retrieved documents as a `ToolMessage`;\n", "4. Final response as a `AIMessage`.\n", "\n", "This model for state is so versatile that LangGraph offers a built-in version for convenience:\n", "```javascript\n", "import { MessagesAnnotation, StateGraph } from \"@langchain/langgraph\";\n", "\n", "const graph = new StateGraph(MessagesAnnotation)\n", "```"]}, {"cell_type": "markdown", "id": "35eeb6a1-29f2-4086-8b6f-8761cf24ce59", "metadata": {}, "source": ["Leveraging [tool-calling](/docs/concepts/tool_calling/) to interact with a retrieval step has another benefit, which is that the query for the retrieval is generated by our model. This is especially important in a conversational setting, where user queries may require contextualization based on the chat history. For instance, consider the following exchange:\n", "\n", "> Human: \"What is Task Decomposition?\"\n", ">\n", "> AI: \"Task decomposition involves breaking down complex tasks into smaller and simpler steps to make them more manageable for an agent or model.\"\n", ">\n", "> Human: \"What are common ways of doing it?\"\n", "\n", "In this scenario, a model could generate a query such as `\"common approaches to task decomposition\"`. Tool-calling facilitates this naturally. As in the [query analysis](/docs/tutorials/rag#query-analysis) section of the RAG tutorial, this allows a model to rewrite user queries into more effective search queries. It also provides support for direct responses that do not involve a retrieval step (e.g., in response to a generic greeting from the user).\n", "\n", "Let's turn our retrieval step into a [tool](/docs/concepts/tools):"]}, {"cell_type": "code", "execution_count": 8, "id": "2f3a87fd-018d-448f-bb63-570cf590d6ac", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "import { tool } from \"@langchain/core/tools\";\n", "\n", "const retrieveSchema = z.object({query: z.string()});\n", "\n", "const retrieve = tool(\n", "  async ({ query }) => {\n", "    const retrievedDocs = await vectorStore.similaritySearch(query, 2);\n", "    const serialized = retrievedDocs.map(\n", "      doc => `Source: ${doc.metadata.source}\\nContent: ${doc.pageContent}`\n", "    ).join(\"\\n\");\n", "    return [\n", "      serialized,\n", "      retrievedDocs,\n", "    ];\n", "  },\n", "  {\n", "    name: \"retrieve\",\n", "    description: \"Retrieve information related to a query.\",\n", "    schema: retrieveSchema,\n", "    responseFormat: \"content_and_artifact\",\n", "  }\n", ");"]}, {"cell_type": "markdown", "id": "9b03f752-d46d-4070-b790-197b742c4dc2", "metadata": {}, "source": ["See [this guide](/docs/how_to/custom_tools/) for more detail on creating tools.\n", "\n", "Our graph will consist of three nodes:\n", "\n", "1. A node that fields the user input, either generating a query for the retriever or responding directly;\n", "2. A node for the retriever tool that executes the retrieval step;\n", "3. A node that generates the final response using the retrieved context.\n", "\n", "We build them below. Note that we leverage another pre-built LangGraph component, [ToolNode](https://langchain-ai.github.io/langgraph/reference/prebuilt/#langgraph.prebuilt.tool_node.ToolNode), that executes the tool and adds the result as a `ToolMessage` to the state."]}, {"cell_type": "code", "execution_count": 9, "id": "ca2d1da6-ee39-4823-a9f1-be95b1e8a1fa", "metadata": {}, "outputs": [], "source": ["import { \n", "    AIMessage,\n", "    HumanMessage,\n", "    SystemMessage,\n", "    ToolMessage\n", "} from \"@langchain/core/messages\";\n", "import { MessagesAnnotation } from \"@langchain/langgraph\";\n", "import { ToolNode } from \"@langchain/langgraph/prebuilt\";\n", "\n", "\n", "// Step 1: Generate an AIMessage that may include a tool-call to be sent.\n", "async function queryOrRespond(state: typeof MessagesAnnotation.State) {\n", "  const llmWithTools = llm.bindTools([retrieve])\n", "  const response = await llmWithTools.invoke(state.messages);\n", "  // MessagesState appends messages to state instead of overwriting\n", "  return { messages: [response] };\n", "}\n", "\n", "\n", "// Step 2: Execute the retrieval.\n", "const tools = new ToolNode([retrieve]);\n", "\n", "\n", "// Step 3: Generate a response using the retrieved content.\n", "async function generate(state: typeof MessagesAnnotation.State) {\n", "  // Get generated ToolMessages\n", "  let recentToolMessages = [];\n", "    for (let i = state[\"messages\"].length - 1; i >= 0; i--) {\n", "      let message = state[\"messages\"][i];\n", "      if (message instanceof ToolMessage) {\n", "        recentToolMessages.push(message);\n", "      } else {\n", "        break;\n", "      }\n", "    }\n", "  let toolMessages = recentToolMessages.reverse();\n", "  \n", "  // Format into prompt\n", "  const docsContent = toolMessages.map(doc => doc.content).join(\"\\n\");\n", "  const systemMessageContent = \n", "    \"You are an assistant for question-answering tasks. \" +\n", "    \"Use the following pieces of retrieved context to answer \" +\n", "    \"the question. If you don't know the answer, say that you \" +\n", "    \"don't know. Use three sentences maximum and keep the \" +\n", "    \"answer concise.\" +\n", "    \"\\n\\n\" +\n", "    `${docsContent}`;\n", "\n", "  const conversationMessages = state.messages.filter(message => \n", "    message instanceof HumanMessage || \n", "    message instanceof SystemMessage || \n", "    (message instanceof AIMessage && message.tool_calls.length == 0)\n", "  );\n", "  const prompt = [new SystemMessage(systemMessageContent), ...conversationMessages];\n", "\n", "  // Run\n", "  const response = await llm.invoke(prompt)\n", "  return { messages: [response] };\n", "}"]}, {"cell_type": "markdown", "id": "b409ee5f-2973-47ee-a1bf-112731843c5d", "metadata": {}, "source": ["Finally, we compile our application into a single `graph` object. In this case, we are just connecting the steps into a sequence. We also allow the first `query_or_respond` step to \"short-circuit\" and respond directly to the user if it does not generate a tool call. This allows our application to support conversational experiences-- e.g., responding to generic greetings that may not require a retrieval step"]}, {"cell_type": "code", "execution_count": 10, "id": "60ed5dd0-8636-4a12-961b-b8355b579862", "metadata": {}, "outputs": [], "source": ["import { StateGraph } from \"@langchain/langgraph\";\n", "import { toolsCondition } from \"@langchain/langgraph/prebuilt\";\n", "\n", "\n", "const graphBuilder = new StateGraph(MessagesAnnotation)\n", "  .addNode(\"queryOrRespond\", queryOrRespond)\n", "  .addNode(\"tools\", tools)\n", "  .addNode(\"generate\", generate)\n", "  .addEdge(\"__start__\", \"queryOrRespond\")\n", "  .addConditionalEdges(\n", "    \"queryOrRespond\",\n", "    toolsCondition,\n", "    {__end__: \"__end__\", tools: \"tools\"}\n", "  )\n", "  .addEdge(\"tools\", \"generate\")\n", "  .addEdge(\"generate\", \"__end__\")\n", "\n", "const graph = graphBuilder.compile();"]}, {"cell_type": "markdown", "id": "0a0389e0-c454-4693-b6e2-979326015574", "metadata": {}, "source": ["```javascript\n", "// Note: tslab only works inside a jupyter notebook. Don't worry about running this code yourself!\n", "import * as tslab from \"tslab\";\n", "\n", "const image = await graph.getGraph().drawMermaidPng();\n", "const arrayBuffer = await image.arrayBuffer();\n", "\n", "await tslab.display.png(new Uint8Array(arrayBuffer));\n", "```\n", "\n", "![graph_img_rag_part_2](../../static/img/graph_img_rag_part_2.png)"]}, {"cell_type": "markdown", "id": "3aee3fb6-c7a2-44a5-96f0-2951631caaf2", "metadata": {}, "source": ["Let's test our application.\n", "\n", "```{=mdx}\n", "<details>\n", "<summary>Expand for `prettyPrint` code.</summary>\n", "```"]}, {"cell_type": "code", "execution_count": 11, "id": "cb29afc4-899b-4f11-89bf-092368d21bec", "metadata": {}, "outputs": [], "source": ["import { BaseMessage, isAIMessage } from \"@langchain/core/messages\";\n", "\n", "const prettyPrint = (message: BaseMessage) => {\n", "  let txt = `[${message._getType()}]: ${message.content}`;\n", "  if (\n", "    (isAIMessage(message) && message.tool_calls?.length) ||\n", "    0 > 0\n", "  ) {\n", "    const tool_calls = (message as AIMessage)?.tool_calls\n", "      ?.map((tc) => `- ${tc.name}(${JSON.stringify(tc.args)})`)\n", "      .join(\"\\n\");\n", "    txt += ` \\nTools: \\n${tool_calls}`;\n", "  }\n", "  console.log(txt);\n", "};"]}, {"cell_type": "markdown", "id": "a82d163c-80a7-4551-88f7-25643f8d98b9", "metadata": {}, "source": ["```{=mdx}\n", "</details>\n", "```"]}, {"cell_type": "markdown", "id": "38c08d53-3f18-467b-83d0-07fb0b4d41be", "metadata": {}, "source": ["Note that it responds appropriately to messages that do not require an additional retrieval step:"]}, {"cell_type": "code", "execution_count": 12, "id": "16304ac2-50de-4646-b31b-01ea523d1d5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[human]: Hello\n", "-----\n", "\n", "[ai]: Hello! How can I assist you today?\n", "-----\n", "\n"]}], "source": ["let inputs1 = { messages: [{ role: \"user\", content: \"Hello\" }] };\n", "\n", "for await (\n", "  const step of await graph.stream(inputs1, {\n", "    streamMode: \"values\",\n", "  })\n", ") {\n", "    const lastMessage = step.messages[step.messages.length - 1];\n", "    <PERSON><PERSON><PERSON><PERSON>(lastMessage);\n", "    console.log(\"-----\\n\");\n", "}"]}, {"cell_type": "markdown", "id": "5df5046d-4610-4ffa-9f30-d04453da05a9", "metadata": {}, "source": ["And when executing a search, we can stream the steps to observe the query generation, retrieval, and answer generation:"]}, {"cell_type": "code", "execution_count": 13, "id": "4989fb9b-4867-482f-99b7-380a18c54504", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[human]: What is Task Decomposition?\n", "-----\n", "\n", "[ai]:  \n", "Tools: \n", "- retrieve({\"query\":\"Task Decomposition\"})\n", "-----\n", "\n", "[tool]: Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.Another quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain\n", "Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: System message:Think step by step and reason yourself to the right decisions to make sure we get it right.\n", "You will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.Then you will output the content of each file including ALL code.\n", "Each file must strictly follow a markdown code block format, where the following tokens must be replaced such that\n", "FILENAME is the lowercase file name including the file extension,\n", "LANG is the markup code block language for the code’s language, and CODE is the code:FILENAMEYou will start with the “entrypoint” file, then go to the ones that are imported by that file, and so on.\n", "Please note that the code should be fully functional. No placeholders.Follow a language and framework appropriate best practice file naming convention.\n", "Make sure that files contain all imports, types etc. Make sure that code in different files are compatible with each other.\n", "-----\n", "\n", "[ai]: Task decomposition is the process of breaking down a complex task into smaller, more manageable steps or subgoals. This can be achieved through various methods, such as using prompts for large language models (LLMs), task-specific instructions, or human inputs. It helps in simplifying the problem-solving process and enhances understanding of the task at hand.\n", "-----\n", "\n"]}], "source": ["let inputs2 = { messages: [{ role: \"user\", content: \"What is Task Decomposition?\" }] };\n", "\n", "for await (\n", "  const step of await graph.stream(inputs2, {\n", "    streamMode: \"values\",\n", "  })\n", ") {\n", "    const lastMessage = step.messages[step.messages.length - 1];\n", "    <PERSON><PERSON><PERSON><PERSON>(lastMessage);\n", "    console.log(\"-----\\n\");\n", "}"]}, {"cell_type": "markdown", "id": "a26a9c4d-0e5b-4db9-8b78-68624d829ac2", "metadata": {}, "source": ["Check out the <PERSON><PERSON><PERSON> trace [here](https://smith.langchain.com/public/c6ed4e16-b9ed-46cc-912e-6a580d3c47ed/r)."]}, {"cell_type": "markdown", "id": "c2300c04-019c-4c65-a104-3dbf17c924b7", "metadata": {}, "source": ["### Stateful management of chat history\n", "\n", "```{=mdx}\n", ":::note\n", "\n", "This section of the tutorial previously used the [RunnableWithMessageHistory](https://api.js.langchain.com/classes/_langchain_core.runnables.RunnableWithMessageHistory.html) abstraction. You can access that version of the documentation in the [v0.2 docs](https://js.langchain.com/v0.2/docs/tutorials/qa_chat_history).\n", "\n", "As of the v0.3 release of LangChain, we recommend that LangChain users take advantage of [LangGraph persistence](https://langchain-ai.github.io/langgraphjs/concepts/persistence/) to incorporate `memory` into new LangChain applications.\n", "\n", "If your code is already relying on `RunnableWithMessageHistory` or `BaseChatMessageHistory`, you do **not** need to make any changes. We do not plan on deprecating this functionality in the near future as it works for simple chat applications and any code that uses `RunnableWithMessageHistory` will continue to work as expected.\n", "\n", "Please see [How to migrate to LangGraph Memory](/docs/versions/migrating_memory/) for more details.\n", ":::\n", "```\n", "\n", "In production, the Q&A application will usually persist the chat history into a database, and be able to read and update it appropriately.\n", "\n", "[LangGraph](https://langchain-ai.github.io/langgraphjs/) implements a built-in [persistence layer](https://langchain-ai.github.io/langgraphjs/concepts/persistence/), making it ideal for chat applications that support multiple conversational turns.\n", "\n", "To manage multiple conversational turns and threads, all we have to do is specify a [checkpointer](https://langchain-ai.github.io/langgraphjs/concepts/persistence/) when compiling our application. Because the nodes in our graph are appending messages to the state, we will retain a consistent chat history across invocations.\n", "\n", "LangGraph comes with a simple in-memory checkpointer, which we use below. See its [documentation](https://langchain-ai.github.io/langgraphjs/concepts/persistence/) for more detail, including how to use different persistence backends (e.g., SQLite or Postgres).\n", "\n", "For a detailed walkthrough of how to manage message history, head to the [How to add message history (memory)](/docs/how_to/message_history) guide."]}, {"cell_type": "code", "execution_count": 14, "id": "471c416b-f9b9-4ada-8e59-b4b47abd5d13", "metadata": {}, "outputs": [], "source": ["import { MemorySaver } from \"@langchain/langgraph\";\n", "\n", "const checkpointer = new MemorySaver();\n", "const graphWithMemory = graphBuilder.compile({ checkpointer });\n", "\n", "// Specify an ID for the thread\n", "const threadConfig = {\n", "    configurable: { thread_id: \"abc123\" },\n", "    streamMode: \"values\" as const\n", "};"]}, {"cell_type": "markdown", "id": "f557b169-b33c-42d0-b97e-1b948d0a2914", "metadata": {}, "source": ["We can now invoke similar to before:"]}, {"cell_type": "code", "execution_count": 15, "id": "8f6daf90-6069-44e9-a656-603fea0829f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[human]: What is Task Decomposition?\n", "-----\n", "\n", "[ai]:  \n", "Tools: \n", "- retrieve({\"query\":\"Task Decomposition\"})\n", "-----\n", "\n", "[tool]: Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.Another quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain\n", "Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: System message:Think step by step and reason yourself to the right decisions to make sure we get it right.\n", "You will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.Then you will output the content of each file including ALL code.\n", "Each file must strictly follow a markdown code block format, where the following tokens must be replaced such that\n", "FILENAME is the lowercase file name including the file extension,\n", "LANG is the markup code block language for the code’s language, and CODE is the code:FILENAMEYou will start with the “entrypoint” file, then go to the ones that are imported by that file, and so on.\n", "Please note that the code should be fully functional. No placeholders.Follow a language and framework appropriate best practice file naming convention.\n", "Make sure that files contain all imports, types etc. Make sure that code in different files are compatible with each other.\n", "-----\n", "\n", "[ai]: Task decomposition is the process of breaking down a complex task into smaller, more manageable steps or subgoals. This can be achieved through various methods, such as using prompts for large language models (LLMs), task-specific instructions, or human inputs. It helps in simplifying the problem-solving process and enhances understanding of the task at hand.\n", "-----\n", "\n"]}], "source": ["let inputs3 = { messages: [{ role: \"user\", content: \"What is Task Decomposition?\" }] };\n", "\n", "for await (\n", "  const step of await graphWithMemory.stream(inputs3, threadConfig)\n", ") {\n", "    const lastMessage = step.messages[step.messages.length - 1];\n", "    <PERSON><PERSON><PERSON><PERSON>(lastMessage);\n", "    console.log(\"-----\\n\");\n", "}"]}, {"cell_type": "code", "execution_count": 16, "id": "a87d33af-a954-49e7-bccf-f6dab3ce2411", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[human]: Can you look up some common ways of doing it?\n", "-----\n", "\n", "[ai]:  \n", "Tools: \n", "- retrieve({\"query\":\"common methods of task decomposition\"})\n", "-----\n", "\n", "[tool]: Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.Another quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain\n", "Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: be provided by other developers (as in Plugins) or self-defined (as in function calls).HuggingGPT (<PERSON> et al. 2023) is a framework to use ChatGPT as the task planner to select models available in HuggingFace platform according to the model descriptions and summarize the response based on the execution results.The system comprises of 4 stages:(1) Task planning: LLM works as the brain and parses the user requests into multiple tasks. There are four attributes associated with each task: task type, ID, dependencies, and arguments. They use few-shot examples to guide LLM to do task parsing and planning.Instruction:(2) Model selection: LLM distributes the tasks to expert models, where the request is framed as a multiple-choice question. LLM is presented with a list of models to choose from. Due to the limited context length, task type based filtration is needed.Instruction:(3) Task execution: Expert models execute on the specific tasks and log results.Instruction:(4) Response generation:\n", "-----\n", "\n", "[ai]: Common ways of task decomposition include using large language models (LLMs) with simple prompts like \"Steps for XYZ\" or \"What are the subgoals for achieving XYZ?\", employing task-specific instructions (e.g., \"Write a story outline\"), and incorporating human inputs. Additionally, methods like the Tree of Thoughts approach explore multiple reasoning possibilities at each step, creating a structured tree of thoughts. These techniques facilitate breaking down tasks into manageable components for better execution.\n", "-----\n", "\n"]}], "source": ["let inputs4 = { messages: [{ role: \"user\", content: \"Can you look up some common ways of doing it?\" }] };\n", "\n", "for await (\n", "  const step of await graphWithMemory.stream(inputs4, threadConfig)\n", ") {\n", "    const lastMessage = step.messages[step.messages.length - 1];\n", "    <PERSON><PERSON><PERSON><PERSON>(lastMessage);\n", "    console.log(\"-----\\n\");\n", "}"]}, {"cell_type": "markdown", "id": "4bbbeef2-d9a1-4857-874f-9f3b5cc4eca9", "metadata": {}, "source": ["Note that the query generated by the model in the second question incorporates the conversational context.\n", "\n", "The [LangSmith](https://smith.langchain.com/public/c8b2c1ba-8c8b-47ab-b298-3502e0688711/r) trace is particularly informative here, as we can see exactly what messages are visible to our chat model at each step."]}, {"cell_type": "markdown", "id": "0ad23c71-3c99-4d9d-b494-9b7a08a557c0", "metadata": {}, "source": ["## Agents\n", "\n", "[Agents](/docs/concepts/agents) leverage the reasoning capabilities of LLMs to make decisions during execution. Using agents allows you to offload additional discretion over the retrieval process. Although their behavior is less predictable than the above \"chain\", they are able to execute multiple retrieval steps in service of a query, or iterate on a single search.\n", "\n", "Below we assemble a minimal RAG agent. Using LangGraph's [pre-built ReAct agent constructor](https://langchain-ai.github.io/langgraph/how-tos/#langgraph.prebuilt.chat_agent_executor.create_react_agent), we can do this in one line.\n", "\n", "```{=mdx}\n", ":::tip\n", "\n", "Check out LangGraph's [Agentic RAG](https://langchain-ai.github.io/langgraphjs/tutorials/rag/langgraph_agentic_rag/) tutorial for more advanced formulations.\n", "\n", ":::\n", "```"]}, {"cell_type": "code", "execution_count": 17, "id": "6d519b4c-29e3-4e14-a5f8-178bd25a1728", "metadata": {}, "outputs": [], "source": ["import { createReactAgent } from \"@langchain/langgraph/prebuilt\";\n", "\n", "const agent = createReactAgent({ llm: llm, tools: [retrieve] });"]}, {"cell_type": "markdown", "id": "7d8f8734-5dcf-4058-a532-11c8a7d0efae", "metadata": {}, "source": ["Let's inspect the graph:"]}, {"cell_type": "markdown", "id": "00cbb651-db1c-405d-905d-6e200c7169a7", "metadata": {}, "source": ["```javascript\n", "// Note: tslab only works inside a jupyter notebook. Don't worry about running this code yourself!\n", "import * as tslab from \"tslab\";\n", "\n", "const image = await agent.getGraph().drawMermaidPng();\n", "const arrayBuffer = await image.arrayBuffer();\n", "\n", "await tslab.display.png(new Uint8Array(arrayBuffer));\n", "```\n", "\n", "![graph_img_react](../../static/img/graph_img_react.png)"]}, {"cell_type": "markdown", "id": "28623a52-7906-440f-8aaf-d6bb5ecbad98", "metadata": {}, "source": ["The key difference from our earlier implementation is that instead of a final generation step that ends the run, here the tool invocation loops back to the original LLM call. The model can then either answer the question using the retrieved context, or generate another tool call to obtain more information.\n", "\n", "Let's test this out. We construct a question that would typically require an iterative sequence of retrieval steps to answer:"]}, {"cell_type": "code", "execution_count": 18, "id": "85cd4d73-fb7b-4447-b20a-b9d24969ffc2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[human]: What is the standard method for Task Decomposition?\n", "Once you get the answer, look up common extensions of that method.\n", "-----\n", "\n", "[ai]:  \n", "Tools: \n", "- retrieve({\"query\":\"standard method for Task Decomposition\"})\n", "-----\n", "\n", "[tool]: Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.Another quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain\n", "Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: System message:Think step by step and reason yourself to the right decisions to make sure we get it right.\n", "You will first lay out the names of the core classes, functions, methods that will be necessary, as well as a quick comment on their purpose.Then you will output the content of each file including ALL code.\n", "Each file must strictly follow a markdown code block format, where the following tokens must be replaced such that\n", "FILENAME is the lowercase file name including the file extension,\n", "LANG is the markup code block language for the code’s language, and CODE is the code:FILENAMEYou will start with the “entrypoint” file, then go to the ones that are imported by that file, and so on.\n", "Please note that the code should be fully functional. No placeholders.Follow a language and framework appropriate best practice file naming convention.\n", "Make sure that files contain all imports, types etc. Make sure that code in different files are compatible with each other.\n", "-----\n", "\n", "[ai]:  \n", "Tools: \n", "- retrieve({\"query\":\"common extensions of Task Decomposition method\"})\n", "-----\n", "\n", "[tool]: Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.Another quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain\n", "Source: https://lilianweng.github.io/posts/2023-06-23-agent/\n", "Content: be provided by other developers (as in Plugins) or self-defined (as in function calls).HuggingGPT (<PERSON> et al. 2023) is a framework to use ChatGPT as the task planner to select models available in HuggingFace platform according to the model descriptions and summarize the response based on the execution results.The system comprises of 4 stages:(1) Task planning: LLM works as the brain and parses the user requests into multiple tasks. There are four attributes associated with each task: task type, ID, dependencies, and arguments. They use few-shot examples to guide LLM to do task parsing and planning.Instruction:(2) Model selection: LLM distributes the tasks to expert models, where the request is framed as a multiple-choice question. LLM is presented with a list of models to choose from. Due to the limited context length, task type based filtration is needed.Instruction:(3) Task execution: Expert models execute on the specific tasks and log results.Instruction:(4) Response generation:\n", "-----\n", "\n", "[ai]: ### Standard Method for Task Decomposition\n", "\n", "The standard method for task decomposition involves breaking down hard tasks into smaller, more manageable steps. This can be achieved through various approaches:\n", "\n", "1. **Chain of Thought (CoT)**: This method transforms large tasks into multiple manageable tasks, providing insight into the model's reasoning process.\n", "2. **Prompting**: Using simple prompts like \"Steps for XYZ\" or \"What are the subgoals for achieving XYZ?\" to guide the decomposition.\n", "3. **Task-Specific Instructions**: Providing specific instructions tailored to the task, such as \"Write a story outline\" for writing a novel.\n", "4. **Human Inputs**: Involving human input to assist in the decomposition process.\n", "\n", "### Common Extensions of Task Decomposition\n", "\n", "Several extensions have been developed to enhance the task decomposition process:\n", "\n", "1. **Tree of Thoughts (ToT)**: This method extends CoT by exploring multiple reasoning possibilities at each step. It decomposes the problem into multiple thought steps and generates various thoughts per step, creating a tree structure. The search process can utilize either breadth-first search (BFS) or depth-first search (DFS), with each state evaluated by a classifier or through majority voting.\n", "\n", "2. **LLM+P**: This approach involves using an external classical planner for long-horizon planning, integrating planning domains to enhance the decomposition process.\n", "\n", "3. **HuggingGPT**: This framework utilizes ChatGPT as a task planner to select models from the HuggingFace platform based on model descriptions. It consists of four stages:\n", "   - **Task Planning**: Parsing user requests into multiple tasks with attributes like task type, ID, dependencies, and arguments.\n", "   - **Model Selection**: Distributing tasks to expert models based on a multiple-choice question format.\n", "   - **Task Execution**: Expert models execute specific tasks and log results.\n", "   - **Response Generation**: Compiling the results into a coherent response.\n", "\n", "These extensions aim to improve the efficiency and effectiveness of task decomposition, making it easier to manage complex tasks.\n", "-----\n", "\n"]}], "source": ["let inputMessage = `What is the standard method for Task Decomposition?\n", "Once you get the answer, look up common extensions of that method.`\n", "\n", "let inputs5 = { messages: [{ role: \"user\", content: inputMessage }] };\n", "\n", "for await (\n", "  const step of await agent.stream(inputs5, {\n", "    streamMode: \"values\",\n", "  })\n", ") {\n", "    const lastMessage = step.messages[step.messages.length - 1];\n", "    <PERSON><PERSON><PERSON><PERSON>(lastMessage);\n", "    console.log(\"-----\\n\");\n", "}"]}, {"cell_type": "markdown", "id": "47ab58d2-92ef-4940-a535-7c8808e75523", "metadata": {}, "source": ["Note that the agent:\n", "\n", "1. Generates a query to search for a standard method for task decomposition;\n", "2. Receiving the answer, generates a second query to search for common extensions of it;\n", "3. Having received all necessary context, answers the question.\n", "\n", "We can see the full sequence of steps, along with latency and other metadata, in the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/67b7642b-78d0-482a-bb49-fe08674bf972/r).\n", "\n", "## Next steps\n", "\n", "We've covered the steps to build a basic conversational Q&A application:\n", "\n", "- We used chains to build a predictable application that generates at most one query per user input;\n", "- We used agents to build an application that can iterate on a sequence of queries.\n", "\n", "To explore different types of retrievers and retrieval strategies, visit the [retrievers](/docs/how_to/#retrievers) section of the how-to guides.\n", "\n", "For a detailed walkthrough of <PERSON><PERSON><PERSON><PERSON>'s conversation memory abstractions, visit the [How to add message history (memory)](/docs/how_to/message_history) guide.\n", "\n", "To learn more about agents, check out the [conceptual guide](/docs/concepts/agents) and LangGraph [agent architectures](https://langchain-ai.github.io/langgraphjs/concepts/agentic_concepts/) page."]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}