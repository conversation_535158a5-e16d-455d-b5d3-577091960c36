{"cells": [{"cell_type": "markdown", "id": "5630b0ca", "metadata": {}, "source": ["# Build a Retrieval Augmented Generation (RAG) App: Part 1\n", "\n", "One of the most powerful applications enabled by LLMs is sophisticated question-answering (Q&A) chatbots. These are applications that can answer questions about specific source information. These applications use a technique known as Retrieval Augmented Generation, or [RAG](/docs/concepts/rag/).\n", "\n", "This is a multi-part tutorial:\n", "\n", "- [Part 1](/docs/tutorials/rag) (this guide) introduces RAG and walks through a minimal implementation.\n", "- [Part 2](/docs/tutorials/qa_chat_history) extends the implementation to accommodate conversation-style interactions and multi-step retrieval processes.\n", "\n", "This tutorial will show how to build a simple Q&A application\n", "over a text data source. Along the way we’ll go over a typical Q&A\n", "architecture and highlight additional resources for more advanced Q&A techniques. We’ll also see\n", "how LangSmith can help us trace and understand our application.\n", "LangSmith will become increasingly helpful as our application grows in\n", "complexity.\n", "\n", "If you're already familiar with basic retrieval, you might also be interested in\n", "this [high-level overview of different retrieval techniques](/docs/concepts/retrieval).\n", "\n", "**Note**: Here we focus on Q&A for unstructured data. If you are interested for RAG over structured data, check out our tutorial on doing [question/answering over SQL data](/docs/tutorials/sql_qa).\n", "\n", "## Overview\n", "A typical RAG application has two main components:\n", "\n", "**Indexing**: a pipeline for ingesting data from a source and indexing it. *This usually happens offline.*\n", "\n", "**Retrieval and generation**: the actual RAG chain, which takes the user query at run time and retrieves the relevant data from the index, then passes that to the model.\n", "\n", "Note: the indexing portion of this tutorial will largely follow the [semantic search tutorial](/docs/tutorials/retrievers).\n", "\n", "The most common full sequence from raw data to answer looks like:\n", "\n", "### Indexing\n", "1. **Load**: First we need to load our data. This is done with [Document Loaders](/docs/concepts/document_loaders).\n", "2. **Split**: [Text splitters](/docs/concepts/text_splitters) break large `Documents` into smaller chunks. This is useful both for indexing data and passing it into a model, as large chunks are harder to search over and won't fit in a model's finite context window.\n", "3. **Store**: We need somewhere to store and index our splits, so that they can be searched over later. This is often done using a [VectorStore](/docs/concepts/vectorstores) and [Embeddings](/docs/concepts/embedding_models) model.\n", "\n", "![index_diagram](../../static/img/rag_indexing.png)\n", "\n", "### Retrieval and generation\n", "4. **Retrieve**: Given a user input, relevant splits are retrieved from storage using a [Retriever](/docs/concepts/retrievers).\n", "5. **Generate**: A [ChatModel](/docs/concepts/chat_models) / [LLM](/docs/concepts/text_llms) produces an answer using a prompt that includes both the question with the retrieved data\n", "\n", "![retrieval_diagram](../../static/img/rag_retrieval_generation.png)\n", "\n", "Once we've indexed our data, we will use [LangGraph](https://langchain-ai.github.io/langgraphjs/) as our orchestration framework to implement the retrieval and generation steps.\n", "\n", "## Setup\n", "\n", "### Jupyter Notebook\n", "\n", "This and other tutorials are perhaps most conveniently run in a [Jupy<PERSON> notebooks](https://jupyter.org/). Going through guides in an interactive environment is a great way to better understand them. See [here](https://jupyter.org/install) for instructions on how to install.\n", "\n", "### Installation\n", "\n", "This guide requires the following dependencies:\n", "\n", "```{=mdx}\n", "import Npm2Yarn from '@theme/Npm2Yarn';\n", "\n", "<Npm2Yarn>\n", "  langchain @langchain/core @langchain/langgraph\n", "</Npm2Yarn>\n", "```\n", "\n", "For more details, see our [Installation guide](/docs/how_to/installation).\n", "\n", "### <PERSON><PERSON><PERSON>\n", "\n", "Many of the applications you build with LangChain will contain multiple steps with multiple invocations of LLM calls.\n", "As these applications get more and more complex, it becomes crucial to be able to inspect what exactly is going on inside your chain or agent.\n", "The best way to do this is with [Lang<PERSON><PERSON>](https://smith.langchain.com).\n", "\n", "After you sign up at the link above, make sure to set your environment variables to start logging traces:\n", "\n", "```shell\n", "export LANGSMITH_TRACING=\"true\"\n", "export LANGSMITH_API_KEY=\"...\"\n", "\n", "# Reduce tracing latency if you are not in a serverless environment\n", "# export LANGCHAIN_CALLBACKS_BACKGROUND=true\n", "```"]}, {"cell_type": "markdown", "id": "9ff1b425", "metadata": {}, "source": ["## Components\n", "\n", "We will need to select three components from Lang<PERSON>hai<PERSON>'s suite of integrations.\n", "\n", "A [chat model](/docs/integrations/chat/):\n", "\n", "```{=mdx}\n", "import ChatModelTabs from \"@theme/ChatModelTabs\";\n", "\n", "<ChatModelTabs customVarName=\"llm\" />\n", "```"]}, {"cell_type": "code", "execution_count": 2, "id": "26ef9d35", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { ChatOpenAI } from '@langchain/openai';\n", "\n", "const llm = new ChatOpenAI({\n", "  model: \"gpt-4o-mini\",\n", "  temperature: 0,\n", "})"]}, {"cell_type": "markdown", "id": "f1b78672-f21e-4827-843e-59514d18ca20", "metadata": {}, "source": ["An [embedding model](/docs/integrations/text_embedding/):\n", "\n", "```{=mdx}\n", "import EmbeddingTabs from \"@theme/EmbeddingTabs\";\n", "\n", "<EmbeddingTabs/>\n", "```"]}, {"cell_type": "code", "execution_count": 3, "id": "a199c764-5dfd-45cf-a4d4-731f2c3d474f", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { OpenAIEmbeddings } from \"@langchain/openai\";\n", "\n", "const embeddings = new OpenAIEmbeddings({model: \"text-embedding-3-large\"});"]}, {"cell_type": "markdown", "id": "859ffca8-055e-4f5a-95fe-55906ed1d63f", "metadata": {}, "source": ["And a [vector store](/docs/integrations/vectorstores/):\n", "\n", "```{=mdx}\n", "import VectorStoreTabs from \"@theme/VectorStoreTabs\";\n", "\n", "<VectorStoreTabs/>\n", "```"]}, {"cell_type": "code", "execution_count": 4, "id": "f4db6b46-ea3f-4994-9d54-d7c84beb50cc", "metadata": {}, "outputs": [], "source": ["// @lc-docs-hide-cell\n", "import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const vectorStore = new MemoryVectorStore(embeddings);"]}, {"cell_type": "markdown", "id": "93b2d316-922c-4318-b72d-486fd6813b94", "metadata": {}, "source": ["## Preview\n", "\n", "In this guide we’ll build an app that answers questions about the website's content. The specific website we will use is the [LLM Powered Autonomous\n", "Agents](https://lilianweng.github.io/posts/2023-06-23-agent/) blog post\n", "by <PERSON><PERSON>, which allows us to ask questions about the contents of\n", "the post.\n", "\n", "We can create a simple indexing pipeline and RAG chain to do this in ~50\n", "lines of code.\n", "\n", "```javascript\n", "import \"cheerio\";\n", "import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "import { Document } from \"@langchain/core/documents\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "import { pull } from \"langchain/hub\";\n", "import { Annotation, StateGraph } from \"@langchain/langgraph\";\n", "import { RecursiveCharacterTextSplitter } from \"@langchain/textsplitters\";\n", "\n", "\n", "// Load and chunk contents of blog\n", "const pTagSelector = \"p\";\n", "const cheerioLoader = new CheerioWebBaseLoader(\n", "  \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "  {\n", "    selector: pTagSelector\n", "  }\n", ");\n", "\n", "const docs = await cheerioLoader.load();\n", "\n", "const splitter = new RecursiveCharacterTextSplitter({\n", "  chunkSize: 1000, chunkOverlap: 200\n", "});\n", "const allSplits = await splitter.splitDocuments(docs);\n", "\n", "\n", "// Index chunks\n", "await vectorStore.addDocuments(allSplits)\n", "\n", "// Define prompt for question-answering\n", "const promptTemplate = await pull<ChatPromptTemplate>(\"rlm/rag-prompt\");\n", "\n", "// Define state for application\n", "const InputStateAnnotation = Annotation.Root({\n", "  question: Annotation<string>,\n", "});\n", "\n", "const StateAnnotation = Annotation.Root({\n", "  question: Annotation<string>,\n", "  context: Annotation<Document[]>,\n", "  answer: Annotation<string>,\n", "});\n", "\n", "// Define application steps\n", "const retrieve = async (state: typeof InputStateAnnotation.State) => {\n", "  const retrievedDocs = await vectorStore.similaritySearch(state.question)\n", "  return { context: retrievedDocs };\n", "};\n", "\n", "\n", "const generate = async (state: typeof StateAnnotation.State) => {\n", "  const docsContent = state.context.map(doc => doc.pageContent).join(\"\\n\");\n", "  const messages = await promptTemplate.invoke({ question: state.question, context: docsContent });\n", "  const response = await llm.invoke(messages);\n", "  return { answer: response.content };\n", "};\n", "\n", "\n", "// Compile application and test\n", "const graph = new StateGraph(StateAnnotation)\n", "  .addNode(\"retrieve\", retrieve)\n", "  .addNode(\"generate\", generate)\n", "  .addEdge(\"__start__\", \"retrieve\")\n", "  .addEdge(\"retrieve\", \"generate\")\n", "  .addEdge(\"generate\", \"__end__\")\n", "  .compile();\n", "```\n", "\n", "```javascript\n", "let inputs = { question: \"What is Task Decomposition?\" };\n", "\n", "const result = await graph.invoke(inputs);\n", "console.log(result.answer)\n", "```\n", "\n", "```\n", "Task decomposition is the process of breaking down complex tasks into smaller, more manageable steps. This can be achieved through various methods, including prompting large language models (LLMs) or using task-specific instructions. Techniques like Chain of Thought (CoT) and Tree of Thoughts further enhance this process by structuring reasoning and exploring multiple possibilities at each step.\n", "```"]}, {"cell_type": "markdown", "id": "9ff8204b-dabc-4790-80ea-50d4cf4fceb0", "metadata": {}, "source": ["Check out the [Lang<PERSON>mith\n", "trace](https://smith.langchain.com/public/84a36239-b466-41bd-ac84-befc33ab50df/r)."]}, {"cell_type": "markdown", "id": "efa9ea6a-f914-4f50-8e35-52e6c34b8001", "metadata": {}, "source": ["## Detailed walkthrough\n", "\n", "Let’s go through the above code step-by-step to really understand what’s\n", "going on.\n", "\n", "## 1. Indexing {#indexing}\n", "\n", "```{=mdx}\n", ":::note\n", "\n", "This section is an abbreviated version of the content in the [semantic search tutorial](/docs/tutorials/retrievers).\n", "If you're comfortable with [document loaders](/docs/concepts/document_loaders), [embeddings](/docs/concepts/embedding_models), and [vector stores](/docs/concepts/vectorstores),\n", "feel free to skip to the next section on [retrieval and generation](/docs/tutorials/rag/#orchestration).\n", "\n", ":::\n", "```\n", "\n", "### Loading documents\n", "\n", "We need to first load the blog post contents. We can use [DocumentLoaders](/docs/concepts/document_loaders) for this, which are objects that load in data from a source and return a list of [Documents](https://api.js.langchain.com/classes/langchain_core.documents.Document.html). A Document is an object with some pageContent (`string`) and metadata (`Record<string, any>`).\n", "\n", "In this case we’ll use the [CheerioWebBaseLoader](https://api.js.langchain.com/classes/langchain.document_loaders_web_cheerio.CheerioWebBaseLoader.html), which uses cheerio to load HTML form web URLs and parse it to text. We can pass custom selectors to the constructor to only parse specific elements:"]}, {"cell_type": "code", "execution_count": 5, "id": "38d1a436-c60e-4709-877d-f5767d8e4eed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total characters: 22360\n"]}], "source": ["import \"cheerio\";\n", "import { CheerioWebBaseLoader } from \"@langchain/community/document_loaders/web/cheerio\";\n", "\n", "const pTagSelector = \"p\";\n", "const cheerioLoader = new CheerioWebBaseLoader(\n", "  \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "  {\n", "    selector: pTagSelector\n", "  }\n", ");\n", "\n", "const docs = await cheerioLoader.load();\n", "\n", "console.assert(docs.length === 1);\n", "console.log(`Total characters: ${docs[0].pageContent.length}`);"]}, {"cell_type": "code", "execution_count": 6, "id": "3305194a-5fde-45af-ab30-ff8c9a70b123", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Building agents with LLM (large language model) as its core controller is a cool concept. Several proof-of-concepts demos, such as AutoGPT, GPT-Engineer and BabyAGI, serve as inspiring examples. The potentiality of LLM extends beyond generating well-written copies, stories, essays and programs; it can be framed as a powerful general problem solver.In a LLM-powered autonomous agent system, LLM functions as the agent’s brain, complemented by several key components:A complicated task usually involv\n"]}], "source": ["console.log(docs[0].pageContent.slice(0, 500));"]}, {"cell_type": "markdown", "id": "e6f11795-e19f-4697-bc6e-6d477355a1cd", "metadata": {}, "source": ["#### Go deeper\n", "\n", "`DocumentLoader`: Class that loads data from a source as list of Documents.\n", "\n", "- [Docs](/docs/concepts/document_loaders): Detailed documentation on how to use\n", "- [Integrations](/docs/integrations/document_loaders/)\n", "- [Interface](https://api.js.langchain.com/classes/langchain.document_loaders_base.BaseDocumentLoader.html): API reference for the base interface.\n", "\n", "### Splitting documents\n", "\n", "Our loaded document is over 42k characters which is too long to fit\n", "into the context window of many models. Even for those models that could\n", "fit the full post in their context window, models can struggle to find\n", "information in very long inputs.\n", "\n", "To handle this we’ll split the `Document` into chunks for embedding and\n", "vector storage. This should help us retrieve only the most relevant parts\n", "of the blog post at run time.\n", "\n", "As in the [semantic search tutorial](/docs/tutorials/retrievers), we use a\n", "[RecursiveCharacterTextSplitter](/docs/how_to/recursive_text_splitter),\n", "which will recursively split the document using common separators like\n", "new lines until each chunk is the appropriate size. This is the\n", "recommended text splitter for generic text use cases."]}, {"cell_type": "code", "execution_count": 7, "id": "4870cbe1-9cf4-4ba2-a759-b2f3fcb4c677", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Split blog post into 29 sub-documents.\n"]}], "source": ["import { RecursiveCharacterTextSplitter } from \"@langchain/textsplitters\";\n", "\n", "const splitter = new RecursiveCharacterTextSplitter({\n", "  chunkSize: 1000, chunkOverlap: 200\n", "});\n", "const allSplits = await splitter.splitDocuments(docs);\n", "console.log(`Split blog post into ${allSplits.length} sub-documents.`);"]}, {"cell_type": "markdown", "id": "f5193e01-6cf1-45b9-9ba5-38caf75162a6", "metadata": {}, "source": ["#### Go deeper\n", "\n", "`TextSplitter`: Object that splits a list of `Document`s into smaller chunks. Subclass of `DocumentTransformers`.\n", "- Explore `Context-aware splitters`, which keep the location (“context”) of each split in the original `Document`: - [Markdown files](/docs/how_to/code_splitter/#markdown) - [Code](/docs/how_to/code_splitter/) (15+ langs) - [Interface](https://api.js.langchain.com/classes/langchain_textsplitters.TextSplitter.html): API reference for the base interface.\n", "\n", "`DocumentTransformer`: Object that performs a transformation on a list of `Document`s. - Docs: Detailed documentation on how to use `DocumentTransformer`s - [Integrations](/docs/integrations/document_transformers) - [Interface](https://api.js.langchain.com/classes/langchain_core.documents.BaseDocumentTransformer.html): API reference for the base interface.\n", "\n", "### Storing documents\n", "\n", "Now we need to index our 66 text chunks so that we can search over them\n", "at runtime. Following the [semantic search tutorial](/docs/tutorials/retrievers),\n", "our approach is to [embed](/docs/concepts/embedding_models/) the contents of each document split and insert these embeddings\n", "into a [vector store](/docs/concepts/vectorstores/). Given an input query, we can then use\n", "vector search to retrieve relevant documents.\n", "\n", "We can embed and store all of our document splits in a single command\n", "using the vector store and embeddings model selected at the [start of the tutorial](/docs/tutorials/rag/#components)."]}, {"cell_type": "code", "execution_count": 8, "id": "3c3ded19-9bcb-46fe-936c-a65a17499b31", "metadata": {}, "outputs": [], "source": ["await vectorStore.addDocuments(allSplits)"]}, {"cell_type": "markdown", "id": "57666234-a5b3-4abc-b079-755241bb2b98", "metadata": {}, "source": ["#### Go deeper\n", "\n", "`Embeddings`: Wrapper around a text embedding model, used for converting text to embeddings. - [Docs](/docs/concepts/embedding_models): Detailed documentation on how to use embeddings. - [Integrations](/docs/integrations/text_embedding): 30+ integrations to choose from. - [Interface](https://api.js.langchain.com/classes/langchain_core.embeddings.Embeddings.html): API reference for the base interface.\n", "\n", "`VectorStore`: Wrapper around a vector database, used for storing and querying embeddings. - [Docs](/docs/concepts/vectorstores): Detailed documentation on how to use vector stores. - [Integrations](/docs/integrations/vectorstores): 40+ integrations to choose from. - [Interface](https://api.js.langchain.com/classes/langchain_core.vectorstores.VectorStore.html): API reference for the base interface.\n", "\n", "This completes the **Indexing** portion of the pipeline. At this point we have a query-able vector store containing the chunked contents of our blog post. Given a user question, we should ideally be able to return the snippets of the blog post that answer the question."]}, {"cell_type": "markdown", "id": "72a8a4be-5214-412b-b807-efde1e201a8f", "metadata": {}, "source": ["## 2. Retrieval and Generation {#orchestration}\n", "\n", "Now let’s write the actual application logic. We want to create a simple\n", "application that takes a user question, searches for documents relevant\n", "to that question, passes the retrieved documents and initial question to\n", "a model, and returns an answer.\n", "\n", "For generation, we will use the chat model selected at the [start of the tutorial](/docs/tutorials/rag/#components).\n", "\n", "We’ll use a prompt for RAG that is checked into the LangChain prompt hub\n", "([here](https://smith.langchain.com/hub/rlm/rag-prompt))."]}, {"cell_type": "code", "execution_count": 9, "id": "6d833d29-4b51-4320-ad3b-ac6b5d5348de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\n", "Question: (question goes here) \n", "Context: (context goes here) \n", "Answer:\n"]}], "source": ["import { pull } from \"langchain/hub\";\n", "import { ChatPromptTemplate } from \"@langchain/core/prompts\";\n", "\n", "const promptTemplate = await pull<ChatPromptTemplate>(\"rlm/rag-prompt\");\n", "\n", "// Example:\n", "const example_prompt = await promptTemplate.invoke(\n", "    { context: \"(context goes here)\", question: \"(question goes here)\" }\n", ")\n", "const example_messages = example_prompt.messages\n", "\n", "console.assert(example_messages.length === 1);\n", "example_messages[0].content"]}, {"cell_type": "markdown", "id": "77dfe84d-cc19-4227-bee4-56b69508ab11", "metadata": {}, "source": ["We'll use [LangGraph](https://langchain-ai.github.io/langgraphjs/) to tie together the retrieval and generation steps into a single application. This will bring a number of benefits:\n", "\n", "- We can define our application logic once and automatically support multiple invocation modes, including streaming, async, and batched calls.\n", "- We get streamlined deployments via [LangGraph Platform](https://langchain-ai.github.io/langgraphjs/concepts/langgraph_platform/).\n", "- Lang<PERSON><PERSON> will automatically trace the steps of our application together.\n", "- We can easily add key features to our application, including [persistence](https://langchain-ai.github.io/langgraphjs/concepts/persistence/) and [human-in-the-loop approval](https://langchain-ai.github.io/langgraphjs/concepts/human_in_the_loop/), with minimal code changes.\n", "\n", "To use LangGraph, we need to define three things:\n", "\n", "1. The state of our application;\n", "2. The nodes of our application (i.e., application steps);\n", "3. The \"control flow\" of our application (e.g., the ordering of the steps).\n", "\n", "#### State:\n", "\n", "The [state](https://langchain-ai.github.io/langgraphjs/concepts/low_level/#state) of our application controls what data is input to the application, transferred between steps, and output by the application.\n", "\n", "For a simple RAG application, we can just keep track of the input question, retrieved context, and generated answer.\n", "\n", "Read more about defining graph states [here](https://langchain-ai.github.io/langgraphjs/how-tos/define-state/)."]}, {"cell_type": "code", "execution_count": 10, "id": "6b765f77-f820-485c-a8a2-af744dc7426b", "metadata": {}, "outputs": [], "source": ["import { Document } from \"@langchain/core/documents\";\n", "import { Annotation } from \"@langchain/langgraph\";\n", "\n", "\n", "const InputStateAnnotation = Annotation.Root({\n", "  question: Annotation<string>,\n", "});\n", "\n", "\n", "const StateAnnotation = Annotation.Root({\n", "  question: Annotation<string>,\n", "  context: Annotation<Document[]>,\n", "  answer: Annotation<string>,\n", "});"]}, {"cell_type": "markdown", "id": "77868d9a-892f-4b2c-b706-850f96b4464f", "metadata": {}, "source": ["#### Nodes (application steps)\n", "\n", "Let's start with a simple sequence of two steps: retrieval and generation."]}, {"cell_type": "code", "execution_count": 11, "id": "7cc48297-f9db-421b-b4b7-6d1bb9dc7931", "metadata": {}, "outputs": [], "source": ["import { concat } from \"@langchain/core/utils/stream\";\n", "\n", "const retrieve = async (state: typeof InputStateAnnotation.State) => {\n", "  const retrievedDocs = await vectorStore.similaritySearch(state.question)\n", "  return { context: retrievedDocs };\n", "};\n", "\n", "\n", "const generate = async (state: typeof StateAnnotation.State) => {\n", "  const docsContent = state.context.map(doc => doc.pageContent).join(\"\\n\");\n", "  const messages = await promptTemplate.invoke({ question: state.question, context: docsContent });\n", "  const response = await llm.invoke(messages);\n", "  return { answer: response.content };\n", "};"]}, {"cell_type": "markdown", "id": "d1ac9dc3-d73d-48c3-be05-4b60e0b8bc17", "metadata": {}, "source": ["Our retrieval step simply runs a similarity search using the input question, and the generation step formats the retrieved context and original question into a prompt for the chat model.\n", "\n", "#### Control flow\n", "\n", "Finally, we compile our application into a single `graph` object. In this case, we are just connecting the retrieval and generation steps into a single sequence."]}, {"cell_type": "code", "execution_count": 12, "id": "9da732c0-7d35-467e-b0d3-16ab73a47766", "metadata": {}, "outputs": [], "source": ["import { StateGraph } from \"@langchain/langgraph\";\n", "\n", "const graph = new StateGraph(StateAnnotation)\n", "  .addNode(\"retrieve\", retrieve)\n", "  .addNode(\"generate\", generate)\n", "  .addEdge(\"__start__\", \"retrieve\")\n", "  .addEdge(\"retrieve\", \"generate\")\n", "  .addEdge(\"generate\", \"__end__\")\n", "  .compile();"]}, {"cell_type": "markdown", "id": "187ef85e-0659-4a19-a153-40876f3aa452", "metadata": {}, "source": ["LangGraph also comes with built-in utilities for visualizing the control flow of your application:"]}, {"cell_type": "markdown", "id": "841cbf7f-b98a-45c8-944d-42730d6c6ba7", "metadata": {}, "source": ["```javascript\n", "// Note: tslab only works inside a jupyter notebook. Don't worry about running this code yourself!\n", "import * as tslab from \"tslab\";\n", "\n", "const image = await graph.getGraph().drawMermaidPng();\n", "const arrayBuffer = await image.arrayBuffer();\n", "\n", "await tslab.display.png(new Uint8Array(arrayBuffer));\n", "```\n", "\n", "![graph_img_rag](../../static/img/graph_img_rag.png)"]}, {"cell_type": "markdown", "id": "31f7dc4d-cac8-4be9-b44c-df097dc28c81", "metadata": {}, "source": ["```{=mdx}\n", "<details>\n", "<summary>Do I need to use LangGraph?</summary>\n", "```\n", "\n", "LangGraph is not required to build a RAG application. Indeed, we can implement the same application logic through invocations of the individual components:\n", "\n", "```javascript\n", "let question = \"...\"\n", "\n", "const retrievedDocs = await vectorStore.similaritySearch(question)\n", "const docsContent = retrievedDocs.map(doc => doc.pageContent).join(\"\\n\");\n", "const messages = await promptTemplate.invoke({ question: question, context: docsContent });\n", "const answer = await llm.invoke(messages);\n", "```\n", "\n", "The benefits of LangGraph include:\n", "\n", "- Support for multiple invocation modes: this logic would need to be rewritten if we wanted to stream output tokens, or stream the results of individual steps;\n", "- Automatic support for tracing via [LangSmith](https://docs.smith.langchain.com/) and deployments via [LangGraph Platform](https://langchain-ai.github.io/langgraphjs/concepts/langgraph_platform/);\n", "- Support for persistence, human-in-the-loop, and other features.\n", "\n", "Many use-cases demand RAG in a conversational experience, such that a user can receive context-informed answers via a stateful conversation. As we will see in [Part 2](/docs/tutorials/qa_chat_history) of the tutorial, LangGraph's management and persistence of state simplifies these applications enormously.\n", "\n", "```{=mdx}\n", "</details>\n", "```"]}, {"cell_type": "markdown", "id": "eee9c057-5a08-46a3-8c7d-6a314d1e777d", "metadata": {}, "source": ["#### Usage\n", "\n", "Let's test our application! LangGraph supports multiple invocation modes, including sync, async, and streaming.\n", "\n", "Invoke:"]}, {"cell_type": "code", "execution_count": 14, "id": "873a174b-3058-4a76-8387-997c2bfaa743", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[\n", "  Document {\n", "    pageContent: \u001b[32m'hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.Tree of Thoughts (<PERSON> et al. 2023) extends CoT by exploring multiple reasoning possibilities at each step. It first decomposes the problem into multiple thought steps and generates multiple thoughts per step, creating a tree structure. The search process can be BFS (breadth-first search) or DFS (depth-first search) with each state evaluated by a classifier (via a prompt) or majority vote.Task decomposition can be done (1) by LLM with simple prompting like \"Steps for XYZ.\\\\n1.\", \"What are the subgoals for achieving XYZ?\", (2) by using task-specific instructions; e.g. \"Write a story outline.\" for writing a novel, or (3) with human inputs.Another quite distinct approach, LLM+P (<PERSON> et al. 2023), involves relying on an external classical planner to do long-horizon planning. This approach utilizes the Planning Domain'\u001b[39m,\n", "    metadata: {\n", "      source: \u001b[32m'https://lilianweng.github.io/posts/2023-06-23-agent/'\u001b[39m,\n", "      loc: \u001b[36m[Object]\u001b[39m\n", "    },\n", "    id: \u001b[90mundefined\u001b[39m\n", "  },\n", "  Document {\n", "    pageContent: \u001b[32m'Building agents with LLM (large language model) as its core controller is a cool concept. Several proof-of-concepts demos, such as <PERSON>GPT, GPT-Engineer and BabyAGI, serve as inspiring examples. The potentiality of LLM extends beyond generating well-written copies, stories, essays and programs; it can be framed as a powerful general problem solver.In a LLM-powered autonomous agent system, LLM functions as the agent’s brain, complemented by several key components:A complicated task usually involves many steps. An agent needs to know what they are and plan ahead.Chain of thought (CoT; <PERSON> et al. 2022) has become a standard prompting technique for enhancing model performance on complex tasks. The model is instructed to “think step by step” to utilize more test-time computation to decompose hard tasks into smaller and simpler steps. CoT transforms big tasks into multiple manageable tasks and shed lights into an interpretation of the model’s thinking process.Tree of Thoughts (<PERSON> et al.'\u001b[39m,\n", "    metadata: {\n", "      source: \u001b[32m'https://lilianweng.github.io/posts/2023-06-23-agent/'\u001b[39m,\n", "      loc: \u001b[36m[Object]\u001b[39m\n", "    },\n", "    id: \u001b[90mundefined\u001b[39m\n", "  }\n", "]\n", "\n", "Answer: Task decomposition is the process of breaking down complex tasks into smaller, more manageable steps. This can be achieved through various methods, including prompting large language models (LLMs) to outline steps or using task-specific instructions. Techniques like Chain of Thought (CoT) and Tree of Thoughts further enhance this process by structuring reasoning and exploring multiple possibilities at each step.\n"]}], "source": ["let inputs = { question: \"What is Task Decomposition?\" };\n", "\n", "const result = await graph.invoke(inputs);\n", "console.log(result.context.slice(0, 2));\n", "console.log(`\\nAnswer: ${result[\"answer\"]}`);"]}, {"cell_type": "markdown", "id": "4ef88f30-40ca-476b-808d-794cb72d401f", "metadata": {}, "source": ["Stream steps:"]}, {"cell_type": "code", "execution_count": 15, "id": "5ba9b967-19b6-409a-9a7f-b96a36d0cef9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{ question: \u001b[32m'What is Task Decomposition?'\u001b[39m }\n", "\n", "====\n", "\n", "{\n", "  retrieve: { context: [ \u001b[36m[Document]\u001b[39m, \u001b[36m[Document]\u001b[39m, \u001b[36m[Document]\u001b[39m, \u001b[36m[Document]\u001b[39m ] }\n", "}\n", "\n", "====\n", "\n", "{\n", "  generate: {\n", "    answer: \u001b[32m'Task decomposition is the process of breaking down complex tasks into smaller, more manageable steps. This can be achieved through various methods, including prompting large language models (LLMs) or using task-specific instructions. Techniques like Chain of Thought (CoT) and Tree of Thoughts further enhance this process by structuring reasoning and exploring multiple possibilities at each step.'\u001b[39m\n", "  }\n", "}\n", "\n", "====\n", "\n"]}], "source": ["console.log(inputs)\n", "console.log(\"\\n====\\n\");\n", "for await (\n", "  const chunk of await graph.stream(inputs, {\n", "    streamMode: \"updates\",\n", "  })\n", ") {\n", "  console.log(chunk);\n", "  console.log(\"\\n====\\n\");\n", "}"]}, {"cell_type": "markdown", "id": "f860142d-d50b-4526-a03f-a59a763117fe", "metadata": {}, "source": ["Stream [tokens](/docs/concepts/tokens/) (requires `@langchain/core` >= 0.3.24 and `@langchain/langgraph` >= 0.2.34 with above implementation):"]}, {"cell_type": "code", "execution_count": 16, "id": "acb80ba0-d5d6-4425-9683-aaeab7081e6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|Task| decomposition| is| the| process| of| breaking| down| complex| tasks| into| smaller|,| more| manageable| steps|.| This| can| be| achieved| through| various| methods|,| including| prompting| large| language| models| (|LL|Ms|)| to| outline| steps| or| using| task|-specific| instructions|.| Techniques| like| Chain| of| Thought| (|Co|T|)| and| Tree| of| Thoughts| further| enhance| this| process| by| struct|uring| reasoning| and| exploring| multiple| possibilities| at| each| step|.||"]}], "source": ["const stream = await graph.stream(\n", "  inputs,\n", "  { streamMode: \"messages\" },\n", ");\n", "\n", "for await (const [message, _metadata] of stream) {\n", "  process.stdout.write(message.content + \"|\");\n", "}"]}, {"cell_type": "markdown", "id": "5aeb45ad-9bd5-4ee4-8356-9dca9ece76c5", "metadata": {}, "source": ["```{=mdx}\n", ":::note\n", "\n", "Streaming tokens with the current implementation, using `.invoke` in the `generate` step, requires `@langchain/core` >= 0.3.24 and `@langchain/langgraph` >= 0.2.34. See details [here](https://langchain-ai.github.io/langgraphjs/how-tos/stream-tokens/).\n", "\n", ":::\n", "```"]}, {"cell_type": "markdown", "id": "406534d4-66a3-4c27-b277-2bd2f5930cf5", "metadata": {}, "source": ["#### Returning sources\n", "\n", "Note that by storing the retrieved context in the state of the graph, we recover sources for the model's generated answer in the `\"context\"` field of the state. See [this guide](/docs/how_to/qa_sources/) on returning sources for more detail.\n", "\n", "#### Go deeper\n", "\n", "[Chat models](/docs/concepts/chat_models) take in a sequence of messages and return a message.\n", "\n", "- [Docs](/docs/how_to#chat-models)\n", "- [Integrations](/docs/integrations/chat/): 25+ integrations to choose from.\n", "\n", "**Customizing the prompt**\n", "\n", "As shown above, we can load prompts (e.g., [this RAG\n", "prompt](https://smith.langchain.com/hub/rlm/rag-prompt)) from the prompt\n", "hub. The prompt can also be easily customized. For example:"]}, {"cell_type": "code", "execution_count": 17, "id": "93d03690-c858-4891-8ecd-62d6e12a7929", "metadata": {}, "outputs": [], "source": ["const template = `Use the following pieces of context to answer the question at the end.\n", "If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "Use three sentences maximum and keep the answer as concise as possible.\n", "Always say \"thanks for asking!\" at the end of the answer.\n", "\n", "{context}\n", "\n", "Question: {question}\n", "\n", "Helpful Answer:`\n", "\n", "const promptTemplateCustom = ChatPromptTemplate.fromMessages(\n", "  [\n", "    [\"user\", template]\n", "  ]\n", ")"]}, {"cell_type": "markdown", "id": "217cf819-da76-4595-8f75-33f931f1f92a", "metadata": {}, "source": ["## Query analysis\n", "\n", "So far, we are executing the retrieval using the raw input query. However, there are some advantages to allowing a model to generate the query for retrieval purposes. For example:\n", "\n", "- In addition to semantic search, we can build in structured filters (e.g., \"Find documents since the year 2020.\");\n", "- The model can rewrite user queries, which may be multifaceted or include irrelevant language, into more effective search queries.\n", "\n", "[Query analysis](/docs/concepts/retrieval/#query-analysis) employs models to transform or construct optimized search queries from raw user input. We can easily incorporate a query analysis step into our application. For illustrative purposes, let's add some metadata to the documents in our vector store. We will add some (contrived) sections to the document which we can filter on later."]}, {"cell_type": "code", "execution_count": 17, "id": "ecf61ed5-6f2e-4b99-a43c-0590336ec2c3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  source: \u001b[32m'https://lilianweng.github.io/posts/2023-06-23-agent/'\u001b[39m,\n", "  loc: { lines: { from: \u001b[33m1\u001b[39m, to: \u001b[33m1\u001b[39m } },\n", "  section: \u001b[32m'beginning'\u001b[39m\n", "}\n"]}], "source": ["const totalDocuments = allSplits.length;\n", "const third = Math.floor(totalDocuments / 3);\n", "\n", "allSplits.forEach((document, i) => {\n", "    if (i < third) {\n", "        document.metadata[\"section\"] = \"beginning\";\n", "    } else if (i < 2 * third) {\n", "        document.metadata[\"section\"] = \"middle\";\n", "    } else {\n", "        document.metadata[\"section\"] = \"end\";\n", "    }\n", "});\n", "\n", "allSplits[0].metadata;"]}, {"cell_type": "markdown", "id": "114878bd-a334-41ed-8013-ec4ce0a9112b", "metadata": {}, "source": ["We will need to update the documents in our vector store. We will use a simple [MemoryVectorStore](https://api.js.langchain.com/classes/langchain.vectorstores_memory.MemoryVectorStore.html) for this, as we will use some of its specific features (i.e., metadata filtering). Refer to the vector store [integration documentation](/docs/integrations/vectorstores/) for relevant features of your chosen vector store."]}, {"cell_type": "code", "execution_count": 18, "id": "5daf62fd-4086-49ad-8b3a-514c4fa214ea", "metadata": {}, "outputs": [], "source": ["import { MemoryVectorStore } from \"langchain/vectorstores/memory\";\n", "\n", "const vectorStoreQA = new MemoryVectorStore(embeddings);\n", "await vectorStoreQA.addDocuments(allSplits)"]}, {"cell_type": "markdown", "id": "45e561ad-1193-48ae-8487-7d29cb04e312", "metadata": {}, "source": ["Let's next define a schema for our search query. We will use [structured output](/docs/concepts/structured_outputs/) for this purpose. Here we define a query as containing a string query and a document section (either \"beginning\", \"middle\", or \"end\"), but this can be defined however you like."]}, {"cell_type": "code", "execution_count": 19, "id": "7b4864dd-172a-441f-8224-0661b156ed29", "metadata": {}, "outputs": [], "source": ["import { z } from \"zod\";\n", "\n", "\n", "const searchSchema = z.object({\n", "  query: z.string().describe(\"Search query to run.\"),\n", "  section: z.enum([\"beginning\", \"middle\", \"end\"]).describe(\"Section to query.\"),\n", "});\n", "\n", "const structuredLlm = llm.withStructuredOutput(searchSchema)"]}, {"cell_type": "markdown", "id": "c908874d-d5d2-4f81-95ad-5fa0295ff6c8", "metadata": {}, "source": ["Finally, we add a step to our LangGraph application to generate a query from the user's raw input:"]}, {"cell_type": "code", "execution_count": 20, "id": "afdbe4e7-b1e1-41ff-9c9e-0194ebd73049", "metadata": {}, "outputs": [], "source": ["const StateAnnotationQA = Annotation.Root({\n", "  question: Annotation<string>,\n", "  // highlight-start\n", "  search: Annotation<z.infer<typeof searchSchema>>,\n", "  // highlight-end\n", "  context: Annotation<Document[]>,\n", "  answer: Annotation<string>,\n", "});\n", "\n", "\n", "// highlight-start\n", "const analyzeQuery = async (state: typeof InputStateAnnotation.State) => {\n", "  const result = await structuredLlm.invoke(state.question)\n", "  return { search: result }\n", "};\n", "// highlight-end\n", "\n", "\n", "const retrieveQA = async (state: typeof StateAnnotationQA.State) => {\n", "  // highlight-start\n", "  const filter = (doc) => doc.metadata.section === state.search.section;\n", "  const retrievedDocs = await vectorStore.similaritySearch(\n", "    state.search.query,\n", "    2,\n", "    filter\n", "  )\n", "  // highlight-end\n", "  return { context: retrievedDocs };\n", "};\n", "\n", "\n", "const generateQA = async (state: typeof StateAnnotationQA.State) => {\n", "  const docsContent = state.context.map(doc => doc.pageContent).join(\"\\n\");\n", "  const messages = await promptTemplate.invoke({ question: state.question, context: docsContent });\n", "  const response = await llm.invoke(messages);\n", "  return { answer: response.content };\n", "};\n", "\n", "\n", "\n", "const graphQA = new StateGraph(StateAnnotationQA)\n", "  .addNode(\"analyzeQuery\", analyzeQuery)\n", "  .addNode(\"retrieveQA\", retrieveQA)\n", "  .addNode(\"generateQA\", generateQA)\n", "  .addEdge(\"__start__\", \"analyzeQuery\")\n", "  .addEdge(\"analyzeQuery\", \"retrieveQA\")\n", "  .addEdge(\"retrieveQA\", \"generateQA\")\n", "  .addEdge(\"generateQA\", \"__end__\")\n", "  .compile();"]}, {"cell_type": "markdown", "id": "ea7913a9-3c1b-4008-a573-0fd0afa9824e", "metadata": {}, "source": ["```javascript\n", "// Note: tslab only works inside a jupyter notebook. Don't worry about running this code yourself!\n", "import * as tslab from \"tslab\";\n", "\n", "const image = await graphQA.getGraph().drawMermaidPng();\n", "const arrayBuffer = await image.arrayBuffer();\n", "\n", "await tslab.display.png(new Uint8Array(arrayBuffer));\n", "```\n", "\n", "![graph_img_rag_qa](../../static/img/graph_img_rag_qa.png)"]}, {"cell_type": "markdown", "id": "653cf8dc-a201-43ea-9965-02fcfd2fc316", "metadata": {}, "source": ["We can test our implementation by specifically asking for context from the end of the post. Note that the model includes different information in its answer."]}, {"cell_type": "code", "execution_count": 23, "id": "ecb5f0d6-dcda-4929-ac72-d534d547a426", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  question: \u001b[32m'What does the end of the post say about Task Decomposition?'\u001b[39m\n", "}\n", "\n", "====\n", "\n", "{\n", "  analyzeQuery: { search: { query: \u001b[32m'Task Decomposition'\u001b[39m, section: \u001b[32m'end'\u001b[39m } }\n", "}\n", "\n", "====\n", "\n", "{ retrieveQA: { context: [ \u001b[36m[Document]\u001b[39m, \u001b[36m[Document]\u001b[39m ] } }\n", "\n", "====\n", "\n", "{\n", "  generateQA: {\n", "    answer: \u001b[32m'The end of the post emphasizes the importance of task decomposition by outlining a structured approach to organizing code into separate files and functions. It highlights the need for clarity and compatibility among different components, ensuring that each part of the architecture is well-defined and functional. This methodical breakdown aids in maintaining best practices and enhances code readability and manageability.'\u001b[39m\n", "  }\n", "}\n", "\n", "====\n", "\n"]}], "source": ["let inputsQA = { question: \"What does the end of the post say about Task Decomposition?\" };\n", "\n", "console.log(inputsQA)\n", "console.log(\"\\n====\\n\");\n", "for await (\n", "  const chunk of await graphQA.stream(inputsQA, {\n", "    streamMode: \"updates\",\n", "  })\n", ") {\n", "  console.log(chunk);\n", "  console.log(\"\\n====\\n\");\n", "}"]}, {"cell_type": "markdown", "id": "5875a48a-c849-4da9-99e0-558b04884fb0", "metadata": {}, "source": ["In both the streamed steps and the [<PERSON><PERSON><PERSON> trace](https://smith.langchain.com/public/8ff4742c-a5d4-41b2-adf9-22915a876a30/r), we can now observe the structured query that was fed into the retrieval step.\n", "\n", "Query Analysis is a rich problem with a wide range of approaches. Refer to the [how-to guides](/docs/how_to/#query-analysis) for more examples."]}, {"cell_type": "markdown", "id": "82e4d779", "metadata": {}, "source": ["## Next steps\n", "\n", "We've covered the steps to build a basic Q&A app over data:\n", "\n", "- Loading data with a [Document Loader](/docs/concepts/document_loaders)\n", "- Chunking the indexed data with a [Text Splitter](/docs/concepts/text_splitters) to make it more easily usable by a model\n", "- [Embedding the data](/docs/concepts/embedding_models) and storing the data in a [vectorstore](/docs/how_to/vectorstores)\n", "- [Retrieving](/docs/concepts/retrievers) the previously stored chunks in response to incoming questions\n", "- Generating an answer using the retrieved chunks as context.\n", "\n", "In [Part 2](/docs/tutorials/qa_chat_history) of the tutorial, we will extend the implementation here to accommodate conversation-style interactions and multi-step retrieval processes.\n", "\n", "Further reading:\n", "\n", "- [Return sources](/docs/how_to/qa_sources): Learn how to return source documents\n", "- [Streaming](/docs/how_to/streaming): Learn how to stream outputs and intermediate steps\n", "- [Add chat history](/docs/how_to/message_history): Learn how to add chat history to your app\n", "- [Retrieval conceptual guide](/docs/concepts/retrieval): A high-level overview of specific retrieval techniques"]}], "metadata": {"kernelspec": {"display_name": "TypeScript", "language": "typescript", "name": "tslab"}, "language_info": {"codemirror_mode": {"mode": "typescript", "name": "javascript", "typescript": true}, "file_extension": ".ts", "mimetype": "text/typescript", "name": "typescript", "version": "3.7.2"}}, "nbformat": 4, "nbformat_minor": 5}