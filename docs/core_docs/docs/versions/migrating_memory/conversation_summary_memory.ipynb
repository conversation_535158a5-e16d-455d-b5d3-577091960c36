{"cells": [{"cell_type": "markdown", "id": "ce8457ed-c0b1-4a74-abbd-9d3d2211270f", "metadata": {}, "source": ["# Migrating off ConversationSummaryMemory or ConversationSummaryBufferMemory\n", "\n", "Follow this guide if you're trying to migrate off one of the old memory classes listed below:\n", "\n", "\n", "| Memory Type                          | Description                                                                                                                                          |\n", "|---------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------|\n", "| `ConversationSummaryMemory`           | Continually summarizes the conversation history. The summary is updated after each conversation turn. The abstraction returns the summary of the conversation history. |\n", "| `ConversationSummaryBufferMemory`     | Provides a running summary of the conversation together with the most recent messages in the conversation under the constraint that the total number of tokens in the conversation does not exceed a certain limit. |\n", "\n", "Please follow the following [how-to guide on summarization](https://langchain-ai.github.io/langgraphjs/how-tos/add-summary-conversation-history/) in LangGraph. \n", "\n", "This guide shows how to maintain a running summary of the conversation while discarding older messages, ensuring they aren't re-processed during later turns."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 5}