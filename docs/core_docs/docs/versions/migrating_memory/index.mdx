---
sidebar_position: 1
---

# How to migrate to LangGraph memory

As of the v0.3 release of LangChain, we recommend that LangChain users take advantage of LangGraph persistence to incorporate `memory` into their LangChain application.

- Users that rely on `RunnableWithMessageHistory` or `BaseChatMessageHistory` do **not** need to make any changes, but are encouraged to consider using LangGraph for more complex use cases.
- Users that rely on deprecated memory abstractions from LangChain 0.0.x should follow this guide to upgrade to the new LangGraph persistence feature in LangChain 0.3.x.

## Why use LangGraph for memory?

The main advantages of persistence in LangGraph are:

- Built-in support for multiple users and conversations, which is a typical requirement for real-world conversational AI applications.
- Ability to save and resume complex conversations at any point. This helps with:
  - Error recovery
  - Allowing human intervention in AI workflows
  - Exploring different conversation paths ("time travel")
- Full compatibility with both traditional [language models](/docs/concepts/text_llms) and modern [chat models](/docs/concepts/chat_models). Early memory implementations in LangChain weren't designed for newer chat model APIs, causing issues with features like tool-calling. LangGraph memory can persist any custom state.
- Highly customizable, allowing you to fully control how memory works and use different storage backends.

## Evolution of memory in LangChain

The concept of memory has evolved significantly in LangChain since its initial release.

### LangChain 0.0.x memory

Broadly speaking, LangChain 0.0.x memory was used to handle three main use cases:

| Use Case                             | Example                                                                                                                           |
| ------------------------------------ | --------------------------------------------------------------------------------------------------------------------------------- |
| Managing conversation history        | Keep only the last `n` turns of the conversation between the user and the AI.                                                     |
| Extraction of structured information | Extract structured information from the conversation history, such as a list of facts learned about the user.                     |
| Composite memory implementations     | Combine multiple memory sources, e.g., a list of known facts about the user along with facts learned during a given conversation. |

While the LangChain 0.0.x memory abstractions were useful, they were limited in their capabilities and not well suited for real-world conversational AI applications. These memory abstractions lacked built-in support for multi-user, multi-conversation scenarios, which are essential for practical conversational AI systems.

Most of these implementations have been officially deprecated in LangChain 0.3.x in favor of LangGraph persistence.

### RunnableWithMessageHistory and BaseChatMessageHistory

:::note
Please see [How to use BaseChatMessageHistory with LangGraph](./chat_history), if you would like to use `BaseChatMessageHistory` (with or without `RunnableWithMessageHistory`) in LangGraph.
:::

As of LangChain v0.1, we started recommending that users rely primarily on [BaseChatMessageHistory](https://api.js.langchain.com/classes/_langchain_core.chat_history.BaseChatMessageHistory.html). `BaseChatMessageHistory` serves
as a simple persistence for storing and retrieving messages in a conversation.

At that time, the only option for orchestrating LangChain chains was via [LCEL](/docs/how_to/#langchain-expression-language-lcel). To incorporate memory with `LCEL`, users had to use the [RunnableWithMessageHistory](https://api.js.langchain.com/classes/_langchain_core.runnables.RunnableWithMessageHistory.html) interface. While sufficient for basic chat applications, many users found the API unintuitive and challenging to use.

As of LangChain v0.3, we recommend that **new** code takes advantage of LangGraph for both orchestration and persistence:

- Orchestration: In LangGraph, users define [graphs](https://langchain-ai.github.io/langgraphjs/concepts/low_level/) that specify the flow of the application. This allows users to keep using `LCEL` within individual nodes when `LCEL` is needed, while making it easy to define complex orchestration logic that is more readable and maintainable.
- Persistence: Users can rely on LangGraph's persistence to store and retrieve data. LangGraph persistence is extremely flexible and can support a much wider range of use cases than the `RunnableWithMessageHistory` interface.

:::important
If you have been using `RunnableWithMessageHistory` or `BaseChatMessageHistory`, you do not need to make any changes. We do not plan on deprecating either functionality in the near future. This functionality is sufficient for simple chat applications and any code that uses `RunnableWithMessageHistory` will continue to work as expected.
:::

## Migrations

:::info Prerequisites

These guides assume some familiarity with the following concepts:

- [LangGraph](https://langchain-ai.github.io/langgraphjs/)
- [v0.0.x Memory](https://js.langchain.com/v0.1/docs/modules/memory/)
- [How to add persistence ("memory") to your graph](https://langchain-ai.github.io/langgraphjs/how-tos/persistence/)

:::

### 1. Managing conversation history

The goal of managing conversation history is to store and retrieve the history in a way that is optimal for a chat model to use.

Often this involves trimming and / or summarizing the conversation history to keep the most relevant parts of the conversation while having the conversation fit inside the context window of the chat model.

Memory classes that fall into this category include:

| Memory Type                       | How to Migrate                                               | Description                                                                                                                                                                                                         |
| --------------------------------- | :----------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `ConversationTokenBufferMemory`   | [Link to Migration Guide](conversation_buffer_window_memory) | Keeps only the most recent messages in the conversation under the constraint that the total number of tokens in the conversation does not exceed a certain limit.                                                   |
| `ConversationSummaryMemory`       | [Link to Migration Guide](conversation_summary_memory)       | Continually summarizes the conversation history. The summary is updated after each conversation turn. The abstraction returns the summary of the conversation history.                                              |
| `ConversationSummaryBufferMemory` | [Link to Migration Guide](conversation_summary_memory)       | Provides a running summary of the conversation together with the most recent messages in the conversation under the constraint that the total number of tokens in the conversation does not exceed a certain limit. |

### 2. Extraction of structured information from the conversation history

Memory classes that fall into this category include:

| Memory Type       | Description                                                                                                                                                                                                    |
| ----------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `BaseEntityStore` | An abstract interface that resembles a key-value store. It was used for storing structured information learned during the conversation. The information had to be represented as an object of key-value pairs. |

And specific backend implementations of abstractions:

| Memory Type           | Description                                                                                              |
| --------------------- | -------------------------------------------------------------------------------------------------------- |
| `InMemoryEntityStore` | An implementation of `BaseEntityStore` that stores the information in the literal computer memory (RAM). |

These abstractions have not received much development since their initial release. The reason
is that for these abstractions to be useful they typically require a lot of specialization for a particular application, so these
abstractions are not as widely used as the conversation history management abstractions.

For this reason, there are no migration guides for these abstractions. If you're struggling to migrate an application
that relies on these abstractions, please pen an issue on the LangChain GitHub repository, explain your use case, and we'll try to provide more guidance on how to migrate these abstractions.

The general strategy for extracting structured information from the conversation history is to use a chat model with tool calling capabilities to extract structured information from the conversation history.
The extracted information can then be saved into an appropriate data structure (e.g., an object), and information from it can be retrieved and added into the prompt as needed.

### 3. Implementations that provide composite logic on top of one or more memory implementations

Memory classes that fall into this category include:

| Memory Type      | Description                                                                                                                    |
| ---------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| `CombinedMemory` | This abstraction accepted a list of `BaseMemory` and fetched relevant memory information from each of them based on the input. |

These implementations did not seem to be used widely or provide significant value. Users should be able
to re-implement these without too much difficulty in custom code.

## Related Resources

Explore persistence with LangGraph:

- [LangGraph quickstart tutorial](https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/)
- [How to add persistence ("memory") to your graph](https://langchain-ai.github.io/langgraphjs/how-tos/persistence/)
- [How to manage conversation history](https://langchain-ai.github.io/langgraphjs/how-tos/manage-conversation-history/)
- [How to add summary of the conversation history](https://langchain-ai.github.io/langgraphjs/how-tos/add-summary-conversation-history/)

Add persistence with simple LCEL (favor langgraph for more complex use cases):

- [How to add message history](/docs/how_to/message_history/)

Working with message history:

- [How to trim messages](/docs/how_to/trim_messages)
- [How to filter messages](/docs/how_to/filter_messages/)
- [How to merge message runs](/docs/how_to/merge_message_runs/)
