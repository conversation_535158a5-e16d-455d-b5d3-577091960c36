---
sidebar_position: 2
sidebar_label: Migrating to streamEvents v2
---

# Migrating to streamEvents v2

:::danger

This migration guide is a work in progress and is not complete.

:::

We've added a `v2` of the [`streamEvents`](/docs/how_to/streaming#using-stream-events) API with the release of `0.2.0`. You can see this [PR](https://github.com/langchain-ai/langchainjs/pull/5539/) for more details.

The `v2` version is a re-write of the `v1` version, and should be more efficient, with more consistent output for the events. The `v1` version of the API will be deprecated in favor of the `v2` version and will be removed in `0.4.0`.

Below is a list of changes between the `v1` and `v2` versions of the API.

### output for `on_chat_model_end`

In `v1`, the outputs associated with `on_chat_model_end` changed depending on whether the
chat model was run as a root level runnable or as part of a chain.

As a root level runnable the output was:

```ts
{
  data: {
    output: AIMessageChunk((content = "hello world!"), (id = "some id"));
  }
}
```

As part of a chain the output was:

```
{
  data: {
    output: {
      generations: [
        [
          {
            generation_info: None,
            message: AIMessageChunk(
                content="hello world!", id="some id"
            ),
            text: "hello world!",
          }
        ]
      ],
    }
  },
}
```

As of `v2`, the output will always be the simpler representation:

```ts
{
  data: {
    output: AIMessageChunk((content = "hello world!"), (id = "some id"));
  }
}
```

:::note
Non chat models (i.e., regular LLMs) will be consistently associated with the more verbose format for now.
:::

### output for `on_retriever_end`

`on_retriever_end` output will always return a list of `Documents`.

This was the output in `v1`:

```ts
{
  data: {
    output: {
      documents: [
        Document(...),
        Document(...),
        ...
      ]
    }
  }
}
```

And here is the new output for `v2`:

```ts
{
  data: {
    output: [
      Document(...),
      Document(...),
      ...
    ]
  }
}
```

### Removed `on_retriever_stream`

The `on_retriever_stream` event was an artifact of the implementation and has been removed.

Full information associated with the event is already available in the `on_retriever_end` event.

Please use `on_retriever_end` instead.

### Removed `on_tool_stream`

The `on_tool_stream` event was an artifact of the implementation and has been removed.

Full information associated with the event is already available in the `on_tool_end` event.

Please use `on_tool_end` instead.

### Propagating Names

Names of runnables have been updated to be more consistent.

If you're filtering by event names, check if you need to update your filters.
