---
sidebar_position: 0
sidebar_label: v0.3
---

# <PERSON><PERSON>hai<PERSON> v0.3

_Last updated: 09.14.24_

## What's changed

- All LangChain packages now have `@langchain/core` as a peer dependency instead of a direct dependency to help avoid type errors around [core version conflicts](/docs/how_to/installation/#installing-integration-packages).
  - You will now need to explicitly install `@langchain/core` rather than relying on an internally resolved version from other packages.
- Callbacks are now backgrounded and non-blocking by default rather than blocking.
  - This means that if you are using e.g. LangSmith for tracing in a serverless environment, you will need to [await the callbacks to ensure they finish before your function ends](/docs/how_to/callbacks_serverless).
- Removed deprecated document loader and self-query entrypoints from `langchain` in favor of entrypoints in [`@langchain/community`](https://www.npmjs.com/package/@langchain/community) and integration packages.
- Removed deprecated Google PaLM entrypoints from community in favor of entrypoints in [`@langchain/google-vertexai`](https://www.npmjs.com/package/@langchain/google-vertexai) and [`@langchain/google-genai`](https://www.npmjs.com/package/@langchain/google-genai).
- Deprecated using objects with a `"type"` as a [`BaseMessageLike`](https://v03.api.js.langchain.com/types/_langchain_core.messages.BaseMessageLike.html) in favor of the more OpenAI-like [`MessageWithRole`](https://v03.api.js.langchain.com/types/_langchain_core.messages.MessageFieldWithRole.html)

## What’s new

The following features have been added during the development of 0.2.x:

- Simplified tool definition and usage. Read more [here](https://blog.langchain.dev/improving-core-tool-interfaces-and-docs-in-langchain/).
- Added a [generalized chat model constructor](https://js.langchain.com/docs/how_to/chat_models_universal_init/).
- Added the ability to [dispatch custom events](https://js.langchain.com/docs/how_to/callbacks_custom_events/).
- Released LangGraph.js 0.2.0 and made it the [recommended way to create agents](https://js.langchain.com/docs/how_to/migrate_agent) with LangChain.js.
- Revamped integration docs and API reference. Read more [here](https://blog.langchain.dev/langchain-integration-docs-revamped/).

## How to update your code

If you're using `langchain` / `@langchain/community` / `@langchain/core` 0.0 or 0.1, we recommend that you first [upgrade to 0.2](https://js.langchain.com/v0.2/docs/versions/v0_2/).

If you're using `@langchain/langgraph`, upgrade to `@langchain/langgraph>=0.2.3`. This will work with either 0.2 or 0.3 versions of all the base packages.

Here is a complete list of all packages that have been released and what we recommend upgrading your version constraints to in your `package.json`.
Any package that now supports `@langchain/core` 0.3 had a minor version bump.

### Base packages

| Package                  | Latest | Recommended `package.json` constraint |
| ------------------------ | ------ | ------------------------------------- |
| langchain                | 0.3.0  | >=0.3.0 <0.4.0                        |
| @langchain/community     | 0.3.0  | >=0.3.0 <0.4.0                        |
| @langchain/textsplitters | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/core          | 0.3.0  | >=0.3.0 <0.4.0                        |

### Downstream packages

| Package              | Latest | Recommended `package.json` constraint |
| -------------------- | ------ | ------------------------------------- |
| @langchain/langgraph | 0.2.3  | >=0.2.3 <0.3                          |

### Integration packages

| Package                           | Latest | Recommended `package.json` constraint |
| --------------------------------- | ------ | ------------------------------------- |
| @langchain/anthropic              | 0.3.0  | >=0.3.0 <0.4.0                        |
| @langchain/aws                    | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/azure-cosmosdb         | 0.2.0  | >=0.2.0 <0.3.0                        |
| @langchain/azure-dynamic-sessions | 0.2.0  | >=0.2.0 <0.3.0                        |
| @langchain/baidu-qianfan          | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/cloudflare             | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/cohere                 | 0.3.0  | >=0.3.0 <0.4.0                        |
| @langchain/exa                    | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/google-genai           | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/google-vertexai        | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/google-vertexai-web    | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/groq                   | 0.1.1  | >=0.1.1 <0.2.0                        |
| @langchain/mistralai              | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/mixedbread-ai          | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/mongodb                | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/nomic                  | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/ollama                 | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/openai                 | 0.3.0  | >=0.3.0 <0.4.0                        |
| @langchain/pinecone               | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/qdrant                 | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/redis                  | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/weaviate               | 0.1.0  | >=0.1.0 <0.2.0                        |
| @langchain/yandex                 | 0.1.0  | >=0.1.0 <0.2.0                        |

Once you've updated to recent versions of the packages, you will need to explicitly install `@langchain/core` if you haven't already:

```bash npm2yarn
npm install @langchain/core
```

We also suggest checking your lockfile or running the [appropriate package manager command](/docs/how_to/installation/#installing-integration-packages) to make sure that your package manager only has one version of `@langchain/core` installed.

If you are currently running your code in a serverless environment (e.g., a Cloudflare Worker, Edge function, or AWS Lambda function)
and you are using LangSmith tracing or other callbacks, you will need to [await callbacks to ensure they finish before your function ends](/docs/how_to/callbacks_serverless).
Here's a quick example:

```ts
import { RunnableLambda } from "@langchain/core/runnables";
import { awaitAllCallbacks } from "@langchain/core/callbacks/promises";

const runnable = RunnableLambda.from(() => "hello!");

const customHandler = {
  handleChainEnd: async () => {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    console.log("Call finished");
  },
};

const startTime = new Date().getTime();

await runnable.invoke({ number: "2" }, { callbacks: [customHandler] });

console.log(`Elapsed time: ${new Date().getTime() - startTime}ms`);

await awaitAllCallbacks();

console.log(`Final elapsed time: ${new Date().getTime() - startTime}ms`);
```

```
Elapsed time: 1ms
Call finished
Final elapsed time: 2164ms
```

You can also set `LANGCHAIN_CALLBACKS_BACKGROUND` to `"false"` to make all callbacks blocking:

```ts
process.env.LANGCHAIN_CALLBACKS_BACKGROUND = "false";

const startTimeBlocking = new Date().getTime();

await runnable.invoke({ number: "2" }, { callbacks: [customHandler] });

console.log(
  `Initial elapsed time: ${new Date().getTime() - startTimeBlocking}ms`
);
```

```
Call finished
Initial elapsed time: 2002ms
```
