{"name": "core_docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "yarn quarto && rimraf ./docs/api && NODE_OPTIONS=--max-old-space-size=7168 docusaurus start", "build": "yarn clean && yarn quarto && rimraf ./build && NODE_OPTIONS=--max-old-space-size=7168 DOCUSAURUS_SSR_CONCURRENCY=4 docusaurus build", "build:vercel": "yarn clean && bash ./scripts/vercel_build.sh && yarn quarto:vercel && rimraf ./build && NODE_OPTIONS=--max-old-space-size=7168 DOCUSAURUS_SSR_CONCURRENCY=4 docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint": "yarn lint:eslint", "lint:fix": "yarn lint --fix", "precommit": "lint-staged", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,md,mdx}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,md,mdx}\"", "clean": "rm -rf .docusaurus/ .turbo/ .build/", "quarto": "quarto render docs/ && node ./scripts/quarto-build.js && python3 ./scripts/append_related_links.py ./docs", "quarto:vercel": "node ./scripts/quarto-build.js && python3 ./scripts/append_related_links.py ./docs", "gen": "yarn gen:supabase", "gen:supabase": "npx supabase gen types typescript --project-id 'xsqpnijvmbodcxyapnyq' --schema public > ./src/supabase.d.ts", "broken-links": "node ./scripts/check-broken-links.js", "check:broken-links": "yarn quarto && yarn broken-links", "check:broken-links:ci": "yarn quarto:vercel && yarn broken-links", "validate": "yarn notebook_validate"}, "dependencies": {"@docusaurus/core": "2.4.3", "@docusaurus/plugin-google-tag-manager": "2.4.3", "@docusaurus/preset-classic": "2.4.3", "@docusaurus/remark-plugin-npm2yarn": "2.4.3", "@docusaurus/theme-mermaid": "2.4.3", "@mdx-js/react": "^1.6.22", "@supabase/supabase-js": "^2.45.0", "clsx": "^1.2.1", "cookie": "^0.6.0", "isomorphic-dompurify": "^2.9.0", "json-loader": "^0.5.7", "marked": "^12.0.2", "process": "^0.11.10", "react": "^17.0.2", "react-dom": "^17.0.2", "uuid": "^10.0.0", "webpack": "^5.75.0"}, "devDependencies": {"@babel/eslint-parser": "^7.18.2", "@langchain/langgraph": "^0.2.34", "@langchain/scripts": "workspace:*", "@microsoft/fetch-event-source": "^2.0.1", "@swc/core": "^1.3.62", "@types/cookie": "^0", "docusaurus-plugin-typedoc": "1.0.0-next.5", "dotenv": "^16.4.5", "eslint": "^8.19.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-header": "^3.1.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "glob": "^10.3.10", "prettier": "^2.8.3", "rimraf": "^5.0.1", "supabase": "^1.148.6", "swc-loader": "^0.2.3", "ts-morph": "^23.0.0", "tsx": "^3.12.3", "typedoc": "^0.24.4", "typedoc-plugin-markdown": "next", "typescript": "~5.1.6", "yaml-loader": "^0.8.0"}, "packageManager": "yarn@3.4.1", "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"typedoc-plugin-markdown@next": "patch:typedoc-plugin-markdown@npm%3A4.0.0-next.6#./.yarn/patches/typedoc-plugin-markdown-npm-4.0.0-next.6-96b4b47746.patch"}, "engines": {"node": ">=18"}}