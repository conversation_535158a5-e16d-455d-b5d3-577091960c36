/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 */

/**
 * Any CSS included here will be global. The classic template
 * bundles Infima by default. Infima is a CSS framework designed to
 * work well for content-centric websites.
 */

@font-face {
  font-family: 'Manrope';
  src: url('/fonts/Manrope-VariableFont_wght.ttf') format('truetype');
}
@font-face {
  font-family: 'Public Sans';
  src: url('/fonts/PublicSans-VariableFont_wght.ttf') format('truetype');
}

/* You can override the default Infima variables here. */
:root {
  --ifm-color-primary: #2e8555;
  --ifm-color-primary-dark: #29784c;
  --ifm-color-primary-darker: #277148;
  --ifm-color-primary-darkest: #205d3b;
  --ifm-color-primary-light: #33925d;
  --ifm-color-primary-lighter: #359962;
  --ifm-color-primary-lightest: #3cad6e;
  --ifm-font-weight-bold: 600;
  --ifm-code-font-size: 95%;
  --ifm-font-family-base: 'Public Sans';
  --ifm-menu-link-padding-horizontal: 0.5rem;
  --ifm-menu-link-padding-vertical: 0.5rem;
  --doc-sidebar-width: 275px !important;
  /* Code highlighting background color */
  --docusaurus-highlighted-code-line-bg: rgb(202, 203, 205);

}

/* For readability concerns, you should choose a lighter palette in dark mode. */
[data-theme="dark"] {
  --ifm-color-primary: #25c2a0;
  --ifm-color-primary-dark: #21af90;
  --ifm-color-primary-darker: #1fa588;
  --ifm-color-primary-darkest: #1a8870;
  --ifm-color-primary-light: #29d5b0;
  --ifm-color-primary-lighter: #32d8b4;
  --ifm-color-primary-lightest: #4fddbf;
  /* Code highlighting background color */
  --docusaurus-highlighted-code-line-bg: rgb(73, 73, 73);
}

nav, h1, h2, h3, h4 {
  font-family: 'Manrope';
}

.footer__links {
  margin-top: 1rem;
  margin-bottom: 3rem;
}

.footer__col {
  text-align: center;
}

.footer__copyright {
  opacity: 0.6;
}

.node-only,
.web-only,
.beta {
  position: relative;
}


.menu__list-item.node-only .menu__link {
  padding-right: 80px;
}

.node-only::after,
.beta::after,
.web-only::after {
  position: absolute;
  right: 0.35rem;
  top: 5px;
  content: "Node-only";
  background: #026e00;
  color: #fff;
  border-radius: 0.25rem;
  padding: 0 0.5rem;
  pointer-events: none;
  font-size: 0.85rem;
}

[data-theme="dark"] .node-only::after {
  background: #026e00;
  color: #fff;
}

/* Override `beta` color */
.beta::after {
  content: "Beta";
  color: #fff;
  background: #58006e;
  border: 1px solid #58006e;
}

/* Override `beta` color */
[data-theme="dark"] .beta::after {
  background: #58006e;
  color: #fff;
}

/* Override `web-only` color */
.web-only::after {
  content: "Web-only";
  color: #fff;
  background: #0a0072;
  border: 1px solid #0a0072;
}

/* Override `web-only` color */
[data-theme="dark"] .web-only::after {
  background: #0a0072;
  color: #fff;
}

.node-only-category,
.beta-category {
  position: relative;
}

.node-only-category::after,
.beta-category::after {
  position: absolute;
  right: 3rem;
  top: 5px;
  content: "Node-only";
  background: #026e00;
  color: #fff;
  border-radius: 0.25rem;
  padding: 0 0.5rem;
  pointer-events: none;
  font-size: 0.85rem;
}

[data-theme="dark"] .node-only-category::after {
  background: #026e00;
  color: #fff;
}

/* Override `beta` color */
.beta-category::after {
  content: "Beta";
  color: #58006e;
  border: 1px solid #58006e;
}

/* Override `beta` color */
[data-theme="dark"] .beta::after {
  background: #58006e;
  color: #fff;
}


.theme-code-block.language-python,
.theme-code-block.language-javascript,
.theme-code-block.language-js,
.theme-code-block.language-typescript,
.theme-code-block.language-ts {
  position: relative; /* Ensure this is set so the ::before pseudo-element is positioned relative to this element */
  padding-left: 4px;
  border: 1px solid var(--ifm-color-primary-darkest);
}

.theme-code-block.language-python::before,
.theme-code-block.language-javascript::before,
.theme-code-block.language-js::before,
.theme-code-block.language-typescript::before,
.theme-code-block.language-ts::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 3px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  background-color: var(--ifm-color-primary-light);
  z-index: 1;
}

.theme-doc-sidebar-menu > .theme-doc-sidebar-item-category:not(:first-of-type),
.theme-doc-sidebar-menu > .theme-doc-sidebar-item-link,
.theme-doc-sidebar-menu > .theme-doc-sidebar-item-link.theme-doc-sidebar-item-link-level-1:not(:first-of-type) {
  margin-top: 1rem;
}

.theme-doc-sidebar-menu .theme-doc-sidebar-item-link,
.theme-doc-sidebar-menu .theme-doc-sidebar-item-category {
  margin-top: 0;
  padding-bottom: 0;
  padding-top: 0;
}

.theme-doc-sidebar-menu .theme-doc-sidebar-item-category > ul {
  margin-top: 0;
}

.theme-doc-sidebar-menu .theme-doc-sidebar-item-link a,
.theme-doc-sidebar-menu .theme-doc-sidebar-item-category a {
  margin-top: 0;
}

.theme-doc-sidebar-item-category, .theme-doc-sidebar-menu > .theme-doc-sidebar-item-link {
  font-size: 1rem;
  font-weight: 700;
}

.theme-doc-sidebar-item-category button:before {
  height: 1rem;
  width: 1.25rem;
}

.theme-doc-sidebar-item-link, .theme-doc-sidebar-item-category .theme-doc-sidebar-item-category {
  font-size: .9rem;
  font-weight: 500;
}

.theme-doc-sidebar-item-category > div > a {
  flex: 1 1 0;
  overflow: hidden;
  word-break: break-word;
}

.theme-doc-sidebar-item-category > div > button {
  opacity: 0.5;
}

/* Hack for "More" style caret buttons */
.theme-doc-sidebar-item-category > div > a::after {
  opacity: 0.5;
}

.markdown {
  line-height: 2em;
}

.markdown > h2 {
  margin-top: 2rem;
  border-bottom-color: var(--ifm-color-primary);
  border-bottom-width: 2px;
  padding-bottom: 1rem;
}

.markdown > :not(h2) +  h3 {
  margin-top: 1rem;
}

.markdown > h4 {
  margin-bottom: 0.2rem;
  font-weight: 600;
}

.markdown > h4:has(+ table) {
  margin-bottom: 0.4rem;
}

.markdown > h5 {
  margin-bottom: 0.2rem;
  font-weight: 600;
}

.hidden {
  display: none !important;
}

.header-github-link:hover {
  opacity: 0.6;
}

.header-github-link::before {
  content: '';
  width: 24px;
  height: 24px;
  display: flex;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E")
    no-repeat;
}

[data-theme="dark"] .header-github-link::before {
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='white' d='M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12'/%3E%3C/svg%3E") no-repeat;
}

.announcementBar_node_modules-\@docusaurus-theme-classic-lib-theme-AnnouncementBar-styles-module {
  height: 40px !important;
  font-size: 20px !important;
}

[data-theme='dark'] .announcementBar_node_modules-\@docusaurus-theme-classic-lib-theme-AnnouncementBar-styles-module {
  background-color: #1b1b1b;
  color: #fff;
}

[data-theme='dark'] .announcementBar_node_modules-\@docusaurus-theme-classic-lib-theme-AnnouncementBar-styles-module button {
  color: #fff;
}

.announcement-bar-text {
  font-size: 16px;
}

div[class^='announcementBar_'] {
  padding: 20px 0; 
}
