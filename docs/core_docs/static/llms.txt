# LangChain JavaScript

This guide provides explanations of the key concepts behind the LangChain framework and AI applications more broadly.

[Tutorials](https://js.langchain.com/docs/tutorials/):  looking for LangChain tutorial resources, needing to get started with LLM application development, or searching for structured learning paths in LangChain. (The page contains a comprehensive collection of tutorials organized into three main sections: "Get started" tutorials covering basic components like chat models and prompts, "Orchestration" tutorials for building complex applications with LangGraph, and "LangSmith" tutorials for evaluation and monitoring of LLM applications.)

[How-to guides](https://js.langchain.com/docs/how_to/):  (needing to find specific implementation guides for LangChain.js, looking for solutions to common LLM application problems, or planning how to implement particular features in a LangChain project) (Comprehensive index of how-to guides for LangChain.js covering core functionality including installation, LCEL, components (prompts, models, retrievers, etc.), and use cases like RAG, extraction, chatbots, and SQL/graph database integration, with links to related technologies like LangGraph.js and LangSmith)

## Concepts

[Why LangChain?](https://js.langchain.com/docs/concepts/why_langchain/):  needing to understand LangChain's purpose, when evaluating if LangChain is suitable for an AI application, or when exploring LangChain ecosystem components. (This page explains why developers should use LangChain, highlighting its three main features: standardized component interfaces that make it easy to switch between providers, orchestration capabilities for complex applications through LangGraph, and observability/evaluation tools via LangSmith to monitor and improve applications.)
- **[Architecture](https://js.langchain.com/docs/concepts/architecture/)**: How packages are organized in the LangChain ecosystem.
- **[Chat models](https://js.langchain.com/docs/concepts/chat_models/)**: LLMs exposed via a chat API that process sequences of messages as input and output a message.
- **[Messages](https://js.langchain.com/docs/concepts/messages/)**: The unit of communication in chat models, used to represent model input and output.
- **[Chat history](https://js.langchain.com/docs/concepts/chat_history/)**: A conversation represented as a sequence of messages, alternating between user messages and model responses.
- **[Tools](https://js.langchain.com/docs/concepts/tools/)**: A function with an associated schema defining the function's name, description, and the arguments it accepts.
- **[Tool calling](https://js.langchain.com/docs/concepts/tool_calling/)**: A type of chat model API that accepts tool schemas, along with messages, as input and returns invocations of those tools as part of the output message.
- **[Structured output](https://js.langchain.com/docs/concepts/structured_outputs/)**: A technique to make a chat model respond in a structured format, such as JSON that matches a given schema.
[Memory](https://langchain-ai.github.io/langgraphjs/concepts/memory/):  needing to understand memory management in LangGraph, implementing short-term or long-term memory in agents, or handling long conversation histories. (Comprehensive guide to memory in LangGraph, covering both short-term memory (within conversation threads) and long-term memory (across threads), with techniques for managing long conversations, editing message lists, summarizing past conversations, writing/managing memories, and representing memories as instructions or few-shot examples.)
[Multimodality](https://js.langchain.com/docs/concepts/multimodality/):  (seeking to understand multimodal capabilities in LangChain, implementing models that process both text and image inputs, or developing applications that need to handle various data types) (Conceptual guide to multimodality in LangChain covering how chat models can accept and process multiple data formats like text and images, current limitations of multimodal outputs and tool use, and the future potential for multimodal embedding models and vector stores)
- **[Runnable interface](https://js.langchain.com/docs/concepts/runnables/)**: The base abstraction that many LangChain components and the LangChain Expression Language are built on.
[stream](https://js.langchain.com/docs/concepts/streaming/):  looking for ways to implement real-time output streaming in LLM applications, when needing to understand different streaming APIs in LangChain, or when troubleshooting performance issues with streaming implementations. (The page explains streaming in LangChain, covering what data to stream (LLM outputs, workflow progress, custom data), the main streaming APIs (stream() and streamEvents()), how to write custom data to streams, auto-streaming features for chat models, and links to related resources and how-to guides.)
[LangChain Expression Language (LCEL)](https://js.langchain.com/docs/concepts/lcel/):  needing to understand LangChain Expression Language, looking for ways to optimize chain execution, or deciding between LCEL and LangGraph. (LCEL is a declarative approach for building chains from Runnables, offering benefits including optimized parallel execution, simplified streaming, LangSmith tracing, a standard API, and LangServe deployment. Key composition primitives include RunnableSequence for sequential operations and RunnableParallel for concurrent execution, with shorthand syntax like the pipe method for improved readability.)
[Document Loaders](https://js.langchain.com/docs/concepts/document_loaders/):  needing to load documents from external sources, understanding document loader concepts, or implementing data ingestion pipelines. (Document loaders are components that load data from various sources into Document objects for LLM processing. The page explains the loader interface with .load() and .lazy_load() methods, provides code examples, and points to numerous integrations with services like Slack, Notion, and Google Drive.)
[Retrieval](https://js.langchain.com/docs/concepts/retrieval/):  (seeking to understand different retrieval systems, implementing query analysis techniques, or building a RAG application) (Comprehensive guide on retrieval systems that covers key concepts like query analysis and information retrieval. Explains techniques for query re-writing and construction, and details various retrieval systems including lexical search indexes, vector indexes, relational databases, and graph databases. Provides implementation examples for specific approaches like decomposition, step-back prompting, HyDE, and self-query retrieval.)
[Text Splitters](https://js.langchain.com/docs/concepts/text_splitters/):  (looking for document chunking strategies, implementing text splitting for RAG applications, or overcoming model context limitations) (Comprehensive guide to text splitters in LangChain, covering why document splitting is necessary and four main approaches: length-based splitting (character/token), text-structure based (recursive splitting), document-structure based (for HTML/markdown/code), and semantic meaning based splitting. Includes code examples and explanations of when to use each approach.)
[Embedding Models](https://js.langchain.com/docs/concepts/embedding_models/):  (needing to understand embedding models for vector search, implementing retrieval systems, or comparing similarity between text samples) (Conceptual overview of embedding models explaining how they transform text into numerical vectors, their historical evolution from BERT to modern implementations, how to use LangChain's embedding interface methods, and common similarity metrics like cosine similarity for comparing embeddings)
[Vector stores](https://js.langchain.com/docs/concepts/vectorstores/):  (needing to understand vector stores for RAG applications, implementing semantic search capabilities, or working with document embeddings) (Vector stores are specialized data stores for indexing and retrieving information using vector representations called embeddings. The page covers the standard interface for working with vector stores including initialization, adding documents, deleting documents, and conducting similarity searches. It explains similarity metrics, search algorithms, metadata filtering, and advanced techniques like Maximal Marginal Relevance to improve search quality and diversity.)
[Retrievers](https://js.langchain.com/docs/concepts/retrievers/):  (needing to understand how retrievers work in LangChain, implementing RAG systems, or comparing different retrieval approaches) (Explains retrievers in LangChain which provide a standard interface for retrieving documents using natural language queries. Covers the basic retriever interface, common types including search APIs, relational/graph databases, lexical search, and vector stores. Discusses advanced patterns like ensemble retrievers and source document retention strategies including ParentDocument and MultiVector retrievers.)
[Retrieval Augmented Generation (RAG)](https://js.langchain.com/docs/concepts/rag/):  needing to understand RAG concepts, implementing a retrieval system for LLMs, or designing knowledge augmentation solutions. (Conceptual overview of Retrieval Augmented Generation (RAG), explaining how it enhances language models by retrieving and incorporating external knowledge into prompts, with details on retrieval systems, benefits of RAG including up-to-date information access and reduced hallucinations, and implementation patterns with code examples.)
[Agents](https://js.langchain.com/docs/concepts/agents/):  (needing to understand agent capabilities in LangChain, migrating from AgentExecutor to LangGraph, or seeking resources about agent implementation) (Explains that agents are systems that use LLMs as reasoning engines to take actions, recommends using LangGraph for building agents instead of the legacy AgentExecutor, and provides links to migration guides and documentation resources for both approaches)
[Prompt Templates](https://js.langchain.com/docs/concepts/prompt_templates/):  needing to understand prompt templates in LangChain, implementing different types of prompts, or working with message placeholders. (This page explains LangChain's prompt templates that help translate user inputs into instructions for language models. It covers three main types: String PromptTemplates for formatting single strings, ChatPromptTemplates for formatting lists of messages, and MessagesPlaceholder for inserting lists of messages at specific positions in prompts.)
[Output Parsers](https://js.langchain.com/docs/concepts/output_parsers/):  needing to parse structured outputs from language models, when handling JSON/XML responses from LLMs, or when selecting appropriate output parsers for specific data formats. (This page describes various output parsers in LangChain that transform LLM outputs into structured formats. It covers JSON, XML, CSV, OutputFixing, Datetime, and Structured parsers, with details about their streaming support, format instructions, and use cases. The page notes that function/tool calling is recommended over output parsing when possible.)
[Few-shot prompting](https://js.langchain.com/docs/concepts/few_shot_prompting/):  wanting to learn about few-shot prompting techniques, improving model performance with examples, or implementing effective example selection strategies. (This page explains few-shot prompting in 4 key areas: generating good examples (manual, better model, feedback), determining optimal example quantity, selecting examples (random, similarity-based), and formatting examples properly in prompts. It covers both single-turn and multi-turn examples, considerations for tool-calling, and best practices for different models.)
[Example Selectors](https://js.langchain.com/docs/concepts/example_selectors/):  (learning about dynamic example selection for few-shot prompting, needing to understand example selector concepts, implementing context-aware example selection) (Overview of Example Selectors which dynamically choose and format examples for prompts, improving LLM performance through few-shot prompting rather than using hardcoded examples, with links to related how-to guides and API reference)
[Callbacks](https://js.langchain.com/docs/concepts/callbacks/):  learning about LangChain's callback system, implementing event tracking in LLM applications, or customizing monitoring for chains and tools. (Comprehensive guide to LangChain's callback system explaining how to hook into various stages of LLM applications. Covers callback events, callback handlers, and how to pass callbacks at both constructor and request time. Includes details on event types like model starts/ends, token generation, chain events, tool events, and retriever events.)
[Tracing](https://js.langchain.com/docs/concepts/tracing/):  needing to understand tracing capabilities in LangChain or when troubleshooting observability issues in LangChain applications. (Tracing in LangChain records the series of steps from input to output, capturing individual runs from models, retrievers, tools, or sub-chains to provide observability for diagnosing issues in chains and agents.)
[Evaluation](https://js.langchain.com/docs/concepts/evaluation/):  needing to understand evaluation methods for LLM applications, searching for tools to assess model performance, or learning how to implement testing frameworks. (This page explains LangChain's evaluation concepts for assessing LLM application performance. It highlights how LangSmith assists with creating datasets, defining metrics, running evaluations, and tracking results over time. The page emphasizes the importance of evaluation for building reliable LLM applications and points to more detailed documentation in LangSmith.)

## Glossary

- **[AIMessageChunk](https://js.langchain.com/docs/concepts/messages/#aimessagechunk)**: A partial response from an AI message. Used when streaming responses from a chat model.
- **[AIMessage](https://js.langchain.com/docs/concepts/messages/#aimessage)**: Represents a complete response from an AI model.
[StructuredTool](https://api.js.langchain.com/classes/_langchain_core.tools.StructuredTool.html/):  (needing to create structured tools for LangChain, understanding StructuredTool implementation details, examining tool input validation patterns) (Core documentation for the abstract StructuredTool class, which is the base class for LangChain tools accepting input defined by Zod schemas, including properties like name, description, schema, methods for invoking tools, and configuration options)
- **[batch](https://js.langchain.com/docs/concepts/runnables/)**: Use to execute a runnable with batch inputs a Runnable.
[Tool binding](https://js.langchain.com/docs/concepts/tool_calling/#tool-binding):  (needing to understand how tool calling works in LangChain, implementing LLM-powered function execution, or developing applications that require structured outputs from models) (This page explains tool calling in LangChain, covering the four key concepts: tool creation using the tool() function, tool binding to models via bindTools(), tool calling where models decide when to use tools and return structured arguments, and tool execution using the Runnable interface. It includes code examples, best practices, and diagrams illustrating the process flow.)
- **[Caching](https://js.langchain.com/docs/concepts/chat_models/#caching)**: Storing results to avoid redundant calls to a chat model.
- **[Context window](https://js.langchain.com/docs/concepts/chat_models/#context-window)**: The maximum size of input a chat model can process.
- **[Conversation patterns](https://js.langchain.com/docs/concepts/chat_history/#conversation-patterns)**: Common patterns in chat interactions.
[Document](https://api.js.langchain.com/classes/_langchain_core.documents.Document.html/):  needing details about Document class implementation in LangChain.js, understanding document structure in LangChain, or implementing document handling with metadata. (Documentation for the Document class in @langchain/core, which is the interface for interacting with documents, showing its constructor, properties including pageContent, metadata, and optional id field, and type parameters for metadata customization)
[Embedding Models](https://js.langchain.com/docs/concepts/embedding_models/):  (needing to understand embedding models for vector search, implementing retrieval systems, or comparing similarity between text samples) (Conceptual overview of embedding models explaining how they transform text into numerical vectors, their historical evolution from BERT to modern implementations, how to use LangChain's embedding interface methods, and common similarity metrics like cosine similarity for comparing embeddings)
- **[HumanMessage](https://js.langchain.com/docs/concepts/messages/#humanmessage)**: Represents a message from a human user.
- **[input and output types](https://js.langchain.com/docs/concepts/runnables/#input-and-output-types)**: Types used for input and output in Runnables.
- **[Integration packages](https://js.langchain.com/docs/concepts/architecture/#integration-packages)**: Third-party packages that integrate with LangChain.
- **[invoke](https://js.langchain.com/docs/concepts/runnables/)**: A standard method to invoke a Runnable.
- **[JSON mode](https://js.langchain.com/docs/concepts/structured_outputs/#json-mode)**: Returning responses in JSON format.
- **[@langchain/community](https://js.langchain.com/docs/concepts/architecture/#langchaincommunity)**: Community-driven components for LangChain.
- **[@langchain/core](https://js.langchain.com/docs/concepts/architecture/#langchaincore)**: Core langchain package. Includes base interfaces and in-memory implementations.
- **[langchain](https://js.langchain.com/docs/concepts/architecture/#langchain)**: A package for higher level components (e.g., some pre-built chains).
[@langchain/langgraph](https://js.langchain.com/docs/concepts/architecture/#langchainlanggraph):  needing to understand LangChain's framework structure, looking for package dependencies, or planning integration implementations. (Overview of LangChain's architecture showing the hierarchical organization with @langchain/core providing base abstractions, langchain containing cognitive architecture components, integration packages for third-party services, @langchain/community for community-maintained integrations, @langchain/langgraph for orchestration, and LangSmith for debugging and monitoring LLM applications.)
[Managing chat history](https://js.langchain.com/docs/concepts/chat_history/#managing-chat-history):  needing to understand chat history structures, implementing conversation management in LLM applications, or configuring proper message sequences for tool calling. (This page explains chat history concepts in LangChain, including conversation patterns between users and assistants, proper message sequencing, managing history to avoid context window limits, and guidelines for maintaining correct conversation structure with system, user, assistant, and tool messages.)
- **[OpenAI format](https://js.langchain.com/docs/concepts/messages/#openai-format)**: OpenAI's message format for chat models.
- **[Propagation of RunnableConfig](https://js.langchain.com/docs/concepts/runnables/#propagation-of-runnableconfig)**: Propagating configuration through Runnables.
- **[RemoveMessage](https://js.langchain.com/docs/concepts/messages/#removemessage)**: An abstraction used to remove a message from chat history, used primarily in LangGraph.
- **[role](https://js.langchain.com/docs/concepts/messages/#role)**: Represents the role (e.g., user, assistant) of a chat message.
[RunnableConfig](https://js.langchain.com/docs/concepts/runnables/#runnableconfig):  (needing to understand the Runnable interface in LangChain, implementing custom processing logic in LCEL chains, or configuring execution parameters for LangChain components) (Comprehensive guide to the Runnable interface which is fundamental to LangChain components. Covers core methods like invoke, batch, stream and streamEvents; explains RunnableConfig options including runName, tags, callbacks, and concurrency settings; details input/output types across components; demonstrates how to create custom Runnables with RunnableLambda and RunnableGenerator functions)
[Standard parameters for chat models](https://js.langchain.com/docs/concepts/chat_models/#standard-parameters):  (needing to understand chat model interfaces in LangChain, implementing tool calling with LLMs, or requiring information about structured outputs from models) (Comprehensive guide to chat models in LangChain covering the model interface, key methods, standard parameters, tool calling capabilities, structured outputs, multimodality, context windows, and advanced topics like caching. Includes information on integrations with various providers and the standard message format system.)
[stream](https://js.langchain.com/docs/concepts/streaming/):  looking for ways to implement real-time output streaming in LLM applications, when needing to understand different streaming APIs in LangChain, or when troubleshooting performance issues with streaming implementations. (The page explains streaming in LangChain, covering what data to stream (LLM outputs, workflow progress, custom data), the main streaming APIs (stream() and streamEvents()), how to write custom data to streams, auto-streaming features for chat models, and links to related resources and how-to guides.)
- **[Tokenization](https://js.langchain.com/docs/concepts/tokens/#how-tokens-work-in-language-models)**: The process of converting data into tokens and vice versa.
[Tokens](https://js.langchain.com/docs/concepts/tokens/):  needing to understand how language models process text, explaining tokenization to users, or working with token-related code and limitations. (Explains what tokens are in LLMs - the basic processing units that can represent words, word parts, or other linguistic elements. Covers tokenization process, how tokens enable better language understanding than character-level processing, token counting metrics, and notes that tokens can also represent non-text modalities like images or audio.)
- **[Tool artifacts](https://js.langchain.com/docs/concepts/tools/#tool-artifacts)**: Add artifacts to the output of a tool that will not be sent to the model, but will be available for downstream processing.
[Tool binding](https://js.langchain.com/docs/concepts/tool_calling/#tool-binding):  (needing to understand how tool calling works in LangChain, implementing LLM-powered function execution, or developing applications that require structured outputs from models) (This page explains tool calling in LangChain, covering the four key concepts: tool creation using the tool() function, tool binding to models via bindTools(), tool calling where models decide when to use tools and return structured arguments, and tool execution using the Runnable interface. It includes code examples, best practices, and diagrams illustrating the process flow.)
- **[`tool`](https://js.langchain.com/docs/concepts/tools/)**: Function for creating tools in LangChain.
[Toolkits](https://js.langchain.com/docs/concepts/tools/#toolkits):  learning about tool creation in LangChain, implementing function calling with LLMs, or building agents that use external functions. (Comprehensive guide to LangChain's tool abstraction which associates TypeScript functions with schemas, allowing models to call functions with specific inputs. Covers tool creation with the 'tool' function, tool interfaces, schemas, tool artifacts, runtime configurations, best practices, and toolkits.)
[ToolMessage](https://js.langchain.com/docs/concepts/messages/#toolmessage):  (needing to understand message types in LangChain, implementing chat interfaces, or working with different message roles) (Comprehensive guide to Messages in LangChain, covering message roles (system, user, assistant, tool), message content types, standardized message structure, conversation formats, and how different message types like SystemMessage, HumanMessage, AIMessage, ToolMessage and AIMessageChunk are implemented)
[Vector stores](https://js.langchain.com/docs/concepts/vectorstores/):  (needing to understand vector stores for RAG applications, implementing semantic search capabilities, or working with document embeddings) (Vector stores are specialized data stores for indexing and retrieving information using vector representations called embeddings. The page covers the standard interface for working with vector stores including initialization, adding documents, deleting documents, and conducting similarity searches. It explains similarity metrics, search algorithms, metadata filtering, and advanced techniques like Maximal Marginal Relevance to improve search quality and diversity.)
[withStructuredOutput](https://js.langchain.com/docs/concepts/structured_outputs/#structured-output-method):  needing to implement structured outputs with LLMs, understanding schema definition methods, or configuring models to return data in specific formats. (Comprehensive guide to structured outputs in LangChain, covering schema definition using Zod or JSONSchema, implementation methods including tool calling and JSON mode, and the recommended .withStructuredOutput() method that simplifies binding schemas to models and parsing responses.)


## Integrations

### Chat Models

Key chat model integrations include:

[BedrockChat](https://js.langchain.com/docs/integrations/chat/bedrock/):  (looking for information about Amazon Bedrock Chat integration in LangChain, setting up AWS Bedrock credentials, or implementing tool calling with Bedrock models) (Documentation for BedrockChat integration in LangChain covering installation, authentication with AWS, model instantiation, invocation methods, chaining with prompt templates, tool calling capabilities, and supported model features including token-level streaming and image input)
[ChatBedrockConverse](https://js.langchain.com/docs/integrations/chat/bedrock_converse/):  (needing to implement AWS Bedrock Converse chat models, looking for Amazon Bedrock conversational interface information, or configuring AWS chat models with tool calling capabilities) (Guide to using ChatBedrockConverse which provides a unified conversational interface for Amazon Bedrock foundation models, including setup instructions, authentication methods, model invocation examples, chaining techniques, and support for tool calling, structured output, and image inputs)
[ChatAnthropic](https://js.langchain.com/docs/integrations/chat/anthropic/):  (integrating Anthropic Claude models with LangChain, using citations with Anthropic models, or implementing prompt caching) (Documentation for the ChatAnthropic integration in LangChain.js, covering model setup, credentials, installation, invocation, content blocks, tool calling, custom headers, prompt caching, custom clients, and citation features)
[ChatCloudflareWorkersAI](https://js.langchain.com/docs/integrations/chat/cloudflare_workersai/):  (seeking to integrate with Cloudflare Workers AI chat models, implementing Cloudflare AI in LangChain applications, or building applications that need to run ML models on Cloudflare's network) (This page documents the ChatCloudflareWorkersAI integration for LangChain, covering model features, credentials setup, installation, instantiation, invocation examples, chaining with prompt templates, and API reference links. The integration supports image input and token-level streaming but not tool calling or structured output.)
[ChatCohere](https://js.langchain.com/docs/integrations/chat/cohere/):  (looking for information about using Cohere's chat models in LangChain.js, implementing RAG with Cohere, or using Cohere's web search connector) (Documentation for ChatCohere integration with LangChain.js including setup, credentials, model instantiation, basic invocation, chaining, RAG capabilities with documents as context, and web search connector functionality)
[ChatFireworks](https://js.langchain.com/docs/integrations/chat/fireworks/):  needing to integrate with Fireworks AI chat models, when implementing token-level streaming with Fireworks, or when setting up tool calling with Fireworks models. (Documentation for ChatFireworks integration in LangChain, including model features, setup instructions, API credentials, installation, basic usage examples, chaining with prompt templates, and references to advanced features like tool calling, structured output, and token usage tracking)
[ChatGoogleGenerativeAI](https://js.langchain.com/docs/integrations/chat/google_generativeai/):  (working with Google Generative AI/Gemini models, implementing tool calling with Google AI, setting up multimodal inputs or search retrieval tools) (Detailed guide to ChatGoogleGenerativeAI integration in LangChain, covering setup, authentication, instantiation, calling methods, safety settings, tool calling, built-in Google Search retrieval, code execution capabilities, context caching, and model limitations)
[ChatVertexAI](https://js.langchain.com/docs/integrations/chat/google_vertex_ai/):  (setting up Google Vertex AI chat models in LangChain, implementing tool calling with Google Search retrieval, or configuring context caching with Vertex AI) (ChatVertexAI provides integration with Google's Vertex AI service for accessing models like Gemini. The page covers setup, credentials, invocation examples, tool calling with Google Search retrieval, context caching, and model chaining. It details model features including tool calling, structured output, multimodal inputs, and token usage tracking.)
[ChatGroq](https://js.langchain.com/docs/integrations/chat/groq/):  needing to implement Groq's chat models in LangChain, when setting up API connections to Groq, or when configuring specific features like JSON mode or tool calling. (This page provides comprehensive documentation for integrating ChatGroq with LangChain JS/TS, including installation instructions, authentication setup, model instantiation, basic invocation patterns, JSON response formatting, chaining with prompts, and supported model features like tool calling and structured output.)
[ChatMistralAI](https://js.langchain.com/docs/integrations/chat/mistral/):  (finding documentation about integrating with MistralAI chat models in LangChain, troubleshooting MistralAI implementation, or exploring tool calling capabilities with MistralAI) (This page documents the ChatMistralAI integration in LangChain, covering setup requirements, instantiation, invocation methods, chaining with templates, tool calling implementation, custom hooks, and model features including token usage tracking and image input support)
[ChatOllama](https://js.langchain.com/docs/integrations/chat/ollama/):  (looking to integrate Ollama's open-source LLMs locally, implementing tool calling with Ollama models, or working with multimodal capabilities in Ollama) (ChatOllama integration documentation covering installation, model instantiation, invocation, chaining with prompt templates, tool calling functionality, JSON mode, structured output, and multimodal image input capabilities for local LLM deployment)
[ChatOpenAI](https://js.langchain.com/docs/integrations/chat/openai/):  (looking for details on ChatOpenAI integration, setting up OpenAI chat models, or implementing advanced OpenAI features like tool calling) (Comprehensive guide to using OpenAI chat models in LangChain, covering installation, authentication, basic usage, advanced features like tool calling, structured output, custom URLs, headers, audio capabilities, and the Responses API for built-in web/file search and conversation state management)
[ChatTogetherAI](https://js.langchain.com/docs/integrations/chat/togetherai/):  (implementing TogetherAI chat models in LangChain, using open-source models via Together API, or adding multimodal capabilities to LangChain projects) (Documentation for ChatTogetherAI integration in LangChain, covering installation, API key setup, model instantiation, invocation, chaining, and supported features including tool calling, structured output, JSON mode, and multimodal inputs)
[ChatXAI](https://js.langchain.com/docs/integrations/chat/xai/):  needing integration details for xAI's Grok model, implementing ChatXAI in JavaScript/TypeScript, or configuring tool calling with xAI. (Documentation for the ChatXAI integration in LangChain JS, covering model features, setup, credentials, installation, instantiation, invocation, chaining functionality, and API references for the xAI Grok model)

[chat model integrations](https://js.langchain.com/docs/integrations/chat/):  (looking for chat model integrations, comparing features of different chat model providers, or implementing a chat model in a LangChain application) (Comprehensive directory of chat model integrations in LangChain, including installation instructions, environment setup, and feature comparison across providers like OpenAI, Anthropic, Groq, Mistral, Google and many others with details about their capabilities like streaming, JSON mode, tool calling and multimodal support)

## How-to guides

Here you'll find answers to “How do I….?” types of questions.
These guides are _goal-oriented_ and _concrete_; they're meant to help you complete a specific task.
[Conceptual Guides](https://js.langchain.com/docs/concepts/):  needing to understand LangChain's conceptual framework, learning about key components, or finding definitions of LangChain terminology. (A comprehensive guide to LangChain concepts including chat models, messages, tools, tool calling, structured output, runnables, streaming, LCEL, retrieval, embedding models, vector stores, agents, and more with a detailed glossary of LangChain terms and components.)
[Tutorials](https://js.langchain.com/docs/tutorials/):  looking for LangChain tutorial resources, needing to get started with LLM application development, or searching for structured learning paths in LangChain. (The page contains a comprehensive collection of tutorials organized into three main sections: "Get started" tutorials covering basic components like chat models and prompts, "Orchestration" tutorials for building complex applications with LangGraph, and "LangSmith" tutorials for evaluation and monitoring of LLM applications.)
[API Reference](https://api.js.langchain.com/):  learning about LangChain.js framework, understanding its core components, or exploring how to build LLM-powered applications. (LangChain.js is a TypeScript framework for developing applications powered by language models. It offers composable tools and integrations across Model I/O, Retrieval, and Agents modules. The ecosystem includes multiple packages (@langchain/core, @langchain/community, langchain, LangGraph.js), supports various environments, and enables context-aware applications that can reason using LLMs.)

### Installation

[How to: install LangChain packages](https://js.langchain.com/docs/how_to/installation/):  (wanting to install LangChain in a JavaScript/TypeScript project, troubleshooting version conflicts, or determining compatible environments) (This page covers LangChain installation instructions for various environments (Node.js, Cloudflare, Vercel/Next.js, Deno, Browser), explains how to install core and integration packages, provides solutions for dependency conflicts with @langchain/core, and offers guidance for different module systems (ESM, CommonJS))

### Key features

This highlights functionality that is core to using LangChain.

[How to: get models to return structured output](https://js.langchain.com/docs/how_to/structured_output/):  (wanting to extract structured data from LLM outputs) (building applications that require specific JSON schema responses) (needing to parse LLM responses into a required format) (This page covers methods for getting structured data from models, including using the .withStructuredOutput() method with Zod or JSON schema, different output methods like function calling and JSON mode, handling raw outputs, and alternate approaches using prompting techniques with JsonOutputParser or custom parsing functions)
[How to: use chat models to call tools](https://js.langchain.com/docs/how_to/tool_calling/):  (needing to implement tool calling with chat models, integrating LLM-powered tools into applications, troubleshooting tool call formats) (Guide explaining how to use chat models to call tools, including binding LangChain tool objects to models, handling tool call responses, supporting different model providers like Anthropic/OpenAI/Mistral, and formatting model-specific tool schemas)
[How to: stream runnables](https://js.langchain.com/docs/how_to/streaming/):  (implementing streaming functionality with LangChain JS, troubleshooting streaming issues in LLM applications, or learning how to display partial outputs while an LLM generates responses) (Comprehensive guide on streaming in LangChain JS, covering two main approaches: .stream() for final output streaming and streamEvents() for intermediate steps streaming. Includes code examples for working with Chat Models, handling JSON outputs, filtering events, handling non-streaming components, and implementing streaming over HTTP connections.)
[How to: debug your LLM apps](https://js.langchain.com/docs/how_to/debugging/):  (debugging LLM applications, diagnosing errors in agent workflows, or trying to understand intermediate steps in complex chains) (This page covers debugging methods for LangChain applications including LangSmith tracing for visualizing execution flow, various verbose logging options (for components or specific tools), and custom callback handlers for monitoring intermediate steps and outputs)

### LangChain Expression Language (LCEL)

[`Runnable`](https://api.js.langchain.com/classes/langchain_core.runnables.Runnable.html/):  (learning about the core Runnable interface in LangChain.js, understanding how to compose components together, or implementing custom runnables) (Documentation for the abstract Runnable class in LangChain.js, which is the fundamental building block for creating composable components. Includes methods for invoking, streaming, batching, composition via pipe(), transformation, binding configurations, and adding retry/fallback logic.)

[**LCEL cheatsheet**](https://js.langchain.com/docs/how_to/lcel_cheatsheet/):  (needing a quick reference for LCEL primitives, composing runnables in LangChain, or debugging runnable execution patterns) (Comprehensive cheatsheet for LangChain Expression Language covering invoke, batch, stream methods, composing runnables with pipe/sequence, parallel execution, transforming functions to runnables, input/output management, error handling with fallbacks and retries, configuration options, and advanced patterns like streaming events and graph visualization)

[How to: chain runnables](https://js.langchain.com/docs/how_to/sequence/):  implementing sequential chain workflows, needing to connect different LLM components, or looking to create streamlined language processing pipelines. (This page explains how to chain runnables in LangChain Expression Language (LCEL) using the .pipe() method, allowing outputs from one component to flow as inputs to another, with examples showing how to connect prompt templates, chat models, and output parsers while maintaining efficient streaming and debugging capabilities.)
[How to: stream runnables](https://js.langchain.com/docs/how_to/streaming/):  (implementing streaming functionality with LangChain JS, troubleshooting streaming issues in LLM applications, or learning how to display partial outputs while an LLM generates responses) (Comprehensive guide on streaming in LangChain JS, covering two main approaches: .stream() for final output streaming and streamEvents() for intermediate steps streaming. Includes code examples for working with Chat Models, handling JSON outputs, filtering events, handling non-streaming components, and implementing streaming over HTTP connections.)
[How to: invoke runnables in parallel](https://js.langchain.com/docs/how_to/parallel/):  (needing to parallelize operations in LangChain, formatting outputs from multiple runnables, or creating computation graphs with fork and merge patterns) (Guide to using RunnableParallel/RunnableMap in LangChain to run multiple operations concurrently, format inputs/outputs between chain components, and create parallelized computation patterns with examples of joke/poem generation and retrieval augmentation)
[How to: attach runtime arguments to a runnable](https://js.langchain.com/docs/how_to/binding/):  needing to pass constant arguments to Runnables within a sequence, when implementing stop sequences to limit model output, or when configuring tool calling functionality. (This page explains how to use the Runnable.bind() method to attach runtime arguments to a Runnable component in LangChain, with examples showing how to add stop sequences to control model output length and how to attach OpenAI tools for function calling capabilities.)
[How to: run custom functions](https://js.langchain.com/docs/how_to/functions/):  (needing to integrate custom functions into LangChain, implementing streaming functionality, or wanting to use custom logic in LCEL chains) (This page explains how to use custom functions in LangChain Expression Language as RunnableLambdas, covering explicit creation with RunnableLambda.from(), automatic coercion in chains, passing run metadata to nested runs, and implementing streaming with generator functions that preserve streaming capabilities while transforming outputs)
[How to: pass through arguments from one step to the next](https://js.langchain.com/docs/how_to/passthrough/):  needing to pass data from one step to another in LangChain chains, wanting to preserve inputs for later steps, or constructing retrieval chains. (Explains RunnablePassthrough class which allows unchanged data to be passed to later steps in a chain, often used with RunnableParallel to combine modified and unmodified data streams. Includes code examples for basic passthrough and a RAG implementation.)
[How to: add values to a chain's state](https://js.langchain.com/docs/how_to/assign/):  learning how to add new values to a chain's state without overwriting existing data, when working with RunnablePassthrough.assign(), or when implementing streaming data patterns in LCEL chains. (This page explains how to use RunnablePassthrough.assign() to add new values to a chain's state while preserving original values, with examples showing both basic usage and advanced streaming implementations where values pass through as soon as they're available)
[How to: add message history](https://js.langchain.com/docs/how_to/message_history/):  (implementing conversation memory in chatbots, managing message history across multiple chat sessions, or converting a stateless LangChain application to support stateful conversations) (Guide explaining how to add message history to LangChain applications using LangGraph, including setup with various chat models, implementing persistence for both message-only and structured inputs, accessing stored conversation state, and techniques for manipulating message history)
[How to: route execution within a chain](https://js.langchain.com/docs/how_to/routing/):  (needing to implement conditional routing in a chain) (building complex chains where next steps depend on previous outputs) (creating decision-based workflow systems) (Guide to implementing routing in LangChain Expression Language with multiple approaches: using custom functions with RunnableLambda, routing by semantic similarity with embeddings, and using RunnableBranch for conditional execution, with complete code examples for each method)
[How to: add fallbacks](https://js.langchain.com/docs/how_to/fallbacks/):  (dealing with LLM API failures, handling context length limitations, or creating robust model fallback strategies) (Guide to implementing fallbacks in LangChain, covering how to handle LLM API errors by configuring alternative models, creating fallback sequences with different prompts for different model types, managing long inputs by falling back to models with larger context windows, and implementing quality-based fallbacks where simpler models are tried first before more capable ones)
[How to: cancel execution](https://js.langchain.com/docs/how_to/cancel_execution/):  wanting to abort a running LangChain process, needing to interrupt long-running chains, or implementing user cancellation features. (This page explains how to cancel the execution of LangChain runnables using AbortController and signal objects, including examples of cancellation with both standard invocation and streaming, with comparison of different cancellation approaches and their timing differences.)

### Components

These are the core building blocks you can use when building applications.

#### Prompt templates

[Prompt Templates](https://js.langchain.com/docs/concepts/prompt_templates/):  needing to understand prompt templates in LangChain, implementing different types of prompts, or working with message placeholders. (This page explains LangChain's prompt templates that help translate user inputs into instructions for language models. It covers three main types: String PromptTemplates for formatting single strings, ChatPromptTemplates for formatting lists of messages, and MessagesPlaceholder for inserting lists of messages at specific positions in prompts.)

[How to: use few shot examples](https://js.langchain.com/docs/how_to/few_shot_examples/):  (needing to implement few-shot learning in prompts, creating templates with examples, or using semantic similarity for example selection) (This page explains how to create few-shot prompts with FewShotPromptTemplate, format examples with PromptTemplate, and use SemanticSimilarityExampleSelector to dynamically select the most relevant examples based on input similarity)
[How to: use few shot examples in chat models](https://js.langchain.com/docs/how_to/few_shot_examples_chat/):  (needing to use few-shot examples with chat models, learning to implement dynamic example selection, or optimizing chat model prompting with examples) (Tutorial on implementing few-shot examples in chat models using FewShotChatMessagePromptTemplate, covering both fixed examples and dynamic example selection with SemanticSimilarityExampleSelector, with code examples for various chat models including OpenAI, Anthropic, Groq, and others)
[How to: partially format prompt templates](https://js.langchain.com/docs/how_to/prompts_partial/):  needing to populate a subset of prompt template variables early, when creating reusable prompt templates with dynamic components, or when integrating time-sensitive data into prompts. (This page explains how to partially format prompt templates in LangChain by providing only some of the required variables, creating a new template that expects the remaining variables. It covers two approaches: partial formatting with string values and partial formatting with functions that return string values (useful for dynamic data like current dates).)
[How to: compose prompts together](https://js.langchain.com/docs/how_to/prompts_composition/):  (needing to combine multiple prompt templates, reusing prompt components, or creating complex prompts with variable substitution) (The page explains how to compose different prompts together in LangChain, covering string prompt composition, chat prompt composition, and using PipelinePromptTemplate to create reusable prompt structures with formatted variables that get passed between templates)

#### Example selectors

[Example Selectors](https://js.langchain.com/docs/concepts/example_selectors/):  (learning about dynamic example selection for few-shot prompting, needing to understand example selector concepts, implementing context-aware example selection) (Overview of Example Selectors which dynamically choose and format examples for prompts, improving LLM performance through few-shot prompting rather than using hardcoded examples, with links to related how-to guides and API reference)

[How to: use example selectors](https://js.langchain.com/docs/how_to/example_selectors/):  (learning to select few-shot examples for prompts, implementing example selection logic, or customizing example selection criteria) (Explains how to use example selectors in LangChain to dynamically choose which few-shot examples to include in prompts, with code for creating custom example selectors that choose examples based on specific criteria like word length, and overview of built-in selectors including similarity-based and length-based options)
[How to: select examples by length](https://js.langchain.com/docs/how_to/example_selectors_length_based/):  understanding how to select examples based on input length or when managing context window limitations in prompt templates. (A guide to using LengthBasedExampleSelector which dynamically selects how many examples to include in a few-shot prompt based on input length - longer inputs get fewer examples to avoid exceeding context windows, with code examples showing implementation in LangChain JS)
[How to: select examples by semantic similarity](https://js.langchain.com/docs/how_to/example_selectors_similarity/):  (wanting to implement context-aware example selection, needing to select examples based on input similarity, or customizing vector store retrieval for few-shot learning) (Demonstrates how to use SemanticSimilarityExampleSelector to select examples based on cosine similarity for few-shot prompting, with options for loading from existing vector stores, filtering examples by metadata, and using custom vector store retrievers including maximal marginal relevance search)
[How to: select examples from LangSmith few-shot datasets](https://js.langchain.com/docs/how_to/example_selectors_langsmith/):  (needing to implement few-shot example selection from LangSmith datasets, wanting to leverage similarity search for examples, or building systems that require dynamic example selection) (This page explains how to use LangSmith datasets as few-shot example selectors by utilizing their built-in similarity search capabilities. It covers setting up LangSmith credentials, creating and indexing datasets, searching for similar examples, and demonstrates a complete implementation with tools for a math problem solving application.)

#### Chat models

[Chat Models](https://js.langchain.com/docs/concepts/chat_models/) are newer forms of language models that take messages in and output a message.

[How to: use chat models to call tools](https://js.langchain.com/docs/how_to/tool_calling/):  (needing to implement tool calling with chat models, integrating LLM-powered tools into applications, troubleshooting tool call formats) (Guide explaining how to use chat models to call tools, including binding LangChain tool objects to models, handling tool call responses, supporting different model providers like Anthropic/OpenAI/Mistral, and formatting model-specific tool schemas)
[How to: get models to return structured output](https://js.langchain.com/docs/how_to/structured_output/):  (wanting to extract structured data from LLM outputs) (building applications that require specific JSON schema responses) (needing to parse LLM responses into a required format) (This page covers methods for getting structured data from models, including using the .withStructuredOutput() method with Zod or JSON schema, different output methods like function calling and JSON mode, handling raw outputs, and alternate approaches using prompting techniques with JsonOutputParser or custom parsing functions)
[How to: cache model responses](https://js.langchain.com/docs/how_to/chat_model_caching/):  (looking to optimize API usage with chat models, implementing caching for LLM responses, or developing applications that need to reduce costs and latency) (Guide to implementing different caching solutions for chat model responses in LangChain, including in-memory caching, Redis, Upstash Redis, Vercel KV, Cloudflare KV, and local file system caching, with code examples for each implementation method)
[How to: create a custom chat model class](https://js.langchain.com/docs/how_to/custom_chat/):  (learning how to create custom chat model wrappers, implementing streaming outputs with chat models, or adding tracing capabilities to custom models) (A detailed guide on creating custom chat model classes by extending SimpleChatModel or BaseChatModel, including implementation of required methods like _call, _llmType, and optional _streamResponseChunks, with examples for basic and advanced implementations that support streaming, token tracking, and LangSmith tracing)
[How to: get log probabilities](https://js.langchain.com/docs/how_to/logprobs/):  (understanding how to access log probabilities from LLMs, analyzing token-level confidence, or implementing token probability analysis in applications) (Guide on retrieving log probabilities from OpenAI models, specifically showing how to enable logprobs, access token-level probability data in responses, stream probability data, and use topLogprobs parameter to view alternative token generations)
[How to: stream a response back](https://js.langchain.com/docs/how_to/chat_streaming/):  (learning how to implement token-by-token streaming with chat models, handling streaming outputs in LangChain applications, or implementing event-based streaming for more granular control) (Guide to streaming chat model responses in LangChain JS, covering basic streaming with different models (Groq, OpenAI, Anthropic, FireworksAI, MistralAI, VertexAI), implementing token-by-token output, and using streamEvents() for more granular event handling in larger LLM applications)
[How to: track token usage](https://js.langchain.com/docs/how_to/chat_token_usage_tracking/):  tracking token usage with chat models, monitoring API costs, or implementing usage metrics for LLM applications. (This page explains how to track token usage in LangChain JS when using chat models, covering multiple methods: retrieving usage data from AIMessage.usage_metadata, accessing provider-specific data in AIMessage.response_metadata, capturing usage data in streaming scenarios, and utilizing callbacks to monitor token consumption for supported providers like OpenAI and Anthropic.)
[How to: pass tool outputs to chat models](https://js.langchain.com/docs/how_to/tool_results_pass_to_model/):  (needing to pass tool call results back to chat models) (implementing tool call execution cycles) (building agents that use tool calling) (Guide on how to pass tool outputs to chat models, including defining tools, getting tool calls from chat models, executing tool functions, and passing the results back to the model as ToolMessages with matching tool_call_ids)
[How to: stream tool calls](https://js.langchain.com/docs/how_to/tool_streaming/):  (trying to implement streaming tool calls in LangChain, debugging tool call streaming issues, or building UIs with incremental tool call data) (This page explains how to stream tool calls in LangChain, showing how AIMessageChunk objects contain ToolCallChunk data with fields for tool name, args, id, and index. It demonstrates how to process these chunks, how they're merged when concatenated, and provides code examples for displaying and accumulating tool call information during streaming.)
[How to: few shot prompt tool behavior](https://js.langchain.com/docs/how_to/tools_few_shot/):  (learning how to add example messages for tool calling, implementing custom operator interpretation with LLMs, or improving tool calling behavior with few-shot examples) (Tutorial on how to use few-shot prompting with tool calling in LangChain, demonstrating how to send example AIMessages with ToolCalls and corresponding ToolMessages to teach models specific tool usage patterns through examples)
[How to: force a specific tool call](https://js.langchain.com/docs/how_to/tool_choice/):  (needing to force specific tool usage in LLM responses) (making an LLM use a tool even when not appropriate) (controlling tool selection behavior) (This page explains how to force LLMs to use specific tools by using the tool_choice parameter, with examples showing how to make a model call a specific named tool or use "any"/"required" to ensure some tool is used even when not contextually appropriate)
[How to: disable parallel tool calling](https://js.langchain.com/docs/how_to/tool_calling_parallel/):  (wanting to disable parallel tool calling with OpenAI models, needing to constrain a model to only call one tool at a time, or troubleshooting tool calling behavior) (This page explains how to disable parallel tool calling in OpenAI models by setting the "parallel_tool_calls" parameter to false, either when binding tools to a model or at runtime through invoke parameters. It provides code examples showing the difference between default parallel behavior and constrained single tool calling behavior.)
[How to: init any model in one line](https://js.langchain.com/docs/how_to/chat_models_universal_init/):  (looking to initialize chat models using a single line of code, creating configurable model instances, or building applications that allow users to switch between different LLM providers) (How to use the initChatModel() helper method to easily initialize different chat model integrations with inferred providers, configurable fields, and default values, enabling runtime model switching and declarative use with tools and structured outputs)

#### Messages

[Messages](https://js.langchain.com/docs/concepts/###message-types):  exploring LangChain's core concepts, understanding the framework's architecture, or seeking clarification on terminology and components. (Conceptual guide that explains key LangChain concepts including chat models, messages, tools, memory, LCEL, retrieval, embedding models, vectorstores, agents, prompt templates, output parsers, streaming, callbacks, and evaluation. Includes a glossary of essential terms and explains how components relate to each other in the LangChain ecosystem.)

[How to: trim messages](https://js.langchain.com/docs/how_to/trim_messages/):  (needing to manage message context lengths, handling long conversation histories, or optimizing token usage in chat applications) (Guide to trimming message lists to fit model context windows using strategies like "last" or "first" tokens, with options for preserving system messages and customizing token counting functions)
[How to: filter messages](https://js.langchain.com/docs/how_to/filter_messages/):  (filtering chat message history by specific criteria) (determining which messages to pass to a model in complex chains) (needing to clean up message context for LLM calls) The page explains how to use the filterMessages utility function to filter chat message lists by type, id, or name, both imperatively and declaratively within LangChain chains, with examples showing how to include/exclude messages based on different criteria.
[How to: merge consecutive messages of the same type](https://js.langchain.com/docs/how_to/merge_message_runs/):  (needing to merge consecutive chat messages of the same type, fixing errors with models that don't support consecutive same-type messages, or integrating message merging within a chain) (Tutorial on using the mergeMessageRuns utility function from @langchain/core to combine consecutive messages of the same type, with examples of both imperative usage and declarative chaining implementations)

#### LLMs

[LLMs](https://js.langchain.com/docs/concepts/text_llms/):  needing to understand older string-based language models, when distinguishing between string-in/string-out LLMs and chat models, or when looking for information about BaseLLM implementations. This page explains that string-in, string-out LLMs are older language model implementations that take a string input and return a string output, implement the BaseLLM interface, and have been largely superseded by Chat Models which most providers now use as their primary interface.

[How to: cache model responses](https://js.langchain.com/docs/how_to/llm_caching/):  needing to implement caching for LLMs to save money and API calls, or when looking to optimize application speed by reducing duplicate API calls. (This page explains how to cache model responses in LangChain, covering in-memory caching, as well as distributed caching options including Momento, Redis, Upstash Redis, Vercel KV, Cloudflare KV, and local file system caching, with code examples for each implementation method.)
[How to: create a custom LLM class](https://js.langchain.com/docs/how_to/custom_llm/):  creating custom LLM wrappers, implementing streaming capabilities for custom LLMs, or extending LangChain's LLM functionality. (Guide on creating custom LLM classes by extending the LLM base class, implementing required methods like _call and _llmType, adding optional streaming capabilities through _streamResponseChunks, and working with advanced implementations using BaseLLM for additional metadata and token tracking.)
[How to: stream a response back](https://js.langchain.com/docs/how_to/streaming_llm/):  (implementing streaming responses from LLMs, needing token-by-token output, or integrating streaming callbacks in LangChain apps) (This page explains how to implement streaming responses from LLMs in LangChain, covering two main approaches: using the .stream() method to get a readable stream of tokens, and using callback handlers with the handleLLMNewToken event. It includes code examples for both methods using OpenAI's implementation.)
[How to: track token usage](https://js.langchain.com/docs/how_to/llm_token_usage_tracking/):  tracking token usage for LLM calls, monitoring API consumption, or implementing usage-based cost tracking. (This page demonstrates how to track token usage for LLM calls using callbacks, specifically showing an OpenAI implementation that provides detailed token metrics including prompt tokens, completion tokens, and total tokens for each LLM invocation.)

#### Output parsers

[Output Parsers](https://js.langchain.com/docs/concepts/output_parsers/):  needing to parse structured outputs from language models, when handling JSON/XML responses from LLMs, or when selecting appropriate output parsers for specific data formats. (This page describes various output parsers in LangChain that transform LLM outputs into structured formats. It covers JSON, XML, CSV, OutputFixing, Datetime, and Structured parsers, with details about their streaming support, format instructions, and use cases. The page notes that function/tool calling is recommended over output parsing when possible.)

[How to: use output parsers to parse an LLM response into structured format](https://js.langchain.com/docs/how_to/output_parser_structured/):  (needing to extract structured data from LLM outputs) (working with models that don't natively support structured outputs) (building applications that require validated, typed responses) (Guide to using output parsers like StructuredOutputParser and JsonOutputParser to convert LLM text responses into structured objects with validation, including code examples for defining schemas with Zod, handling validation errors, and implementing streaming for structured outputs)
[How to: parse JSON output](https://js.langchain.com/docs/how_to/output_parser_json/):  (needing to parse JSON outputs from LLMs, implementing structured data extraction, or handling model responses that should conform to specific JSON schemas) (Guide on using JsonOutputParser to prompt language models to return valid JSON and parse their responses, including setup with different model providers, code examples that demonstrate basic usage and streaming capabilities, and techniques for guiding models to format their outputs according to specified schemas)
[How to: parse XML output](https://js.langchain.com/docs/how_to/output_parser_xml/):  (needing to parse XML output from LLMs, working with models that generate XML better than JSON, wanting structured data in XML format) (Guide to using XMLOutputParser to prompt language models for well-formed XML output and convert it to structured data, featuring examples with Claude model, setting custom XML tags, and adding format instructions to prompts)
[How to: try to fix errors in output parsing](https://js.langchain.com/docs/how_to/output_parser_fixing/):  encountering formatting errors in LLM outputs, when implementing error-handling for parsing structured data, or when needing to validate and fix JSON/structured outputs. (This page explains how to use OutputFixingParser to handle LLM output formatting errors by wrapping another parser (like StructuredOutputParser) and using a secondary LLM to correct formatting mistakes when the primary parser fails to parse the output according to the specified schema.)

#### Document loaders

[Document Loaders](https://js.langchain.com/docs/concepts/document_loaders/):  needing to load documents from external sources, understanding document loader concepts, or implementing data ingestion pipelines. (Document loaders are components that load data from various sources into Document objects for LLM processing. The page explains the loader interface with .load() and .lazy_load() methods, provides code examples, and points to numerous integrations with services like Slack, Notion, and Google Drive.)

[How to: load CSV data](https://js.langchain.com/docs/how_to/document_loader_csv/):  (needing to load and parse CSV data, working with document loaders in LangChain, converting tabular data into documents) (Tutorial showing how to use the CSVLoader in LangChain to load CSV files as documents, with examples of extracting all columns or a single column, including installation instructions for d3-dsv dependency)
[How to: load data from a directory](https://js.langchain.com/docs/how_to/document_loader_directory/):  looking for guidance on loading multiple document types from a directory, when implementing document processing pipelines, or when needing to handle mixed file formats in data loading. (This page demonstrates how to use the DirectoryLoader to load documents from a directory with different file types (.json, .jsonl, .txt, .csv) by specifying appropriate loaders for each file extension, allowing for batch processing of multiple documents in a single operation.)
[How to: load PDF files](https://js.langchain.com/docs/how_to/document_loader_pdf/):  needing to load PDF documents into LangChain, implementing document parsing features, or troubleshooting PDF handling in LangChain applications. (The page explains how to use PDFLoader in LangChain to load PDF files, covering installation of pdf-parse, basic usage with one document per page or per file, using custom pdfjs builds, and eliminating extra spaces when parsing PDFs.)
[How to: write a custom document loader](https://js.langchain.com/docs/how_to/document_loader_custom/):  needing to create custom document loaders, loading data from non-standard sources, or extending existing loader functionality. (The page explains how to create custom document loaders in LangChain by subclassing one of three base classes: BaseDocumentLoader for general document loading, TextLoader for parsing text files, or BufferLoader for handling binary files. Each approach requires implementing specific abstract methods like load() or parse().)
[How to: load HTML data](https://js.langchain.com/docs/how_to/document_loader_html/):  needing to process HTML documents, working with web content in LangChain, or setting up Unstructured API integration. (Guide for loading HTML files into LangChain Document objects using the Unstructured API, including installation instructions, setup requirements for Docker container and API keys, and code example of loading HTML with UnstructuredLoader)
[How to: load Markdown data](https://js.langchain.com/docs/how_to/document_loader_markdown/):  looking to load and process Markdown files or when implementing document loaders for Markdown content in LangChain. (This page explains how to use UnstructuredLoader to load Markdown documents into LangChain Document objects, including basic setup with API keys, simple usage examples, and retaining document elements from Markdown structure.)

#### Text splitters

[Text Splitters](https://js.langchain.com/docs/concepts/text_splitters/):  (looking for document chunking strategies, implementing text splitting for RAG applications, or overcoming model context limitations) (Comprehensive guide to text splitters in LangChain, covering why document splitting is necessary and four main approaches: length-based splitting (character/token), text-structure based (recursive splitting), document-structure based (for HTML/markdown/code), and semantic meaning based splitting. Includes code examples and explanations of when to use each approach.)

[How to: recursively split text](https://js.langchain.com/docs/how_to/recursive_text_splitter/):  seeking to implement text chunking for RAG systems, needing to split texts while preserving semantic meaning, or implementing document processing pipelines. (Guide to using RecursiveCharacterTextSplitter which splits text based on a hierarchy of characters, keeping semantically related pieces together. Shows how to create text chunks with customizable parameters like chunkSize, chunkOverlap, and separators, with examples of splitting raw text and Documents.)
[How to: split by character](https://js.langchain.com/docs/how_to/character_text_splitter/):  needing to split text documents by character, implementing text chunking, or working with character-based text splitters in LangChain. (This page explains how to use CharacterTextSplitter in LangChain to divide text by specified character sequences (defaulting to "\n\n"). It demonstrates creating text chunks with methods like .splitText() and .createDocuments(), shows how to set chunk size and overlap parameters, and explains how metadata can be propagated to output chunks.)
[How to: split code](https://js.langchain.com/docs/how_to/code_splitter/):  (looking to split programming code into manageable chunks) (developing text splitters for code documentation) (processing code for retrieval systems) (Guide for using RecursiveCharacterTextSplitter with programming languages including JavaScript, Python, HTML, Markdown, LaTeX, Solidity, and PHP, showing how to leverage language-specific separators to intelligently split code into chunks while preserving semantic meaning)
[How to: split by tokens](https://js.langchain.com/docs/how_to/split_by_token/):  trying to manage token limits for LLMs, implementing text chunking by tokens, or working with TokenTextSplitter in LangChain. (This page explains how to split text by tokens using the TokenTextSplitter class from LangChain, which leverages js-tiktoken to count tokens in the same way OpenAI models do. It includes a code example showing how to initialize the splitter with chunkSize and chunkOverlap parameters, with a warning about potential Unicode character issues when splitting languages like Chinese or Japanese.)

#### Embedding models

[Embedding Models](https://js.langchain.com/docs/concepts/embedding_models/):  (needing to understand embedding models for vector search, implementing retrieval systems, or comparing similarity between text samples) (Conceptual overview of embedding models explaining how they transform text into numerical vectors, their historical evolution from BERT to modern implementations, how to use LangChain's embedding interface methods, and common similarity metrics like cosine similarity for comparing embeddings)

[How to: embed text data](https://js.langchain.com/docs/how_to/embed_text/):  looking for ways to create vector representations of text data, when implementing semantic search functionality, or when needing to convert text to embeddings for vector databases. (Guide on using embedding models in LangChain.js to convert text into vector representations, with examples using OpenAIEmbeddings class through embedQuery for single texts and embedDocuments for batches, includes code samples and integrations with text embedding providers)
[How to: cache embedding results](https://js.langchain.com/docs/how_to/caching_embeddings/):  searching for ways to optimize embedding operations, implementing caching for vector embeddings, or reducing API costs for embedding requests. (This page explains how to cache embedding results using CacheBackedEmbeddings to avoid recomputing embeddings for the same text. It covers implementation with in-memory storage and Redis, demonstrates significant performance improvements with cached embeddings, and explains how to set up namespaces to avoid collisions between different embedding models.)

#### Vector stores

[Vector stores](https://js.langchain.com/docs/concepts/#vectorstores):  seeking conceptual understanding of LangChain framework, when needing a high-level overview of AI application components, or when looking for definitions of key LangChain terms. (Comprehensive conceptual guide to LangChain explaining its key components including chat models, messages, tools, memory, retrieval, agents, prompt templates, and other foundational concepts that make up the framework's architecture. Contains a detailed glossary of LangChain terminology and links to more detailed resources for each concept.)

[How to: create and query vector stores](https://js.langchain.com/docs/how_to/vectorstores/):  needing to create vector stores for document retrieval, when choosing the right vector store implementation for a specific use case, or when implementing RAG systems. (This page explains how to create and query vector stores in LangChain using different implementations - from creating indexes with MemoryVectorStore, loading documents into vector stores, creating vector stores from text chunks directly, and provides guidance on which vector store implementation to choose based on specific requirements and environments.)

#### Retrievers

[Retrievers](https://js.langchain.com/docs/concepts/retrievers/):  (needing to understand how retrievers work in LangChain, implementing RAG systems, or comparing different retrieval approaches) (Explains retrievers in LangChain which provide a standard interface for retrieving documents using natural language queries. Covers the basic retriever interface, common types including search APIs, relational/graph databases, lexical search, and vector stores. Discusses advanced patterns like ensemble retrievers and source document retention strategies including ParentDocument and MultiVector retrievers.)

[How to: use a vector store to retrieve data](https://js.langchain.com/docs/how_to/vectorstore_retriever/):  (needing to implement RAG with vector store retrieval, creating question-answering systems over documents, learning how to convert vector stores to retrievers) (Tutorial on converting vector stores into retrievers, showing a complete RAG implementation including text splitting, embedding, retrieval, and question answering with a formatted example using MemoryVectorStore, OpenAI embeddings, and a chain that processes retrieved context)
[How to: generate multiple queries to retrieve data for](https://js.langchain.com/docs/how_to/multiple_queries/):  (looking to improve RAG retrieval effectiveness, implementing multiple-query strategies for vectorstores, or needing to generate diverse perspectives on a query) (The page explains how to use MultiQueryRetriever to automatically generate multiple perspectives on a user query, overcoming limitations of distance-based vector retrieval by asking the same question in different ways and merging the unique results, with examples for basic implementation and customization options including support for non-English languages)
[How to: use contextual compression to compress the data retrieved](https://js.langchain.com/docs/how_to/contextual_compression/):  (looking to improve retrieval quality by filtering irrelevant information, optimizing RAG systems, or reducing token usage in document retrieval) (This page explains contextual compression for retrieval, showing how to compress documents based on query relevance using LLMChainExtractor, EmbeddingsFilter, and DocumentCompressorPipeline to extract only relevant content from retrieved documents or filter out irrelevant documents completely)
[How to: write a custom retriever class](https://js.langchain.com/docs/how_to/custom_retriever/):  needing to create a custom retriever, implementing specialized document retrieval, or extending LangChain's retrieval capabilities. (This page explains how to create a custom retriever by extending the BaseRetriever class, implementing the _getRelevantDocuments method, and shows a complete code example of a static document retriever with instructions on how to use it.)
[How to: combine the results from multiple retrievers](https://js.langchain.com/docs/how_to/ensemble_retriever/):  needing to combine multiple retrievers, implementing hybrid search strategies, or improving retrieval performance through ensembling. (This page explains how to use EnsembleRetriever to combine results from multiple retrievers using Reciprocal Rank Fusion algorithm, with a practical example combining a custom keyword-based retriever with a vector store retriever, demonstrating how to leverage complementary strengths of different retrieval approaches.)
[How to: generate multiple embeddings per document](https://js.langchain.com/docs/how_to/multi_vector/):  seeking to improve RAG retrieval performance or needing to generate multiple vector representations of documents. (Guide explaining how to use MultiVectorRetriever to create multiple embeddings per document through three methods: splitting documents into smaller chunks, creating document summaries, and generating hypothetical questions about document content. Includes code samples for each approach.)
[How to: retrieve the whole document for a chunk](https://js.langchain.com/docs/how_to/parent_document_retriever/):  (looking to implement RAG with proper context preservation, trying to balance document chunk size for retrieval, or needs to retrieve full documents from vector embeddings of smaller chunks) (Guide showing how ParentDocumentRetriever resolves the conflict between small chunks for accurate embeddings and larger contexts for completeness by storing small chunks but retrieving parent documents, with examples for basic usage, score thresholds, contextual headers, and reranking)
[How to: generate metadata filters](https://js.langchain.com/docs/how_to/self_query/):  (needing to create a retrieval system that can interpret natural language queries to filter document metadata, implementing self-querying retrievers, or working with vector stores that need both semantic and metadata filtering). (A comprehensive guide to implementing self-querying retrievers that can transform natural language questions into structured queries with metadata filters, including code examples for creating and using a SelfQueryRetriever with vector stores, defining attribute information for filtering, and testing various query types against document collections.)
[How to: create a time-weighted retriever](https://js.langchain.com/docs/how_to/time_weighted_vectorstore/):  needing to implement time-based document retrieval, when building memory systems that prioritize recently accessed information, or when creating RAG systems with temporal awareness. (This page explains how to create a TimeWeightedVectorStoreRetriever that combines semantic similarity with time decay to score and retrieve documents, with documents that were accessed more recently receiving higher scores based on a configurable decay rate parameter.)
[How to: reduce retrieval latency](https://js.langchain.com/docs/how_to/reduce_retrieval_latency/):  (looking for ways to improve RAG retrieval speed, implementing Matryoshka Representation Learning for search optimization, or reducing latency in vector search operations) (Explains how to reduce retrieval latency using Adaptive Retrieval with the MatryoshkaRetriever, which performs a two-step search process: first using lower-dimensional embeddings for fast initial retrieval, then re-ranking top results with full high-dimensional embeddings for better accuracy, with code example using OpenAI embeddings and Chroma vector store)

#### Indexing

Indexing is the process of keeping your vectorstore in-sync with the underlying data source.

[How to: reindex data to keep your vectorstore in-sync with the underlying data source](https://js.langchain.com/docs/how_to/indexing/):  (needing to maintain sync between vector databases and source documents) (implementing RAG systems that need to handle document updates or deletions) (optimizing embedding costs by avoiding redundant processing) (Guide on using the LangChain indexing API to keep vector stores synchronized with source documents. Covers three deletion modes (None, Incremental, Full), explains implementation details, provides code examples with PostgresRecordManager, and demonstrates how to handle document updates, avoid duplicates, and optimize embedding operations.)

#### Tools

LangChain [Tools](https://js.langchain.com/docs/concepts/tools/) contain a description of the tool (to pass to the language model) as well as the implementation of the function to call.

[How to: define a custom tool](https://js.langchain.com/docs/how_to/custom_tools/):  (needing to create custom tools for LLM agents, implementing tool calling functionality, or adding artifact return capability to tools) (Comprehensive guide on creating custom LangChain tools using different methods: StructuredToolParams schema for basic definition, tool function wrapper for executable tools, DynamicStructuredTool for complex schema-based tools, and DynamicTool for single-input tools. Also covers how to return both content for models and artifacts for downstream usage using response_format parameter.)
[How to: use built-in tools and toolkits](https://js.langchain.com/docs/how_to/tools_builtin/):  (looking for guidance on using tools in LangChain, creating agent systems that can interact with external tools, or exploring built-in tool capabilities) (Overview of LangChain tools functionality, explaining the five key components of tools: name, description, input schema, function to call, and returnDirect flag. Includes examples of using built-in tools like WikipediaQueryRun, how to interpret tool properties, and introduction to toolkits which are collections of related tools.)
[How to: use chat models to call tools](https://js.langchain.com/docs/how_to/tool_calling/):  (needing to implement tool calling with chat models, integrating LLM-powered tools into applications, troubleshooting tool call formats) (Guide explaining how to use chat models to call tools, including binding LangChain tool objects to models, handling tool call responses, supporting different model providers like Anthropic/OpenAI/Mistral, and formatting model-specific tool schemas)
[How to: pass tool outputs to chat models](https://js.langchain.com/docs/how_to/tool_results_pass_to_model/):  (needing to pass tool call results back to chat models) (implementing tool call execution cycles) (building agents that use tool calling) (Guide on how to pass tool outputs to chat models, including defining tools, getting tool calls from chat models, executing tool functions, and passing the results back to the model as ToolMessages with matching tool_call_ids)
[How to: few shot prompt tool behavior](https://js.langchain.com/docs/how_to/tools_few_shot/):  (learning how to add example messages for tool calling, implementing custom operator interpretation with LLMs, or improving tool calling behavior with few-shot examples) (Tutorial on how to use few-shot prompting with tool calling in LangChain, demonstrating how to send example AIMessages with ToolCalls and corresponding ToolMessages to teach models specific tool usage patterns through examples)
[How to: pass run time values to tools](https://js.langchain.com/docs/how_to/tool_runtime/):  (needing to pass user-specific values to tools at runtime, implementing secure tools that need contextual data not controlled by the LLM, or debugging tool authentication issues) (Shows two approaches for passing runtime values to tools: using context variables with getContextVariable/setContextVariable for accessing values across scope boundaries, or dynamically generating tools at request time with bound parameters like userId)
[How to: handle tool errors](https://js.langchain.com/docs/how_to/tools_error/):  handling errors in LLM tool calls, implementing fallback strategies, or creating more robust tool-calling chains. (This page explains how to implement error handling when tools fail during LLM invocation, including using try/catch wrappers to return helpful error messages and implementing fallback chains with more capable models when tool calls fail with the primary model.)
[How to: force a specific tool call](https://js.langchain.com/docs/how_to/tool_choice/):  (needing to force specific tool usage in LLM responses) (making an LLM use a tool even when not appropriate) (controlling tool selection behavior) (This page explains how to force LLMs to use specific tools by using the tool_choice parameter, with examples showing how to make a model call a specific named tool or use "any"/"required" to ensure some tool is used even when not contextually appropriate)
[How to: disable parallel tool calling](https://js.langchain.com/docs/how_to/tool_calling_parallel/):  (wanting to disable parallel tool calling with OpenAI models, needing to constrain a model to only call one tool at a time, or troubleshooting tool calling behavior) (This page explains how to disable parallel tool calling in OpenAI models by setting the "parallel_tool_calls" parameter to false, either when binding tools to a model or at runtime through invoke parameters. It provides code examples showing the difference between default parallel behavior and constrained single tool calling behavior.)
[How to: access the `RunnableConfig` object within a custom tool](https://js.langchain.com/docs/how_to/tool_configure/):  needing to access configuration within custom tools, when building complex tools that require configuration passing, or when creating nested runnable invocations within tools. (A guide explaining how to access the RunnableConfig object in custom tools, specifically focusing on accessing it as the second parameter in functions created with the tool() helper method, allowing configuration properties to be used within the tool implementation)
[How to: stream events from child runs within a custom tool](https://js.langchain.com/docs/how_to/tool_stream_events/):  (looking for ways to stream events from tools in chains, debugging missing event callbacks in custom tools, or implementing streaming in complex nested runnable structures) (Tutorial on properly passing RunnableConfig to child runnables within tools to enable event streaming and callbacks, with clear examples showing how to fix broken streaming in custom tools by explicitly propagating the config parameter)
[How to: return artifacts from a tool](https://js.langchain.com/docs/how_to/tool_artifacts/):  (looking to return additional data beyond a standard tool response) (needing to selectively expose some tool outputs to models while preserving full data for downstream components) (How to create tools that return both content for the model and separate artifacts for downstream processing, including how to define such tools with the proper response format, invoke them with ToolCall, and integrate them with chat models)
[How to: convert Runnables to tools](https://js.langchain.com/docs/how_to/convert_runnable_to_tool/):  (needing to convert Runnables to Tools for agent applications, implementing Tool calling in LangChain, creating custom Tools from existing Runnables) (The page explains how to convert LangChain Runnables into Tools using the asTool() method, including how to specify names, descriptions, and argument schemas. It demonstrates converting both string-input Runnables and more complex Runnables like retrievers and RAG chains into Tools that can be used with agents.)
[How to: add ad-hoc tool calling capability to models](https://js.langchain.com/docs/how_to/tools_prompting/):  (trying to implement tool calling with LLMs that don't have built-in tool calling APIs, customizing tool invocation flows, or building multi-tool selection chains) (Tutorial showing how to add ad-hoc tool calling to LLMs using text prompting rather than specialized APIs, covering tool creation, prompt engineering, parsing model outputs as JSON, invoking tools based on model decisions, handling multiple tools, and returning both inputs and outputs from tool executions)

#### Agents

:::note

[LangGraph.js](https://langchain-ai.github.io/langgraphjs/):  (needing an introduction to LangGraph.js, wanting to understand its capabilities for agent orchestration, or exploring how to build controllable AI agents) (LangGraph.js is a low-level orchestration framework for building controllable AI agents with features like reliability, extensibility, and streaming support. It enables custom agent architectures, long-term memory, and human-in-the-loop integration. The page introduces its ecosystem, deployment options via LangGraph Platform, and provides links to additional resources like tutorials and templates.)

:::

[How to: use legacy LangChain Agents (AgentExecutor)](https://js.langchain.com/docs/how_to/agent_executor/):  (needing to implement legacy AgentExecutor-based agents, understanding basic agent setup with tools, migrating from older agent patterns) (Tutorial showing how to create legacy LangChain Agents using AgentExecutor with tools like search and retrieval, including how to add memory for conversation history and when to consider upgrading to LangGraph for more advanced agent capabilities)
[How to: migrate from legacy LangChain agents to LangGraph](https://js.langchain.com/docs/how_to/migrate_agent/):  (migrating from legacy LangChain agents to LangGraph agents, implementing agent workflows, or troubleshooting agent transitions) (A comprehensive guide showing how to convert AgentExecutor implementations to LangGraph, covering basic usage, prompt templates, memory management, step iteration, intermediate steps tracking, recursion limits, and error handling with practical code examples)

#### Callbacks

[Callbacks](https://js.langchain.com/docs/concepts/callbacks/):  learning about LangChain's callback system, implementing event tracking in LLM applications, or customizing monitoring for chains and tools. (Comprehensive guide to LangChain's callback system explaining how to hook into various stages of LLM applications. Covers callback events, callback handlers, and how to pass callbacks at both constructor and request time. Includes details on event types like model starts/ends, token generation, chain events, tool events, and retriever events.)

[How to: pass in callbacks at runtime](https://js.langchain.com/docs/how_to/callbacks_runtime/):  needing to dynamically add callback handlers at runtime, when working with nested LangChain objects, or when implementing logging/tracing in complex chains. (This page explains how to pass callbacks at runtime using the callbacks keyword argument, which allows handlers to be issued by all nested objects in a chain's execution without having to manually attach them to each component. Examples show using ConsoleCallbackHandler with a simple prompt-model chain.)
[How to: attach callbacks to a module](https://js.langchain.com/docs/how_to/callbacks_attach/):  needing to attach callbacks to LangChain modules or when wanting to reuse callbacks across multiple chain executions. (This page explains how to use the .withConfig() method to attach callbacks to a runnable module in LangChain, allowing you to trace execution without repeatedly passing callback handlers at each invocation. It includes a complete code example using ConsoleCallbackHandler that demonstrates how callbacks are propagated through nested module runs.)
[How to: pass callbacks into a module constructor](https://js.langchain.com/docs/how_to/callbacks_constructor/):  needing to implement callbacks at object creation time, or when troubleshooting callback execution scope in LangChain modules. (This page demonstrates how to pass callback handlers in a constructor to LangChain modules, allowing callback events to be limited to that specific instance and any nested runs. Includes a code example using ConsoleCallbackHandler with a ChatAnthropic model.)
[How to: create custom callback handlers](https://js.langchain.com/docs/how_to/custom_callbacks/):  needing to implement custom callback handling for LangChain operations, monitoring specific model events, or tracking operation progress. (This page explains how to create custom callback handlers in LangChain, including how to define handlers for specific events like model starts and token generation, with a practical code example showing implementation of custom callbacks for a chat model that logs the start of a model run and streams tokens.)
[How to: await callbacks in serverless environments](https://js.langchain.com/docs/how_to/callbacks_serverless/):  needing to implement callbacks in serverless environments, or when experiencing issues with callback timing in AWS Lambda or Cloudflare Workers. (This page explains how to properly handle callbacks in serverless environments by setting LANGCHAIN_CALLBACKS_BACKGROUND to "false" or using awaitAllCallbacks() to ensure callbacks complete before execution continues, with code examples showing both background and blocking behavior.)
[How to: dispatch custom callback events](https://js.langchain.com/docs/how_to/callbacks_custom_events/):  (learning how to create custom events within LangChain runnables) (tracking progress of long-running tools) (communicating intermediate steps to end users) (How to use the dispatchCustomEvent API to emit custom events from within LangChain Runnables that can be consumed via the Stream Events API or custom callback handlers, with code examples for both Node.js and web environments)

#### Custom

All of LangChain components can easily be extended to support your own versions.

[How to: create a custom chat model class](https://js.langchain.com/docs/how_to/custom_chat/):  (learning how to create custom chat model wrappers, implementing streaming outputs with chat models, or adding tracing capabilities to custom models) (A detailed guide on creating custom chat model classes by extending SimpleChatModel or BaseChatModel, including implementation of required methods like _call, _llmType, and optional _streamResponseChunks, with examples for basic and advanced implementations that support streaming, token tracking, and LangSmith tracing)
[How to: create a custom LLM class](https://js.langchain.com/docs/how_to/custom_llm/):  creating custom LLM wrappers, implementing streaming capabilities for custom LLMs, or extending LangChain's LLM functionality. (Guide on creating custom LLM classes by extending the LLM base class, implementing required methods like _call and _llmType, adding optional streaming capabilities through _streamResponseChunks, and working with advanced implementations using BaseLLM for additional metadata and token tracking.)
[How to: write a custom retriever class](https://js.langchain.com/docs/how_to/custom_retriever/):  needing to create a custom retriever, implementing specialized document retrieval, or extending LangChain's retrieval capabilities. (This page explains how to create a custom retriever by extending the BaseRetriever class, implementing the _getRelevantDocuments method, and shows a complete code example of a static document retriever with instructions on how to use it.)
[How to: write a custom document loader](https://js.langchain.com/docs/how_to/document_loader_custom/):  needing to create custom document loaders, loading data from non-standard sources, or extending existing loader functionality. (The page explains how to create custom document loaders in LangChain by subclassing one of three base classes: BaseDocumentLoader for general document loading, TextLoader for parsing text files, or BufferLoader for handling binary files. Each approach requires implementing specific abstract methods like load() or parse().)
[How to: create custom callback handlers](https://js.langchain.com/docs/how_to/custom_callbacks/):  needing to implement custom callback handling for LangChain operations, monitoring specific model events, or tracking operation progress. (This page explains how to create custom callback handlers in LangChain, including how to define handlers for specific events like model starts and token generation, with a practical code example showing implementation of custom callbacks for a chat model that logs the start of a model run and streams tokens.)
[How to: define a custom tool](https://js.langchain.com/docs/how_to/custom_tools/):  (needing to create custom tools for LLM agents, implementing tool calling functionality, or adding artifact return capability to tools) (Comprehensive guide on creating custom LangChain tools using different methods: StructuredToolParams schema for basic definition, tool function wrapper for executable tools, DynamicStructuredTool for complex schema-based tools, and DynamicTool for single-input tools. Also covers how to return both content for models and artifacts for downstream usage using response_format parameter.)
[How to: dispatch custom callback events](https://js.langchain.com/docs/how_to/callbacks_custom_events/):  (learning how to create custom events within LangChain runnables) (tracking progress of long-running tools) (communicating intermediate steps to end users) (How to use the dispatchCustomEvent API to emit custom events from within LangChain Runnables that can be consumed via the Stream Events API or custom callback handlers, with code examples for both Node.js and web environments)

#### Generative UI

[How to: build an LLM generated UI](https://js.langchain.com/docs/how_to/generative_ui/):  needing to create interactive UI elements from LLM outputs or implementing tool-based UI generation in Next.js applications. (Guide for building generative UIs with LangChain.js, covering how to use createRunnableUI and streamRunnableUI utilities to yield React elements inside runnables and tool calls, with code examples for implementing tool calling agents that render interactive UI components during streaming.)
[How to: stream agentic data to the client](https://js.langchain.com/docs/how_to/stream_agent_client/):  (implementing agent streaming in a web app, setting up real-time agent execution feedback, using React Server Components with LangChain) (Guide for streaming agent execution data to web clients using LangChain with React Server Components, including setup code examples for creating server actions that stream event data from AgentExecutor and implementing client-side code to consume and display that streamed data)
[How to: stream structured output to the client](https://js.langchain.com/docs/how_to/stream_tool_client/):  (setting up streaming of structured tool outputs to clients, implementing React Server Components with LangChain, integrating tool calling with streaming responses) (Guide for implementing streaming of structured data from tool calls to client applications using LangChain and AI SDK, covering server setup with createStreamableValue, defining tool schemas with Zod, binding tools to chat models, and implementing streaming with step-by-step code examples in a server action)

#### Multimodal

[How to: pass multimodal data directly to models](https://js.langchain.com/docs/how_to/multimodal_inputs/):  (trying to work with images alongside text input, implementing multimodal features, or requiring instructions for passing images to LLMs) (Tutorial showing how to pass multimodal data like images to language models, including examples with base64-encoded images and image URLs, demonstrating usage with models like Claude and GPT-4o, and explaining how to pass multiple images in a single prompt)
[How to: use multimodal prompts](https://js.langchain.com/docs/how_to/multimodal_prompts/):  (learning to format multimodal prompts with images, trying to pass multiple images to chat models, or implementing image description capabilities) (This guide demonstrates how to create multimodal prompts that include images using ChatPromptTemplate, including examples of describing a single image and comparing multiple images with the GPT-4o model via LangChain.js. Shows code examples for fetching images, converting to base64, creating prompts, and piping them to chat models.)
[How to: call tools with multimodal data](https://js.langchain.com/docs/how_to/tool_calls_multimodal/):  (needing to call tools with images or audio, implementing multimodal tool calling, or working with different LLM providers for multimodal tools) (Tutorial on how to call tools with multimodal data like images and audio using different LLM providers including OpenAI, Anthropic, and Google Generative AI, with code examples for each provider)

### Use cases

These guides cover use-case specific details.

#### Q&A with RAG

Retrieval Augmented Generation (RAG) is a way to connect LLMs to external sources of data.
[this guide](https://js.langchain.com/docs/tutorials/rag/):  (needing to build a RAG application, learning how to implement retrieval augmented generation, or understanding vectorstore indexing in LangChain). (Comprehensive tutorial on building a RAG application with LangChain and LangGraph that explains the entire process from indexing documents to retrieval and generation. Covers loading and splitting documents, storing them in vector stores, retrieving relevant context, and using LLMs to generate answers. Includes code examples for implementing a simple question-answering system over a blog post, with extensions for query analysis and metadata filtering.)

[How to: add chat history](https://js.langchain.com/docs/how_to/qa_chat_history_how_to/):  (building a RAG application that needs chat history, implementing conversational document Q&A, or converting standalone RAG into conversational RAG) (Guide on adding chat history to RAG applications using LangGraph, showing both chain-based and agent-based approaches. Covers creating history-aware retrievers, stateful management with LangGraph persistence, and techniques for generating contextually-informed retrieval queries from conversation history.)
[How to: stream](https://js.langchain.com/docs/how_to/qa_streaming/):  needing to implement streaming in RAG applications or when handling question-answering chains that return both answers and sources. (This page demonstrates how to stream responses from a question-answering chain, including retrieving and displaying source documents alongside generated answers, implementing basic streaming functionality, and adding logic to compile streamed outputs as they're being returned.)
[How to: return sources](https://js.langchain.com/docs/how_to/qa_sources/):  needing to return source documents from RAG systems, implementing citation functionality, or enhancing transparency in question-answering applications. (This page demonstrates how to return source documents alongside generated answers in RAG applications using LCEL, with code examples showing how to modify a basic RAG chain to pass retrieved documents through the chain and include them in the final response.)
[How to: return citations](https://js.langchain.com/docs/how_to/qa_citations/):  (needing to implement citation functionality in RAG systems, wanting to show sources in retrieval-based answers, or exploring different citation techniques) (Tutorial on implementing document citations in RAG systems using multiple approaches: tool calling with structured output for citing document IDs, extracting verbatim quotes, direct XML-based prompting without tool calling, and retrieval post-processing techniques to improve citation quality)
[How to: do per-user retrieval](https://js.langchain.com/docs/how_to/qa_per_user/):  (needing to implement retrieval for multiple users, building multi-tenant RAG applications, or separating user data in vector databases) (Guide for implementing per-user retrieval in LangChain applications, covering how to configure retrievers that support user separation, pass namespace parameters through chain configurations, and access only specific user data when querying vector stores like Pinecone)

#### Extraction

Extraction is when you use LLMs to extract structured information from unstructured text.
[this guide](https://js.langchain.com/docs/tutorials/extraction/):  (building an extraction chain, implementing structured information extraction from text, working with schema-based extraction) (Tutorial showing how to build a chain that extracts structured information from unstructured text using schemas defined with Zod, tool calling with LLMs, handling multiple entities, and improving extraction quality with proper schema documentation)

[How to: use reference examples](https://js.langchain.com/docs/how_to/extraction_examples/):  (needing to improve extraction quality, working with few-shot examples, or troubleshooting inconsistent extraction results) (This page explains how to use reference examples to improve information extraction using LLMs. It covers creating example input-output pairs, converting examples to message format, defining schemas for extraction, and demonstrates how examples significantly improve extraction accuracy compared to zero-shot approaches.)
[How to: handle long text](https://js.langchain.com/docs/how_to/extraction_long_text/):  handling documents that exceed LLM context windows, extracting information from long texts, or implementing chunking strategies for text processing. (This page explains methods for handling long text in LangChain: 1) using LLMs with larger context windows, 2) the brute force approach of chunking text and extracting from each chunk, 3) RAG-based approaches that identify and extract from relevant chunks only. It includes code examples for implementing each method, handling parallel extraction, merging results, and discusses common issues with these approaches.)
[How to: do extraction without using function calling](https://js.langchain.com/docs/how_to/extraction_parse/):  (extracting structured data without function calling, parsing LLM outputs without tool calling, or working with models that don't support function calling) (Guide on extracting structured information from LLMs without using function calling, including two approaches: using StructuredOutputParser with Zod schemas and implementing custom parsing with JSON extraction from model outputs)

#### Chatbots

Chatbots involve using an LLM to have a conversation.
[this guide](https://js.langchain.com/docs/tutorials/chatbot/):  (wanting to build a basic conversational chatbot, needing to implement message persistence, or managing conversation history) (This page provides a tutorial on building a chatbot using LangGraph, covering model implementation, message persistence across sessions, prompt templates, and strategies for managing conversation history to prevent context overflow)

[How to: manage memory](https://js.langchain.com/docs/how_to/chatbots_memory/):  (implementing chatbots with memory, handling context length limitations, or managing conversation state) (Detailed guide on adding memory capabilities to chatbots using LangGraph, with examples of message passing, automatic history management with checkpointers, and techniques for modifying chat history including message trimming and conversation summarization)
[How to: do retrieval](https://js.langchain.com/docs/how_to/chatbots_retrieval/):  (implementing retrieval for LLM applications, handling conversational retrieval with context, implementing query transformation for chat history) (This page explains how to implement retrieval in LangChain JS/TS, covering vectorstore creation, document chain implementation, retrieval chain composition, query transformation for handling followup questions, and streaming responses in retrieval systems)
[How to: use tools](https://js.langchain.com/docs/how_to/chatbots_tools/):  (adding tools to chatbots, building conversational agents, integrating search capabilities into chat interfaces) (This page provides step-by-step instructions on how to add tools to chatbots, specifically focusing on creating agents that can search the web using Tavily. It covers setting up the necessary dependencies, creating a tool-calling agent with LangGraph's createReactAgent, running the agent with both simple queries and those requiring search, and managing conversational context through memory implementation.)

#### Query analysis

Query Analysis is the task of using an LLM to generate a query to send to a retriever.
[this guide](https://js.langchain.com/docs/tutorials/rag/#query-analysis):  needing to build a RAG application, implementing question-answering over documents, or learning the basic components of retrieval augmented generation systems. (A detailed tutorial on building a Retrieval Augmented Generation application with LangChain and LangGraph, covering the main components: document loading, text splitting, embedding and vector storage for indexing, and retrieval+generation workflow using LangGraph with examples of query analysis, source attribution, and multiple invocation modes.)

[How to: add examples to the prompt](https://js.langchain.com/docs/how_to/query_few_shot/):  (needing to add examples to prompts for query analysis, implementing few-shot learning with structured outputs, or improving query decomposition) (This page demonstrates how to enhance query analysis by adding examples to prompts, showing the implementation of few-shot learning with structured outputs using Zod schemas, toolExampleToMessages formatting, and techniques for decomposing complex questions into more specific sub-queries)
[How to: handle cases where no queries are generated](https://js.langchain.com/docs/how_to/query_no_queries/):  (handling queries that might not need retrieval, implementing RAG systems with conditional search, handling irrelevant questions in query analysis) (Guide showing how to build a system that can determine when retrieval is necessary and when to skip it, using function calling with optional tool invocation, implementing conditional retrieval logic in LangChain, and parsing tool calls to determine when to perform retrieval)
[How to: handle multiple queries](https://js.langchain.com/docs/how_to/query_multiple_queries/):  (handling multiple search queries in RAG systems, implementing query analysis that generates multiple search terms, or building more comprehensive retrieval systems) (This page explains how to handle multiple queries in a retrieval system using function calling to analyze user questions and generate multiple search queries, then asynchronously retrieve documents for each query and combine the results, improving information retrieval completeness compared to single-query approaches)
[How to: handle multiple retrievers](https://js.langchain.com/docs/how_to/query_multiple_retrievers/):  (needing to manage systems with multiple retrievers, implementing query routing between different data sources, or building retrieval systems that need to selectively pick sources based on query analysis) (Detailed guide on implementing a system that intelligently selects between multiple retrievers based on query analysis, using function calling to parse queries, determine the appropriate retriever, and route information retrieval appropriately. Includes code examples for creating retrievers, analyzing queries with various LLM providers, and implementing custom retrieval chains.)
[How to: construct filters](https://js.langchain.com/docs/how_to/query_constructing_filters/):  constructing filters for retrievers, translating Zod schemas to retriever filters, or handling structured query parameters in RAG systems. (This page demonstrates how to construct filters for retrievers by transforming query parameters into comparison operations that can be translated into retriever-specific filter formats using translators like ChromaTranslator. It shows working with structured data including number and string filters, creating comparison objects, building operations, and converting them to retriever-compatible filter formats.)
[How to: deal with high cardinality categorical variables](https://js.langchain.com/docs/how_to/query_high_cardinality/):  (handling high cardinality categorical variables in RAG systems, needing to match user input with exact categorical values from a large set, optimizing query analysis with large sets of categorical data) (This guide explains techniques for handling high cardinality categorical variables in query analysis, particularly when you need exact matches from many possible values. It covers challenges when values won't fit in context windows, demonstrates using vector stores to find relevant categorical values, and shows how to implement a semantic search approach to match user input with the most similar categorical values.)

#### Q&A over SQL + CSV

You can use LLMs to do question answering over tabular data.
[this guide](https://js.langchain.com/docs/tutorials/sql_qa/):  (building a question-answering system over SQL databases, implementing SQL agents, or creating SQL database interfaces with LLMs) (Tutorial on creating SQL Q&A systems using LangChain with both chain and agent implementations; covers SQL query generation, execution, answer formulation, human-in-the-loop approval, and techniques for handling high-cardinality data like artist names)

[How to: use prompting to improve results](https://js.langchain.com/docs/how_to/sql_prompting/):  (needing to improve SQL query generation, optimizing prompts for database interactions, implementing few-shot examples for SQL tasks) (Guide on prompting strategies for better SQL generation including dialect-specific prompting, table definition inclusion, few-shot examples, and dynamic semantic similarity example selection with code samples using the Chinook SQLite database)
[How to: do query validation](https://js.langchain.com/docs/how_to/sql_query_checking/):  needing to validate SQL queries generated by LLMs, preventing SQL errors, or implementing safer database interactions. (This page demonstrates how to implement SQL query validation in LangChain, including both a two-step approach with separate validation and a single-step approach that combines generation and validation in one prompt, with examples using SQLite and the Chinook database.)
[How to: deal with large databases](https://js.langchain.com/docs/how_to/sql_large_db/):  (dealing with large SQL databases, handling many tables, or working with high-cardinality columns in database queries) (This page provides techniques for managing large SQL databases with LangChain, including methods to dynamically select relevant tables for prompts, categorize tables into groups, and handle high-cardinality columns through vector stores for proper noun correction in queries)

#### Q&A over graph databases

You can use an LLM to do question answering over graph databases.
[this guide](https://js.langchain.com/docs/tutorials/graph/):  needing to build question answering systems over graph databases, understanding Neo4j integration with LLMs, or implementing Cypher query generation. (Tutorial on creating a Q&A application over a graph database using LangChain and Neo4j, covering database setup, schema extraction, query generation with LLMs, and the three-step architecture: converting questions to Cypher queries, executing queries, and generating natural language answers from results)

[How to: map values to a database](https://js.langchain.com/docs/how_to/graph_mapping/):  needing to map user query entities to database values, working with graph databases, or generating accurate Cypher queries. (This page demonstrates how to improve graph database query generation by mapping values from user inputs to database entities, using Neo4j with a movie database example, extracting entities from user queries, and creating custom Cypher generating chains that incorporate mapped entities)
[How to: add a semantic layer over the database](https://js.langchain.com/docs/how_to/graph_semantic/):  needing to add semantic layers over graph databases, when implementing Cypher templates as tools, or when creating agents to query Neo4j databases without direct Cypher generation. (Tutorial on creating a semantic layer for Neo4j databases using custom tools with Cypher templates rather than direct Cypher generation, including setting up dependencies, creating template-based tools, and implementing an OpenAI agent to query movie information through the semantic layer)
[How to: improve results with prompting](https://js.langchain.com/docs/how_to/graph_prompting/):  (needing to improve Neo4j graph database query generation, implementing few-shot prompting for graph databases, or using semantic similarity to select relevant examples) (Guide for improving prompt engineering with Neo4j databases, covering setup, creating fixed few-shot examples, and implementing dynamic example selection using semantic similarity to generate better Cypher queries)
[How to: construct knowledge graphs](https://js.langchain.com/docs/how_to/graph_constructing/):  (needing to convert unstructured text into knowledge graphs, implementing graph-based RAG applications, or learning how to extract structured relationships from text) (This page covers how to construct knowledge graphs from unstructured text using LLMGraphTransformer and Neo4j. It explains the two-step process: extracting structured information from text using an LLM, and storing this information in a graph database. The guide includes code examples for setup, connection to Neo4j, using ChatOpenAI for extraction, filtering node and relationship types, and storing the extracted graph data.)

[LangGraph.js](https://langchain-ai.github.io/langgraphjs/):  (needing an introduction to LangGraph.js, wanting to understand its capabilities for agent orchestration, or exploring how to build controllable AI agents) (LangGraph.js is a low-level orchestration framework for building controllable AI agents with features like reliability, extensibility, and streaming support. It enables custom agent architectures, long-term memory, and human-in-the-loop integration. The page introduces its ecosystem, deployment options via LangGraph Platform, and provides links to additional resources like tutorials and templates.)

LangGraph.js is an extension of LangChain aimed at
building robust and stateful multi-actor applications with LLMs by modeling steps as edges and nodes in a graph.

LangGraph.js documentation is currently hosted on a separate site.
[LangGraph.js how-to guides here](https://langchain-ai.github.io/langgraphjs/how-tos/):  (needing to find specific how-to guides for LangGraph.js, looking for implementation patterns for specific agent features, or searching for detailed instructions on using LangGraph Platform) (Comprehensive collection of how-to guides for LangGraph.js covering installation, controllability, persistence, memory, human-in-the-loop interactions, time travel, streaming, tool calling, subgraphs, multi-agent systems, state management, prebuilt ReAct agents, and LangGraph Platform deployment options)

[LangSmith](https://docs.smith.langchain.com/):  looking for an overview of LangSmith's capabilities, understanding how to integrate LangSmith with LLM applications, or finding starting points for observability, evaluation, and prompt engineering. (LangSmith is a platform for building production-grade LLM applications with three core features: Observability for monitoring application performance and behavior; Evaluation tools for testing and scoring applications; and Prompt Engineering capabilities for iterating on prompts with version control. LangSmith works with or without LangChain frameworks and offers comprehensive tools for debugging, optimizing, and managing LLM applications throughout development and production.)

LangSmith allows you to closely trace, monitor and evaluate your LLM application.
It seamlessly integrates with LangChain and LangGraph.js, and you can use it to inspect and debug individual steps of your chains as you build.

LangSmith documentation is hosted on a separate site.
[LangSmith how-to guides here](https://docs.smith.langchain.com/how_to_guides/):  needing an overview of LangSmith capabilities, understanding how to monitor LLM applications, or learning about the platform's core features. (LangSmith is a platform for building production-grade LLM applications with three main components: Observability for monitoring and analyzing application traces; Evaluation tools for testing performance and collecting feedback; and Prompt Engineering features for iterating on prompts with version control. The platform works with or without LangChain frameworks.)
relevant to LangChain below:

#### Evaluation

<span data-heading-keywords="evaluation,evaluate"></span>

Evaluating performance is a vital part of building LLM-powered applications.
LangSmith helps with every step of the process from creating a dataset to defining metrics to running evaluators.

[LangSmith evaluation how-to guides](https://docs.smith.langchain.com/how_to_guides/#evaluation):  looking for an overview of LangSmith capabilities, understanding LangSmith core features, or determining how LangSmith helps with LLM application development. (LangSmith is a platform for building production-grade LLM applications with three main components: Observability for monitoring and analyzing application traces, Evaluation for assessing application performance and gathering feedback, and Prompt Engineering for iterating on prompts with version control. It's framework-agnostic but integrates seamlessly with LangChain and LangGraph.)

#### Tracing

<span data-heading-keywords="trace,tracing"></span>

Tracing gives you observability inside your chains and agents, and is vital in diagnosing issues.

[How to: trace with LangChain](https://docs.smith.langchain.com/how_to_guides/tracing/trace_with_langchain/):  (needing to implement tracing in a LangChain application, troubleshooting trace collection, or customizing trace attributes). (Comprehensive guide for adding LangSmith tracing to LangChain applications in Python and JS/TS, covering setup, selective tracing, project management, adding metadata/tags, customizing run names/IDs, accessing spans, distributed tracing, and interoperability between LangChain and LangSmith SDK)
[How to: add metadata and tags to traces](https://docs.smith.langchain.com/how_to_guides/tracing/trace_with_langchain/#add-metadata-and-tags-to-traces):  (wanting to implement tracing for LangChain applications, needing to monitor LLM application performance, or troubleshooting LangChain integrations with LangSmith) (This page provides comprehensive guidance on using LangSmith to trace LangChain applications in Python and JS/TS, including installation, environment configuration, selective tracing, project management, adding metadata/tags, customizing run names/IDs, ensuring traces are submitted before application exit, distributed tracing, and interoperability between LangChain and LangSmith SDK)

[in this section of the LangSmith docs](https://docs.smith.langchain.com/how_to_guides/tracing/):  needing to implement observability features in LLM applications, when troubleshooting trace issues, or when setting up monitoring for LangChain applications. (This page contains a comprehensive collection of how-to guides for LangSmith observability, organized into sections covering tracing configuration, integrations with various frameworks, UI and API usage, dashboards, automation rules, and human feedback collection methods.)
