{"buildCommand": "yarn build:vercel", "outputDirectory": "build", "trailingSlash": true, "rewrites": [{"source": "/v0.1/:path(.*/?)*", "destination": "https://langchainjs-v01.vercel.app/v0.1/:path*"}, {"source": "/v0.2/:path(.*/?)*", "destination": "https://langchainjs-v02.vercel.app/v0.2/:path*"}], "redirects": [{"source": "/docs/how_to/callbacks_backgrounding(/?)", "destination": "/docs/how_to/callbacks_serverless/"}, {"source": "/docs/get_started/introduction(/?)", "destination": "/docs/introduction/"}, {"source": "/docs(/?)", "destination": "/docs/introduction/"}, {"source": "/docs/get_started/introduction(/?)", "destination": "/docs/introduction/"}, {"source": "/docs/how_to/tool_calls_multi_modal(/?)", "destination": "/docs/how_to/multimodal_inputs/"}, {"source": "/docs/langgraph(/?)", "destination": "https://langchain-ai.github.io/langgraphjs/"}, {"source": "/docs/langsmith(/?)", "destination": "https://docs.smith.langchain.com/"}, {"source": "/docs/integrations/chat/chrome_ai(/?)", "destination": "/docs/integrations/llms/chrome_ai/"}, {"source": "/docs/integrations/retrievers/vectorstore(/?)", "destination": "/docs/how_to/vectorstore_retriever/"}, {"source": "/docs/integrations/chat_memory(/?)", "destination": "/docs/integrations/memory/"}, {"source": "/docs/integrations/chat_memory/:path(.*/?)*", "destination": "/docs/integrations/memory/:path*"}, {"source": "/docs/integrations/llms/togetherai(/?)", "destination": "/docs/integrations/llms/together/"}, {"source": "/docs/tutorials/query_analysis(/?)", "destination": "/docs/tutorials/rag#query-analysis/"}, {"source": "/docs/tutorials/local_rag(/?)", "destination": "/docs/tutorials/rag/"}, {"source": "/docs/tutorials/pdf_qa(/?)", "destination": "/docs/tutorials/retrievers/"}, {"source": "/docs/tutorials/agents(/?)", "destination": "https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/"}, {"source": "/docs/troubleshooting/errors/GRAPH_RECURSION_LIMIT(/?)", "destination": "https://langchain-ai.github.io/langgraphjs/troubleshooting/errors/GRAPH_RECURSION_LIMIT/"}, {"source": "/docs/troubleshooting/errors/INVALID_CONCURRENT_GRAPH_UPDATE(/?)", "destination": "https://langchain-ai.github.io/langgraphjs/troubleshooting/errors/INVALID_CONCURRENT_GRAPH_UPDATE/"}, {"source": "/docs/troubleshooting/errors/INVALID_GRAPH_NODE_RETURN_VALUE(/?)", "destination": "https://langchain-ai.github.io/langgraphjs/troubleshooting/errors/INVALID_GRAPH_NODE_RETURN_VALUE/"}, {"source": "/docs/troubleshooting/errors/MULTIPLE_SUBGRAPHS(/?)", "destination": "https://langchain-ai.github.io/langgraphjs/troubleshooting/errors/MULTIPLE_SUBGRAPHS/"}, {"source": "/docs/integrations/llms/watsonx_ai(/?)", "destination": "https://js.langchain.com/docs/integrations/llms/ibm/"}, {"source": "/docs/modules/model_io/prompts/quick_start/", "destination": "/docs/concepts/prompt_templates"}, {"source": "/docs/modules/model_io/prompts(/?)", "destination": "/docs/concepts/prompt_templates"}, {"source": "/docs/guides/expression_language/cookbook(/?)", "destination": "/docs/how_to/sequence"}, {"source": "/docs/modules/model_io/models(/?)", "destination": "/docs/integrations/chat/"}]}