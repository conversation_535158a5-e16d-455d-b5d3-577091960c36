version: "3"
services:
  test-exports-esbuild:
    image: node:18
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../yarn.lock:/root/yarn.lock
      - ../.yarnrc.yml:/root/.yarnrc.yml
      - ../.yarn:/root/.yarn
      - ../environment_tests/test-exports-esbuild:/package
      - ../environment_tests/scripts:/scripts
      - ../langchain:/langchain
      - ../langchain-core:/langchain-core
      - ../libs/langchain-community:/langchain-community
      - ../libs/langchain-anthropic:/langchain-anthropic
      - ../libs/langchain-openai:/langchain-openai
      - ../libs/langchain-cohere:/langchain-cohere
    command: bash /scripts/docker-ci-entrypoint.sh
  test-exports-esm:
    image: node:18
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../yarn.lock:/root/yarn.lock
      - ../.yarnrc.yml:/root/.yarnrc.yml
      - ../.yarn:/root/.yarn
      - ../environment_tests/test-exports-esm:/package
      - ../environment_tests/scripts:/scripts
      - ../langchain:/langchain
      - ../langchain-core:/langchain-core
      - ../libs/langchain-community:/langchain-community
      - ../libs/langchain-anthropic:/langchain-anthropic
      - ../libs/langchain-openai:/langchain-openai
      - ../libs/langchain-cohere:/langchain-cohere
    command: bash /scripts/docker-ci-entrypoint.sh
  test-exports-tsc:
    image: node:18
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../yarn.lock:/root/yarn.lock
      - ../.yarnrc.yml:/root/.yarnrc.yml
      - ../.yarn:/root/.yarn
      - ../environment_tests/test-exports-tsc:/package
      - ../environment_tests/scripts:/scripts
      - ../langchain:/langchain
      - ../langchain-core:/langchain-core
      - ../libs/langchain-community:/langchain-community
      - ../libs/langchain-anthropic:/langchain-anthropic
      - ../libs/langchain-openai:/langchain-openai
      - ../libs/langchain-cohere:/langchain-cohere
    command: bash /scripts/docker-ci-entrypoint.sh
  test-exports-cjs:
    image: node:18
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../yarn.lock:/root/yarn.lock
      - ../.yarnrc.yml:/root/.yarnrc.yml
      - ../.yarn:/root/.yarn
      - ../environment_tests/test-exports-cjs:/package
      - ../environment_tests/scripts:/scripts
      - ../langchain:/langchain
      - ../langchain-core:/langchain-core
      - ../libs/langchain-community:/langchain-community
      - ../libs/langchain-anthropic:/langchain-anthropic
      - ../libs/langchain-openai:/langchain-openai
      - ../libs/langchain-cohere:/langchain-cohere
    command: bash /scripts/docker-ci-entrypoint.sh
  test-exports-cf:
    image: node:18
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../yarn.lock:/root/yarn.lock
      - ../.yarnrc.yml:/root/.yarnrc.yml
      - ../.yarn:/root/.yarn
      - ../environment_tests/test-exports-cf:/package
      - ../environment_tests/scripts:/scripts
      - ../langchain:/langchain
      - ../langchain-core:/langchain-core
      - ../libs/langchain-community:/langchain-community
      - ../libs/langchain-anthropic:/langchain-anthropic
      - ../libs/langchain-openai:/langchain-openai
      - ../libs/langchain-cohere:/langchain-cohere
    command: bash /scripts/docker-ci-entrypoint.sh
  test-exports-vercel:
    image: node:18
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../yarn.lock:/root/yarn.lock
      - ../.yarnrc.yml:/root/.yarnrc.yml
      - ../.yarn:/root/.yarn
      - ../environment_tests/test-exports-vercel:/package
      - ../environment_tests/scripts:/scripts
      - ../langchain:/langchain
      - ../langchain-core:/langchain-core
      - ../libs/langchain-community:/langchain-community
      - ../libs/langchain-anthropic:/langchain-anthropic
      - ../libs/langchain-openai:/langchain-openai
      - ../libs/langchain-cohere:/langchain-cohere
    command: bash /scripts/docker-ci-entrypoint.sh
  test-exports-vite:
    image: node:18
    environment:
      PUPPETEER_SKIP_DOWNLOAD: "true"
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "true"
    working_dir: /app
    volumes:
      - ../yarn.lock:/root/yarn.lock
      - ../.yarnrc.yml:/root/.yarnrc.yml
      - ../.yarn:/root/.yarn
      - ../environment_tests/test-exports-vite:/package
      - ../environment_tests/scripts:/scripts
      - ../langchain:/langchain
      - ../langchain-core:/langchain-core
      - ../libs/langchain-community:/langchain-community
      - ../libs/langchain-anthropic:/langchain-anthropic
      - ../libs/langchain-openai:/langchain-openai
      - ../libs/langchain-cohere:/langchain-cohere
    command: bash /scripts/docker-ci-entrypoint.sh
  # test-exports-bun:
  #   image: oven/bun
  #   working_dir: /app
  #   volumes:
  #     - ../environment_tests/test-exports-bun:/package
  #     - ../environment_tests/scripts:/scripts
  #     - ../langchain:/langchain-workspace
  #     - ../langchain-core:/langchain-core
  #     - ../libs/langchain-community:/langchain-community-workspace
  #     - ../libs/langchain-anthropic:/langchain-anthropic-workspace
  #   command: bash /scripts/docker-bun-ci-entrypoint.sh
  success:
    image: alpine:3.14
    command: echo "Success"
    depends_on:
      test-exports-esbuild:
        condition: service_completed_successfully
      test-exports-esm:
        condition: service_completed_successfully
      test-exports-tsc:
        condition: service_completed_successfully
      test-exports-cjs:
        condition: service_completed_successfully
      test-exports-cf:
        condition: service_completed_successfully
      test-exports-vercel:
        condition: service_completed_successfully
      test-exports-vite:
        condition: service_completed_successfully
      # test-exports-bun:
      #   condition: service_completed_successfully
