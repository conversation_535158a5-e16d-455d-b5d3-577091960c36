{"name": "test-exports-cf", "version": "0.0.0", "workspaces": ["libs/*"], "devDependencies": {"@cloudflare/workers-types": "^4.20230321.0"}, "dependencies": {"@langchain/anthropic": "workspace:*", "@langchain/core": "workspace:*", "@langchain/openai": "workspace:*", "@tsconfig/recommended": "^1.0.2", "langchain": "workspace:*", "wrangler": "^3.19.0", "vitest": "0.34.3", "typescript": "^5.0.3"}, "private": true, "scripts": {"start": "wrangler dev", "deploy": "wrangler deploy", "build": "wrangler deploy --dry-run --outdir=dist", "test": "vitest run **/*.unit.test.ts", "test:integration": "vitest run **/*.int.test.ts"}}