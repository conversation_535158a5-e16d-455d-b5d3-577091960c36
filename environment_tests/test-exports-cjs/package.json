{"name": "test-exports-cjs", "version": "0.0.0", "workspaces": ["libs/*"], "private": true, "description": "CJS Tests for the things exported by the langchain package", "main": "./index.mjs", "scripts": {"build": "tsc", "test": "npm run test:esm && npm run test:cjs && npm run test:cjs:import && npm run test:entrypoints && npm run test:ts", "test:esm": "node src/index.mjs", "test:cjs": "node src/require.js", "test:cjs:import": "node src/import.js", "test:entrypoints": "node src/entrypoints.js", "test:ts": "node dist/index.js", "format": "prettier --write src", "format:check": "prettier --check src"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@langchain/anthropic": "workspace:*", "@langchain/community": "workspace:*", "@langchain/core": "workspace:*", "@langchain/openai": "workspace:*", "@tsconfig/recommended": "^1.0.2", "@xenova/transformers": "^2.17.2", "langchain": "workspace:*", "typescript": "^5.0.0"}, "devDependencies": {"@types/node": "^18.15.11", "prettier": "^2.8.3"}}