{"name": "test-exports-vite", "version": "0.0.0", "workspaces": ["libs/*"], "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "tsc"}, "dependencies": {"@langchain/anthropic": "workspace:*", "@langchain/community": "workspace:*", "@langchain/core": "workspace:*", "@langchain/openai": "workspace:*", "langchain": "workspace:*", "typescript": "^5.0.0", "vite": "^4.2.0"}}