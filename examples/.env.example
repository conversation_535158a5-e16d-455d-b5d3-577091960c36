ANTHROPIC_API_KEY=ADD_YOURS_HERE # https://www.anthropic.com/
COHERE_API_KEY=ADD_YOURS_HERE # https://dashboard.cohere.ai/api-keys
GOOGLE_PALM_API_KEY=ADD_YOURS_HERE # https://makersuite.google.com/app/apikey
GOOGLE_CALENDAR_PRIVATE_KEY=ADD_YOURS_HERE
GOOGLE_CALENDAR_CLIENT_EMAIL=ADD_YOURS_HERE
GOOGLE_CALENDAR_CALENDAR_ID=ADD_YOURS_HERE
GPLACES_API_KEY=ADD_YOURS_HERE
HUGGINGFACEHUB_API_KEY=ADD_YOURS_HERE # https://huggingface.co/settings/tokens
OPENAI_API_KEY=ADD_YOURS_HERE # https://platform.openai.com/account/api-keys
# Azure Portal -> Cognitive Services -> OpenAI -> Choose your instance -> Keys and Endpoint
AZURE_OPENAI_API_KEY=
AZURE_OPENAI_API_INSTANCE_NAME=ADD_YOURS_HERE # Azure Portal -> Cognitive Services -> OpenAI
AZURE_OPENAI_API_DEPLOYMENT_NAME=ADD_YOURS_HERE # Azure Portal -> Cognitive Services -> OpenAI -> Choose your instance -> Go to Azure OpenAI Studio -> Deployments
AZURE_OPENAI_API_COMPLETIONS_DEPLOYMENT_NAME=ADD_YOURS_HERE # Azure Portal -> Cognitive Services -> OpenAI -> Choose your instance -> Go to Azure OpenAI Studio -> Deployments
AZURE_OPENAI_API_EMBEDDINGS_DEPLOYMENT_NAME=ADD_YOURS_HERE # Azure Portal -> Cognitive Services -> OpenAI -> Choose your instance -> Go to Azure OpenAI Studio -> Deployments
AZURE_OPENAI_API_VERSION=ADD_YOURS_HERE # Azure Portal -> Cognitive Services -> OpenAI -> Choose your instance -> Go to Azure OpenAI Studio -> Completions/Chat -> Choose Deployment -> View Code
AZURE_OPENAI_BASE_PATH=ADD_YOURS_HERE # Azure Portal -> Cognitive Services -> OpenAI -> Choose your instance -> Endpoint (optional)
AZURE_OPENAI_BASE_PATH=ADD_YOURS_HERE # Azure Portal -> Cognitive Services -> OpenAI -> Choose your instance -> Endpoint (optional)
AZURE_CONTAINER_APP_SESSION_POOL_MANAGEMENT_ENDPOINT=ADD_YOURS_HERE # Azure Portal -> Container App Session Pools -> Choose your app -> Pool management endpoint -> Copy the URL
CONNERY_RUNNER_URL=ADD_YOURS_HERE
CONNERY_RUNNER_API_KEY=ADD_YOURS_HERE
ELASTIC_URL=ADD_YOURS_HERE # http://127.0.0.1:9200
ELASTIC_USERNAME="elastic"
ELASTIC_PASSWORD=ADD_YOURS_HERE # Password for the 'elastic' user (at least 6 characters)
ELASTIC_STACK_VERSION=8.8.1
ELASTIC_CLUSTER_NAME=docker-cluster # Set the cluster name
ELASTIC_LICENSE=basic # Set to 'basic' or 'trial' to automatically start the 30-day trial
ELASTIC_ES_PORT=9200
ELASTIC_MEM_LIMIT=********** # Increase or decrease based on the available host memory (in bytes)
OPENSEARCH_URL=ADD_YOURS_HERE # http://127.0.0.1:9200
PINECONE_API_KEY=ADD_YOURS_HERE # https://app.pinecone.io/organizations
PINECONE_ENVIRONMENT=ADD_YOURS_HERE
PINECONE_INDEX=ADD_YOURS_HERE # E.g. "trec-question-classification" when using "Cohere Trec" example index
REPLICATE_API_KEY=ADD_YOURS_HERE # https://replicate.com/account
SEARCHAPI_API_KEY=ADD_YOURS_HERE # https://www.searchapi.io/
SERPAPI_API_KEY=ADD_YOURS_HERE # https://serpapi.com/manage-api-key
SERPER_API_KEY=ADD_YOURS_HERE # https://serper.dev/api-key
DATAFORSEO_LOGIN=ADD_YOURS_HERE
DATAFORSEO_PASSWORD=ADD_YOURS_HERE
SUPABASE_PRIVATE_KEY=ADD_YOURS_HERE # https://app.supabase.com/project/YOUR_PROJECT_ID/settings/api
SUPABASE_URL=ADD_YOURS_HERE # # https://app.supabase.com/project/YOUR_PROJECT_ID/settings/api
WEAVIATE_HOST=ADD_YOURS_HERE
WEAVIATE_SCHEME=ADD_YOURS_HERE
WEAVIATE_API_KEY=ADD_YOURS_HERE
MYSCALE_HOST=ADD_YOURS_HERE
MYSCALE_PORT=ADD_YOURS_HERE
MYSCALE_USERNAME=ADD_YOURS_HERE
MYSCALE_PASSWORD=ADD_YOURS_HERE
CLICKHOUSE_HOST=ADD_YOURS_HERE
CLICKHOUSE_PORT=ADD_YOURS_HERE
CLICKHOUSE_USERNAME=ADD_YOURS_HERE
CLICKHOUSE_PASSWORD=ADD_YOURS_HERE
REDIS_URL=ADD_YOURS_HERE
SINGLESTORE_HOST=ADD_YOURS_HERE
SINGLESTORE_PORT=ADD_YOURS_HERE
SINGLESTORE_USERNAME=ADD_YOURS_HERE
SINGLESTORE_PASSWORD=ADD_YOURS_HERE
SINGLESTORE_DATABASE=ADD_YOURS_HERE
TIGRIS_URI=ADD_YOURS_HERE
TIGRIS_PROJECT=ADD_YOURS_HERE
TIGRIS_CLIENT_ID=ADD_YOURS_HERE
TIGRIS_CLIENT_SECRET=ADD_YOURS_HERE
NOTION_INTEGRATION_TOKEN=ADD_YOURS_HERE
FIGMA_ACCESS_TOKEN=ADD_YOURS_HERE
MOMENTO_API_KEY=ADD_YOURS_HERE # https://console.gomomento.com
ANALYTICDB_HOST=ADD_YOURS_HERE
ANALYTICDB_PORT=ADD_YOURS_HERE
ANALYTICDB_USERNAME=ADD_YOURS_HERE
ANALYTICDB_PASSWORD=ADD_YOURS_HERE
ANALYTICDB_DATABASE=ADD_YOURS_HERE
ASSEMBLYAI_API_KEY=ADD_YOURS_HERE
AWS_ACCESS_KEY_ID=ADD_YOURS_HERE
AWS_SECRET_ACCESS_KEY=ADD_YOURS_HERE
AWS_REGION=ADD_YOURS_HERE
IFLYTEK_APPID=ADD_YOURS_HERE
IFLYTEK_API_KEY=ADD_YOURS_HERE
IFLYTEK_API_SECRET=ADD_YOURS_HERE
ASTRA_DB_APPLICATION_TOKEN=ADD_YOURS_HERE 
ASTRA_DB_ENDPOINT=ADD_YOURS_HERE
ASTRA_DB_NAMESPACE=ADD_YOURS_HERE # defaults to "default_keyspace"
FRIENDLI_TOKEN=ADD_YOURS_HERE # https://suite.friendli.ai/
FRIENDLI_TEAM=ADD_YOURS_HERE # https://suite.friendli.ai/
HANA_HOST=HANA_DB_ADDRESS
HANA_PORT=HANA_DB_PORT
HANA_UID=HANA_DB_USER
HANA_PWD=HANA_DB_PASSWORD
ARK_API_KEY=ADD_YOURS_HERE # https://console.volcengine.com/
JIRA_HOST=ADD_YOURS_HERE
JIRA_USERNAME=ADD_YOURS_HERE
JIRA_ACCESS_TOKEN=ADD_YOURS_HERE
JIRA_PROJECT_KEY=ADD_YOURS_HERE
