{"name": "examples", "version": "0.0.0", "private": true, "description": "Langchain examples", "main": "./dist/index.js", "type": "module", "files": ["dist/"], "scripts": {"build": "tsc --declaration --outDir dist/", "clean": "rm -rf .turbo dist/", "start": "tsx --experimental-wasm-modules -r dotenv/config src/index.ts", "postinstall": "prisma generate --schema ./src/indexes/vector_stores/prisma_vectorstore/prisma/schema.prisma", "start:dist": "yarn build && node -r dotenv/config dist/index.js", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint": "yarn lint:eslint", "lint:fix": "yarn lint --fix", "precommit": "lint-staged", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\""}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@aws-sdk/dsql-signer": "^3.738.0", "@azure/identity": "^4.2.1", "@browserbasehq/stagehand": "^1.3.0", "@clickhouse/client": "^0.2.5", "@elastic/elasticsearch": "^8.4.0", "@faker-js/faker": "^8.4.1", "@getmetal/metal-sdk": "^4.0.0", "@getzep/zep-cloud": "^1.0.6", "@getzep/zep-js": "^0.9.0", "@gomomento/sdk": "^1.51.1", "@google/generative-ai": "^0.7.0", "@lancedb/lancedb": "^0.13.0", "@langchain/anthropic": "workspace:*", "@langchain/aws": "workspace:*", "@langchain/azure-cosmosdb": "workspace:*", "@langchain/azure-dynamic-sessions": "workspace:^", "@langchain/azure-openai": "workspace:*", "@langchain/baidu-qianfan": "workspace:*", "@langchain/cloudflare": "workspace:*", "@langchain/cohere": "workspace:*", "@langchain/community": "workspace:*", "@langchain/core": "workspace:*", "@langchain/deepseek": "workspace:*", "@langchain/exa": "workspace:*", "@langchain/google-cloud-sql-pg": "workspace:^", "@langchain/google-common": "workspace:*", "@langchain/google-genai": "workspace:*", "@langchain/google-vertexai": "workspace:*", "@langchain/google-vertexai-web": "workspace:*", "@langchain/groq": "workspace:*", "@langchain/langgraph": "^0.2.34", "@langchain/mistralai": "workspace:*", "@langchain/mongodb": "workspace:*", "@langchain/nomic": "workspace:*", "@langchain/ollama": "workspace:*", "@langchain/openai": "workspace:*", "@langchain/pinecone": "workspace:*", "@langchain/qdrant": "workspace:*", "@langchain/redis": "workspace:*", "@langchain/scripts": ">=0.1.0 <0.2.0", "@langchain/textsplitters": "workspace:*", "@langchain/weaviate": "workspace:*", "@langchain/xai": "workspace:*", "@langchain/yandex": "workspace:*", "@layerup/layerup-security": "^1.5.12", "@opensearch-project/opensearch": "^2.2.0", "@pinecone-database/pinecone": "^5.0.2", "@planetscale/database": "^1.8.0", "@prisma/client": "^4.11.0", "@qdrant/js-client-rest": "^1.9.0", "@rockset/client": "^0.9.1", "@supabase/supabase-js": "^2.45.0", "@tensorflow/tfjs-backend-cpu": "^4.4.0", "@upstash/redis": "^1.34.7", "@upstash/vector": "^1.2.1", "@vercel/kv": "^3.0.0", "@xata.io/client": "^0.28.0", "@zilliz/milvus2-sdk-node": "^2.3.5", "axios": "^0.26.0", "chromadb": "^1.5.3", "cohere-ai": "^7.14.0", "convex": "^1.3.1", "date-fns": "^3.3.1", "dotenv": "^16.0.3", "duck-duck-scrape": "^2.2.5", "exa-js": "^1.0.12", "firebase-admin": "^12.0.0", "graphql": "^16.6.0", "hdb": "^0.19.8", "ioredis": "^5.3.2", "js-yaml": "^4.1.0", "langchain": "workspace:*", "langsmith": "^0.3.29", "mariadb": "^3.4.0", "mem0ai": "^2.1.8", "mongodb": "^6.3.0", "pg": "^8.11.0", "pickleparser": "^0.2.1", "prisma": "^4.11.0", "readline": "^1.3.0", "redis": "^4.6.13", "sqlite3": "^5.1.4", "typeorm": "^0.3.20", "typesense": "^1.5.3", "uuid": "^10.0.0", "voy-search": "0.6.2", "weaviate-client": "^3.5.2", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.3"}, "devDependencies": {"@tsconfig/recommended": "^1.0.2", "@types/js-yaml": "^4", "@types/uuid": "^9", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^2.8.3", "tsx": "^3.12.3", "typescript": "~5.1.6"}}