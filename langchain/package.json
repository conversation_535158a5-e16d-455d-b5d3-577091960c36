{"name": "langchain", "version": "0.3.28", "description": "Typescript bindings for langchain", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "files": ["dist/", "load.cjs", "load.js", "load.d.ts", "load.d.cts", "load/serializable.cjs", "load/serializable.js", "load/serializable.d.ts", "load/serializable.d.cts", "agents.cjs", "agents.js", "agents.d.ts", "agents.d.cts", "agents/load.cjs", "agents/load.js", "agents/load.d.ts", "agents/load.d.cts", "agents/toolkits.cjs", "agents/toolkits.js", "agents/toolkits.d.ts", "agents/toolkits.d.cts", "agents/toolkits/sql.cjs", "agents/toolkits/sql.js", "agents/toolkits/sql.d.ts", "agents/toolkits/sql.d.cts", "agents/format_scratchpad.cjs", "agents/format_scratchpad.js", "agents/format_scratchpad.d.ts", "agents/format_scratchpad.d.cts", "agents/format_scratchpad/openai_tools.cjs", "agents/format_scratchpad/openai_tools.js", "agents/format_scratchpad/openai_tools.d.ts", "agents/format_scratchpad/openai_tools.d.cts", "agents/format_scratchpad/log.cjs", "agents/format_scratchpad/log.js", "agents/format_scratchpad/log.d.ts", "agents/format_scratchpad/log.d.cts", "agents/format_scratchpad/xml.cjs", "agents/format_scratchpad/xml.js", "agents/format_scratchpad/xml.d.ts", "agents/format_scratchpad/xml.d.cts", "agents/format_scratchpad/log_to_message.cjs", "agents/format_scratchpad/log_to_message.js", "agents/format_scratchpad/log_to_message.d.ts", "agents/format_scratchpad/log_to_message.d.cts", "agents/react/output_parser.cjs", "agents/react/output_parser.js", "agents/react/output_parser.d.ts", "agents/react/output_parser.d.cts", "agents/xml/output_parser.cjs", "agents/xml/output_parser.js", "agents/xml/output_parser.d.ts", "agents/xml/output_parser.d.cts", "agents/openai/output_parser.cjs", "agents/openai/output_parser.js", "agents/openai/output_parser.d.ts", "agents/openai/output_parser.d.cts", "tools.cjs", "tools.js", "tools.d.ts", "tools.d.cts", "tools/chain.cjs", "tools/chain.js", "tools/chain.d.ts", "tools/chain.d.cts", "tools/render.cjs", "tools/render.js", "tools/render.d.ts", "tools/render.d.cts", "tools/retriever.cjs", "tools/retriever.js", "tools/retriever.d.ts", "tools/retriever.d.cts", "tools/sql.cjs", "tools/sql.js", "tools/sql.d.ts", "tools/sql.d.cts", "tools/webbrowser.cjs", "tools/webbrowser.js", "tools/webbrowser.d.ts", "tools/webbrowser.d.cts", "chains.cjs", "chains.js", "chains.d.ts", "chains.d.cts", "chains/combine_documents.cjs", "chains/combine_documents.js", "chains/combine_documents.d.ts", "chains/combine_documents.d.cts", "chains/combine_documents/reduce.cjs", "chains/combine_documents/reduce.js", "chains/combine_documents/reduce.d.ts", "chains/combine_documents/reduce.d.cts", "chains/history_aware_retriever.cjs", "chains/history_aware_retriever.js", "chains/history_aware_retriever.d.ts", "chains/history_aware_retriever.d.cts", "chains/load.cjs", "chains/load.js", "chains/load.d.ts", "chains/load.d.cts", "chains/openai_functions.cjs", "chains/openai_functions.js", "chains/openai_functions.d.ts", "chains/openai_functions.d.cts", "chains/query_constructor.cjs", "chains/query_constructor.js", "chains/query_constructor.d.ts", "chains/query_constructor.d.cts", "chains/query_constructor/ir.cjs", "chains/query_constructor/ir.js", "chains/query_constructor/ir.d.ts", "chains/query_constructor/ir.d.cts", "chains/retrieval.cjs", "chains/retrieval.js", "chains/retrieval.d.ts", "chains/retrieval.d.cts", "chains/sql_db.cjs", "chains/sql_db.js", "chains/sql_db.d.ts", "chains/sql_db.d.cts", "chains/graph_qa/cypher.cjs", "chains/graph_qa/cypher.js", "chains/graph_qa/cypher.d.ts", "chains/graph_qa/cypher.d.cts", "chat_models/universal.cjs", "chat_models/universal.js", "chat_models/universal.d.ts", "chat_models/universal.d.cts", "embeddings/cache_backed.cjs", "embeddings/cache_backed.js", "embeddings/cache_backed.d.ts", "embeddings/cache_backed.d.cts", "embeddings/fake.cjs", "embeddings/fake.js", "embeddings/fake.d.ts", "embeddings/fake.d.cts", "vectorstores/memory.cjs", "vectorstores/memory.js", "vectorstores/memory.d.ts", "vectorstores/memory.d.cts", "text_splitter.cjs", "text_splitter.js", "text_splitter.d.ts", "text_splitter.d.cts", "memory.cjs", "memory.js", "memory.d.ts", "memory.d.cts", "memory/chat_memory.cjs", "memory/chat_memory.js", "memory/chat_memory.d.ts", "memory/chat_memory.d.cts", "document.cjs", "document.js", "document.d.ts", "document.d.cts", "document_loaders/base.cjs", "document_loaders/base.js", "document_loaders/base.d.ts", "document_loaders/base.d.cts", "document_loaders/fs/buffer.cjs", "document_loaders/fs/buffer.js", "document_loaders/fs/buffer.d.ts", "document_loaders/fs/buffer.d.cts", "document_loaders/fs/directory.cjs", "document_loaders/fs/directory.js", "document_loaders/fs/directory.d.ts", "document_loaders/fs/directory.d.cts", "document_loaders/fs/json.cjs", "document_loaders/fs/json.js", "document_loaders/fs/json.d.ts", "document_loaders/fs/json.d.cts", "document_loaders/fs/multi_file.cjs", "document_loaders/fs/multi_file.js", "document_loaders/fs/multi_file.d.ts", "document_loaders/fs/multi_file.d.cts", "document_loaders/fs/text.cjs", "document_loaders/fs/text.js", "document_loaders/fs/text.d.ts", "document_loaders/fs/text.d.cts", "document_transformers/openai_functions.cjs", "document_transformers/openai_functions.js", "document_transformers/openai_functions.d.ts", "document_transformers/openai_functions.d.cts", "sql_db.cjs", "sql_db.js", "sql_db.d.ts", "sql_db.d.cts", "callbacks.cjs", "callbacks.js", "callbacks.d.ts", "callbacks.d.cts", "output_parsers.cjs", "output_parsers.js", "output_parsers.d.ts", "output_parsers.d.cts", "output_parsers/expression.cjs", "output_parsers/expression.js", "output_parsers/expression.d.ts", "output_parsers/expression.d.cts", "retrievers/contextual_compression.cjs", "retrievers/contextual_compression.js", "retrievers/contextual_compression.d.ts", "retrievers/contextual_compression.d.cts", "retrievers/document_compressors.cjs", "retrievers/document_compressors.js", "retrievers/document_compressors.d.ts", "retrievers/document_compressors.d.cts", "retrievers/ensemble.cjs", "retrievers/ensemble.js", "retrievers/ensemble.d.ts", "retrievers/ensemble.d.cts", "retrievers/multi_query.cjs", "retrievers/multi_query.js", "retrievers/multi_query.d.ts", "retrievers/multi_query.d.cts", "retrievers/multi_vector.cjs", "retrievers/multi_vector.js", "retrievers/multi_vector.d.ts", "retrievers/multi_vector.d.cts", "retrievers/parent_document.cjs", "retrievers/parent_document.js", "retrievers/parent_document.d.ts", "retrievers/parent_document.d.cts", "retrievers/time_weighted.cjs", "retrievers/time_weighted.js", "retrievers/time_weighted.d.ts", "retrievers/time_weighted.d.cts", "retrievers/document_compressors/chain_extract.cjs", "retrievers/document_compressors/chain_extract.js", "retrievers/document_compressors/chain_extract.d.ts", "retrievers/document_compressors/chain_extract.d.cts", "retrievers/document_compressors/embeddings_filter.cjs", "retrievers/document_compressors/embeddings_filter.js", "retrievers/document_compressors/embeddings_filter.d.ts", "retrievers/document_compressors/embeddings_filter.d.cts", "retrievers/hyde.cjs", "retrievers/hyde.js", "retrievers/hyde.d.ts", "retrievers/hyde.d.cts", "retrievers/score_threshold.cjs", "retrievers/score_threshold.js", "retrievers/score_threshold.d.ts", "retrievers/score_threshold.d.cts", "retrievers/self_query.cjs", "retrievers/self_query.js", "retrievers/self_query.d.ts", "retrievers/self_query.d.cts", "retrievers/self_query/functional.cjs", "retrievers/self_query/functional.js", "retrievers/self_query/functional.d.ts", "retrievers/self_query/functional.d.cts", "retrievers/matryoshka_retriever.cjs", "retrievers/matryoshka_retriever.js", "retrievers/matryoshka_retriever.d.ts", "retrievers/matryoshka_retriever.d.cts", "cache/file_system.cjs", "cache/file_system.js", "cache/file_system.d.ts", "cache/file_system.d.cts", "stores/doc/base.cjs", "stores/doc/base.js", "stores/doc/base.d.ts", "stores/doc/base.d.cts", "stores/doc/in_memory.cjs", "stores/doc/in_memory.js", "stores/doc/in_memory.d.ts", "stores/doc/in_memory.d.cts", "stores/file/in_memory.cjs", "stores/file/in_memory.js", "stores/file/in_memory.d.ts", "stores/file/in_memory.d.cts", "stores/file/node.cjs", "stores/file/node.js", "stores/file/node.d.ts", "stores/file/node.d.cts", "stores/message/in_memory.cjs", "stores/message/in_memory.js", "stores/message/in_memory.d.ts", "stores/message/in_memory.d.cts", "storage/encoder_backed.cjs", "storage/encoder_backed.js", "storage/encoder_backed.d.ts", "storage/encoder_backed.d.cts", "storage/in_memory.cjs", "storage/in_memory.js", "storage/in_memory.d.ts", "storage/in_memory.d.cts", "storage/file_system.cjs", "storage/file_system.js", "storage/file_system.d.ts", "storage/file_system.d.cts", "hub.cjs", "hub.js", "hub.d.ts", "hub.d.cts", "hub/node.cjs", "hub/node.js", "hub/node.d.ts", "hub/node.d.cts", "util/document.cjs", "util/document.js", "util/document.d.ts", "util/document.d.cts", "util/math.cjs", "util/math.js", "util/math.d.ts", "util/math.d.cts", "util/time.cjs", "util/time.js", "util/time.d.ts", "util/time.d.cts", "experimental/autogpt.cjs", "experimental/autogpt.js", "experimental/autogpt.d.ts", "experimental/autogpt.d.cts", "experimental/openai_assistant.cjs", "experimental/openai_assistant.js", "experimental/openai_assistant.d.ts", "experimental/openai_assistant.d.cts", "experimental/openai_files.cjs", "experimental/openai_files.js", "experimental/openai_files.d.ts", "experimental/openai_files.d.cts", "experimental/babyagi.cjs", "experimental/babyagi.js", "experimental/babyagi.d.ts", "experimental/babyagi.d.cts", "experimental/generative_agents.cjs", "experimental/generative_agents.js", "experimental/generative_agents.d.ts", "experimental/generative_agents.d.cts", "experimental/plan_and_execute.cjs", "experimental/plan_and_execute.js", "experimental/plan_and_execute.d.ts", "experimental/plan_and_execute.d.cts", "experimental/chains/violation_of_expectations.cjs", "experimental/chains/violation_of_expectations.js", "experimental/chains/violation_of_expectations.d.ts", "experimental/chains/violation_of_expectations.d.cts", "experimental/masking.cjs", "experimental/masking.js", "experimental/masking.d.ts", "experimental/masking.d.cts", "experimental/prompts/custom_format.cjs", "experimental/prompts/custom_format.js", "experimental/prompts/custom_format.d.ts", "experimental/prompts/custom_format.d.cts", "experimental/prompts/handlebars.cjs", "experimental/prompts/handlebars.js", "experimental/prompts/handlebars.d.ts", "experimental/prompts/handlebars.d.cts", "evaluation.cjs", "evaluation.js", "evaluation.d.ts", "evaluation.d.cts", "smith.cjs", "smith.js", "smith.d.ts", "smith.d.cts", "runnables/remote.cjs", "runnables/remote.js", "runnables/remote.d.ts", "runnables/remote.d.cts", "indexes.cjs", "indexes.js", "indexes.d.ts", "indexes.d.cts", "schema/query_constructor.cjs", "schema/query_constructor.js", "schema/query_constructor.d.ts", "schema/query_constructor.d.cts", "schema/prompt_template.cjs", "schema/prompt_template.js", "schema/prompt_template.d.ts", "schema/prompt_template.d.cts"], "repository": {"type": "git", "url": "**************:langchain-ai/langchainjs.git"}, "homepage": "https://github.com/langchain-ai/langchainjs/tree/main/langchain/", "scripts": {"build": "yarn turbo:command build:internal --filter=langchain", "build:internal": "yarn lc_build --create-entrypoints --pre --tree-shaking --gen-maps", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "precommit": "lint-staged", "clean": "rm -rf .turbo dist/", "prepack": "yarn build", "release": "release-it --only-version --config .release-it.json", "test": "NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:integration": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\""}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@faker-js/faker": "^7.6.0", "@jest/globals": "^29.5.0", "@langchain/anthropic": "*", "@langchain/aws": "*", "@langchain/cerebras": "*", "@langchain/cohere": "*", "@langchain/core": "workspace:*", "@langchain/deepseek": "*", "@langchain/google-genai": "*", "@langchain/google-vertexai": "*", "@langchain/google-vertexai-web": "*", "@langchain/groq": "*", "@langchain/mistralai": "*", "@langchain/ollama": "*", "@langchain/scripts": ">=0.1.0 <0.2.0", "@langchain/xai": "*", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "@tsconfig/recommended": "^1.0.2", "@types/handlebars": "^4.1.0", "@types/html-to-text": "^9", "@types/js-yaml": "^4", "@types/jsdom": "^21.1.1", "@types/uuid": "^9", "@types/ws": "^8", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "axios": "^0.26.0", "cheerio": "1.0.0-rc.12", "dotenv": "^16.0.3", "dpdm": "^3.12.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "handlebars": "^4.7.8", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "openai": "^4.41.1", "peggy": "^3.0.2", "prettier": "^2.8.3", "reflect-metadata": "^0.2.2", "release-it": "^18.1.2", "rimraf": "^5.0.1", "rollup": "^3.19.1", "ts-jest": "^29.1.0", "typeorm": "^0.3.20", "typescript": "~5.1.6", "wikipedia": "^2.1.2"}, "peerDependencies": {"@langchain/anthropic": "*", "@langchain/aws": "*", "@langchain/cerebras": "*", "@langchain/cohere": "*", "@langchain/core": ">=0.3.58 <0.4.0", "@langchain/deepseek": "*", "@langchain/google-genai": "*", "@langchain/google-vertexai": "*", "@langchain/google-vertexai-web": "*", "@langchain/groq": "*", "@langchain/mistralai": "*", "@langchain/ollama": "*", "@langchain/xai": "*", "axios": "*", "cheerio": "*", "handlebars": "^4.7.8", "peggy": "^3.0.2", "typeorm": "*"}, "peerDependenciesMeta": {"@langchain/anthropic": {"optional": true}, "@langchain/aws": {"optional": true}, "@langchain/cerebras": {"optional": true}, "@langchain/cohere": {"optional": true}, "@langchain/deepseek": {"optional": true}, "@langchain/google-genai": {"optional": true}, "@langchain/google-vertexai": {"optional": true}, "@langchain/google-vertexai-web": {"optional": true}, "@langchain/groq": {"optional": true}, "@langchain/mistralai": {"optional": true}, "@langchain/ollama": {"optional": true}, "@langchain/xai": {"optional": true}, "axios": {"optional": true}, "cheerio": {"optional": true}, "handlebars": {"optional": true}, "peggy": {"optional": true}, "typeorm": {"optional": true}}, "dependencies": {"@langchain/openai": ">=0.1.0 <0.6.0", "@langchain/textsplitters": ">=0.0.0 <0.2.0", "js-tiktoken": "^1.0.12", "js-yaml": "^4.1.0", "jsonpointer": "^5.0.1", "langsmith": "^0.3.29", "openapi-types": "^12.1.3", "p-retry": "4", "uuid": "^10.0.0", "yaml": "^2.2.1", "zod": "^3.25.32"}, "publishConfig": {"access": "public"}, "keywords": ["llm", "ai", "gpt3", "chain", "prompt", "prompt engineering", "chatgpt", "machine learning", "ml", "openai", "embeddings", "vectorstores"], "exports": {"./load": {"types": {"import": "./load.d.ts", "require": "./load.d.cts", "default": "./load.d.ts"}, "import": "./load.js", "require": "./load.cjs"}, "./load/serializable": {"types": {"import": "./load/serializable.d.ts", "require": "./load/serializable.d.cts", "default": "./load/serializable.d.ts"}, "import": "./load/serializable.js", "require": "./load/serializable.cjs"}, "./agents": {"types": {"import": "./agents.d.ts", "require": "./agents.d.cts", "default": "./agents.d.ts"}, "import": "./agents.js", "require": "./agents.cjs"}, "./agents/load": {"types": {"import": "./agents/load.d.ts", "require": "./agents/load.d.cts", "default": "./agents/load.d.ts"}, "import": "./agents/load.js", "require": "./agents/load.cjs"}, "./agents/toolkits": {"types": {"import": "./agents/toolkits.d.ts", "require": "./agents/toolkits.d.cts", "default": "./agents/toolkits.d.ts"}, "import": "./agents/toolkits.js", "require": "./agents/toolkits.cjs"}, "./agents/toolkits/sql": {"types": {"import": "./agents/toolkits/sql.d.ts", "require": "./agents/toolkits/sql.d.cts", "default": "./agents/toolkits/sql.d.ts"}, "import": "./agents/toolkits/sql.js", "require": "./agents/toolkits/sql.cjs"}, "./agents/format_scratchpad": {"types": {"import": "./agents/format_scratchpad.d.ts", "require": "./agents/format_scratchpad.d.cts", "default": "./agents/format_scratchpad.d.ts"}, "import": "./agents/format_scratchpad.js", "require": "./agents/format_scratchpad.cjs"}, "./agents/format_scratchpad/openai_tools": {"types": {"import": "./agents/format_scratchpad/openai_tools.d.ts", "require": "./agents/format_scratchpad/openai_tools.d.cts", "default": "./agents/format_scratchpad/openai_tools.d.ts"}, "import": "./agents/format_scratchpad/openai_tools.js", "require": "./agents/format_scratchpad/openai_tools.cjs"}, "./agents/format_scratchpad/log": {"types": {"import": "./agents/format_scratchpad/log.d.ts", "require": "./agents/format_scratchpad/log.d.cts", "default": "./agents/format_scratchpad/log.d.ts"}, "import": "./agents/format_scratchpad/log.js", "require": "./agents/format_scratchpad/log.cjs"}, "./agents/format_scratchpad/xml": {"types": {"import": "./agents/format_scratchpad/xml.d.ts", "require": "./agents/format_scratchpad/xml.d.cts", "default": "./agents/format_scratchpad/xml.d.ts"}, "import": "./agents/format_scratchpad/xml.js", "require": "./agents/format_scratchpad/xml.cjs"}, "./agents/format_scratchpad/log_to_message": {"types": {"import": "./agents/format_scratchpad/log_to_message.d.ts", "require": "./agents/format_scratchpad/log_to_message.d.cts", "default": "./agents/format_scratchpad/log_to_message.d.ts"}, "import": "./agents/format_scratchpad/log_to_message.js", "require": "./agents/format_scratchpad/log_to_message.cjs"}, "./agents/react/output_parser": {"types": {"import": "./agents/react/output_parser.d.ts", "require": "./agents/react/output_parser.d.cts", "default": "./agents/react/output_parser.d.ts"}, "import": "./agents/react/output_parser.js", "require": "./agents/react/output_parser.cjs"}, "./agents/xml/output_parser": {"types": {"import": "./agents/xml/output_parser.d.ts", "require": "./agents/xml/output_parser.d.cts", "default": "./agents/xml/output_parser.d.ts"}, "import": "./agents/xml/output_parser.js", "require": "./agents/xml/output_parser.cjs"}, "./agents/openai/output_parser": {"types": {"import": "./agents/openai/output_parser.d.ts", "require": "./agents/openai/output_parser.d.cts", "default": "./agents/openai/output_parser.d.ts"}, "import": "./agents/openai/output_parser.js", "require": "./agents/openai/output_parser.cjs"}, "./tools": {"types": {"import": "./tools.d.ts", "require": "./tools.d.cts", "default": "./tools.d.ts"}, "import": "./tools.js", "require": "./tools.cjs"}, "./tools/chain": {"types": {"import": "./tools/chain.d.ts", "require": "./tools/chain.d.cts", "default": "./tools/chain.d.ts"}, "import": "./tools/chain.js", "require": "./tools/chain.cjs"}, "./tools/render": {"types": {"import": "./tools/render.d.ts", "require": "./tools/render.d.cts", "default": "./tools/render.d.ts"}, "import": "./tools/render.js", "require": "./tools/render.cjs"}, "./tools/retriever": {"types": {"import": "./tools/retriever.d.ts", "require": "./tools/retriever.d.cts", "default": "./tools/retriever.d.ts"}, "import": "./tools/retriever.js", "require": "./tools/retriever.cjs"}, "./tools/sql": {"types": {"import": "./tools/sql.d.ts", "require": "./tools/sql.d.cts", "default": "./tools/sql.d.ts"}, "import": "./tools/sql.js", "require": "./tools/sql.cjs"}, "./tools/webbrowser": {"types": {"import": "./tools/webbrowser.d.ts", "require": "./tools/webbrowser.d.cts", "default": "./tools/webbrowser.d.ts"}, "import": "./tools/webbrowser.js", "require": "./tools/webbrowser.cjs"}, "./chains": {"types": {"import": "./chains.d.ts", "require": "./chains.d.cts", "default": "./chains.d.ts"}, "import": "./chains.js", "require": "./chains.cjs"}, "./chains/combine_documents": {"types": {"import": "./chains/combine_documents.d.ts", "require": "./chains/combine_documents.d.cts", "default": "./chains/combine_documents.d.ts"}, "import": "./chains/combine_documents.js", "require": "./chains/combine_documents.cjs"}, "./chains/combine_documents/reduce": {"types": {"import": "./chains/combine_documents/reduce.d.ts", "require": "./chains/combine_documents/reduce.d.cts", "default": "./chains/combine_documents/reduce.d.ts"}, "import": "./chains/combine_documents/reduce.js", "require": "./chains/combine_documents/reduce.cjs"}, "./chains/history_aware_retriever": {"types": {"import": "./chains/history_aware_retriever.d.ts", "require": "./chains/history_aware_retriever.d.cts", "default": "./chains/history_aware_retriever.d.ts"}, "import": "./chains/history_aware_retriever.js", "require": "./chains/history_aware_retriever.cjs"}, "./chains/load": {"types": {"import": "./chains/load.d.ts", "require": "./chains/load.d.cts", "default": "./chains/load.d.ts"}, "import": "./chains/load.js", "require": "./chains/load.cjs"}, "./chains/openai_functions": {"types": {"import": "./chains/openai_functions.d.ts", "require": "./chains/openai_functions.d.cts", "default": "./chains/openai_functions.d.ts"}, "import": "./chains/openai_functions.js", "require": "./chains/openai_functions.cjs"}, "./chains/query_constructor": {"types": {"import": "./chains/query_constructor.d.ts", "require": "./chains/query_constructor.d.cts", "default": "./chains/query_constructor.d.ts"}, "import": "./chains/query_constructor.js", "require": "./chains/query_constructor.cjs"}, "./chains/query_constructor/ir": {"types": {"import": "./chains/query_constructor/ir.d.ts", "require": "./chains/query_constructor/ir.d.cts", "default": "./chains/query_constructor/ir.d.ts"}, "import": "./chains/query_constructor/ir.js", "require": "./chains/query_constructor/ir.cjs"}, "./chains/retrieval": {"types": {"import": "./chains/retrieval.d.ts", "require": "./chains/retrieval.d.cts", "default": "./chains/retrieval.d.ts"}, "import": "./chains/retrieval.js", "require": "./chains/retrieval.cjs"}, "./chains/sql_db": {"types": {"import": "./chains/sql_db.d.ts", "require": "./chains/sql_db.d.cts", "default": "./chains/sql_db.d.ts"}, "import": "./chains/sql_db.js", "require": "./chains/sql_db.cjs"}, "./chains/graph_qa/cypher": {"types": {"import": "./chains/graph_qa/cypher.d.ts", "require": "./chains/graph_qa/cypher.d.cts", "default": "./chains/graph_qa/cypher.d.ts"}, "import": "./chains/graph_qa/cypher.js", "require": "./chains/graph_qa/cypher.cjs"}, "./chat_models/universal": {"types": {"import": "./chat_models/universal.d.ts", "require": "./chat_models/universal.d.cts", "default": "./chat_models/universal.d.ts"}, "import": "./chat_models/universal.js", "require": "./chat_models/universal.cjs"}, "./embeddings/cache_backed": {"types": {"import": "./embeddings/cache_backed.d.ts", "require": "./embeddings/cache_backed.d.cts", "default": "./embeddings/cache_backed.d.ts"}, "import": "./embeddings/cache_backed.js", "require": "./embeddings/cache_backed.cjs"}, "./embeddings/fake": {"types": {"import": "./embeddings/fake.d.ts", "require": "./embeddings/fake.d.cts", "default": "./embeddings/fake.d.ts"}, "import": "./embeddings/fake.js", "require": "./embeddings/fake.cjs"}, "./vectorstores/memory": {"types": {"import": "./vectorstores/memory.d.ts", "require": "./vectorstores/memory.d.cts", "default": "./vectorstores/memory.d.ts"}, "import": "./vectorstores/memory.js", "require": "./vectorstores/memory.cjs"}, "./text_splitter": {"types": {"import": "./text_splitter.d.ts", "require": "./text_splitter.d.cts", "default": "./text_splitter.d.ts"}, "import": "./text_splitter.js", "require": "./text_splitter.cjs"}, "./memory": {"types": {"import": "./memory.d.ts", "require": "./memory.d.cts", "default": "./memory.d.ts"}, "import": "./memory.js", "require": "./memory.cjs"}, "./memory/chat_memory": {"types": {"import": "./memory/chat_memory.d.ts", "require": "./memory/chat_memory.d.cts", "default": "./memory/chat_memory.d.ts"}, "import": "./memory/chat_memory.js", "require": "./memory/chat_memory.cjs"}, "./document": {"types": {"import": "./document.d.ts", "require": "./document.d.cts", "default": "./document.d.ts"}, "import": "./document.js", "require": "./document.cjs"}, "./document_loaders/base": {"types": {"import": "./document_loaders/base.d.ts", "require": "./document_loaders/base.d.cts", "default": "./document_loaders/base.d.ts"}, "import": "./document_loaders/base.js", "require": "./document_loaders/base.cjs"}, "./document_loaders/fs/buffer": {"types": {"import": "./document_loaders/fs/buffer.d.ts", "require": "./document_loaders/fs/buffer.d.cts", "default": "./document_loaders/fs/buffer.d.ts"}, "import": "./document_loaders/fs/buffer.js", "require": "./document_loaders/fs/buffer.cjs"}, "./document_loaders/fs/directory": {"types": {"import": "./document_loaders/fs/directory.d.ts", "require": "./document_loaders/fs/directory.d.cts", "default": "./document_loaders/fs/directory.d.ts"}, "import": "./document_loaders/fs/directory.js", "require": "./document_loaders/fs/directory.cjs"}, "./document_loaders/fs/json": {"types": {"import": "./document_loaders/fs/json.d.ts", "require": "./document_loaders/fs/json.d.cts", "default": "./document_loaders/fs/json.d.ts"}, "import": "./document_loaders/fs/json.js", "require": "./document_loaders/fs/json.cjs"}, "./document_loaders/fs/multi_file": {"types": {"import": "./document_loaders/fs/multi_file.d.ts", "require": "./document_loaders/fs/multi_file.d.cts", "default": "./document_loaders/fs/multi_file.d.ts"}, "import": "./document_loaders/fs/multi_file.js", "require": "./document_loaders/fs/multi_file.cjs"}, "./document_loaders/fs/text": {"types": {"import": "./document_loaders/fs/text.d.ts", "require": "./document_loaders/fs/text.d.cts", "default": "./document_loaders/fs/text.d.ts"}, "import": "./document_loaders/fs/text.js", "require": "./document_loaders/fs/text.cjs"}, "./document_transformers/openai_functions": {"types": {"import": "./document_transformers/openai_functions.d.ts", "require": "./document_transformers/openai_functions.d.cts", "default": "./document_transformers/openai_functions.d.ts"}, "import": "./document_transformers/openai_functions.js", "require": "./document_transformers/openai_functions.cjs"}, "./sql_db": {"types": {"import": "./sql_db.d.ts", "require": "./sql_db.d.cts", "default": "./sql_db.d.ts"}, "import": "./sql_db.js", "require": "./sql_db.cjs"}, "./callbacks": {"types": {"import": "./callbacks.d.ts", "require": "./callbacks.d.cts", "default": "./callbacks.d.ts"}, "import": "./callbacks.js", "require": "./callbacks.cjs"}, "./output_parsers": {"types": {"import": "./output_parsers.d.ts", "require": "./output_parsers.d.cts", "default": "./output_parsers.d.ts"}, "import": "./output_parsers.js", "require": "./output_parsers.cjs"}, "./output_parsers/expression": {"types": {"import": "./output_parsers/expression.d.ts", "require": "./output_parsers/expression.d.cts", "default": "./output_parsers/expression.d.ts"}, "import": "./output_parsers/expression.js", "require": "./output_parsers/expression.cjs"}, "./retrievers/contextual_compression": {"types": {"import": "./retrievers/contextual_compression.d.ts", "require": "./retrievers/contextual_compression.d.cts", "default": "./retrievers/contextual_compression.d.ts"}, "import": "./retrievers/contextual_compression.js", "require": "./retrievers/contextual_compression.cjs"}, "./retrievers/document_compressors": {"types": {"import": "./retrievers/document_compressors.d.ts", "require": "./retrievers/document_compressors.d.cts", "default": "./retrievers/document_compressors.d.ts"}, "import": "./retrievers/document_compressors.js", "require": "./retrievers/document_compressors.cjs"}, "./retrievers/ensemble": {"types": {"import": "./retrievers/ensemble.d.ts", "require": "./retrievers/ensemble.d.cts", "default": "./retrievers/ensemble.d.ts"}, "import": "./retrievers/ensemble.js", "require": "./retrievers/ensemble.cjs"}, "./retrievers/multi_query": {"types": {"import": "./retrievers/multi_query.d.ts", "require": "./retrievers/multi_query.d.cts", "default": "./retrievers/multi_query.d.ts"}, "import": "./retrievers/multi_query.js", "require": "./retrievers/multi_query.cjs"}, "./retrievers/multi_vector": {"types": {"import": "./retrievers/multi_vector.d.ts", "require": "./retrievers/multi_vector.d.cts", "default": "./retrievers/multi_vector.d.ts"}, "import": "./retrievers/multi_vector.js", "require": "./retrievers/multi_vector.cjs"}, "./retrievers/parent_document": {"types": {"import": "./retrievers/parent_document.d.ts", "require": "./retrievers/parent_document.d.cts", "default": "./retrievers/parent_document.d.ts"}, "import": "./retrievers/parent_document.js", "require": "./retrievers/parent_document.cjs"}, "./retrievers/time_weighted": {"types": {"import": "./retrievers/time_weighted.d.ts", "require": "./retrievers/time_weighted.d.cts", "default": "./retrievers/time_weighted.d.ts"}, "import": "./retrievers/time_weighted.js", "require": "./retrievers/time_weighted.cjs"}, "./retrievers/document_compressors/chain_extract": {"types": {"import": "./retrievers/document_compressors/chain_extract.d.ts", "require": "./retrievers/document_compressors/chain_extract.d.cts", "default": "./retrievers/document_compressors/chain_extract.d.ts"}, "import": "./retrievers/document_compressors/chain_extract.js", "require": "./retrievers/document_compressors/chain_extract.cjs"}, "./retrievers/document_compressors/embeddings_filter": {"types": {"import": "./retrievers/document_compressors/embeddings_filter.d.ts", "require": "./retrievers/document_compressors/embeddings_filter.d.cts", "default": "./retrievers/document_compressors/embeddings_filter.d.ts"}, "import": "./retrievers/document_compressors/embeddings_filter.js", "require": "./retrievers/document_compressors/embeddings_filter.cjs"}, "./retrievers/hyde": {"types": {"import": "./retrievers/hyde.d.ts", "require": "./retrievers/hyde.d.cts", "default": "./retrievers/hyde.d.ts"}, "import": "./retrievers/hyde.js", "require": "./retrievers/hyde.cjs"}, "./retrievers/score_threshold": {"types": {"import": "./retrievers/score_threshold.d.ts", "require": "./retrievers/score_threshold.d.cts", "default": "./retrievers/score_threshold.d.ts"}, "import": "./retrievers/score_threshold.js", "require": "./retrievers/score_threshold.cjs"}, "./retrievers/self_query": {"types": {"import": "./retrievers/self_query.d.ts", "require": "./retrievers/self_query.d.cts", "default": "./retrievers/self_query.d.ts"}, "import": "./retrievers/self_query.js", "require": "./retrievers/self_query.cjs"}, "./retrievers/self_query/functional": {"types": {"import": "./retrievers/self_query/functional.d.ts", "require": "./retrievers/self_query/functional.d.cts", "default": "./retrievers/self_query/functional.d.ts"}, "import": "./retrievers/self_query/functional.js", "require": "./retrievers/self_query/functional.cjs"}, "./retrievers/matryoshka_retriever": {"types": {"import": "./retrievers/matryoshka_retriever.d.ts", "require": "./retrievers/matryoshka_retriever.d.cts", "default": "./retrievers/matryoshka_retriever.d.ts"}, "import": "./retrievers/matryoshka_retriever.js", "require": "./retrievers/matryoshka_retriever.cjs"}, "./cache/file_system": {"types": {"import": "./cache/file_system.d.ts", "require": "./cache/file_system.d.cts", "default": "./cache/file_system.d.ts"}, "import": "./cache/file_system.js", "require": "./cache/file_system.cjs"}, "./stores/doc/base": {"types": {"import": "./stores/doc/base.d.ts", "require": "./stores/doc/base.d.cts", "default": "./stores/doc/base.d.ts"}, "import": "./stores/doc/base.js", "require": "./stores/doc/base.cjs"}, "./stores/doc/in_memory": {"types": {"import": "./stores/doc/in_memory.d.ts", "require": "./stores/doc/in_memory.d.cts", "default": "./stores/doc/in_memory.d.ts"}, "import": "./stores/doc/in_memory.js", "require": "./stores/doc/in_memory.cjs"}, "./stores/file/in_memory": {"types": {"import": "./stores/file/in_memory.d.ts", "require": "./stores/file/in_memory.d.cts", "default": "./stores/file/in_memory.d.ts"}, "import": "./stores/file/in_memory.js", "require": "./stores/file/in_memory.cjs"}, "./stores/file/node": {"types": {"import": "./stores/file/node.d.ts", "require": "./stores/file/node.d.cts", "default": "./stores/file/node.d.ts"}, "import": "./stores/file/node.js", "require": "./stores/file/node.cjs"}, "./stores/message/in_memory": {"types": {"import": "./stores/message/in_memory.d.ts", "require": "./stores/message/in_memory.d.cts", "default": "./stores/message/in_memory.d.ts"}, "import": "./stores/message/in_memory.js", "require": "./stores/message/in_memory.cjs"}, "./storage/encoder_backed": {"types": {"import": "./storage/encoder_backed.d.ts", "require": "./storage/encoder_backed.d.cts", "default": "./storage/encoder_backed.d.ts"}, "import": "./storage/encoder_backed.js", "require": "./storage/encoder_backed.cjs"}, "./storage/in_memory": {"types": {"import": "./storage/in_memory.d.ts", "require": "./storage/in_memory.d.cts", "default": "./storage/in_memory.d.ts"}, "import": "./storage/in_memory.js", "require": "./storage/in_memory.cjs"}, "./storage/file_system": {"types": {"import": "./storage/file_system.d.ts", "require": "./storage/file_system.d.cts", "default": "./storage/file_system.d.ts"}, "import": "./storage/file_system.js", "require": "./storage/file_system.cjs"}, "./hub": {"types": {"import": "./hub.d.ts", "require": "./hub.d.cts", "default": "./hub.d.ts"}, "import": "./hub.js", "require": "./hub.cjs"}, "./hub/node": {"types": {"import": "./hub/node.d.ts", "require": "./hub/node.d.cts", "default": "./hub/node.d.ts"}, "import": "./hub/node.js", "require": "./hub/node.cjs"}, "./util/document": {"types": {"import": "./util/document.d.ts", "require": "./util/document.d.cts", "default": "./util/document.d.ts"}, "import": "./util/document.js", "require": "./util/document.cjs"}, "./util/math": {"types": {"import": "./util/math.d.ts", "require": "./util/math.d.cts", "default": "./util/math.d.ts"}, "import": "./util/math.js", "require": "./util/math.cjs"}, "./util/time": {"types": {"import": "./util/time.d.ts", "require": "./util/time.d.cts", "default": "./util/time.d.ts"}, "import": "./util/time.js", "require": "./util/time.cjs"}, "./experimental/autogpt": {"types": {"import": "./experimental/autogpt.d.ts", "require": "./experimental/autogpt.d.cts", "default": "./experimental/autogpt.d.ts"}, "import": "./experimental/autogpt.js", "require": "./experimental/autogpt.cjs"}, "./experimental/openai_assistant": {"types": {"import": "./experimental/openai_assistant.d.ts", "require": "./experimental/openai_assistant.d.cts", "default": "./experimental/openai_assistant.d.ts"}, "import": "./experimental/openai_assistant.js", "require": "./experimental/openai_assistant.cjs"}, "./experimental/openai_files": {"types": {"import": "./experimental/openai_files.d.ts", "require": "./experimental/openai_files.d.cts", "default": "./experimental/openai_files.d.ts"}, "import": "./experimental/openai_files.js", "require": "./experimental/openai_files.cjs"}, "./experimental/babyagi": {"types": {"import": "./experimental/babyagi.d.ts", "require": "./experimental/babyagi.d.cts", "default": "./experimental/babyagi.d.ts"}, "import": "./experimental/babyagi.js", "require": "./experimental/babyagi.cjs"}, "./experimental/generative_agents": {"types": {"import": "./experimental/generative_agents.d.ts", "require": "./experimental/generative_agents.d.cts", "default": "./experimental/generative_agents.d.ts"}, "import": "./experimental/generative_agents.js", "require": "./experimental/generative_agents.cjs"}, "./experimental/plan_and_execute": {"types": {"import": "./experimental/plan_and_execute.d.ts", "require": "./experimental/plan_and_execute.d.cts", "default": "./experimental/plan_and_execute.d.ts"}, "import": "./experimental/plan_and_execute.js", "require": "./experimental/plan_and_execute.cjs"}, "./experimental/chains/violation_of_expectations": {"types": {"import": "./experimental/chains/violation_of_expectations.d.ts", "require": "./experimental/chains/violation_of_expectations.d.cts", "default": "./experimental/chains/violation_of_expectations.d.ts"}, "import": "./experimental/chains/violation_of_expectations.js", "require": "./experimental/chains/violation_of_expectations.cjs"}, "./experimental/masking": {"types": {"import": "./experimental/masking.d.ts", "require": "./experimental/masking.d.cts", "default": "./experimental/masking.d.ts"}, "import": "./experimental/masking.js", "require": "./experimental/masking.cjs"}, "./experimental/prompts/custom_format": {"types": {"import": "./experimental/prompts/custom_format.d.ts", "require": "./experimental/prompts/custom_format.d.cts", "default": "./experimental/prompts/custom_format.d.ts"}, "import": "./experimental/prompts/custom_format.js", "require": "./experimental/prompts/custom_format.cjs"}, "./experimental/prompts/handlebars": {"types": {"import": "./experimental/prompts/handlebars.d.ts", "require": "./experimental/prompts/handlebars.d.cts", "default": "./experimental/prompts/handlebars.d.ts"}, "import": "./experimental/prompts/handlebars.js", "require": "./experimental/prompts/handlebars.cjs"}, "./evaluation": {"types": {"import": "./evaluation.d.ts", "require": "./evaluation.d.cts", "default": "./evaluation.d.ts"}, "import": "./evaluation.js", "require": "./evaluation.cjs"}, "./smith": {"types": {"import": "./smith.d.ts", "require": "./smith.d.cts", "default": "./smith.d.ts"}, "import": "./smith.js", "require": "./smith.cjs"}, "./runnables/remote": {"types": {"import": "./runnables/remote.d.ts", "require": "./runnables/remote.d.cts", "default": "./runnables/remote.d.ts"}, "import": "./runnables/remote.js", "require": "./runnables/remote.cjs"}, "./indexes": {"types": {"import": "./indexes.d.ts", "require": "./indexes.d.cts", "default": "./indexes.d.ts"}, "import": "./indexes.js", "require": "./indexes.cjs"}, "./schema/query_constructor": {"types": {"import": "./schema/query_constructor.d.ts", "require": "./schema/query_constructor.d.cts", "default": "./schema/query_constructor.d.ts"}, "import": "./schema/query_constructor.js", "require": "./schema/query_constructor.cjs"}, "./schema/prompt_template": {"types": {"import": "./schema/prompt_template.d.ts", "require": "./schema/prompt_template.d.cts", "default": "./schema/prompt_template.d.ts"}, "import": "./schema/prompt_template.js", "require": "./schema/prompt_template.cjs"}, "./package.json": "./package.json"}}