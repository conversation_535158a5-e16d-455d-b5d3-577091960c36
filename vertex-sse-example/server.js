import { Hono } from "hono";
import { cors } from "hono/cors";
import { VertexAIClient } from "./vertex-client.js";
import { serve } from "@hono/node-server";
const app = new Hono();
import { setGlobalDispatcher, ProxyAgent } from "undici";
const dispatcher = new ProxyAgent("http://127.0.0.1:7890");
setGlobalDispatcher(dispatcher);
// 配置 CORS
app.use(
  "*",
  cors({
    origin: "*",
    allowMethods: ["GET", "POST", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"],
  })
);

// 配置你的项目信息
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT || "your-project-id";

// 创建 Vertex AI 客户端
const vertexClient = new VertexAIClient("smart-processor-454906-e2");

// 静态文件服务 - 提供测试页面
app.get("/", (c) => {
  return c.html(`
<!DOCTYPE html>
<html>
<head>
    <title>Vertex AI SSE Demo</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { margin-bottom: 20px; }
        textarea { width: 100%; height: 100px; margin-bottom: 10px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        #response { border: 1px solid #ddd; padding: 15px; min-height: 200px; white-space: pre-wrap; }
        .status { color: #666; font-style: italic; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Vertex AI Streaming Demo</h1>
    
    <div class="container">
        <textarea id="messageInput" placeholder="输入你的消息...">你好，请介绍一下人工智能的发展历史。</textarea>
        <br>
        <button id="sendBtn" onclick="sendMessage()">发送消息</button>
        <button id="clearBtn" onclick="clearResponse()">清空响应</button>
    </div>
    
    <div class="container">
        <h3>响应:</h3>
        <div id="response"></div>
    </div>

    <script>
        let eventSource = null;
        
        function sendMessage() {
            const message = document.getElementById('messageInput').value.trim();
            if (!message) return;
            
            const sendBtn = document.getElementById('sendBtn');
            const responseDiv = document.getElementById('response');
            
            // 禁用按钮
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            
            // 清空之前的响应
            responseDiv.innerHTML = '<div class="status">连接中...</div>';
            
            // 关闭之前的连接
            if (eventSource) {
                eventSource.close();
            }
            
            // 创建新的 SSE 连接
            eventSource = new EventSource('/api/chat/stream?' + new URLSearchParams({
                message: message
            }));
            
            eventSource.onopen = function() {
                responseDiv.innerHTML = '<div class="status">已连接，等待响应...</div>';
            };
            
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                
                if (data.type === 'chunk') {
                    // 移除状态信息，开始显示内容
                    if (responseDiv.querySelector('.status')) {
                        responseDiv.innerHTML = '';
                    }
                    responseDiv.textContent += data.text;
                } else if (data.type === 'done') {
                    responseDiv.innerHTML += '<div class="status">\\n\\n--- 响应完成 ---</div>';
                    eventSource.close();
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送消息';
                } else if (data.type === 'error') {
                    responseDiv.innerHTML += '<div class="error">\\n\\n错误: ' + data.message + '</div>';
                    eventSource.close();
                    sendBtn.disabled = false;
                    sendBtn.textContent = '发送消息';
                }
            };
            
            eventSource.onerror = function(event) {
                console.error('SSE error:', event);
                responseDiv.innerHTML += '<div class="error">\\n\\n连接错误，请重试</div>';
                eventSource.close();
                sendBtn.disabled = false;
                sendBtn.textContent = '发送消息';
            };
        }
        
        function clearResponse() {
            document.getElementById('response').innerHTML = '';
            if (eventSource) {
                eventSource.close();
            }
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = false;
            sendBtn.textContent = '发送消息';
        }
        
        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
  `);
});

// SSE 流式聊天接口
app.get("/api/chat/stream", async (c) => {
  const message = c.req.query("message");

  if (!message) {
    return c.json({ error: "Message is required" }, 400);
  }

  // 设置 SSE 响应头
  c.header("Content-Type", "text/event-stream");
  c.header("Cache-Control", "no-cache");
  c.header("Connection", "keep-alive");
  c.header("Access-Control-Allow-Origin", "*");

  const { readable, writable } = new TransformStream();
  const writer = writable.getWriter();
  const encoder = new TextEncoder();

  // 发送 SSE 消息的辅助函数
  const sendSSE = (data) => {
    const message = `data: ${JSON.stringify(data)}\n\n`;
    writer.write(encoder.encode(message));
  };

  // 异步处理流式响应
  (async () => {
    try {
      const messages = [{ role: "user", content: message }];

      console.log("Starting stream for message:", message);

      // 使用 Vertex AI 客户端进行流式生成
      for await (const chunk of vertexClient.streamGenerateContent(messages)) {
        console.log("Received chunk:", chunk.text);

        // 发送文本块到客户端
        sendSSE({
          type: "chunk",
          text: chunk.text,
          finishReason: chunk.finishReason,
        });

        // 如果有结束原因，停止流
        if (chunk.finishReason && chunk.finishReason !== "STOP") {
          console.log("Stream finished with reason:", chunk.finishReason);
          break;
        }
      }

      // 发送完成信号
      sendSSE({ type: "done" });
      console.log("Stream completed");
    } catch (error) {
      console.error("Stream error:", error);
      sendSSE({
        type: "error",
        message: error.message,
      });
    } finally {
      writer.close();
    }
  })();

  return new Response(readable);
});

// 健康检查接口
app.get("/health", (c) => {
  return c.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    project: PROJECT_ID,
    location: LOCATION,
  });
});

// 启动服务器
const port = process.env.PORT || 3004;

console.log(`🚀 Server starting on port ${port}`);
console.log(`📍 Project ID: ${PROJECT_ID}`);
console.log(`🌐 Open http://localhost:${port} to test`);

serve(
  {
    port,
    fetch: app.fetch,
  },
  (info) => {
    console.log(`Server is running on http://localhost:${info.port}`);
  }
);
